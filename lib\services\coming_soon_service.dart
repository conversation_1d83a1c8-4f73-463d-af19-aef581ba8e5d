/// خدمة إدارة الأصناف القريبة
/// تتيح إدارة الأصناف التي ستكون متوفرة قريباً وإضافتها للفواتير
library;

import '../database/database_helper.dart';
import '../models/coming_soon_item.dart';
import '../services/logging_service.dart';

class ComingSoonService {
  final DatabaseHelper _databaseHelper = DatabaseHelper();

  /// إضافة صنف قريباً جديد
  Future<int> addComingSoonItem(ComingSoonItem item) async {
    try {
      final db = await _databaseHelper.database;
      
      LoggingService.info(
        'إضافة صنف قريباً جديد',
        category: 'ComingSoonService',
        data: {
          'name': item.name,
          'expectedDate': item.expectedAvailabilityDate.toIso8601String(),
        },
      );

      final data = item.toMap();
      data.remove('id');
      
      final id = await db.insert('coming_soon_items', data);
      
      LoggingService.info(
        'تم إضافة الصنف القريب بنجاح',
        category: 'ComingSoonService',
        data: {'id': id},
      );
      
      return id;
    } catch (e) {
      LoggingService.error(
        'خطأ في إضافة الصنف القريب',
        category: 'ComingSoonService',
        data: {'error': e.toString()},
      );
      rethrow;
    }
  }

  /// تحديث صنف قريباً
  Future<void> updateComingSoonItem(ComingSoonItem item) async {
    try {
      final db = await _databaseHelper.database;
      
      final data = item.copyWith(updatedAt: DateTime.now()).toMap();
      
      await db.update(
        'coming_soon_items',
        data,
        where: 'id = ?',
        whereArgs: [item.id],
      );
      
      LoggingService.info(
        'تم تحديث الصنف القريب',
        category: 'ComingSoonService',
        data: {'id': item.id},
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في تحديث الصنف القريب',
        category: 'ComingSoonService',
        data: {'error': e.toString()},
      );
      rethrow;
    }
  }

  /// حذف صنف قريباً
  Future<void> deleteComingSoonItem(int id) async {
    try {
      final db = await _databaseHelper.database;
      
      await db.delete(
        'coming_soon_items',
        where: 'id = ?',
        whereArgs: [id],
      );
      
      LoggingService.info(
        'تم حذف الصنف القريب',
        category: 'ComingSoonService',
        data: {'id': id},
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في حذف الصنف القريب',
        category: 'ComingSoonService',
        data: {'error': e.toString()},
      );
      rethrow;
    }
  }

  /// الحصول على جميع الأصناف القريبة
  Future<List<ComingSoonItem>> getAllComingSoonItems() async {
    try {
      final db = await _databaseHelper.database;
      
      final result = await db.query(
        'coming_soon_items',
        where: 'is_active = ?',
        whereArgs: [1],
        orderBy: 'expected_availability_date ASC',
      );
      
      return result.map((map) => ComingSoonItem.fromMap(map)).toList();
    } catch (e) {
      LoggingService.error(
        'خطأ في جلب الأصناف القريبة',
        category: 'ComingSoonService',
        data: {'error': e.toString()},
      );
      return [];
    }
  }

  /// الحصول على الأصناف القريبة حسب الفئة
  Future<List<ComingSoonItem>> getComingSoonItemsByCategory(String category) async {
    try {
      final db = await _databaseHelper.database;
      
      final result = await db.query(
        'coming_soon_items',
        where: 'category = ? AND is_active = ?',
        whereArgs: [category, 1],
        orderBy: 'expected_availability_date ASC',
      );
      
      return result.map((map) => ComingSoonItem.fromMap(map)).toList();
    } catch (e) {
      LoggingService.error(
        'خطأ في جلب الأصناف القريبة حسب الفئة',
        category: 'ComingSoonService',
        data: {'error': e.toString()},
      );
      return [];
    }
  }

  /// البحث في الأصناف القريبة
  Future<List<ComingSoonItem>> searchComingSoonItems(String query) async {
    try {
      final db = await _databaseHelper.database;
      
      final result = await db.query(
        'coming_soon_items',
        where: '(name LIKE ? OR description LIKE ?) AND is_active = ?',
        whereArgs: ['%$query%', '%$query%', 1],
        orderBy: 'expected_availability_date ASC',
      );
      
      return result.map((map) => ComingSoonItem.fromMap(map)).toList();
    } catch (e) {
      LoggingService.error(
        'خطأ في البحث في الأصناف القريبة',
        category: 'ComingSoonService',
        data: {'error': e.toString()},
      );
      return [];
    }
  }

  /// الحصول على الأصناف المتأخرة
  Future<List<ComingSoonItem>> getOverdueItems() async {
    try {
      final db = await _databaseHelper.database;
      final now = DateTime.now();
      
      final result = await db.query(
        'coming_soon_items',
        where: 'expected_availability_date < ? AND is_active = ?',
        whereArgs: [now.toIso8601String(), 1],
        orderBy: 'expected_availability_date DESC',
      );
      
      return result.map((map) => ComingSoonItem.fromMap(map)).toList();
    } catch (e) {
      LoggingService.error(
        'خطأ في جلب الأصناف المتأخرة',
        category: 'ComingSoonService',
        data: {'error': e.toString()},
      );
      return [];
    }
  }

  /// إضافة صنف قريباً لفاتورة
  Future<int> addComingSoonItemToInvoice(ComingSoonInvoiceItem invoiceItem) async {
    try {
      final db = await _databaseHelper.database;
      
      LoggingService.info(
        'إضافة صنف قريباً للفاتورة',
        category: 'ComingSoonService',
        data: {
          'invoiceId': invoiceItem.invoiceId,
          'itemName': invoiceItem.itemName,
          'quantity': invoiceItem.quantity,
        },
      );

      final data = invoiceItem.toMap();
      data.remove('id');
      
      final id = await db.insert('coming_soon_invoice_items', data);
      
      LoggingService.info(
        'تم إضافة الصنف القريب للفاتورة بنجاح',
        category: 'ComingSoonService',
        data: {'id': id},
      );
      
      return id;
    } catch (e) {
      LoggingService.error(
        'خطأ في إضافة الصنف القريب للفاتورة',
        category: 'ComingSoonService',
        data: {'error': e.toString()},
      );
      rethrow;
    }
  }

  /// الحصول على أصناف قريباً في فاتورة
  Future<List<ComingSoonInvoiceItem>> getComingSoonItemsInInvoice(int invoiceId) async {
    try {
      final db = await _databaseHelper.database;
      
      final result = await db.query(
        'coming_soon_invoice_items',
        where: 'invoice_id = ?',
        whereArgs: [invoiceId],
        orderBy: 'created_at ASC',
      );
      
      return result.map((map) => ComingSoonInvoiceItem.fromMap(map)).toList();
    } catch (e) {
      LoggingService.error(
        'خطأ في جلب الأصناف القريبة في الفاتورة',
        category: 'ComingSoonService',
        data: {'error': e.toString()},
      );
      return [];
    }
  }

  /// حذف صنف قريباً من فاتورة
  Future<void> removeComingSoonItemFromInvoice(int invoiceItemId) async {
    try {
      final db = await _databaseHelper.database;
      
      await db.delete(
        'coming_soon_invoice_items',
        where: 'id = ?',
        whereArgs: [invoiceItemId],
      );
      
      LoggingService.info(
        'تم حذف الصنف القريب من الفاتورة',
        category: 'ComingSoonService',
        data: {'invoiceItemId': invoiceItemId},
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في حذف الصنف القريب من الفاتورة',
        category: 'ComingSoonService',
        data: {'error': e.toString()},
      );
      rethrow;
    }
  }

  /// الحصول على إحصائيات الأصناف القريبة
  Future<Map<String, dynamic>> getComingSoonStatistics() async {
    try {
      final db = await _databaseHelper.database;
      
      // إجمالي الأصناف القريبة
      final totalResult = await db.rawQuery(
        'SELECT COUNT(*) as total FROM coming_soon_items WHERE is_active = 1',
      );
      final total = totalResult.first['total'] as int;
      
      // الأصناف المتأخرة
      final overdueResult = await db.rawQuery(
        'SELECT COUNT(*) as overdue FROM coming_soon_items WHERE expected_availability_date < ? AND is_active = 1',
        [DateTime.now().toIso8601String()],
      );
      final overdue = overdueResult.first['overdue'] as int;
      
      // الأصناف القريبة (خلال 30 يوم)
      final soonResult = await db.rawQuery(
        'SELECT COUNT(*) as soon FROM coming_soon_items WHERE expected_availability_date BETWEEN ? AND ? AND is_active = 1',
        [
          DateTime.now().toIso8601String(),
          DateTime.now().add(const Duration(days: 30)).toIso8601String(),
        ],
      );
      final soon = soonResult.first['soon'] as int;
      
      return {
        'total': total,
        'overdue': overdue,
        'comingSoon': soon,
        'future': total - overdue - soon,
      };
    } catch (e) {
      LoggingService.error(
        'خطأ في جلب إحصائيات الأصناف القريبة',
        category: 'ComingSoonService',
        data: {'error': e.toString()},
      );
      return {
        'total': 0,
        'overdue': 0,
        'comingSoon': 0,
        'future': 0,
      };
    }
  }

  /// تفعيل أو إلغاء تفعيل صنف قريباً
  Future<void> toggleComingSoonItemStatus(int id, bool isActive) async {
    try {
      final db = await _databaseHelper.database;
      
      await db.update(
        'coming_soon_items',
        {
          'is_active': isActive ? 1 : 0,
          'updated_at': DateTime.now().toIso8601String(),
        },
        where: 'id = ?',
        whereArgs: [id],
      );
      
      LoggingService.info(
        'تم تغيير حالة الصنف القريب',
        category: 'ComingSoonService',
        data: {'id': id, 'isActive': isActive},
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في تغيير حالة الصنف القريب',
        category: 'ComingSoonService',
        data: {'error': e.toString()},
      );
      rethrow;
    }
  }
}
