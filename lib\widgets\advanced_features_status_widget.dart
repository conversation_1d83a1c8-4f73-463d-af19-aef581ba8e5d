/// ويدجت عرض حالة الميزات المتقدمة
/// يعرض حالة جميع الميزات المتقدمة مع إمكانية إعادة التهيئة
library;

import 'package:flutter/material.dart';
import '../services/advanced_features_initialization_service.dart';
import '../constants/revolutionary_design_colors.dart';

class AdvancedFeaturesStatusWidget extends StatefulWidget {
  const AdvancedFeaturesStatusWidget({super.key});

  @override
  State<AdvancedFeaturesStatusWidget> createState() =>
      _AdvancedFeaturesStatusWidgetState();
}

class _AdvancedFeaturesStatusWidgetState
    extends State<AdvancedFeaturesStatusWidget> {
  final AdvancedFeaturesInitializationService _initService =
      AdvancedFeaturesInitializationService();

  Map<String, dynamic>? _featureStatus;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadFeatureStatus();
  }

  /// تحميل حالة الميزات
  Future<void> _loadFeatureStatus() async {
    setState(() => _isLoading = true);

    try {
      final status = await _initService.getFeatureStatus();
      setState(() {
        _featureStatus = status;
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطأ في تحميل حالة الميزات: $e')),
        );
      }
    }
  }

  /// إعادة تهيئة الميزات
  Future<void> _reinitializeFeatures() async {
    setState(() => _isLoading = true);

    try {
      final success = await _initService.reinitializeFeatures();
      
      if (success) {
        await _loadFeatureStatus();
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم إعادة تهيئة الميزات بنجاح'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } else {
        setState(() => _isLoading = false);
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('فشل في إعادة تهيئة الميزات'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطأ في إعادة التهيئة: $e')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.all(16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(
                  Icons.extension,
                  color: RevolutionaryColors.damascusSky,
                ),
                const SizedBox(width: 8),
                const Expanded(
                  child: Text(
                    'حالة الميزات المتقدمة',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                IconButton(
                  onPressed: _isLoading ? null : _loadFeatureStatus,
                  icon: _isLoading
                      ? const SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        )
                      : const Icon(Icons.refresh),
                  tooltip: 'تحديث',
                ),
                IconButton(
                  onPressed: _isLoading ? null : _reinitializeFeatures,
                  icon: const Icon(Icons.restart_alt),
                  tooltip: 'إعادة تهيئة',
                ),
              ],
            ),
            const SizedBox(height: 16),
            if (_isLoading)
              const Center(
                child: CircularProgressIndicator(),
              )
            else if (_featureStatus == null)
              const Center(
                child: Text('لا توجد بيانات متاحة'),
              )
            else
              _buildStatusContent(),
          ],
        ),
      ),
    );
  }

  /// بناء محتوى الحالة
  Widget _buildStatusContent() {
    final isInitialized = _featureStatus!['isInitialized'] as bool;
    final features = _featureStatus!['features'] as Map<String, dynamic>?;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // الحالة العامة
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: isInitialized
                ? Colors.green.withValues(alpha: 0.1)
                : Colors.red.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: isInitialized ? Colors.green : Colors.red,
              width: 1,
            ),
          ),
          child: Row(
            children: [
              Icon(
                isInitialized ? Icons.check_circle : Icons.error,
                color: isInitialized ? Colors.green : Colors.red,
              ),
              const SizedBox(width: 8),
              Text(
                isInitialized ? 'الميزات مهيأة ونشطة' : 'الميزات غير مهيأة',
                style: TextStyle(
                  color: isInitialized ? Colors.green : Colors.red,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 16),

        // تفاصيل الميزات
        if (features != null && features.isNotEmpty) ...[
          const Text(
            'تفاصيل الميزات:',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          ...features.entries.map((entry) {
            return _buildFeatureItem(entry.key, entry.value);
          }),
        ],

        const SizedBox(height: 16),

        // معلومات إضافية
        Text(
          'آخر تحديث: ${_formatTimestamp(_featureStatus!['timestamp'])}',
          style: const TextStyle(
            fontSize: 12,
            color: Colors.grey,
          ),
        ),
      ],
    );
  }

  /// بناء عنصر الميزة
  Widget _buildFeatureItem(String featureName, Map<String, dynamic> featureData) {
    final status = featureData['status'] as String;
    final isActive = status == 'active';

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Row(
        children: [
          Icon(
            _getFeatureIcon(featureName),
            color: isActive ? Colors.green : Colors.red,
            size: 20,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  _getFeatureName(featureName),
                  style: const TextStyle(fontWeight: FontWeight.w500),
                ),
                if (featureData.containsKey('error'))
                  Text(
                    'خطأ: ${featureData['error']}',
                    style: const TextStyle(
                      fontSize: 12,
                      color: Colors.red,
                    ),
                  )
                else if (isActive) ...[
                  if (featureData.containsKey('templatesCount'))
                    Text(
                      'القوالب: ${featureData['templatesCount']}',
                      style: const TextStyle(fontSize: 12, color: Colors.grey),
                    ),
                  if (featureData.containsKey('declarationsCount'))
                    Text(
                      'الإقرارات: ${featureData['declarationsCount']}',
                      style: const TextStyle(fontSize: 12, color: Colors.grey),
                    ),
                  if (featureData.containsKey('notificationsCount'))
                    Text(
                      'التنبيهات: ${featureData['notificationsCount']}',
                      style: const TextStyle(fontSize: 12, color: Colors.grey),
                    ),
                ],
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: isActive
                  ? Colors.green.withValues(alpha: 0.1)
                  : Colors.red.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              isActive ? 'نشط' : 'خطأ',
              style: TextStyle(
                fontSize: 12,
                color: isActive ? Colors.green : Colors.red,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// الحصول على أيقونة الميزة
  IconData _getFeatureIcon(String featureName) {
    switch (featureName) {
      case 'visualReportBuilder':
        return Icons.design_services;
      case 'syrianTax':
        return Icons.account_balance;
      case 'smartNotifications':
        return Icons.notifications_active;
      default:
        return Icons.extension;
    }
  }

  /// الحصول على اسم الميزة
  String _getFeatureName(String featureName) {
    switch (featureName) {
      case 'visualReportBuilder':
        return 'منشئ التقارير المرئي';
      case 'syrianTax':
        return 'النظام الضريبي السوري';
      case 'smartNotifications':
        return 'التنبيهات الذكية';
      default:
        return featureName;
    }
  }

  /// تنسيق الطابع الزمني
  String _formatTimestamp(String timestamp) {
    try {
      final dateTime = DateTime.parse(timestamp);
      return '${dateTime.day}/${dateTime.month}/${dateTime.year} ${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
    } catch (e) {
      return timestamp;
    }
  }
}
