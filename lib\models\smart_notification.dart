/// نموذج الإشعار الذكي
/// يحتوي على معلومات الإشعار والتنبيهات في النظام
class SmartNotification {
  final int? id;
  final String title;
  final String message;
  final String type; // payment, inventory, backup, system
  final bool isRead;
  final DateTime createdAt;
  final DateTime? readAt;
  final String? actionUrl;
  final Map<String, dynamic>? metadata;
  final String priority; // low, medium, high, urgent
  final String? iconName;
  final String? soundName;

  const SmartNotification({
    this.id,
    required this.title,
    required this.message,
    required this.type,
    required this.isRead,
    required this.createdAt,
    this.readAt,
    this.actionUrl,
    this.metadata,
    required this.priority,
    this.iconName,
    this.soundName,
  });

  /// إنشاء SmartNotification من Map
  factory SmartNotification.fromMap(Map<String, dynamic> map) {
    return SmartNotification(
      id: map['id']?.toInt(),
      title: map['title'] ?? '',
      message: map['message'] ?? '',
      type: map['type'] ?? 'system',
      isRead: map['is_read'] == 1,
      createdAt: DateTime.parse(map['created_at']),
      readAt: map['read_at'] != null ? DateTime.parse(map['read_at']) : null,
      actionUrl: map['action_url'],
      metadata: map['metadata'] != null 
          ? Map<String, dynamic>.from(map['metadata'])
          : null,
      priority: map['priority'] ?? 'medium',
      iconName: map['icon_name'],
      soundName: map['sound_name'],
    );
  }

  /// تحويل SmartNotification إلى Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'title': title,
      'message': message,
      'type': type,
      'is_read': isRead ? 1 : 0,
      'created_at': createdAt.toIso8601String(),
      'read_at': readAt?.toIso8601String(),
      'action_url': actionUrl,
      'metadata': metadata,
      'priority': priority,
      'icon_name': iconName,
      'sound_name': soundName,
    };
  }

  /// إنشاء نسخة معدلة من SmartNotification
  SmartNotification copyWith({
    int? id,
    String? title,
    String? message,
    String? type,
    bool? isRead,
    DateTime? createdAt,
    DateTime? readAt,
    String? actionUrl,
    Map<String, dynamic>? metadata,
    String? priority,
    String? iconName,
    String? soundName,
  }) {
    return SmartNotification(
      id: id ?? this.id,
      title: title ?? this.title,
      message: message ?? this.message,
      type: type ?? this.type,
      isRead: isRead ?? this.isRead,
      createdAt: createdAt ?? this.createdAt,
      readAt: readAt ?? this.readAt,
      actionUrl: actionUrl ?? this.actionUrl,
      metadata: metadata ?? this.metadata,
      priority: priority ?? this.priority,
      iconName: iconName ?? this.iconName,
      soundName: soundName ?? this.soundName,
    );
  }

  /// تحديد الإشعار كمقروء
  SmartNotification markAsRead() {
    return copyWith(
      isRead: true,
      readAt: DateTime.now(),
    );
  }

  /// تحديد الإشعار كغير مقروء
  SmartNotification markAsUnread() {
    return copyWith(
      isRead: false,
      readAt: null,
    );
  }

  /// الحصول على اسم نوع الإشعار
  String get typeDisplayName {
    switch (type) {
      case 'payment':
        return 'دفعات';
      case 'inventory':
        return 'مخزون';
      case 'backup':
        return 'نسخ احتياطية';
      case 'system':
        return 'نظام';
      case 'user':
        return 'مستخدم';
      case 'security':
        return 'أمان';
      default:
        return 'عام';
    }
  }

  /// الحصول على اسم الأولوية
  String get priorityDisplayName {
    switch (priority) {
      case 'low':
        return 'منخفضة';
      case 'medium':
        return 'متوسطة';
      case 'high':
        return 'عالية';
      case 'urgent':
        return 'عاجلة';
      default:
        return 'متوسطة';
    }
  }

  /// التحقق من انتهاء صلاحية الإشعار
  bool get isExpired {
    final now = DateTime.now();
    final daysSinceCreated = now.difference(createdAt).inDays;
    return daysSinceCreated > 30; // انتهاء الصلاحية بعد 30 يوم
  }

  /// التحقق من كون الإشعار جديد (أقل من 24 ساعة)
  bool get isNew {
    final now = DateTime.now();
    final hoursSinceCreated = now.difference(createdAt).inHours;
    return hoursSinceCreated < 24;
  }

  @override
  String toString() {
    return 'SmartNotification(id: $id, title: $title, type: $type, isRead: $isRead)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is SmartNotification &&
        other.id == id &&
        other.title == title &&
        other.message == message &&
        other.type == type;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        title.hashCode ^
        message.hashCode ^
        type.hashCode;
  }
}

/// أنواع الإشعارات
class NotificationTypes {
  static const String payment = 'payment';
  static const String inventory = 'inventory';
  static const String backup = 'backup';
  static const String system = 'system';
  static const String user = 'user';
  static const String security = 'security';

  /// الحصول على جميع الأنواع
  static List<String> getAllTypes() {
    return [payment, inventory, backup, system, user, security];
  }

  /// الحصول على اسم النوع
  static String getDisplayName(String type) {
    switch (type) {
      case payment:
        return 'دفعات';
      case inventory:
        return 'مخزون';
      case backup:
        return 'نسخ احتياطية';
      case system:
        return 'نظام';
      case user:
        return 'مستخدم';
      case security:
        return 'أمان';
      default:
        return 'عام';
    }
  }
}

/// أولويات الإشعارات
class NotificationPriorities {
  static const String low = 'low';
  static const String medium = 'medium';
  static const String high = 'high';
  static const String urgent = 'urgent';

  /// الحصول على جميع الأولويات
  static List<String> getAllPriorities() {
    return [low, medium, high, urgent];
  }

  /// الحصول على اسم الأولوية
  static String getDisplayName(String priority) {
    switch (priority) {
      case low:
        return 'منخفضة';
      case medium:
        return 'متوسطة';
      case high:
        return 'عالية';
      case urgent:
        return 'عاجلة';
      default:
        return 'متوسطة';
    }
  }
}

/// منشئ الإشعارات
class NotificationBuilder {
  /// إنشاء إشعار دفعة مستحقة
  static SmartNotification createPaymentDueNotification({
    required String customerName,
    required double amount,
    required DateTime dueDate,
  }) {
    return SmartNotification(
      title: 'دفعة مستحقة',
      message: 'دفعة بقيمة $amount ل.س مستحقة للعميل $customerName',
      type: NotificationTypes.payment,
      isRead: false,
      createdAt: DateTime.now(),
      priority: NotificationPriorities.high,
      metadata: {
        'customer_name': customerName,
        'amount': amount,
        'due_date': dueDate.toIso8601String(),
      },
    );
  }

  /// إنشاء إشعار مخزون منخفض
  static SmartNotification createLowStockNotification({
    required String itemName,
    required int currentStock,
    required int minimumStock,
  }) {
    return SmartNotification(
      title: 'مخزون منخفض',
      message: 'المخزون المتاح للصنف $itemName هو $currentStock (الحد الأدنى: $minimumStock)',
      type: NotificationTypes.inventory,
      isRead: false,
      createdAt: DateTime.now(),
      priority: currentStock == 0 ? NotificationPriorities.urgent : NotificationPriorities.high,
      metadata: {
        'item_name': itemName,
        'current_stock': currentStock,
        'minimum_stock': minimumStock,
      },
    );
  }

  /// إنشاء إشعار نسخة احتياطية
  static SmartNotification createBackupNotification({
    required String status,
    required String message,
  }) {
    return SmartNotification(
      title: 'النسخ الاحتياطية',
      message: message,
      type: NotificationTypes.backup,
      isRead: false,
      createdAt: DateTime.now(),
      priority: status == 'success' ? NotificationPriorities.low : NotificationPriorities.medium,
      metadata: {
        'backup_status': status,
      },
    );
  }

  /// إنشاء إشعار نظام
  static SmartNotification createSystemNotification({
    required String title,
    required String message,
    String priority = NotificationPriorities.medium,
  }) {
    return SmartNotification(
      title: title,
      message: message,
      type: NotificationTypes.system,
      isRead: false,
      createdAt: DateTime.now(),
      priority: priority,
    );
  }
}
