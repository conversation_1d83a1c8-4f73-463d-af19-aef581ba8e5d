/// خدمة تقييم الأداء
/// توفر جميع العمليات المتعلقة بتقييم أداء الموظفين
library;

import '../database/database_helper.dart';
import '../services/logging_service.dart';
import '../services/audit_service.dart';
import '../exceptions/validation_exception.dart';
import '../models/performance_evaluation_models.dart';

class PerformanceEvaluationService {
  final DatabaseHelper _databaseHelper = DatabaseHelper();

  /// إنشاء جداول تقييم الأداء
  Future<void> _createEvaluationTables() async {
    final db = await _databaseHelper.database;

    // جدول معايير التقييم
    await db.execute('''
      CREATE TABLE IF NOT EXISTS evaluation_criteria (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        description TEXT NOT NULL,
        category TEXT NOT NULL,
        weight REAL NOT NULL CHECK (weight >= 0 AND weight <= 1),
        max_score INTEGER DEFAULT 5,
        evaluation_type TEXT DEFAULT 'rating',
        is_active BOOLEAN DEFAULT 1,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL
      )
    ''');

    // جدول دورات التقييم
    await db.execute('''
      CREATE TABLE IF NOT EXISTS evaluation_cycles (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        description TEXT NOT NULL,
        start_date TEXT NOT NULL,
        end_date TEXT NOT NULL,
        status TEXT DEFAULT 'draft',
        evaluation_type TEXT DEFAULT 'annual',
        is_active BOOLEAN DEFAULT 1,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL
      )
    ''');

    // جدول تقييمات الموظفين
    await db.execute('''
      CREATE TABLE IF NOT EXISTS employee_evaluations (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        employee_id INTEGER NOT NULL,
        cycle_id INTEGER NOT NULL,
        evaluator_id INTEGER,
        status TEXT DEFAULT 'draft',
        overall_score REAL,
        overall_rating TEXT,
        strengths TEXT,
        weaknesses TEXT,
        development_areas TEXT,
        goals TEXT,
        comments TEXT,
        submitted_at TEXT,
        reviewed_at TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        FOREIGN KEY (employee_id) REFERENCES employees (id),
        FOREIGN KEY (cycle_id) REFERENCES evaluation_cycles (id),
        FOREIGN KEY (evaluator_id) REFERENCES employees (id),
        UNIQUE(employee_id, cycle_id)
      )
    ''');

    // جدول تفاصيل التقييم
    await db.execute('''
      CREATE TABLE IF NOT EXISTS evaluation_details (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        evaluation_id INTEGER NOT NULL,
        criteria_id INTEGER NOT NULL,
        score REAL NOT NULL,
        rating TEXT,
        comments TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        FOREIGN KEY (evaluation_id) REFERENCES employee_evaluations (id),
        FOREIGN KEY (criteria_id) REFERENCES evaluation_criteria (id),
        UNIQUE(evaluation_id, criteria_id)
      )
    ''');
  }

  /// إضافة معيار تقييم جديد
  Future<EvaluationCriteria> addEvaluationCriteria(
    EvaluationCriteria criteria,
  ) async {
    try {
      await _createEvaluationTables();

      // التحقق من عدم تكرار اسم المعيار
      final existing = await _getCriteriaByName(criteria.name);
      if (existing != null) {
        throw ValidationException('يوجد معيار تقييم بنفس الاسم');
      }

      final db = await _databaseHelper.database;
      final id = await db.insert('evaluation_criteria', criteria.toMap());

      final savedCriteria = criteria.copyWith(id: id);

      // تسجيل في سجل المراجعة
      await AuditService.log(
        action: 'CREATE',
        entityType: 'EvaluationCriteria',
        entityId: id,
        description: 'إضافة معيار تقييم جديد: ${criteria.name}',
        newValues: savedCriteria.toMap(),
      );

      LoggingService.info(
        'تم إضافة معيار تقييم جديد',
        category: 'PerformanceEvaluationService',
        data: {'criteriaId': id, 'name': criteria.name},
      );

      return savedCriteria;
    } catch (e) {
      LoggingService.error(
        'خطأ في إضافة معيار التقييم',
        category: 'PerformanceEvaluationService',
        data: {'error': e.toString()},
      );
      rethrow;
    }
  }

  /// الحصول على جميع معايير التقييم
  Future<List<EvaluationCriteria>> getAllEvaluationCriteria({
    String? category,
    bool? isActive,
  }) async {
    try {
      await _createEvaluationTables();
      final db = await _databaseHelper.database;

      String whereClause = '1=1';
      List<dynamic> whereArgs = [];

      if (category != null) {
        whereClause += ' AND category = ?';
        whereArgs.add(category);
      }

      if (isActive != null) {
        whereClause += ' AND is_active = ?';
        whereArgs.add(isActive ? 1 : 0);
      }

      final result = await db.query(
        'evaluation_criteria',
        where: whereClause,
        whereArgs: whereArgs.isNotEmpty ? whereArgs : null,
        orderBy: 'category, name',
      );

      return result.map((map) => EvaluationCriteria.fromMap(map)).toList();
    } catch (e) {
      LoggingService.error(
        'خطأ في جلب معايير التقييم',
        category: 'PerformanceEvaluationService',
        data: {'error': e.toString()},
      );
      return [];
    }
  }

  /// إضافة دورة تقييم جديدة
  Future<EvaluationCycle> addEvaluationCycle(EvaluationCycle cycle) async {
    try {
      await _createEvaluationTables();

      // التحقق من صحة التواريخ
      if (cycle.startDate.isAfter(cycle.endDate)) {
        throw ValidationException(
          'تاريخ البداية لا يمكن أن يكون بعد تاريخ النهاية',
        );
      }

      final db = await _databaseHelper.database;
      final id = await db.insert('evaluation_cycles', cycle.toMap());

      final savedCycle = cycle.copyWith(id: id);

      // تسجيل في سجل المراجعة
      await AuditService.log(
        action: 'CREATE',
        entityType: 'EvaluationCycle',
        entityId: id,
        description: 'إضافة دورة تقييم جديدة: ${cycle.name}',
        newValues: savedCycle.toMap(),
      );

      LoggingService.info(
        'تم إضافة دورة تقييم جديدة',
        category: 'PerformanceEvaluationService',
        data: {'cycleId': id, 'name': cycle.name},
      );

      return savedCycle;
    } catch (e) {
      LoggingService.error(
        'خطأ في إضافة دورة التقييم',
        category: 'PerformanceEvaluationService',
        data: {'error': e.toString()},
      );
      rethrow;
    }
  }

  /// الحصول على جميع دورات التقييم
  Future<List<EvaluationCycle>> getAllEvaluationCycles({
    String? status,
    bool? isActive,
  }) async {
    try {
      await _createEvaluationTables();
      final db = await _databaseHelper.database;

      String whereClause = '1=1';
      List<dynamic> whereArgs = [];

      if (status != null) {
        whereClause += ' AND status = ?';
        whereArgs.add(status);
      }

      if (isActive != null) {
        whereClause += ' AND is_active = ?';
        whereArgs.add(isActive ? 1 : 0);
      }

      final result = await db.query(
        'evaluation_cycles',
        where: whereClause,
        whereArgs: whereArgs.isNotEmpty ? whereArgs : null,
        orderBy: 'start_date DESC',
      );

      return result.map((map) => EvaluationCycle.fromMap(map)).toList();
    } catch (e) {
      LoggingService.error(
        'خطأ في جلب دورات التقييم',
        category: 'PerformanceEvaluationService',
        data: {'error': e.toString()},
      );
      return [];
    }
  }

  /// إنشاء تقييم موظف
  Future<EmployeeEvaluation> createEmployeeEvaluation({
    required int employeeId,
    required int cycleId,
    int? evaluatorId,
  }) async {
    try {
      await _createEvaluationTables();

      // التحقق من عدم وجود تقييم مسبق
      final existing = await _getEmployeeEvaluation(employeeId, cycleId);
      if (existing != null) {
        throw ValidationException('يوجد تقييم مسبق للموظف في هذه الدورة');
      }

      final evaluation = EmployeeEvaluation(
        employeeId: employeeId,
        cycleId: cycleId,
        evaluatorId: evaluatorId,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      final db = await _databaseHelper.database;
      final id = await db.insert('employee_evaluations', evaluation.toMap());

      final savedEvaluation = evaluation.copyWith(id: id);

      // تسجيل في سجل المراجعة
      await AuditService.log(
        action: 'CREATE',
        entityType: 'EmployeeEvaluation',
        entityId: id,
        description: 'إنشاء تقييم موظف جديد',
        newValues: savedEvaluation.toMap(),
      );

      LoggingService.info(
        'تم إنشاء تقييم موظف جديد',
        category: 'PerformanceEvaluationService',
        data: {
          'evaluationId': id,
          'employeeId': employeeId,
          'cycleId': cycleId,
        },
      );

      return savedEvaluation;
    } catch (e) {
      LoggingService.error(
        'خطأ في إنشاء تقييم الموظف',
        category: 'PerformanceEvaluationService',
        data: {
          'employeeId': employeeId,
          'cycleId': cycleId,
          'error': e.toString(),
        },
      );
      rethrow;
    }
  }

  /// إضافة تفاصيل التقييم
  Future<EvaluationDetail> addEvaluationDetail(EvaluationDetail detail) async {
    try {
      await _createEvaluationTables();

      final db = await _databaseHelper.database;

      // حذف التفصيل الموجود إن وجد (للتحديث)
      await db.delete(
        'evaluation_details',
        where: 'evaluation_id = ? AND criteria_id = ?',
        whereArgs: [detail.evaluationId, detail.criteriaId],
      );

      final id = await db.insert('evaluation_details', detail.toMap());
      final savedDetail = detail.copyWith(id: id);

      LoggingService.info(
        'تم إضافة تفصيل التقييم',
        category: 'PerformanceEvaluationService',
        data: {
          'detailId': id,
          'evaluationId': detail.evaluationId,
          'criteriaId': detail.criteriaId,
          'score': detail.score,
        },
      );

      return savedDetail;
    } catch (e) {
      LoggingService.error(
        'خطأ في إضافة تفصيل التقييم',
        category: 'PerformanceEvaluationService',
        data: {'error': e.toString()},
      );
      rethrow;
    }
  }

  // دوال مساعدة خاصة
  Future<EvaluationCriteria?> _getCriteriaByName(String name) async {
    final db = await _databaseHelper.database;
    final result = await db.query(
      'evaluation_criteria',
      where: 'name = ?',
      whereArgs: [name],
      limit: 1,
    );
    return result.isNotEmpty ? EvaluationCriteria.fromMap(result.first) : null;
  }

  Future<EmployeeEvaluation?> _getEmployeeEvaluation(
    int employeeId,
    int cycleId,
  ) async {
    final db = await _databaseHelper.database;
    final result = await db.query(
      'employee_evaluations',
      where: 'employee_id = ? AND cycle_id = ?',
      whereArgs: [employeeId, cycleId],
      limit: 1,
    );
    return result.isNotEmpty ? EmployeeEvaluation.fromMap(result.first) : null;
  }

  /// الحصول على تقييمات الموظف
  Future<List<EmployeeEvaluation>> getEmployeeEvaluations({
    int? employeeId,
    int? cycleId,
    String? status,
  }) async {
    try {
      await _createEvaluationTables();
      final db = await _databaseHelper.database;

      String whereClause = '1=1';
      List<dynamic> whereArgs = [];

      if (employeeId != null) {
        whereClause += ' AND employee_id = ?';
        whereArgs.add(employeeId);
      }

      if (cycleId != null) {
        whereClause += ' AND cycle_id = ?';
        whereArgs.add(cycleId);
      }

      if (status != null) {
        whereClause += ' AND status = ?';
        whereArgs.add(status);
      }

      final result = await db.query(
        'employee_evaluations',
        where: whereClause,
        whereArgs: whereArgs.isNotEmpty ? whereArgs : null,
        orderBy: 'created_at DESC',
      );

      return result.map((map) => EmployeeEvaluation.fromMap(map)).toList();
    } catch (e) {
      LoggingService.error(
        'خطأ في جلب تقييمات الموظفين',
        category: 'PerformanceEvaluationService',
        data: {'error': e.toString()},
      );
      return [];
    }
  }

  /// الحصول على تفاصيل التقييم
  Future<List<EvaluationDetail>> getEvaluationDetails(int evaluationId) async {
    try {
      await _createEvaluationTables();
      final db = await _databaseHelper.database;

      final result = await db.query(
        'evaluation_details',
        where: 'evaluation_id = ?',
        whereArgs: [evaluationId],
        orderBy: 'criteria_id',
      );

      return result.map((map) => EvaluationDetail.fromMap(map)).toList();
    } catch (e) {
      LoggingService.error(
        'خطأ في جلب تفاصيل التقييم',
        category: 'PerformanceEvaluationService',
        data: {'evaluationId': evaluationId, 'error': e.toString()},
      );
      return [];
    }
  }

  /// تحديث حالة التقييم
  Future<EmployeeEvaluation> updateEvaluationStatus({
    required int evaluationId,
    required String status,
    double? overallScore,
    String? overallRating,
    String? strengths,
    String? weaknesses,
    String? developmentAreas,
    String? goals,
    String? comments,
  }) async {
    try {
      await _createEvaluationTables();
      final db = await _databaseHelper.database;

      final updateData = <String, dynamic>{
        'status': status,
        'updated_at': DateTime.now().toIso8601String(),
      };

      if (overallScore != null) updateData['overall_score'] = overallScore;
      if (overallRating != null) updateData['overall_rating'] = overallRating;
      if (strengths != null) updateData['strengths'] = strengths;
      if (weaknesses != null) updateData['weaknesses'] = weaknesses;
      if (developmentAreas != null) {
        updateData['development_areas'] = developmentAreas;
      }
      if (goals != null) updateData['goals'] = goals;
      if (comments != null) updateData['comments'] = comments;

      if (status == 'submitted') {
        updateData['submitted_at'] = DateTime.now().toIso8601String();
      } else if (status == 'reviewed' || status == 'approved') {
        updateData['reviewed_at'] = DateTime.now().toIso8601String();
      }

      await db.update(
        'employee_evaluations',
        updateData,
        where: 'id = ?',
        whereArgs: [evaluationId],
      );

      // الحصول على التقييم المحدث
      final result = await db.query(
        'employee_evaluations',
        where: 'id = ?',
        whereArgs: [evaluationId],
        limit: 1,
      );

      if (result.isEmpty) {
        throw ValidationException('التقييم غير موجود');
      }

      final updatedEvaluation = EmployeeEvaluation.fromMap(result.first);

      // تسجيل في سجل المراجعة
      await AuditService.log(
        action: 'UPDATE',
        entityType: 'EmployeeEvaluation',
        entityId: evaluationId,
        description: 'تحديث حالة التقييم إلى: $status',
        newValues: updatedEvaluation.toMap(),
      );

      LoggingService.info(
        'تم تحديث حالة التقييم',
        category: 'PerformanceEvaluationService',
        data: {'evaluationId': evaluationId, 'status': status},
      );

      return updatedEvaluation;
    } catch (e) {
      LoggingService.error(
        'خطأ في تحديث حالة التقييم',
        category: 'PerformanceEvaluationService',
        data: {'evaluationId': evaluationId, 'error': e.toString()},
      );
      rethrow;
    }
  }

  /// حساب النتيجة الإجمالية للتقييم
  Future<double> calculateOverallScore(int evaluationId) async {
    try {
      await _createEvaluationTables();
      final db = await _databaseHelper.database;

      // الحصول على تفاصيل التقييم مع معايير التقييم
      final result = await db.rawQuery(
        '''
        SELECT
          ed.score,
          ec.weight,
          ec.max_score
        FROM evaluation_details ed
        JOIN evaluation_criteria ec ON ed.criteria_id = ec.id
        WHERE ed.evaluation_id = ? AND ec.is_active = 1
      ''',
        [evaluationId],
      );

      if (result.isEmpty) {
        return 0.0;
      }

      double totalWeightedScore = 0.0;
      double totalWeight = 0.0;

      for (final row in result) {
        final score = (row['score'] as num).toDouble();
        final weight = (row['weight'] as num).toDouble();
        final maxScore = (row['max_score'] as num).toDouble();

        // تحويل النتيجة إلى نسبة مئوية ثم ضربها في الوزن
        final normalizedScore = (score / maxScore) * 100;
        totalWeightedScore += normalizedScore * weight;
        totalWeight += weight;
      }

      return totalWeight > 0 ? totalWeightedScore / totalWeight : 0.0;
    } catch (e) {
      LoggingService.error(
        'خطأ في حساب النتيجة الإجمالية',
        category: 'PerformanceEvaluationService',
        data: {'evaluationId': evaluationId, 'error': e.toString()},
      );
      return 0.0;
    }
  }

  /// الحصول على إحصائيات التقييم
  Future<Map<String, dynamic>> getEvaluationStatistics() async {
    try {
      await _createEvaluationTables();
      final db = await _databaseHelper.database;

      // إحصائيات عامة
      final totalCycles = await db.rawQuery(
        'SELECT COUNT(*) as count FROM evaluation_cycles',
      );
      final activeCycles = await db.rawQuery(
        'SELECT COUNT(*) as count FROM evaluation_cycles WHERE status = "active"',
      );
      final totalEvaluations = await db.rawQuery(
        'SELECT COUNT(*) as count FROM employee_evaluations',
      );
      final completedEvaluations = await db.rawQuery(
        'SELECT COUNT(*) as count FROM employee_evaluations WHERE status IN ("approved", "completed")',
      );

      // توزيع التقييمات حسب الحالة
      final statusDistribution = await db.rawQuery('''
        SELECT status, COUNT(*) as count
        FROM employee_evaluations
        GROUP BY status
        ORDER BY count DESC
      ''');

      // متوسط النتائج
      final averageScores = await db.rawQuery('''
        SELECT AVG(overall_score) as average_score
        FROM employee_evaluations
        WHERE overall_score IS NOT NULL
      ''');

      // أفضل الموظفين
      final topPerformers = await db.rawQuery('''
        SELECT
          ee.employee_id,
          AVG(ee.overall_score) as average_score,
          COUNT(ee.id) as evaluation_count
        FROM employee_evaluations ee
        WHERE ee.overall_score IS NOT NULL
        GROUP BY ee.employee_id
        HAVING evaluation_count >= 1
        ORDER BY average_score DESC
        LIMIT 5
      ''');

      return {
        'totalCycles': totalCycles.first['count'] ?? 0,
        'activeCycles': activeCycles.first['count'] ?? 0,
        'totalEvaluations': totalEvaluations.first['count'] ?? 0,
        'completedEvaluations': completedEvaluations.first['count'] ?? 0,
        'completionRate': totalEvaluations.first['count'] != 0
            ? ((completedEvaluations.first['count'] as int) /
                      (totalEvaluations.first['count'] as int) *
                      100)
                  .toStringAsFixed(1)
            : '0.0',
        'statusDistribution': statusDistribution,
        'averageScore': averageScores.first['average_score'] ?? 0.0,
        'topPerformers': topPerformers,
      };
    } catch (e) {
      LoggingService.error(
        'خطأ في جلب إحصائيات التقييم',
        category: 'PerformanceEvaluationService',
        data: {'error': e.toString()},
      );
      return {};
    }
  }

  /// إنشاء معايير التقييم الافتراضية
  Future<void> createDefaultCriteria() async {
    try {
      final defaultCriteria = [
        {
          'name': 'جودة العمل',
          'description': 'مستوى الدقة والإتقان في أداء المهام',
          'category': 'الأداء الوظيفي',
          'weight': 0.25,
        },
        {
          'name': 'الالتزام بالمواعيد',
          'description': 'الحضور في الوقت المحدد وإنجاز المهام في المواعيد',
          'category': 'الانضباط',
          'weight': 0.15,
        },
        {
          'name': 'التعاون مع الفريق',
          'description': 'القدرة على العمل بفعالية مع الزملاء',
          'category': 'المهارات الشخصية',
          'weight': 0.20,
        },
        {
          'name': 'المبادرة والإبداع',
          'description': 'اقتراح حلول جديدة والمبادرة في حل المشاكل',
          'category': 'الإبداع',
          'weight': 0.15,
        },
        {
          'name': 'التطوير المهني',
          'description': 'السعي لتطوير المهارات والمعرفة',
          'category': 'التطوير',
          'weight': 0.10,
        },
        {
          'name': 'التواصل الفعال',
          'description': 'القدرة على التواصل بوضوح وفعالية',
          'category': 'المهارات الشخصية',
          'weight': 0.15,
        },
      ];

      for (final criteriaData in defaultCriteria) {
        final existing = await _getCriteriaByName(
          criteriaData['name'] as String,
        );
        if (existing == null) {
          final criteria = EvaluationCriteria(
            name: criteriaData['name'] as String,
            description: criteriaData['description'] as String,
            category: criteriaData['category'] as String,
            weight: criteriaData['weight'] as double,
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          );

          await addEvaluationCriteria(criteria);
        }
      }

      LoggingService.info(
        'تم إنشاء معايير التقييم الافتراضية',
        category: 'PerformanceEvaluationService',
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في إنشاء معايير التقييم الافتراضية',
        category: 'PerformanceEvaluationService',
        data: {'error': e.toString()},
      );
    }
  }
}
