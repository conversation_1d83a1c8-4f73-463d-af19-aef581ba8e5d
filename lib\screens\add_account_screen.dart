import 'package:flutter/material.dart';
import '../constants/revolutionary_design_colors.dart';
import '../constants/app_constants.dart';
import '../models/account.dart';
import '../services/account_service.dart';
import '../services/focus_management_service.dart';


class AddAccountScreen extends StatefulWidget {
  final Account? accountToEdit;

  const AddAccountScreen({super.key, this.accountToEdit});

  @override
  State<AddAccountScreen> createState() => _AddAccountScreenState();
}

class _AddAccountScreenState extends State<AddAccountScreen> {
  final _formKey = GlobalKey<FormState>();
  final _accountService = AccountService();

  // Controllers
  final _codeController = TextEditingController();
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();

  // Focus nodes
  late final FocusNode _codeFocusNode;
  late final FocusNode _nameFocusNode;
  late final FocusNode _descriptionFocusNode;
  late final FocusNode _typeFocusNode;
  late final FocusNode _parentFocusNode;
  late final FocusNode _saveFocusNode;

  // Form data
  String _selectedAccountType = AppConstants.accountTypeAsset;
  Account? _selectedParentAccount;
  bool _isActive = true;
  bool _isLoading = false;

  // Parent accounts list
  List<Account> _parentAccounts = [];

  @override
  void initState() {
    super.initState();

    // تهيئة focus nodes
    _codeFocusNode = FocusManagementService.createFieldFocusNode('code');
    _nameFocusNode = FocusManagementService.createFieldFocusNode('name');
    _descriptionFocusNode = FocusManagementService.createFieldFocusNode(
      'description',
    );
    _typeFocusNode = FocusManagementService.createFieldFocusNode('type');
    _parentFocusNode = FocusManagementService.createFieldFocusNode('parent');
    _saveFocusNode = FocusManagementService.createFieldFocusNode('save');

    _loadParentAccounts();
    if (widget.accountToEdit != null) {
      _populateFormWithExistingAccount();
    } else {
      _generateAccountCode();
    }
  }

  @override
  void dispose() {
    _codeController.dispose();
    _nameController.dispose();
    _descriptionController.dispose();

    // تنظيف focus nodes
    FocusManagementService.disposeFocusNodes([
      _codeFocusNode,
      _nameFocusNode,
      _descriptionFocusNode,
      _typeFocusNode,
      _parentFocusNode,
      _saveFocusNode,
    ]);

    super.dispose();
  }

  Future<void> _loadParentAccounts() async {
    try {
      final accounts = await _accountService.getParentAccounts();
      setState(() {
        _parentAccounts = accounts;
      });
    } catch (e) {
      _showErrorSnackBar('حدث خطأ في تحميل الحسابات الرئيسية: $e');
    }
  }

  Future<void> _generateAccountCode() async {
    try {
      final code = await _accountService.generateAccountCode(
        _selectedAccountType,
      );
      setState(() {
        _codeController.text = code;
      });
    } catch (e) {
      _showErrorSnackBar('حدث خطأ في إنشاء كود الحساب: $e');
    }
  }

  void _populateFormWithExistingAccount() {
    final account = widget.accountToEdit!;
    _codeController.text = account.code;
    _nameController.text = account.name;
    _descriptionController.text = account.description ?? '';
    _selectedAccountType = account.type;
    _isActive = account.isActive;

    // Set parent account if exists
    if (account.parentId != null) {
      _loadParentAccounts().then((_) {
        setState(() {
          _selectedParentAccount = _parentAccounts
              .where((parent) => parent.id == account.parentId)
              .firstOrNull;
        });
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Shortcuts(
      shortcuts: FocusManagementService.createFormShortcuts(),
      child: Actions(
        actions: FocusManagementService.createFormActions(
          context,
          onSave: _saveAccount,
        ),
        child: Scaffold(
          appBar: AppBar(
            title: Text(
              widget.accountToEdit != null ? 'تعديل الحساب' : 'إضافة حساب جديد',
            ),
            actions: [
              Focus(
                focusNode: _saveFocusNode,
                child: IconButton(
                  icon: const Icon(Icons.save),
                  onPressed: _saveAccount,
                  tooltip: 'حفظ (Ctrl+S)',
                ),
              ),
            ],
          ),
          body: _isLoading
              ? const Center(child: CircularProgressIndicator())
              : FocusTraversalGroup(
                  policy: FocusManagementService.createFormTraversalPolicy(),
                  child: Form(
                    key: _formKey,
                    child: SingleChildScrollView(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          _buildAccountTypeSection(),
                          const SizedBox(height: 24),
                          _buildBasicInfoSection(),
                          const SizedBox(height: 24),
                          _buildParentAccountSection(),
                          const SizedBox(height: 24),
                          _buildDescriptionSection(),
                          const SizedBox(height: 24),
                          _buildStatusSection(),
                          const SizedBox(height: 32),
                          _buildSaveButton(),
                        ],
                      ),
                    ),
                  ),
                ),
        ),
      ),
    );
  }

  Widget _buildAccountTypeSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'نوع الحساب',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
                color: RevolutionaryColors.textPrimary,
              ),
            ),
            const SizedBox(height: 16),
            DropdownButtonFormField<String>(
              value: _selectedAccountType,
              decoration: const InputDecoration(
                labelText: 'نوع الحساب',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.category),
              ),
              items: _getAccountTypeItems(),
              onChanged: (value) {
                if (value != null) {
                  setState(() {
                    _selectedAccountType = value;
                  });
                  _generateAccountCode();
                }
              },
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return AppConstants.requiredFieldMessage;
                }
                return null;
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBasicInfoSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'المعلومات الأساسية',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
                color: RevolutionaryColors.textPrimary,
              ),
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _codeController,
              focusNode: _codeFocusNode,
              decoration: const InputDecoration(
                labelText: 'كود الحساب',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.numbers),
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return AppConstants.requiredFieldMessage;
                }
                return null;
              },
              onFieldSubmitted: (_) {
                FocusManagementService.moveToNextField(
                  context,
                  _codeFocusNode,
                  _nameFocusNode,
                );
              },
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _nameController,
              focusNode: _nameFocusNode,
              decoration: const InputDecoration(
                labelText: 'اسم الحساب',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.account_balance),
              ),
              maxLength: AppConstants.maxAccountNameLength,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return AppConstants.requiredFieldMessage;
                }
                return null;
              },
              onFieldSubmitted: (_) {
                FocusManagementService.moveToNextField(
                  context,
                  _nameFocusNode,
                  _descriptionFocusNode,
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildParentAccountSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'الحساب الرئيسي',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
                color: RevolutionaryColors.textPrimary,
              ),
            ),
            const SizedBox(height: 16),
            DropdownButtonFormField<Account>(
              value: _selectedParentAccount,
              decoration: const InputDecoration(
                labelText: 'الحساب الرئيسي (اختياري)',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.account_tree),
              ),
              items: _getParentAccountItems(),
              onChanged: (value) {
                setState(() {
                  _selectedParentAccount = value;
                });
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDescriptionSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'الوصف',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
                color: RevolutionaryColors.textPrimary,
              ),
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _descriptionController,
              decoration: const InputDecoration(
                labelText: 'وصف الحساب (اختياري)',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.description),
              ),
              maxLines: 3,
              maxLength: AppConstants.maxDescriptionLength,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'حالة الحساب',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
                color: RevolutionaryColors.textPrimary,
              ),
            ),
            const SizedBox(height: 16),
            SwitchListTile(
              title: const Text('حساب نشط'),
              subtitle: const Text('يمكن استخدام الحساب في العمليات المحاسبية'),
              value: _isActive,
              onChanged: (value) {
                setState(() {
                  _isActive = value;
                });
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSaveButton() {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: _saveAccount,
        style: ElevatedButton.styleFrom(
          backgroundColor: RevolutionaryColors.damascusSky,
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(vertical: 16),
        ),
        child: const Text(
          'حفظ الحساب',
          style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
        ),
      ),
    );
  }

  List<DropdownMenuItem<String>> _getAccountTypeItems() {
    return [
      DropdownMenuItem(
        value: AppConstants.accountTypeAsset,
        child: Row(
          children: [
            Text(AppConstants.accountIcons[AppConstants.accountTypeAsset]!),
            const SizedBox(width: 8),
            const Text('أصول'),
          ],
        ),
      ),
      DropdownMenuItem(
        value: AppConstants.accountTypeLiability,
        child: Row(
          children: [
            Text(AppConstants.accountIcons[AppConstants.accountTypeLiability]!),
            const SizedBox(width: 8),
            const Text('خصوم'),
          ],
        ),
      ),
      DropdownMenuItem(
        value: AppConstants.accountTypeEquity,
        child: Row(
          children: [
            Text(AppConstants.accountIcons[AppConstants.accountTypeEquity]!),
            const SizedBox(width: 8),
            const Text('حقوق الملكية'),
          ],
        ),
      ),
      DropdownMenuItem(
        value: AppConstants.accountTypeRevenue,
        child: Row(
          children: [
            Text(AppConstants.accountIcons[AppConstants.accountTypeRevenue]!),
            const SizedBox(width: 8),
            const Text('إيرادات'),
          ],
        ),
      ),
      DropdownMenuItem(
        value: AppConstants.accountTypeExpense,
        child: Row(
          children: [
            Text(AppConstants.accountIcons[AppConstants.accountTypeExpense]!),
            const SizedBox(width: 8),
            const Text('مصروفات'),
          ],
        ),
      ),
      DropdownMenuItem(
        value: AppConstants.accountTypePurchase,
        child: Row(
          children: [
            Text(AppConstants.accountIcons[AppConstants.accountTypePurchase]!),
            const SizedBox(width: 8),
            const Text('مشتريات'),
          ],
        ),
      ),
      DropdownMenuItem(
        value: AppConstants.accountTypeSale,
        child: Row(
          children: [
            Text(AppConstants.accountIcons[AppConstants.accountTypeSale]!),
            const SizedBox(width: 8),
            const Text('مبيعات'),
          ],
        ),
      ),
      DropdownMenuItem(
        value: AppConstants.accountTypeInventory,
        child: Row(
          children: [
            Text(AppConstants.accountIcons[AppConstants.accountTypeInventory]!),
            const SizedBox(width: 8),
            const Text('مخزون'),
          ],
        ),
      ),
    ];
  }

  List<DropdownMenuItem<Account>> _getParentAccountItems() {
    return [
      const DropdownMenuItem<Account>(
        value: null,
        child: Text('لا يوجد حساب رئيسي'),
      ),
      ..._parentAccounts.map(
        (account) => DropdownMenuItem<Account>(
          value: account,
          child: Row(
            children: [
              Text(account.accountIcon),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  '${account.code} - ${account.name}',
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
        ),
      ),
    ];
  }

  Future<void> _saveAccount() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final account = Account(
        id: widget.accountToEdit?.id,
        code: _codeController.text.trim(),
        name: _nameController.text.trim(),
        type: _selectedAccountType,
        parentId: _selectedParentAccount?.id,
        level: _selectedParentAccount != null ? 2 : 1,
        isActive: _isActive,
        balance: widget.accountToEdit?.balance ?? 0.0,
        currencyId:
            widget.accountToEdit?.currencyId ?? 1, // Default to Syrian Pound
        description: _descriptionController.text.trim().isEmpty
            ? null
            : _descriptionController.text.trim(),
        createdAt: widget.accountToEdit?.createdAt,
        updatedAt: widget.accountToEdit?.updatedAt,
      );

      if (widget.accountToEdit != null) {
        // Update existing account
        await _accountService.updateAccount(account);
      } else {
        // Insert new account
        await _accountService.insertAccount(account);
      }

      if (mounted) {
        Navigator.pop(context, true);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              widget.accountToEdit != null
                  ? 'تم تحديث الحساب بنجاح'
                  : 'تم حفظ الحساب بنجاح',
            ),
            backgroundColor: RevolutionaryColors.successGlow,
          ),
        );
      }
    } catch (e) {
      _showErrorSnackBar(
        widget.accountToEdit != null
            ? 'حدث خطأ في تحديث الحساب: $e'
            : 'حدث خطأ في حفظ الحساب: $e',
      );
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _showErrorSnackBar(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(message), backgroundColor: RevolutionaryColors.errorCoral),
      );
    }
  }
}
