/// شاشة إدارة المخازن المحسنة
/// تحتوي على جميع الميزات الجديدة للمواقع المتعددة والنقل والباركود
library;

import 'package:flutter/material.dart';
import '../models/warehouse.dart';
import '../models/warehouse_location.dart';
import '../models/inventory_transfer.dart';

import '../services/warehouse_service.dart';
import '../services/inventory_transfer_service.dart';
import '../services/barcode_service.dart';
import '../services/item_location_stock_service.dart';
import '../widgets/loading_widget.dart';

// ألوان التطبيق
class AppColors {
  static const Color primary = Color(0xFF1976D2);
  static const Color secondary = Color(0xFF424242);
  static const Color success = Color(0xFF4CAF50);
  static const Color warning = Color(0xFFFF9800);
  static const Color error = Color(0xFFF44336);
}

// ألوان الثورة
class RevolutionaryColors {
  static const Color damascusSky = Color(0xFF1976D2);
}

// فئات النص المستجيب
class ResponsiveText extends StatelessWidget {
  final String text;
  final Color? color;
  final TextAlign? textAlign;
  final int? maxLines;
  final TextOverflow? overflow;
  final double fontSize;
  final FontWeight fontWeight;

  const ResponsiveText.h6(
    this.text, {
    super.key,
    this.color,
    this.textAlign,
    this.maxLines,
    this.overflow,
  }) : fontSize = 16,
       fontWeight = FontWeight.bold;

  const ResponsiveText.body1(
    this.text, {
    super.key,
    this.color,
    this.textAlign,
    this.maxLines,
    this.overflow,
  }) : fontSize = 14,
       fontWeight = FontWeight.normal;

  const ResponsiveText.body2(
    this.text, {
    super.key,
    this.color,
    this.textAlign,
    this.maxLines,
    this.overflow,
  }) : fontSize = 12,
       fontWeight = FontWeight.normal;

  const ResponsiveText.caption(
    this.text, {
    super.key,
    this.color,
    this.textAlign,
    this.maxLines,
    this.overflow,
  }) : fontSize = 10,
       fontWeight = FontWeight.normal;

  @override
  Widget build(BuildContext context) {
    return Text(
      text,
      style: TextStyle(
        fontSize: fontSize,
        fontWeight: fontWeight,
        color: color,
      ),
      textAlign: textAlign,
      maxLines: maxLines,
      overflow: overflow,
    );
  }
}

// أيقونات مستجيبة
class ResponsiveIcon extends StatelessWidget {
  final IconData icon;
  final Color? color;
  final double size;

  const ResponsiveIcon.small(this.icon, {super.key, this.color}) : size = 16;

  const ResponsiveIcon.medium(this.icon, {super.key, this.color}) : size = 24;

  const ResponsiveIcon.large(this.icon, {super.key, this.color}) : size = 36;

  @override
  Widget build(BuildContext context) {
    return Icon(icon, color: color, size: size);
  }
}

class EnhancedWarehouseScreen extends StatefulWidget {
  const EnhancedWarehouseScreen({super.key});

  @override
  State<EnhancedWarehouseScreen> createState() =>
      _EnhancedWarehouseScreenState();
}

class _EnhancedWarehouseScreenState extends State<EnhancedWarehouseScreen>
    with TickerProviderStateMixin {
  final WarehouseService _warehouseService = WarehouseService();
  final InventoryTransferService _transferService = InventoryTransferService();
  final BarcodeService _barcodeService = BarcodeService();
  final ItemLocationStockService _stockService = ItemLocationStockService();

  late TabController _tabController;
  late TextEditingController _searchController;

  List<Warehouse> _warehouses = [];
  List<WarehouseLocation> _locations = [];
  List<InventoryTransfer> _transfers = [];

  Warehouse? _selectedWarehouse;
  String? _selectedZone;

  bool _isLoading = true;
  String _searchQuery = '';
  String? _selectedTransferStatus;
  Warehouse? _selectedTransferWarehouse;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _searchController = TextEditingController(text: _searchQuery);
    _searchController.addListener(() {
      setState(() {
        _searchQuery = _searchController.text;
      });
    });
    _initializeData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _initializeData() async {
    setState(() => _isLoading = true);

    try {
      await _warehouseService.createTables();
      await _transferService.createTables();
      await _barcodeService.createTables();
      await _stockService.createTables();

      await _loadWarehouses();
      await _loadTransfers();
    } catch (e) {
      _showErrorSnackBar('خطأ في تحميل البيانات: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _loadWarehouses() async {
    final warehouses = await _warehouseService.getAllWarehouses();
    setState(() {
      _warehouses = warehouses;
      if (_selectedWarehouse == null && warehouses.isNotEmpty) {
        _selectedWarehouse = warehouses.first;
        _loadLocations();
      }
    });
  }

  Future<void> _loadLocations() async {
    if (_selectedWarehouse == null) return;

    final locations = await _warehouseService.getWarehouseLocations(
      _selectedWarehouse!.id!,
      activeOnly: true,
    );

    setState(() => _locations = locations);
  }

  Future<void> _loadTransfers() async {
    final transfers = await _transferService.getAllTransfers(limit: 50);
    setState(() => _transfers = transfers);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إدارة المخازن المتقدمة'),
        backgroundColor: Colors.blue[700],
        foregroundColor: Colors.white,
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          tabs: const [
            Tab(icon: Icon(Icons.warehouse), text: 'المستودعات'),
            Tab(icon: Icon(Icons.location_on), text: 'المواقع'),
            Tab(icon: Icon(Icons.transfer_within_a_station), text: 'النقل'),
            Tab(icon: Icon(Icons.qr_code), text: 'الباركود'),
          ],
        ),
      ),
      body: _isLoading
          ? const LoadingWidget()
          : TabBarView(
              controller: _tabController,
              children: [
                _buildWarehousesTab(),
                _buildLocationsTab(),
                _buildTransfersTab(),
                _buildBarcodeTab(),
              ],
            ),
      floatingActionButton: _buildFloatingActionButton(),
    );
  }

  Widget _buildFloatingActionButton() {
    switch (_tabController.index) {
      case 0:
        return FloatingActionButton(
          onPressed: _showAddWarehouseDialog,
          backgroundColor: Colors.blue[700],
          child: const Icon(Icons.add, color: Colors.white),
        );
      case 1:
        return FloatingActionButton(
          onPressed: _showAddLocationDialog,
          backgroundColor: Colors.blue[700],
          child: const Icon(Icons.add_location, color: Colors.white),
        );
      case 2:
        return FloatingActionButton(
          onPressed: _showCreateTransferDialog,
          backgroundColor: Colors.blue[700],
          child: const Icon(
            Icons.transfer_within_a_station,
            color: Colors.white,
          ),
        );
      case 3:
        return FloatingActionButton(
          onPressed: _showBarcodeGeneratorDialog,
          backgroundColor: Colors.blue[700],
          child: const Icon(Icons.qr_code, color: Colors.white),
        );
      default:
        return const SizedBox.shrink();
    }
  }

  Widget _buildWarehousesTab() {
    return Column(
      children: [
        _buildWarehouseSelector(),
        Expanded(
          child: _warehouses.isEmpty
              ? _buildEmptyState('لا توجد مستودعات', Icons.warehouse)
              : _buildWarehousesList(),
        ),
      ],
    );
  }

  Widget _buildWarehouseSelector() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        border: Border(bottom: BorderSide(color: Colors.grey[300]!)),
      ),
      child: Row(
        children: [
          Icon(Icons.warehouse, color: Colors.blue[700], size: 24),
          const SizedBox(width: 12),
          Expanded(
            child: DropdownButtonFormField<Warehouse>(
              value: _selectedWarehouse,
              decoration: const InputDecoration(
                labelText: 'اختر المستودع',
                border: OutlineInputBorder(),
                contentPadding: EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 8,
                ),
              ),
              items: _warehouses.map((warehouse) {
                return DropdownMenuItem(
                  value: warehouse,
                  child: ResponsiveText.body1(warehouse.name),
                );
              }).toList(),
              onChanged: (warehouse) {
                setState(() {
                  _selectedWarehouse = warehouse;
                  _selectedZone = null;
                });
                if (warehouse != null) {
                  _loadLocations();
                }
              },
            ),
          ),
          const SizedBox(width: 12),
          ElevatedButton.icon(
            onPressed: _showWarehouseStatistics,
            icon: const ResponsiveIcon.small(Icons.analytics),
            label: const ResponsiveText.body2('إحصائيات'),
            style: ElevatedButton.styleFrom(
              backgroundColor: RevolutionaryColors.damascusSky,
              foregroundColor: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildWarehousesList() {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _warehouses.length,
      itemBuilder: (context, index) {
        final warehouse = _warehouses[index];
        return Card(
          margin: const EdgeInsets.only(bottom: 12),
          elevation: 2,
          child: ListTile(
            leading: CircleAvatar(
              backgroundColor: warehouse.isActive
                  ? RevolutionaryColors.damascusSky
                  : Colors.grey,
              child: ResponsiveIcon.medium(
                Icons.warehouse,
                color: Colors.white,
              ),
            ),
            title: ResponsiveText.h6(warehouse.name),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                ResponsiveText.body2(warehouse.description ?? 'لا يوجد وصف'),
                const SizedBox(height: 4),
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 2,
                      ),
                      decoration: BoxDecoration(
                        color: warehouse.isActive ? Colors.green : Colors.red,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: ResponsiveText.caption(
                        warehouse.isActive ? 'نشط' : 'غير نشط',
                        color: Colors.white,
                      ),
                    ),
                    if (warehouse.isDefault) ...[
                      const SizedBox(width: 8),
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 2,
                        ),
                        decoration: BoxDecoration(
                          color: RevolutionaryColors.damascusSky,
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: const ResponsiveText.caption(
                          'افتراضي',
                          color: Colors.white,
                        ),
                      ),
                    ],
                  ],
                ),
              ],
            ),
            trailing: PopupMenuButton(
              itemBuilder: (context) => [
                const PopupMenuItem(
                  value: 'edit',
                  child: ListTile(
                    leading: Icon(Icons.edit),
                    title: Text('تعديل'),
                    contentPadding: EdgeInsets.zero,
                  ),
                ),
                const PopupMenuItem(
                  value: 'locations',
                  child: ListTile(
                    leading: Icon(Icons.location_on),
                    title: Text('إدارة المواقع'),
                    contentPadding: EdgeInsets.zero,
                  ),
                ),
                const PopupMenuItem(
                  value: 'barcode',
                  child: ListTile(
                    leading: Icon(Icons.qr_code),
                    title: Text('إنشاء باركود'),
                    contentPadding: EdgeInsets.zero,
                  ),
                ),
                if (!warehouse.isDefault)
                  const PopupMenuItem(
                    value: 'delete',
                    child: ListTile(
                      leading: Icon(Icons.delete, color: Colors.red),
                      title: Text('حذف', style: TextStyle(color: Colors.red)),
                      contentPadding: EdgeInsets.zero,
                    ),
                  ),
              ],
              onSelected: (value) => _handleWarehouseAction(value, warehouse),
            ),
            onTap: () {
              setState(() => _selectedWarehouse = warehouse);
              _loadLocations();
            },
          ),
        );
      },
    );
  }

  Widget _buildLocationsTab() {
    if (_selectedWarehouse == null) {
      return _buildEmptyState('اختر مستودعاً لعرض المواقع', Icons.location_on);
    }

    return Column(
      children: [
        _buildZoneFilter(),
        Expanded(
          child: _locations.isEmpty
              ? _buildEmptyState(
                  'لا توجد مواقع في هذا المستودع',
                  Icons.location_on,
                )
              : _buildLocationsList(),
        ),
        // زر لعرض إحصائيات المخزون في المستودع المحدد
        Padding(
          padding: const EdgeInsets.all(16.0),
          child: ElevatedButton.icon(
            onPressed: _showLocationStockStatistics,
            icon: const Icon(Icons.analytics),
            label: const Text('إحصائيات المخزون'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.green[700],
              foregroundColor: Colors.white,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildZoneFilter() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        border: Border(bottom: BorderSide(color: Colors.grey[300]!)),
      ),
      child: Row(
        children: [
          const ResponsiveIcon.medium(
            Icons.filter_list,
            color: RevolutionaryColors.damascusSky,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: FutureBuilder<List<String>>(
              future: _warehouseService.getWarehouseZones(
                _selectedWarehouse!.id!,
              ),
              builder: (context, snapshot) {
                final zones = snapshot.data ?? [];
                return DropdownButtonFormField<String>(
                  value: _selectedZone,
                  decoration: const InputDecoration(
                    labelText: 'تصفية حسب المنطقة',
                    border: OutlineInputBorder(),
                    contentPadding: EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 8,
                    ),
                  ),
                  items: [
                    const DropdownMenuItem(
                      value: null,
                      child: ResponsiveText.body1('جميع المناطق'),
                    ),
                    ...zones.map((zone) {
                      return DropdownMenuItem(
                        value: zone,
                        child: ResponsiveText.body1(zone),
                      );
                    }),
                  ],
                  onChanged: (zone) {
                    setState(() => _selectedZone = zone);
                    _filterLocationsByZone();
                  },
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLocationsList() {
    final filteredLocations = _selectedZone != null
        ? _locations.where((loc) => loc.zone == _selectedZone).toList()
        : _locations;

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: filteredLocations.length,
      itemBuilder: (context, index) {
        final location = filteredLocations[index];
        return Card(
          margin: const EdgeInsets.only(bottom: 12),
          elevation: 2,
          child: ExpansionTile(
            leading: CircleAvatar(
              backgroundColor: location.isActive
                  ? RevolutionaryColors.damascusSky
                  : Colors.grey,
              child: ResponsiveIcon.medium(
                Icons.location_on,
                color: Colors.white,
              ),
            ),
            title: ResponsiveText.h6(location.name),
            subtitle: ResponsiveText.body2(_buildLocationAddress(location)),
            trailing: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                if (location.locationBarcode != null)
                  const ResponsiveIcon.small(
                    Icons.qr_code,
                    color: Colors.green,
                  ),
                IconButton(
                  icon: const ResponsiveIcon.small(Icons.more_vert),
                  onPressed: () => _showLocationActions(location),
                ),
              ],
            ),
            children: [
              // عرض الأصناف الموجودة في هذا الموقع
              FutureBuilder(
                future: _stockService.getLocationItems(location.id!),
                builder: (context, snapshot) {
                  if (snapshot.connectionState == ConnectionState.waiting) {
                    return const Padding(
                      padding: EdgeInsets.all(16.0),
                      child: Center(child: CircularProgressIndicator()),
                    );
                  }

                  if (snapshot.hasError) {
                    return Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Text(
                        'خطأ في تحميل الأصناف: ${snapshot.error}',
                        style: const TextStyle(color: Colors.red),
                      ),
                    );
                  }

                  final items = snapshot.data ?? [];

                  if (items.isEmpty) {
                    return const Padding(
                      padding: EdgeInsets.all(16.0),
                      child: Text('لا توجد أصناف في هذا الموقع'),
                    );
                  }

                  return Column(
                    children: items
                        .map(
                          (item) => ListTile(
                            dense: true,
                            leading: const Icon(Icons.inventory_2, size: 20),
                            title: Text(
                              item.itemName ?? 'صنف ${item.itemId}',
                              style: const TextStyle(fontSize: 14),
                            ),
                            subtitle: Text(
                              'الكمية: ${item.quantity} | المتاح: ${item.availableQuantity}',
                              style: const TextStyle(fontSize: 12),
                            ),
                            trailing: item.reservedQuantity > 0
                                ? Container(
                                    padding: const EdgeInsets.symmetric(
                                      horizontal: 6,
                                      vertical: 2,
                                    ),
                                    decoration: BoxDecoration(
                                      color: Colors.orange,
                                      borderRadius: BorderRadius.circular(10),
                                    ),
                                    child: Text(
                                      'محجوز: ${item.reservedQuantity}',
                                      style: const TextStyle(
                                        color: Colors.white,
                                        fontSize: 10,
                                      ),
                                    ),
                                  )
                                : null,
                          ),
                        )
                        .toList(),
                  );
                },
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildTransfersTab() {
    return Column(
      children: [
        _buildTransferFilters(),
        Expanded(
          child: _transfers.isEmpty
              ? _buildEmptyState(
                  'لا توجد عمليات نقل',
                  Icons.transfer_within_a_station,
                )
              : _buildTransfersList(),
        ),
      ],
    );
  }

  Widget _buildBarcodeTab() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          _buildBarcodeStatistics(),
          const SizedBox(height: 20),
          _buildBarcodeActions(),
          const SizedBox(height: 20),
          Expanded(child: _buildBarcodeHistory()),
        ],
      ),
    );
  }

  Widget _buildEmptyState(String message, IconData icon) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          ResponsiveIcon.large(icon, color: Colors.grey),
          const SizedBox(height: 16),
          ResponsiveText.h6(message, color: Colors.grey),
        ],
      ),
    );
  }

  String _buildLocationAddress(WarehouseLocation location) {
    final parts = <String>[];
    if (location.zone != null) parts.add('منطقة: ${location.zone}');
    if (location.aisle != null) parts.add('ممر: ${location.aisle}');
    if (location.shelf != null) parts.add('رف: ${location.shelf}');
    if (location.bin != null) parts.add('خانة: ${location.bin}');

    return parts.isEmpty ? location.code : parts.join(' • ');
  }

  // طرق مساعدة للأحداث
  void _handleWarehouseAction(String action, Warehouse warehouse) {
    switch (action) {
      case 'edit':
        _editWarehouse(warehouse);
        break;
      case 'locations':
        setState(() => _selectedWarehouse = warehouse);
        _tabController.animateTo(1);
        _loadLocations();
        break;
      case 'barcode':
        _generateWarehouseBarcode(warehouse);
        break;
      case 'delete':
        _deleteWarehouse(warehouse);
        break;
    }
  }

  void _filterLocationsByZone() async {
    if (_selectedWarehouse == null) return;

    if (_selectedZone == null) {
      await _loadLocations();
    } else {
      final filteredLocations = await _warehouseService.getLocationsByZone(
        _selectedWarehouse!.id!,
        _selectedZone!,
      );
      setState(() => _locations = filteredLocations);
    }
  }

  // طرق الحوارات والإجراءات (مبسطة للمثال)
  void _showAddWarehouseDialog() {
    _showErrorSnackBar('ميزة إضافة المستودع قيد التطوير');
  }

  void _showAddLocationDialog() {
    _showErrorSnackBar('ميزة إضافة الموقع قيد التطوير');
  }

  void _showCreateTransferDialog() {
    _showErrorSnackBar('ميزة إنشاء النقل قيد التطوير');
  }

  void _showBarcodeGeneratorDialog() {
    _showErrorSnackBar('ميزة إنشاء الباركود قيد التطوير');
  }

  void _showLocationActions(WarehouseLocation location) {
    _showErrorSnackBar('إجراءات الموقع قيد التطوير');
  }

  void _editWarehouse(Warehouse warehouse) {
    _showErrorSnackBar('تعديل المستودع قيد التطوير');
  }

  void _generateWarehouseBarcode(Warehouse warehouse) {
    _showErrorSnackBar('إنشاء باركود المستودع قيد التطوير');
  }

  void _deleteWarehouse(Warehouse warehouse) {
    _showErrorSnackBar('حذف المستودع قيد التطوير');
  }

  Widget _buildTransferFilters() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        border: Border(bottom: BorderSide(color: Colors.grey[300]!)),
      ),
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: TextField(
                  controller: _searchController,
                  decoration: InputDecoration(
                    labelText: 'البحث في طلبات النقل',
                    hintText: 'رقم النقل، الملاحظات، أو اسم المستخدم',
                    prefixIcon: const Icon(Icons.search),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 8,
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 12),
              ElevatedButton.icon(
                onPressed: _showTransferStatistics,
                icon: const Icon(Icons.analytics),
                label: const Text('الإحصائيات'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue[700],
                  foregroundColor: Colors.white,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: DropdownButtonFormField<String>(
                  value: _selectedTransferStatus,
                  decoration: InputDecoration(
                    labelText: 'حالة النقل',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 8,
                    ),
                  ),
                  items: const [
                    DropdownMenuItem(value: null, child: Text('جميع الحالات')),
                    DropdownMenuItem(value: 'pending', child: Text('معلق')),
                    DropdownMenuItem(value: 'approved', child: Text('معتمد')),
                    DropdownMenuItem(value: 'completed', child: Text('مكتمل')),
                    DropdownMenuItem(value: 'cancelled', child: Text('ملغي')),
                  ],
                  onChanged: (value) {
                    setState(() {
                      _selectedTransferStatus = value;
                    });
                  },
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: DropdownButtonFormField<Warehouse>(
                  value: _selectedTransferWarehouse,
                  decoration: InputDecoration(
                    labelText: 'المستودع المصدر',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 8,
                    ),
                  ),
                  items: [
                    const DropdownMenuItem(
                      value: null,
                      child: Text('جميع المستودعات'),
                    ),
                    ..._warehouses.map((warehouse) {
                      return DropdownMenuItem(
                        value: warehouse,
                        child: Text(warehouse.name),
                      );
                    }),
                  ],
                  onChanged: (value) {
                    setState(() {
                      _selectedTransferWarehouse = value;
                    });
                  },
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildTransfersList() {
    // Apply all filters to transfers
    final filteredTransfers = _transfers.where((transfer) {
      // Apply search query filter
      if (_searchQuery.isNotEmpty) {
        final query = _searchQuery.toLowerCase();
        bool matchesSearch =
            transfer.transferNumber.toLowerCase().contains(query) ||
            (transfer.notes?.toLowerCase().contains(query) ?? false) ||
            (transfer.requestedBy?.toLowerCase().contains(query) ?? false) ||
            (transfer.approvedBy?.toLowerCase().contains(query) ?? false) ||
            (transfer.completedBy?.toLowerCase().contains(query) ?? false);

        if (!matchesSearch) return false;
      }

      // Apply status filter
      if (_selectedTransferStatus != null) {
        if (transfer.status.code != _selectedTransferStatus) {
          return false;
        }
      }

      // Apply warehouse filter
      if (_selectedTransferWarehouse != null) {
        if (transfer.fromWarehouseId != _selectedTransferWarehouse!.id &&
            transfer.toWarehouseId != _selectedTransferWarehouse!.id) {
          return false;
        }
      }

      return true;
    }).toList();

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: filteredTransfers.length,
      itemBuilder: (context, index) {
        final transfer = filteredTransfers[index];
        return Card(
          margin: const EdgeInsets.only(bottom: 12),
          elevation: 2,
          child: ExpansionTile(
            leading: CircleAvatar(
              backgroundColor: _getTransferStatusColor(transfer.status),
              child: Icon(
                _getTransferStatusIcon(transfer.status),
                color: Colors.white,
                size: 20,
              ),
            ),
            title: Text(
              transfer.transferNumber,
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('التاريخ: ${_formatDate(transfer.transferDate)}'),
                Text('الحالة: ${_getTransferStatusText(transfer.status)}'),
                if (transfer.notes?.isNotEmpty == true)
                  Text('ملاحظات: ${transfer.notes}'),
              ],
            ),
            trailing: PopupMenuButton(
              itemBuilder: (context) => [
                PopupMenuItem(
                  value: 'view',
                  child: const Row(
                    children: [
                      Icon(Icons.visibility),
                      SizedBox(width: 8),
                      Text('عرض التفاصيل'),
                    ],
                  ),
                ),
                if (transfer.status == TransferStatus.pending)
                  PopupMenuItem(
                    value: 'approve',
                    child: const Row(
                      children: [
                        Icon(Icons.check, color: Colors.green),
                        SizedBox(width: 8),
                        Text('موافقة'),
                      ],
                    ),
                  ),
                if (transfer.status == TransferStatus.approved)
                  PopupMenuItem(
                    value: 'execute',
                    child: const Row(
                      children: [
                        Icon(Icons.play_arrow, color: Colors.blue),
                        SizedBox(width: 8),
                        Text('تنفيذ'),
                      ],
                    ),
                  ),
                if (transfer.status == TransferStatus.pending ||
                    transfer.status == TransferStatus.approved)
                  PopupMenuItem(
                    value: 'cancel',
                    child: const Row(
                      children: [
                        Icon(Icons.cancel, color: Colors.red),
                        SizedBox(width: 8),
                        Text('إلغاء'),
                      ],
                    ),
                  ),
              ],
              onSelected: (value) => _handleTransferAction(transfer, value),
            ),
            children: [
              Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Expanded(
                          child: _buildTransferInfoCard(
                            'المستودع المصدر',
                            'المستودع ${transfer.fromWarehouseId}',
                            Icons.warehouse,
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: _buildTransferInfoCard(
                            'المستودع الوجهة',
                            'المستودع ${transfer.toWarehouseId}',
                            Icons.warehouse,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 12),
                    Row(
                      children: [
                        Expanded(
                          child: _buildTransferInfoCard(
                            'إجمالي الأصناف',
                            '${transfer.totalItems}',
                            Icons.inventory,
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: _buildTransferInfoCard(
                            'إجمالي الكمية',
                            '${transfer.totalQuantity}',
                            Icons.numbers,
                          ),
                        ),
                      ],
                    ),
                    if (transfer.items.isNotEmpty) ...[
                      const SizedBox(height: 16),
                      const Text(
                        'تفاصيل الأصناف:',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                      const SizedBox(height: 8),
                      ...transfer.items.map(
                        (item) => Card(
                          color: Colors.grey[50],
                          child: ListTile(
                            title: Text('الصنف ${item.itemId}'),
                            subtitle: Text(
                              'الكمية المطلوبة: ${item.requestedQuantity}',
                            ),
                            trailing: item.transferredQuantity > 0
                                ? Text(
                                    'الكمية المنقولة: ${item.transferredQuantity}',
                                    style: const TextStyle(
                                      color: Colors.green,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  )
                                : null,
                          ),
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildBarcodeStatistics() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(8),
      ),
      child: const ResponsiveText.body1('إحصائيات الباركود قيد التطوير'),
    );
  }

  Widget _buildBarcodeActions() {
    return Row(
      children: [
        Expanded(
          child: ElevatedButton.icon(
            onPressed: _showBarcodeGeneratorDialog,
            icon: const ResponsiveIcon.small(Icons.qr_code),
            label: const ResponsiveText.body2('إنشاء باركود'),
            style: ElevatedButton.styleFrom(
              backgroundColor: RevolutionaryColors.damascusSky,
              foregroundColor: Colors.white,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildBarcodeHistory() {
    return const Center(
      child: ResponsiveText.body1('سجل الباركود قيد التطوير'),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message, style: const TextStyle(color: Colors.white)),
        backgroundColor: Colors.red,
      ),
    );
  }

  // وظائف مساعدة للنقل
  Color _getTransferStatusColor(TransferStatus status) {
    switch (status) {
      case TransferStatus.pending:
        return Colors.orange;
      case TransferStatus.approved:
        return Colors.blue;
      case TransferStatus.completed:
        return Colors.green;
      case TransferStatus.cancelled:
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  IconData _getTransferStatusIcon(TransferStatus status) {
    switch (status) {
      case TransferStatus.pending:
        return Icons.pending;
      case TransferStatus.approved:
        return Icons.check_circle;
      case TransferStatus.completed:
        return Icons.done_all;
      case TransferStatus.cancelled:
        return Icons.cancel;
      default:
        return Icons.help;
    }
  }

  String _getTransferStatusText(TransferStatus status) {
    switch (status) {
      case TransferStatus.pending:
        return 'معلق';
      case TransferStatus.approved:
        return 'معتمد';
      case TransferStatus.completed:
        return 'مكتمل';
      case TransferStatus.cancelled:
        return 'ملغي';
      default:
        return 'غير محدد';
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  Widget _buildTransferInfoCard(String title, String value, IconData icon) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.blue[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.blue[200]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, size: 16, color: Colors.blue[700]),
              const SizedBox(width: 4),
              Text(
                title,
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.blue[700],
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: const TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
          ),
        ],
      ),
    );
  }

  void _handleTransferAction(InventoryTransfer transfer, String action) {
    switch (action) {
      case 'view':
        _showTransferDetails(transfer);
        break;
      case 'approve':
        _approveTransfer(transfer);
        break;
      case 'execute':
        _executeTransfer(transfer);
        break;
      case 'cancel':
        _cancelTransfer(transfer);
        break;
    }
  }

  void _showTransferDetails(InventoryTransfer transfer) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('تفاصيل النقل ${transfer.transferNumber}'),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text('الحالة: ${_getTransferStatusText(transfer.status)}'),
              Text('التاريخ: ${_formatDate(transfer.transferDate)}'),
              Text('المستودع المصدر: ${transfer.fromWarehouseId}'),
              Text('المستودع الوجهة: ${transfer.toWarehouseId}'),
              Text('إجمالي الأصناف: ${transfer.totalItems}'),
              Text('إجمالي الكمية: ${transfer.totalQuantity}'),
              if (transfer.notes?.isNotEmpty == true)
                Text('الملاحظات: ${transfer.notes}'),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  void _approveTransfer(InventoryTransfer transfer) {
    _showErrorSnackBar('الموافقة على النقل قيد التطوير');
  }

  void _executeTransfer(InventoryTransfer transfer) {
    _showErrorSnackBar('تنفيذ النقل قيد التطوير');
  }

  void _cancelTransfer(InventoryTransfer transfer) {
    _showErrorSnackBar('إلغاء النقل قيد التطوير');
  }

  void _showWarehouseStatistics() async {
    if (_selectedWarehouse == null) {
      _showErrorSnackBar('الرجاء اختيار مستودع أولاً');
      return;
    }

    try {
      final stats = await _stockService.getInventoryStatistics(
        warehouseId: _selectedWarehouse!.id,
      );

      if (!mounted) return;

      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: Text('إحصائيات مستودع ${_selectedWarehouse!.name}'),
          content: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                _buildStatRow('إجمالي الأصناف', '${stats['total_items']}'),
                _buildStatRow('أصناف متوفرة', '${stats['items_in_stock']}'),
                _buildStatRow('أصناف نافدة', '${stats['items_out_of_stock']}'),
                _buildStatRow(
                  'أصناف محجوزة',
                  '${stats['items_with_reservations']}',
                ),
                const Divider(),
                _buildStatRow(
                  'إجمالي الكمية',
                  '${stats['total_quantity']?.toStringAsFixed(2)}',
                ),
                _buildStatRow(
                  'الكمية المحجوزة',
                  '${stats['total_reserved_quantity']?.toStringAsFixed(2)}',
                ),
                _buildStatRow(
                  'الكمية المتاحة',
                  '${stats['total_available_quantity']?.toStringAsFixed(2)}',
                ),
                const Divider(),
                _buildStatRow(
                  'إجمالي القيمة',
                  '${stats['total_value']?.toStringAsFixed(2)}',
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('إغلاق'),
            ),
          ],
        ),
      );
    } catch (e) {
      if (mounted) {
        _showErrorSnackBar('خطأ في تحميل الإحصائيات: $e');
      }
    }
  }

  void _showLocationStockStatistics() async {
    if (_selectedWarehouse == null) {
      _showErrorSnackBar('الرجاء اختيار مستودع أولاً');
      return;
    }

    try {
      // إحصائيات المخزون حسب المستودع
      final warehouseStats = await _stockService.getInventoryStatistics(
        warehouseId: _selectedWarehouse!.id,
      );

      if (!mounted) return;

      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: Text('إحصائيات المخزون - ${_selectedWarehouse!.name}'),
          content: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                _buildStatRow(
                  'إجمالي الأصناف',
                  '${warehouseStats['total_items']}',
                ),
                _buildStatRow(
                  'أصناف متوفرة',
                  '${warehouseStats['items_in_stock']}',
                ),
                _buildStatRow(
                  'أصناف نافدة',
                  '${warehouseStats['items_out_of_stock']}',
                ),
                const Divider(),
                _buildStatRow(
                  'إجمالي الكمية',
                  '${warehouseStats['total_quantity']?.toStringAsFixed(2)}',
                ),
                _buildStatRow(
                  'الكمية المحجوزة',
                  '${warehouseStats['total_reserved_quantity']?.toStringAsFixed(2)}',
                ),
                _buildStatRow(
                  'الكمية المتاحة',
                  '${warehouseStats['total_available_quantity']?.toStringAsFixed(2)}',
                ),
                const Divider(),
                _buildStatRow(
                  'إجمالي القيمة',
                  '${warehouseStats['total_value']?.toStringAsFixed(2)}',
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('إغلاق'),
            ),
          ],
        ),
      );
    } catch (e) {
      if (mounted) {
        _showErrorSnackBar('خطأ في تحميل إحصائيات المخزون: $e');
      }
    }
  }

  void _showTransferStatistics() async {
    try {
      final stats = await _transferService.getTransferStatistics();

      if (!mounted) return;

      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('إحصائيات النقل'),
          content: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                _buildStatRow(
                  'إجمالي طلبات النقل',
                  '${stats['total_transfers']}',
                ),
                _buildStatRow(
                  'إجمالي الأصناف المنقولة',
                  '${stats['total_items_transferred']}',
                ),
                _buildStatRow(
                  'إجمالي الكمية المنقولة',
                  '${stats['total_quantity_transferred']}',
                ),
                const Divider(),
                _buildStatRow('طلبات معلقة', '${stats['pending_count']}'),
                _buildStatRow('طلبات معتمدة', '${stats['approved_count']}'),
                _buildStatRow('طلبات مكتملة', '${stats['completed_count']}'),
                _buildStatRow('طلبات ملغية', '${stats['cancelled_count']}'),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('إغلاق'),
            ),
          ],
        ),
      );
    } catch (e) {
      if (mounted) {
        _showErrorSnackBar('خطأ في تحميل الإحصائيات: $e');
      }
    }
  }

  Widget _buildStatRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label),
          Text(value, style: const TextStyle(fontWeight: FontWeight.bold)),
        ],
      ),
    );
  }
}
