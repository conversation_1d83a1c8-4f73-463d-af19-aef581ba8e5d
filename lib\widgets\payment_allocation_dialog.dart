/// حوار ربط الدفعات بالفواتير المتعددة
/// يتيح توزيع دفعة واحدة على عدة فواتير أو ربط عدة دفعات بفاتورة واحدة
library;

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../models/payment.dart';
import '../models/invoice.dart';
import '../services/payment_tracking_service.dart';
import '../services/invoice_service.dart';
import '../constants/revolutionary_design_colors.dart';
import '../widgets/loading_widget.dart';

class PaymentAllocationDialog extends StatefulWidget {
  final Payment? payment;
  final Invoice? invoice;
  final Function()? onAllocationComplete;

  const PaymentAllocationDialog({
    super.key,
    this.payment,
    this.invoice,
    this.onAllocationComplete,
  });

  @override
  State<PaymentAllocationDialog> createState() =>
      _PaymentAllocationDialogState();
}

class _PaymentAllocationDialogState extends State<PaymentAllocationDialog> {
  final PaymentTrackingService _trackingService = PaymentTrackingService();
  final InvoiceService _invoiceService = InvoiceService();

  List<Invoice> _availableInvoices = [];
  List<PaymentAllocation> _allocations = [];
  String _allocationMethod = 'manual';
  bool _isLoading = true;
  bool _isSubmitting = false;
  double _totalAllocated = 0.0;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    setState(() => _isLoading = true);

    try {
      if (widget.payment != null) {
        // إذا كانت الدفعة محددة، نحمل الفواتير المتاحة للربط
        await _loadInvoicesForPayment();
      } else if (widget.invoice != null) {
        // إذا كانت الفاتورة محددة، نحمل الدفعات المتاحة للربط
        await _loadPaymentsForInvoice();
      }

      setState(() => _isLoading = false);
    } catch (e) {
      setState(() => _isLoading = false);
      _showError('خطأ في تحميل البيانات: $e');
    }
  }

  Future<void> _loadInvoicesForPayment() async {
    // تحميل الفواتير غير المدفوعة بالكامل
    final invoices = await _invoiceService.getAllInvoices();

    setState(() {
      _availableInvoices = invoices
          .where((inv) => inv.remainingAmount > 0.01)
          .toList();

      // إنشاء تخصيص افتراضي إذا كان هناك فاتورة واحدة فقط
      if (_availableInvoices.length == 1 && widget.payment != null) {
        final invoice = _availableInvoices.first;
        final allocatedAmount = [
          widget.payment!.amount,
          invoice.remainingAmount,
        ].reduce((a, b) => a < b ? a : b);

        _allocations = [
          PaymentAllocation(
            paymentId: widget.payment!.id!,
            invoiceId: invoice.id!,
            allocatedAmount: allocatedAmount,
          ),
        ];
        _calculateTotalAllocated();
      }
    });
  }

  Future<void> _loadPaymentsForInvoice() async {
    // هذه الوظيفة للمستقبل - ربط عدة دفعات بفاتورة واحدة
    // حالياً نركز على ربط دفعة واحدة بعدة فواتير
  }

  void _addAllocation(Invoice invoice) {
    if (widget.payment == null) return;

    // التحقق من عدم وجود تخصيص مسبق لهذه الفاتورة
    final existingIndex = _allocations.indexWhere(
      (a) => a.invoiceId == invoice.id,
    );
    if (existingIndex != -1) {
      _showError('هذه الفاتورة مضافة مسبقاً');
      return;
    }

    final remainingPaymentAmount = widget.payment!.amount - _totalAllocated;
    if (remainingPaymentAmount <= 0.01) {
      _showError('تم توزيع كامل مبلغ الدفعة');
      return;
    }

    final allocatedAmount = [
      remainingPaymentAmount,
      invoice.remainingAmount,
    ].reduce((a, b) => a < b ? a : b);

    setState(() {
      _allocations.add(
        PaymentAllocation(
          paymentId: widget.payment!.id!,
          invoiceId: invoice.id!,
          allocatedAmount: allocatedAmount,
        ),
      );
      _calculateTotalAllocated();
    });
  }

  void _removeAllocation(int index) {
    setState(() {
      _allocations.removeAt(index);
      _calculateTotalAllocated();
    });
  }

  void _updateAllocationAmount(int index, double newAmount) {
    if (widget.payment == null) return;

    final allocation = _allocations[index];
    final invoice = _availableInvoices.firstWhere(
      (inv) => inv.id == allocation.invoiceId,
    );

    // التحقق من الحدود
    final otherAllocationsTotal = _allocations
        .where((a) => a != allocation)
        .fold(0.0, (sum, a) => sum + a.allocatedAmount);

    final maxAllowed = [
      widget.payment!.amount - otherAllocationsTotal,
      invoice.remainingAmount,
    ].reduce((a, b) => a < b ? a : b);

    if (newAmount > maxAllowed) {
      _showError(
        'المبلغ يتجاوز الحد المسموح: ${maxAllowed.toStringAsFixed(2)}',
      );
      return;
    }

    setState(() {
      _allocations[index] = allocation.copyWith(allocatedAmount: newAmount);
      _calculateTotalAllocated();
    });
  }

  void _calculateTotalAllocated() {
    _totalAllocated = _allocations.fold(
      0.0,
      (sum, allocation) => sum + allocation.allocatedAmount,
    );
  }

  void _autoAllocate() {
    if (widget.payment == null || _availableInvoices.isEmpty) return;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('التوزيع التلقائي'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('اختر طريقة التوزيع التلقائي:'),
            const SizedBox(height: 16),
            RadioListTile<String>(
              title: const Text('توزيع نسبي'),
              subtitle: const Text('حسب قيمة كل فاتورة'),
              value: 'proportional',
              groupValue: _allocationMethod,
              onChanged: (value) => setState(() => _allocationMethod = value!),
            ),
            RadioListTile<String>(
              title: const Text('الأقدم أولاً'),
              subtitle: const Text('حسب تاريخ الفاتورة'),
              value: 'oldest_first',
              groupValue: _allocationMethod,
              onChanged: (value) => setState(() => _allocationMethod = value!),
            ),
            RadioListTile<String>(
              title: const Text('الأكبر أولاً'),
              subtitle: const Text('حسب قيمة الفاتورة'),
              value: 'largest_first',
              groupValue: _allocationMethod,
              onChanged: (value) => setState(() => _allocationMethod = value!),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.of(context).pop();
              await _performAutoAllocation();
            },
            child: const Text('تطبيق'),
          ),
        ],
      ),
    );
  }

  Future<void> _performAutoAllocation() async {
    try {
      await _trackingService.autoAllocatePayment(
        paymentId: widget.payment!.id!,
        invoiceIds: _availableInvoices.map((inv) => inv.id!).toList(),
        allocationMethod: _allocationMethod,
      );

      // إعادة تحميل التخصيصات
      final allocations = await _trackingService.getPaymentAllocations(
        widget.payment!.id!,
      );
      setState(() {
        _allocations = allocations;
        _calculateTotalAllocated();
      });

      _showSuccess('تم التوزيع التلقائي بنجاح');
    } catch (e) {
      _showError('خطأ في التوزيع التلقائي: $e');
    }
  }

  Future<void> _saveAllocations() async {
    if (_allocations.isEmpty) {
      _showError('يرجى إضافة تخصيص واحد على الأقل');
      return;
    }

    setState(() => _isSubmitting = true);

    try {
      await _trackingService.allocatePayment(
        paymentId: widget.payment!.id!,
        allocations: _allocations,
      );

      widget.onAllocationComplete?.call();

      if (mounted) {
        Navigator.of(context).pop();
        _showSuccess('تم حفظ التخصيصات بنجاح');
      }
    } catch (e) {
      _showError('خطأ في حفظ التخصيصات: $e');
    } finally {
      setState(() => _isSubmitting = false);
    }
  }

  void _showError(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message), backgroundColor: Colors.red),
    );
  }

  void _showSuccess(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: RevolutionaryColors.successGlow,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        height: MediaQuery.of(context).size.height * 0.8,
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildHeader(),
            const SizedBox(height: 16),
            if (_isLoading)
              const Expanded(child: LoadingWidget())
            else ...[
              _buildPaymentInfo(),
              const SizedBox(height: 16),
              Expanded(
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Expanded(flex: 2, child: _buildAvailableInvoices()),
                    const SizedBox(width: 16),
                    Expanded(flex: 3, child: _buildAllocations()),
                  ],
                ),
              ),
            ],
            const SizedBox(height: 16),
            _buildButtons(),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      children: [
        Icon(Icons.link, color: RevolutionaryColors.damascusSky),
        const SizedBox(width: 8),
        Text(
          'ربط الدفعات بالفواتير',
          style: Theme.of(
            context,
          ).textTheme.headlineSmall?.copyWith(fontWeight: FontWeight.bold),
        ),
        const Spacer(),
        IconButton(
          onPressed: () => Navigator.of(context).pop(),
          icon: const Icon(Icons.close),
        ),
      ],
    );
  }

  Widget _buildPaymentInfo() {
    if (widget.payment == null) return const SizedBox.shrink();

    final remainingAmount = widget.payment!.amount - _totalAllocated;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'معلومات الدفعة',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: _buildInfoItem(
                    'مبلغ الدفعة',
                    '${widget.payment!.amount.toStringAsFixed(2)} ل.س',
                    RevolutionaryColors.damascusSky,
                  ),
                ),
                Expanded(
                  child: _buildInfoItem(
                    'المبلغ الموزع',
                    '${_totalAllocated.toStringAsFixed(2)} ل.س',
                    RevolutionaryColors.successGlow,
                  ),
                ),
                Expanded(
                  child: _buildInfoItem(
                    'المبلغ المتبقي',
                    '${remainingAmount.toStringAsFixed(2)} ل.س',
                    remainingAmount > 0.01
                        ? Colors.orange
                        : RevolutionaryColors.successGlow,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            LinearProgressIndicator(
              value: widget.payment!.amount > 0
                  ? _totalAllocated / widget.payment!.amount
                  : 0,
              backgroundColor: Colors.grey[300],
              valueColor: AlwaysStoppedAnimation<Color>(
                remainingAmount <= 0.01
                    ? RevolutionaryColors.successGlow
                    : Colors.orange,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoItem(String label, String value, Color color) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall,
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: color,
            fontSize: 16,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildAvailableInvoices() {
    return Card(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Text(
                  'الفواتير المتاحة',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                ElevatedButton.icon(
                  onPressed: _autoAllocate,
                  icon: const Icon(Icons.auto_fix_high, size: 16),
                  label: const Text('توزيع تلقائي'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: RevolutionaryColors.damascusSky,
                    foregroundColor: Colors.white,
                  ),
                ),
              ],
            ),
          ),
          Expanded(
            child: ListView.builder(
              itemCount: _availableInvoices.length,
              itemBuilder: (context, index) {
                final invoice = _availableInvoices[index];
                final isAllocated = _allocations.any(
                  (a) => a.invoiceId == invoice.id,
                );

                return ListTile(
                  leading: CircleAvatar(
                    backgroundColor: isAllocated
                        ? RevolutionaryColors.successGlow
                        : RevolutionaryColors.damascusSky,
                    child: Icon(
                      isAllocated ? Icons.check : Icons.receipt,
                      color: Colors.white,
                      size: 20,
                    ),
                  ),
                  title: Text(
                    invoice.invoiceNumber,
                    style: TextStyle(
                      fontWeight: isAllocated
                          ? FontWeight.bold
                          : FontWeight.normal,
                    ),
                  ),
                  subtitle: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'الإجمالي: ${invoice.totalAmount.toStringAsFixed(2)} ل.س',
                      ),
                      Text(
                        'المتبقي: ${invoice.remainingAmount.toStringAsFixed(2)} ل.س',
                      ),
                      Text(
                        'التاريخ: ${invoice.invoiceDate.day}/${invoice.invoiceDate.month}/${invoice.invoiceDate.year}',
                      ),
                    ],
                  ),
                  trailing: isAllocated
                      ? Icon(Icons.check_circle, color: RevolutionaryColors.successGlow)
                      : IconButton(
                          icon: const Icon(Icons.add_circle_outline),
                          onPressed: () => _addAllocation(invoice),
                        ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAllocations() {
    return Card(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Text(
              'التخصيصات (${_allocations.length})',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(
            child: _allocations.isEmpty
                ? Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(Icons.link_off, size: 48, color: Colors.grey),
                        const SizedBox(height: 8),
                        Text(
                          'لا توجد تخصيصات',
                          style: TextStyle(color: Colors.grey[600]),
                        ),
                      ],
                    ),
                  )
                : ListView.builder(
                    itemCount: _allocations.length,
                    itemBuilder: (context, index) {
                      final allocation = _allocations[index];
                      final invoice = _availableInvoices.firstWhere(
                        (inv) => inv.id == allocation.invoiceId,
                      );

                      return Card(
                        margin: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 4,
                        ),
                        child: Padding(
                          padding: const EdgeInsets.all(12),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                children: [
                                  Expanded(
                                    child: Text(
                                      invoice.invoiceNumber,
                                      style: const TextStyle(
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                  ),
                                  IconButton(
                                    icon: const Icon(
                                      Icons.delete,
                                      color: Colors.red,
                                    ),
                                    onPressed: () => _removeAllocation(index),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 8),
                              Row(
                                children: [
                                  Expanded(
                                    child: TextField(
                                      controller: TextEditingController(
                                        text: allocation.allocatedAmount
                                            .toStringAsFixed(2),
                                      ),
                                      keyboardType: TextInputType.number,
                                      inputFormatters: [
                                        FilteringTextInputFormatter.allow(
                                          RegExp(r'^\d+\.?\d{0,2}'),
                                        ),
                                      ],
                                      decoration: InputDecoration(
                                        labelText: 'المبلغ المخصص',
                                        suffixText: 'ل.س',
                                        border: OutlineInputBorder(
                                          borderRadius: BorderRadius.circular(
                                            8,
                                          ),
                                        ),
                                        isDense: true,
                                      ),
                                      onChanged: (value) {
                                        final amount =
                                            double.tryParse(value) ?? 0.0;
                                        _updateAllocationAmount(index, amount);
                                      },
                                    ),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 4),
                              Text(
                                'من أصل: ${invoice.remainingAmount.toStringAsFixed(2)} ل.س',
                                style: Theme.of(context).textTheme.bodySmall,
                              ),
                            ],
                          ),
                        ),
                      );
                    },
                  ),
          ),
        ],
      ),
    );
  }

  Widget _buildButtons() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('إلغاء'),
        ),
        const SizedBox(width: 8),
        ElevatedButton(
          onPressed: _isSubmitting || _allocations.isEmpty
              ? null
              : _saveAllocations,
          style: ElevatedButton.styleFrom(
            backgroundColor: RevolutionaryColors.damascusSky,
            foregroundColor: Colors.white,
          ),
          child: _isSubmitting
              ? const SizedBox(
                  height: 20,
                  width: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  ),
                )
              : const Text('حفظ التخصيصات'),
        ),
      ],
    );
  }
}
