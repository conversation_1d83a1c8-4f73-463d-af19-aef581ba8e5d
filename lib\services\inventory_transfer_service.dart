/// خدمة إدارة نقل المخزون بين المواقع والمستودعات
/// تتعامل مع جميع عمليات النقل مع تتبع مفصل وتدقيق شامل
library;

import '../database/database_helper.dart';
import '../models/inventory_transfer.dart';
import '../services/item_location_stock_service.dart';
import '../services/logging_service.dart';
import '../services/audit_service.dart';
import '../exceptions/validation_exception.dart';
import '../constants/app_constants.dart';

class InventoryTransferService {
  final DatabaseHelper _databaseHelper = DatabaseHelper();
  final ItemLocationStockService _stockService = ItemLocationStockService();

  /// إنشاء جداول النقل
  Future<void> createTables() async {
    final db = await _databaseHelper.database;

    await db.transaction((txn) async {
      // جدول طلبات النقل
      await txn.execute('''
        CREATE TABLE IF NOT EXISTS inventory_transfers (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          transfer_number TEXT NOT NULL UNIQUE,
          from_warehouse_id INTEGER NOT NULL,
          to_warehouse_id INTEGER NOT NULL,
          from_location_id INTEGER,
          to_location_id INTEGER,
          status TEXT NOT NULL DEFAULT 'pending',
          transfer_date TEXT NOT NULL,
          requested_by TEXT,
          approved_by TEXT,
          completed_by TEXT,
          notes TEXT,
          total_items INTEGER NOT NULL DEFAULT 0,
          total_quantity REAL NOT NULL DEFAULT 0,
          created_at TEXT NOT NULL,
          updated_at TEXT NOT NULL,
          FOREIGN KEY (from_warehouse_id) REFERENCES warehouses (id),
          FOREIGN KEY (to_warehouse_id) REFERENCES warehouses (id),
          FOREIGN KEY (from_location_id) REFERENCES warehouse_locations (id),
          FOREIGN KEY (to_location_id) REFERENCES warehouse_locations (id)
        )
      ''');

      // جدول تفاصيل النقل
      await txn.execute('''
        CREATE TABLE IF NOT EXISTS inventory_transfer_items (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          transfer_id INTEGER NOT NULL,
          item_id INTEGER NOT NULL,
          requested_quantity REAL NOT NULL,
          transferred_quantity REAL NOT NULL DEFAULT 0,
          unit_cost REAL NOT NULL DEFAULT 0,
          batch_number TEXT,
          expiry_date TEXT,
          serial_number TEXT,
          notes TEXT,
          created_at TEXT NOT NULL,
          FOREIGN KEY (transfer_id) REFERENCES inventory_transfers (id),
          FOREIGN KEY (item_id) REFERENCES ${AppConstants.itemsTable} (id)
        )
      ''');

      // جدول سجل حالات النقل
      await txn.execute('''
        CREATE TABLE IF NOT EXISTS transfer_status_history (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          transfer_id INTEGER NOT NULL,
          from_status TEXT NOT NULL,
          to_status TEXT NOT NULL,
          changed_by TEXT,
          change_reason TEXT,
          changed_at TEXT NOT NULL,
          FOREIGN KEY (transfer_id) REFERENCES inventory_transfers (id)
        )
      ''');

      // إنشاء فهارس للأداء
      await txn.execute(
        'CREATE INDEX IF NOT EXISTS idx_transfers_number ON inventory_transfers(transfer_number)',
      );
      await txn.execute(
        'CREATE INDEX IF NOT EXISTS idx_transfers_status ON inventory_transfers(status)',
      );
      await txn.execute(
        'CREATE INDEX IF NOT EXISTS idx_transfers_date ON inventory_transfers(transfer_date)',
      );
      await txn.execute(
        'CREATE INDEX IF NOT EXISTS idx_transfer_items_transfer ON inventory_transfer_items(transfer_id)',
      );
    });

    LoggingService.info(
      'تم إنشاء جداول نقل المخزون',
      category: 'InventoryTransferService',
    );
  }

  /// إنشاء طلب نقل جديد
  Future<int> createTransfer(InventoryTransfer transfer) async {
    try {
      await _validateTransfer(transfer);

      final db = await _databaseHelper.database;

      return await db.transaction((txn) async {
        // إنشاء رقم النقل
        final transferNumber = await _generateTransferNumber();

        // إدراج طلب النقل
        final transferData = transfer.toMap();
        transferData['transfer_number'] = transferNumber;
        transferData.remove('id');

        final transferId = await txn.insert(
          'inventory_transfers',
          transferData,
        );

        // إدراج تفاصيل الأصناف
        for (final item in transfer.items) {
          final itemData = item.toMap();
          itemData['transfer_id'] = transferId;
          itemData.remove('id');

          await txn.insert('inventory_transfer_items', itemData);
        }

        // تسجيل تغيير الحالة
        await _recordStatusChange(
          transferId,
          '',
          transfer.status.code,
          transfer.requestedBy,
          'إنشاء طلب نقل جديد',
          txn,
        );

        // تسجيل العملية
        await AuditService.log(
          action: 'CREATE',
          entityType: 'inventory_transfers',
          entityId: transferId,
          entityName: transferNumber,
          description: 'إنشاء طلب نقل جديد: $transferNumber',
          newValues: transferData,
        );

        LoggingService.info(
          'تم إنشاء طلب نقل جديد',
          category: 'InventoryTransferService',
          data: {
            'transfer_id': transferId,
            'transfer_number': transferNumber,
            'items_count': transfer.items.length,
          },
        );

        return transferId;
      });
    } catch (e) {
      LoggingService.error(
        'خطأ في إنشاء طلب النقل',
        category: 'InventoryTransferService',
        data: {'error': e.toString()},
      );
      rethrow;
    }
  }

  /// الموافقة على طلب النقل
  Future<void> approveTransfer(int transferId, String approvedBy) async {
    try {
      final transfer = await getTransferById(transferId);
      if (transfer == null) {
        throw ValidationException('طلب النقل غير موجود');
      }

      if (transfer.status != TransferStatus.pending) {
        throw ValidationException('يمكن الموافقة على الطلبات المعلقة فقط');
      }

      await _updateTransferStatus(
        transferId,
        TransferStatus.approved,
        approvedBy,
        'تم الموافقة على طلب النقل',
      );

      LoggingService.info(
        'تم الموافقة على طلب النقل',
        category: 'InventoryTransferService',
        data: {'transfer_id': transferId, 'approved_by': approvedBy},
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في الموافقة على طلب النقل',
        category: 'InventoryTransferService',
        data: {'transfer_id': transferId, 'error': e.toString()},
      );
      rethrow;
    }
  }

  /// تنفيذ النقل
  Future<void> executeTransfer(
    int transferId,
    String completedBy, {
    Map<int, double>? actualQuantities,
  }) async {
    try {
      final transfer = await getTransferById(transferId);
      if (transfer == null) {
        throw ValidationException('طلب النقل غير موجود');
      }

      if (transfer.status != TransferStatus.approved) {
        throw ValidationException('يجب الموافقة على الطلب أولاً');
      }

      final db = await _databaseHelper.database;

      await db.transaction((txn) async {
        // تنفيذ النقل لكل صنف
        for (final item in transfer.items) {
          final actualQty =
              actualQuantities?[item.id!] ?? item.requestedQuantity;

          if (actualQty > 0) {
            // خصم من الموقع المصدر
            await _stockService.subtractItemFromLocation(
              itemId: item.itemId,
              locationId: transfer.fromLocationId!,
              quantity: actualQty,
              description: 'نقل إلى ${transfer.transferNumber}',
            );

            // إضافة للموقع المستهدف
            await _stockService.addItemToLocation(
              itemId: item.itemId,
              warehouseId: transfer.toWarehouseId,
              locationId: transfer.toLocationId!,
              quantity: actualQty,
              unitCost: item.unitCost,
              description: 'استلام من ${transfer.transferNumber}',
            );

            // تحديث الكمية المنقولة
            await txn.update(
              'inventory_transfer_items',
              {'transferred_quantity': actualQty},
              where: 'id = ?',
              whereArgs: [item.id],
            );

            // تسجيل حركة المخزون
            await _recordInventoryMovement(
              item.itemId,
              transfer.fromLocationId!,
              transfer.toLocationId!,
              actualQty,
              item.unitCost,
              transfer.transferNumber,
              txn,
            );
          }
        }

        // تحديث حالة النقل
        await txn.update(
          'inventory_transfers',
          {
            'status': TransferStatus.completed.code,
            'completed_by': completedBy,
            'updated_at': DateTime.now().toIso8601String(),
          },
          where: 'id = ?',
          whereArgs: [transferId],
        );

        // تسجيل تغيير الحالة
        await _recordStatusChange(
          transferId,
          TransferStatus.approved.code,
          TransferStatus.completed.code,
          completedBy,
          'تم تنفيذ النقل بنجاح',
          txn,
        );
      });

      LoggingService.info(
        'تم تنفيذ النقل بنجاح',
        category: 'InventoryTransferService',
        data: {'transfer_id': transferId, 'completed_by': completedBy},
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في تنفيذ النقل',
        category: 'InventoryTransferService',
        data: {'transfer_id': transferId, 'error': e.toString()},
      );
      rethrow;
    }
  }

  /// إلغاء طلب النقل
  Future<void> cancelTransfer(
    int transferId,
    String cancelledBy,
    String reason,
  ) async {
    try {
      final transfer = await getTransferById(transferId);
      if (transfer == null) {
        throw ValidationException('طلب النقل غير موجود');
      }

      if (transfer.status == TransferStatus.completed) {
        throw ValidationException('لا يمكن إلغاء النقل المكتمل');
      }

      await _updateTransferStatus(
        transferId,
        TransferStatus.cancelled,
        cancelledBy,
        reason,
      );

      LoggingService.info(
        'تم إلغاء طلب النقل',
        category: 'InventoryTransferService',
        data: {
          'transfer_id': transferId,
          'cancelled_by': cancelledBy,
          'reason': reason,
        },
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في إلغاء طلب النقل',
        category: 'InventoryTransferService',
        data: {'transfer_id': transferId, 'error': e.toString()},
      );
      rethrow;
    }
  }

  /// الحصول على طلب نقل بالمعرف
  Future<InventoryTransfer?> getTransferById(int id) async {
    try {
      final db = await _databaseHelper.database;

      final transferMaps = await db.query(
        'inventory_transfers',
        where: 'id = ?',
        whereArgs: [id],
      );

      if (transferMaps.isEmpty) return null;

      final transfer = InventoryTransfer.fromMap(transferMaps.first);

      // تحميل تفاصيل الأصناف
      final itemMaps = await db.query(
        'inventory_transfer_items',
        where: 'transfer_id = ?',
        whereArgs: [id],
        orderBy: 'id ASC',
      );

      final items = itemMaps
          .map((map) => InventoryTransferItem.fromMap(map))
          .toList();

      return transfer.copyWith(items: items);
    } catch (e) {
      LoggingService.error(
        'خطأ في الحصول على طلب النقل',
        category: 'InventoryTransferService',
        data: {'id': id, 'error': e.toString()},
      );
      return null;
    }
  }

  /// الحصول على سجل حالات النقل
  Future<List<Map<String, dynamic>>> getTransferStatusHistory(
    int transferId,
  ) async {
    try {
      final db = await _databaseHelper.database;

      final result = await db.query(
        'transfer_status_history',
        where: 'transfer_id = ?',
        whereArgs: [transferId],
        orderBy: 'changed_at ASC',
      );

      return result;
    } catch (e) {
      LoggingService.error(
        'خطأ في الحصول على سجل حالات النقل',
        category: 'InventoryTransferService',
        data: {'transfer_id': transferId, 'error': e.toString()},
      );
      return [];
    }
  }

  /// الحصول على جميع طلبات النقل
  Future<List<InventoryTransfer>> getAllTransfers({
    TransferStatus? status,
    int? fromWarehouseId,
    int? toWarehouseId,
    DateTime? fromDate,
    DateTime? toDate,
    int? limit,
    int? offset,
  }) async {
    try {
      final db = await _databaseHelper.database;

      String whereClause = '';
      List<dynamic> whereArgs = [];

      if (status != null) {
        whereClause += whereClause.isEmpty ? 'WHERE ' : ' AND ';
        whereClause += 'status = ?';
        whereArgs.add(status.code);
      }

      if (fromWarehouseId != null) {
        whereClause += whereClause.isEmpty ? 'WHERE ' : ' AND ';
        whereClause += 'from_warehouse_id = ?';
        whereArgs.add(fromWarehouseId);
      }

      if (toWarehouseId != null) {
        whereClause += whereClause.isEmpty ? 'WHERE ' : ' AND ';
        whereClause += 'to_warehouse_id = ?';
        whereArgs.add(toWarehouseId);
      }

      if (fromDate != null) {
        whereClause += whereClause.isEmpty ? 'WHERE ' : ' AND ';
        whereClause += 'transfer_date >= ?';
        whereArgs.add(fromDate.toIso8601String().split('T')[0]);
      }

      if (toDate != null) {
        whereClause += whereClause.isEmpty ? 'WHERE ' : ' AND ';
        whereClause += 'transfer_date <= ?';
        whereArgs.add(toDate.toIso8601String().split('T')[0]);
      }

      final transferMaps = await db.rawQuery('''
        SELECT * FROM inventory_transfers
        $whereClause
        ORDER BY transfer_date DESC, id DESC
        ${limit != null ? 'LIMIT $limit' : ''}
        ${offset != null ? 'OFFSET $offset' : ''}
      ''', whereArgs);

      List<InventoryTransfer> transfers = [];
      for (final map in transferMaps) {
        final transfer = InventoryTransfer.fromMap(map);

        // تحميل تفاصيل الأصناف
        final itemMaps = await db.query(
          'inventory_transfer_items',
          where: 'transfer_id = ?',
          whereArgs: [transfer.id],
          orderBy: 'id ASC',
        );

        final items = itemMaps
            .map((map) => InventoryTransferItem.fromMap(map))
            .toList();

        transfers.add(transfer.copyWith(items: items));
      }

      return transfers;
    } catch (e) {
      LoggingService.error(
        'خطأ في الحصول على طلبات النقل',
        category: 'InventoryTransferService',
        data: {'error': e.toString()},
      );
      return [];
    }
  }

  /// الحصول على طلبات النقل حسب الحالة
  Future<List<InventoryTransfer>> getTransfersByStatus(
    TransferStatus status, {
    int? limit,
    int? offset,
  }) async {
    return getAllTransfers(status: status, limit: limit, offset: offset);
  }

  /// الحصول على طلبات النقل المعلقة
  Future<List<InventoryTransfer>> getPendingTransfers({
    int? limit,
    int? offset,
  }) async {
    return getTransfersByStatus(
      TransferStatus.pending,
      limit: limit,
      offset: offset,
    );
  }

  /// الحصول على طلبات النقل المعتمدة
  Future<List<InventoryTransfer>> getApprovedTransfers({
    int? limit,
    int? offset,
  }) async {
    return getTransfersByStatus(
      TransferStatus.approved,
      limit: limit,
      offset: offset,
    );
  }

  /// الحصول على طلبات النقل المكتملة
  Future<List<InventoryTransfer>> getCompletedTransfers({
    int? limit,
    int? offset,
  }) async {
    return getTransfersByStatus(
      TransferStatus.completed,
      limit: limit,
      offset: offset,
    );
  }

  /// الحصول على إحصائيات النقل
  Future<Map<String, dynamic>> getTransferStatistics({
    DateTime? fromDate,
    DateTime? toDate,
  }) async {
    try {
      final db = await _databaseHelper.database;

      String dateFilter = '';
      List<dynamic> dateArgs = [];

      if (fromDate != null) {
        dateFilter += ' AND transfer_date >= ?';
        dateArgs.add(fromDate.toIso8601String().split('T')[0]);
      }

      if (toDate != null) {
        dateFilter += ' AND transfer_date <= ?';
        dateArgs.add(toDate.toIso8601String().split('T')[0]);
      }

      // إحصائيات عامة
      final generalStats = await db.rawQuery(
        '''
        SELECT
          COUNT(*) as total_transfers,
          SUM(total_items) as total_items_transferred,
          SUM(total_quantity) as total_quantity_transferred,
          COUNT(CASE WHEN status = ? THEN 1 END) as pending_count,
          COUNT(CASE WHEN status = ? THEN 1 END) as approved_count,
          COUNT(CASE WHEN status = ? THEN 1 END) as completed_count,
          COUNT(CASE WHEN status = ? THEN 1 END) as cancelled_count
        FROM inventory_transfers
        WHERE 1=1 $dateFilter
      ''',
        [
          TransferStatus.pending.code,
          TransferStatus.approved.code,
          TransferStatus.completed.code,
          TransferStatus.cancelled.code,
          ...dateArgs,
        ],
      );

      final stats = generalStats.first;

      return {
        'total_transfers': stats['total_transfers'] ?? 0,
        'total_items_transferred': stats['total_items_transferred'] ?? 0,
        'total_quantity_transferred':
            stats['total_quantity_transferred'] ?? 0.0,
        'pending_count': stats['pending_count'] ?? 0,
        'approved_count': stats['approved_count'] ?? 0,
        'completed_count': stats['completed_count'] ?? 0,
        'cancelled_count': stats['cancelled_count'] ?? 0,
      };
    } catch (e) {
      LoggingService.error(
        'خطأ في الحصول على إحصائيات النقل',
        category: 'InventoryTransferService',
        data: {'error': e.toString()},
      );
      return {
        'total_transfers': 0,
        'total_items_transferred': 0,
        'total_quantity_transferred': 0.0,
        'pending_count': 0,
        'approved_count': 0,
        'completed_count': 0,
        'cancelled_count': 0,
      };
    }
  }

  /// البحث في طلبات النقل
  Future<List<InventoryTransfer>> searchTransfers(
    String query, {
    int? limit,
    int? offset,
  }) async {
    try {
      final db = await _databaseHelper.database;

      final transferMaps = await db.rawQuery(
        '''
        SELECT * FROM inventory_transfers
        WHERE transfer_number LIKE ?
           OR notes LIKE ?
           OR requested_by LIKE ?
           OR approved_by LIKE ?
           OR completed_by LIKE ?
        ORDER BY transfer_date DESC, id DESC
        ${limit != null ? 'LIMIT $limit' : ''}
        ${offset != null ? 'OFFSET $offset' : ''}
      ''',
        ['%$query%', '%$query%', '%$query%', '%$query%', '%$query%'],
      );

      List<InventoryTransfer> transfers = [];
      for (final map in transferMaps) {
        final transfer = InventoryTransfer.fromMap(map);

        // تحميل تفاصيل الأصناف
        final itemMaps = await db.query(
          'inventory_transfer_items',
          where: 'transfer_id = ?',
          whereArgs: [transfer.id],
          orderBy: 'id ASC',
        );

        final items = itemMaps
            .map((map) => InventoryTransferItem.fromMap(map))
            .toList();

        transfers.add(transfer.copyWith(items: items));
      }

      return transfers;
    } catch (e) {
      LoggingService.error(
        'خطأ في البحث في طلبات النقل',
        category: 'InventoryTransferService',
        data: {'query': query, 'error': e.toString()},
      );
      return [];
    }
  }

  /// طرق مساعدة خاصة

  /// التحقق من صحة طلب النقل
  Future<void> _validateTransfer(InventoryTransfer transfer) async {
    if (transfer.fromWarehouseId == transfer.toWarehouseId &&
        transfer.fromLocationId == transfer.toLocationId) {
      throw ValidationException('لا يمكن النقل إلى نفس الموقع');
    }

    if (transfer.items.isEmpty) {
      throw ValidationException('يجب إضافة أصناف للنقل');
    }

    for (final item in transfer.items) {
      if (item.requestedQuantity <= 0) {
        throw ValidationException('الكمية المطلوبة يجب أن تكون أكبر من الصفر');
      }

      // التحقق من توفر الكمية في الموقع المصدر
      if (transfer.fromLocationId != null) {
        final stock = await _stockService.getItemLocationStock(
          item.itemId,
          transfer.fromLocationId!,
        );

        if (stock == null || stock.availableQuantity < item.requestedQuantity) {
          throw ValidationException(
            'الكمية المتاحة غير كافية للصنف ${item.itemId}',
          );
        }
      }
    }
  }

  /// إنشاء رقم النقل
  Future<String> _generateTransferNumber() async {
    final db = await _databaseHelper.database;
    final now = DateTime.now();
    final prefix = 'TR${now.year}${now.month.toString().padLeft(2, '0')}';

    final result = await db.rawQuery(
      '''
      SELECT COUNT(*) as count
      FROM inventory_transfers
      WHERE transfer_number LIKE ?
    ''',
      ['$prefix%'],
    );

    final count = result.first['count'] as int;
    final sequence = (count + 1).toString().padLeft(4, '0');

    return '$prefix$sequence';
  }

  /// تحديث حالة النقل
  Future<void> _updateTransferStatus(
    int transferId,
    TransferStatus newStatus,
    String changedBy,
    String reason,
  ) async {
    final db = await _databaseHelper.database;

    await db.transaction((txn) async {
      // الحصول على الحالة الحالية
      final currentResult = await txn.query(
        'inventory_transfers',
        columns: ['status'],
        where: 'id = ?',
        whereArgs: [transferId],
      );

      if (currentResult.isEmpty) {
        throw ValidationException('طلب النقل غير موجود');
      }

      final currentStatus = currentResult.first['status'] as String;

      // تحديث الحالة
      await txn.update(
        'inventory_transfers',
        {
          'status': newStatus.code,
          'updated_at': DateTime.now().toIso8601String(),
        },
        where: 'id = ?',
        whereArgs: [transferId],
      );

      // تسجيل تغيير الحالة
      await _recordStatusChange(
        transferId,
        currentStatus,
        newStatus.code,
        changedBy,
        reason,
        txn,
      );
    });
  }

  /// تسجيل تغيير الحالة
  Future<void> _recordStatusChange(
    int transferId,
    String fromStatus,
    String toStatus,
    String? changedBy,
    String? reason,
    dynamic txn,
  ) async {
    await txn.insert('transfer_status_history', {
      'transfer_id': transferId,
      'from_status': fromStatus,
      'to_status': toStatus,
      'changed_by': changedBy,
      'change_reason': reason,
      'changed_at': DateTime.now().toIso8601String(),
    });
  }

  /// تسجيل حركة المخزون
  Future<void> _recordInventoryMovement(
    int itemId,
    int fromLocationId,
    int toLocationId,
    double quantity,
    double unitCost,
    String transferNumber,
    dynamic txn,
  ) async {
    // حركة خروج من الموقع المصدر
    await txn.insert('inventory_movements', {
      'item_id': itemId,
      'location_id': fromLocationId,
      'movement_type': 'transfer_out',
      'quantity': -quantity,
      'unit_cost': unitCost,
      'total_cost': -quantity * unitCost,
      'reference_type': 'transfer',
      'description': 'نقل إلى موقع آخر - $transferNumber',
      'from_location_id': fromLocationId,
      'to_location_id': toLocationId,
      'movement_date': DateTime.now().toIso8601String(),
      'created_at': DateTime.now().toIso8601String(),
    });

    // حركة دخول للموقع المستهدف
    await txn.insert('inventory_movements', {
      'item_id': itemId,
      'location_id': toLocationId,
      'movement_type': 'transfer_in',
      'quantity': quantity,
      'unit_cost': unitCost,
      'total_cost': quantity * unitCost,
      'reference_type': 'transfer',
      'description': 'استلام من موقع آخر - $transferNumber',
      'from_location_id': fromLocationId,
      'to_location_id': toLocationId,
      'movement_date': DateTime.now().toIso8601String(),
      'created_at': DateTime.now().toIso8601String(),
    });
  }
}
