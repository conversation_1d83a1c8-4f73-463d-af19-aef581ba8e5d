import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../constants/revolutionary_design_colors.dart';
import '../constants/app_constants.dart';
import '../models/invoice.dart';
import '../models/invoice_status.dart';
import '../models/customer.dart';
import '../models/supplier.dart';
import '../models/item.dart';
import '../services/invoice_service.dart';
import '../services/customer_service.dart';
import '../services/supplier_service.dart';
import '../services/item_service.dart';
import '../widgets/item_selection_dialog.dart';

// Intent classes for keyboard shortcuts
class SaveIntent extends Intent {
  const SaveIntent();
}

class AddItemIntent extends Intent {
  const AddItemIntent();
}

class CancelIntent extends Intent {
  const CancelIntent();
}

class AddInvoiceScreen extends StatefulWidget {
  final String invoiceType;

  const AddInvoiceScreen({super.key, required this.invoiceType});

  @override
  State<AddInvoiceScreen> createState() => _AddInvoiceScreenState();
}

class _AddInvoiceScreenState extends State<AddInvoiceScreen> {
  final _formKey = GlobalKey<FormState>();
  final InvoiceService _invoiceService = InvoiceService();
  final CustomerService _customerService = CustomerService();
  final SupplierService _supplierService = SupplierService();
  final ItemService _itemService = ItemService();

  // Controllers
  final _invoiceNumberController = TextEditingController();
  final _notesController = TextEditingController();

  // Form data
  DateTime _invoiceDate = DateTime.now();
  Customer? _selectedCustomer;
  Supplier? _selectedSupplier;
  List<Customer> _customers = [];
  List<Supplier> _suppliers = [];
  List<Item> _items = [];
  final List<InvoiceItem> _invoiceItems = [];

  double _subtotal = 0.0;
  double _taxAmount = 0.0;
  double _discountAmount = 0.0;
  double _totalAmount = 0.0;

  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadData();
    _generateInvoiceNumber();
  }

  @override
  void dispose() {
    _invoiceNumberController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    setState(() => _isLoading = true);

    try {
      final customers = await _customerService.getActiveCustomers();
      final suppliers = await _supplierService.getActiveSuppliers();
      final items = await _itemService.getActiveItems();

      setState(() {
        _customers = customers;
        _suppliers = suppliers;
        _items = items;
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      _showErrorSnackBar('حدث خطأ في تحميل البيانات: $e');
    }
  }

  Future<void> _generateInvoiceNumber() async {
    try {
      final invoiceNumber = await _invoiceService.generateInvoiceNumber(
        widget.invoiceType,
      );
      _invoiceNumberController.text = invoiceNumber;
    } catch (e) {
      _showErrorSnackBar('حدث خطأ في إنشاء رقم الفاتورة: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Shortcuts(
      shortcuts: <LogicalKeySet, Intent>{
        LogicalKeySet(LogicalKeyboardKey.control, LogicalKeyboardKey.keyS):
            const SaveIntent(),
        LogicalKeySet(LogicalKeyboardKey.control, LogicalKeyboardKey.keyN):
            const AddItemIntent(),
        LogicalKeySet(LogicalKeyboardKey.escape): const CancelIntent(),
      },
      child: Actions(
        actions: <Type, Action<Intent>>{
          SaveIntent: CallbackAction<SaveIntent>(
            onInvoke: (SaveIntent intent) => _saveInvoice(),
          ),
          AddItemIntent: CallbackAction<AddItemIntent>(
            onInvoke: (AddItemIntent intent) => _addItem(),
          ),
          CancelIntent: CallbackAction<CancelIntent>(
            onInvoke: (CancelIntent intent) => Navigator.of(context).pop(),
          ),
        },
        child: Scaffold(
          appBar: AppBar(
            title: Text(_getScreenTitle()),
            actions: [
              IconButton(
                icon: const Icon(Icons.add),
                onPressed: _addItem,
                tooltip: 'إضافة صنف (Ctrl+N)',
              ),
              IconButton(
                icon: const Icon(Icons.save),
                onPressed: _saveInvoice,
                tooltip: 'حفظ (Ctrl+S)',
              ),
            ],
          ),
          body: _isLoading
              ? const Center(child: CircularProgressIndicator())
              : Form(
                  key: _formKey,
                  child: Column(
                    children: [
                      Expanded(
                        child: SingleChildScrollView(
                          padding: const EdgeInsets.all(16),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              _buildInvoiceHeader(),
                              const SizedBox(height: 24),
                              _buildCustomerSupplierSection(),
                              const SizedBox(height: 24),
                              _buildItemsSection(),
                              const SizedBox(height: 24),
                              _buildTotalsSection(),
                              const SizedBox(height: 24),
                              _buildNotesSection(),
                            ],
                          ),
                        ),
                      ),
                      _buildBottomBar(),
                    ],
                  ),
                ),
        ),
      ),
    );
  }

  Widget _buildInvoiceHeader() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'معلومات الفاتورة',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: TextFormField(
                    controller: _invoiceNumberController,
                    decoration: const InputDecoration(
                      labelText: 'رقم الفاتورة',
                      prefixIcon: Icon(Icons.numbers),
                    ),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'يرجى إدخال رقم الفاتورة';
                      }
                      return null;
                    },
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: InkWell(
                    onTap: _selectDate,
                    child: InputDecorator(
                      decoration: const InputDecoration(
                        labelText: 'تاريخ الفاتورة',
                        prefixIcon: Icon(Icons.calendar_today),
                      ),
                      child: Text(
                        '${_invoiceDate.day}/${_invoiceDate.month}/${_invoiceDate.year}',
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCustomerSupplierSection() {
    final isSale =
        widget.invoiceType == AppConstants.invoiceTypeSale ||
        widget.invoiceType == AppConstants.invoiceTypeSaleReturn;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              isSale ? 'العميل' : 'المورد',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 16),
            if (isSale)
              DropdownButtonFormField<Customer>(
                value: _selectedCustomer,
                decoration: const InputDecoration(
                  labelText: 'اختر العميل',
                  prefixIcon: Icon(Icons.person),
                ),
                items: _customers.map((customer) {
                  return DropdownMenuItem(
                    value: customer,
                    child: Text('${customer.name} (${customer.code})'),
                  );
                }).toList(),
                onChanged: (customer) {
                  setState(() {
                    _selectedCustomer = customer;
                  });
                },
                validator: (value) {
                  if (value == null) {
                    return 'يرجى اختيار العميل';
                  }
                  return null;
                },
              )
            else
              DropdownButtonFormField<Supplier>(
                value: _selectedSupplier,
                decoration: const InputDecoration(
                  labelText: 'اختر المورد',
                  prefixIcon: Icon(Icons.business),
                ),
                items: _suppliers.map((supplier) {
                  return DropdownMenuItem(
                    value: supplier,
                    child: Text('${supplier.name} (${supplier.code})'),
                  );
                }).toList(),
                onChanged: (supplier) {
                  setState(() {
                    _selectedSupplier = supplier;
                  });
                },
                validator: (value) {
                  if (value == null) {
                    return 'يرجى اختيار المورد';
                  }
                  return null;
                },
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildItemsSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Text('الأصناف', style: Theme.of(context).textTheme.titleLarge),
                const Spacer(),
                ElevatedButton.icon(
                  onPressed: _addItem,
                  icon: const Icon(Icons.add),
                  label: const Text('إضافة صنف'),
                ),
              ],
            ),
            const SizedBox(height: 16),
            if (_invoiceItems.isEmpty)
              Container(
                padding: const EdgeInsets.all(32),
                child: Center(
                  child: Column(
                    children: [
                      Icon(
                        Icons.inventory_2_outlined,
                        size: 48,
                        color: RevolutionaryColors.textHint,
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'لم يتم إضافة أصناف بعد',
                        style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                          color: RevolutionaryColors.textHint,
                        ),
                      ),
                    ],
                  ),
                ),
              )
            else
              ListView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: _invoiceItems.length,
                itemBuilder: (context, index) {
                  final item = _invoiceItems[index];
                  return _buildInvoiceItemCard(item, index);
                },
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildInvoiceItemCard(InvoiceItem invoiceItem, int index) {
    final item = _items.firstWhere((i) => i.id == invoiceItem.itemId);

    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          children: [
            Row(
              children: [
                // معلومات الصنف
                Expanded(
                  flex: 3,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        item.name,
                        style: Theme.of(context).textTheme.titleMedium
                            ?.copyWith(fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'كود: ${item.code}',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: RevolutionaryColors.textSecondary,
                        ),
                      ),
                      Text(
                        'الوحدة: ${item.unit}',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: RevolutionaryColors.textSecondary,
                        ),
                      ),
                    ],
                  ),
                ),
                // الكمية والسعر
                Expanded(
                  flex: 2,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Text(
                        'الكمية',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: RevolutionaryColors.textSecondary,
                        ),
                      ),
                      Text(
                        invoiceItem.quantity.toStringAsFixed(2),
                        style: const TextStyle(fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'السعر',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: RevolutionaryColors.textSecondary,
                        ),
                      ),
                      Text(
                        '${invoiceItem.unitPrice.toStringAsFixed(2)} ل.س',
                        style: const TextStyle(fontWeight: FontWeight.bold),
                      ),
                    ],
                  ),
                ),
                // الإجمالي والأزرار
                Expanded(
                  flex: 2,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Text(
                        'الإجمالي',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: RevolutionaryColors.textSecondary,
                        ),
                      ),
                      Text(
                        '${invoiceItem.netAmount.toStringAsFixed(2)} ل.س',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: RevolutionaryColors.damascusSky,
                          fontSize: 16,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          IconButton(
                            icon: const Icon(Icons.edit, size: 20),
                            onPressed: () => _editItem(index),
                            tooltip: 'تعديل',
                            color: RevolutionaryColors.damascusSky,
                          ),
                          IconButton(
                            icon: const Icon(Icons.delete, size: 20),
                            onPressed: () => _removeItem(index),
                            tooltip: 'حذف',
                            color: Colors.red,
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
            // عرض تفاصيل الخصم والضريبة إذا وجدت
            if (invoiceItem.discountPercentage > 0 ||
                invoiceItem.taxPercentage > 0)
              Container(
                margin: const EdgeInsets.only(top: 8),
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: RevolutionaryColors.backgroundCard,
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                  children: [
                    if (invoiceItem.discountPercentage > 0)
                      Text(
                        'خصم: ${invoiceItem.discountPercentage.toStringAsFixed(1)}% (${invoiceItem.discountAmount.toStringAsFixed(2)} ل.س)',
                        style: Theme.of(context).textTheme.bodySmall,
                      ),
                    if (invoiceItem.taxPercentage > 0)
                      Text(
                        'ضريبة: ${invoiceItem.taxPercentage.toStringAsFixed(1)}% (${invoiceItem.taxAmount.toStringAsFixed(2)} ل.س)',
                        style: Theme.of(context).textTheme.bodySmall,
                      ),
                  ],
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildTotalsSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('الإجماليات', style: Theme.of(context).textTheme.titleLarge),
            const SizedBox(height: 16),
            _buildTotalRow('المجموع الفرعي:', _subtotal),
            _buildTotalRow('الخصم:', _discountAmount),
            _buildTotalRow('الضريبة:', _taxAmount),
            const Divider(),
            _buildTotalRow('الإجمالي:', _totalAmount, isTotal: true),
          ],
        ),
      ),
    );
  }

  Widget _buildTotalRow(String label, double amount, {bool isTotal = false}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
            ),
          ),
          Text(
            '${amount.toStringAsFixed(2)} ل.س',
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
              color: isTotal ? RevolutionaryColors.damascusSky : null,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNotesSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('ملاحظات', style: Theme.of(context).textTheme.titleLarge),
            const SizedBox(height: 16),
            TextFormField(
              controller: _notesController,
              decoration: const InputDecoration(
                hintText: 'أدخل ملاحظات إضافية...',
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBottomBar() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: RevolutionaryColors.backgroundCard,
        boxShadow: [
          BoxShadow(
            color: RevolutionaryColors.shadowMedium,
            blurRadius: 4,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Row(
        children: [
          Expanded(
            child: OutlinedButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('إلغاء'),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: ElevatedButton(
              onPressed: _saveInvoice,
              child: const Text('حفظ الفاتورة'),
            ),
          ),
        ],
      ),
    );
  }

  String _getScreenTitle() {
    switch (widget.invoiceType) {
      case AppConstants.invoiceTypeSale:
        return 'فاتورة مبيعات جديدة';
      case AppConstants.invoiceTypePurchase:
        return 'فاتورة مشتريات جديدة';
      case AppConstants.invoiceTypeSaleReturn:
        return 'مردود مبيعات جديد';
      case AppConstants.invoiceTypePurchaseReturn:
        return 'مردود مشتريات جديد';
      default:
        return 'فاتورة جديدة';
    }
  }

  Future<void> _selectDate() async {
    final date = await showDatePicker(
      context: context,
      initialDate: _invoiceDate,
      firstDate: DateTime(2020),
      lastDate: DateTime(2030),
    );

    if (date != null) {
      setState(() {
        _invoiceDate = date;
      });
    }
  }

  Future<void> _addItem() async {
    // الحصول على معرفات الأصناف المضافة مسبقاً
    final excludeItemIds = _invoiceItems.map((item) => item.itemId).toList();

    final result = await showDialog<Map<String, dynamic>>(
      context: context,
      builder: (context) => ItemSelectionDialog(
        title: 'إضافة صنف للفاتورة',
        excludeItemIds: excludeItemIds,
        invoiceType: widget.invoiceType,
      ),
    );

    if (result != null) {
      final item = result['item'] as Item;
      final invoiceItem = InvoiceItem(
        invoiceId: 0, // سيتم تحديثه عند الحفظ
        itemId: item.id!,
        quantity: result['quantity'],
        unitPrice: result['unitPrice'],
        totalPrice: result['totalPrice'],
        discountPercentage: result['discountPercentage'],
        discountAmount: result['discountAmount'],
        taxPercentage: result['taxPercentage'],
        taxAmount: result['taxAmount'],
        netAmount: result['netAmount'],
      );

      setState(() {
        _invoiceItems.add(invoiceItem);
        _calculateTotals();
      });

      // عرض رسالة نجاح
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('تم إضافة ${item.name} بنجاح'),
            backgroundColor: RevolutionaryColors.successGlow,
          ),
        );
      }
    }
  }

  Future<void> _editItem(int index) async {
    final currentItem = _invoiceItems[index];
    final item = _items.firstWhere((i) => i.id == currentItem.itemId);

    // إنشاء حوار تعديل مخصص
    final result = await showDialog<Map<String, dynamic>>(
      context: context,
      builder: (context) => _buildEditItemDialog(currentItem, item),
    );

    if (result != null) {
      final updatedItem = InvoiceItem(
        id: currentItem.id,
        invoiceId: currentItem.invoiceId,
        itemId: currentItem.itemId,
        quantity: result['quantity'],
        unitPrice: result['unitPrice'],
        totalPrice: result['totalPrice'],
        discountPercentage: result['discountPercentage'],
        discountAmount: result['discountAmount'],
        taxPercentage: result['taxPercentage'],
        taxAmount: result['taxAmount'],
        netAmount: result['netAmount'],
        createdAt: currentItem.createdAt,
      );

      setState(() {
        _invoiceItems[index] = updatedItem;
        _calculateTotals();
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('تم تحديث ${item.name} بنجاح'),
            backgroundColor: RevolutionaryColors.successGlow,
          ),
        );
      }
    }
  }

  void _removeItem(int index) {
    setState(() {
      _invoiceItems.removeAt(index);
      _calculateTotals();
    });
  }

  void _calculateTotals() {
    _subtotal = _invoiceItems.fold(0.0, (sum, item) => sum + item.totalPrice);
    _taxAmount = _invoiceItems.fold(0.0, (sum, item) => sum + item.taxAmount);
    _discountAmount = _invoiceItems.fold(
      0.0,
      (sum, item) => sum + item.discountAmount,
    );
    _totalAmount = _invoiceItems.fold(0.0, (sum, item) => sum + item.netAmount);
  }

  Future<void> _saveInvoice() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (_invoiceItems.isEmpty) {
      _showErrorSnackBar('يرجى إضافة أصناف للفاتورة');
      return;
    }

    setState(() => _isLoading = true);

    try {
      final invoice = Invoice(
        invoiceNumber: _invoiceNumberController.text,
        invoiceDate: _invoiceDate,
        type: widget.invoiceType,
        customerId: _selectedCustomer?.id,
        supplierId: _selectedSupplier?.id,
        subtotal: _subtotal,
        taxAmount: _taxAmount,
        discountAmount: _discountAmount,
        totalAmount: _totalAmount,
        paidAmount: 0.0,
        remainingAmount: _totalAmount,
        currencyId: 1,
        status: InvoiceStatus.draft,
        notes: _notesController.text.isEmpty ? null : _notesController.text,
        items: _invoiceItems,
      );

      await _invoiceService.insertInvoice(invoice);

      if (mounted) {
        Navigator.pop(context, true);
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم حفظ الفاتورة بنجاح'),
            backgroundColor: RevolutionaryColors.successGlow,
          ),
        );
      }
    } catch (e) {
      setState(() => _isLoading = false);
      _showErrorSnackBar('حدث خطأ في حفظ الفاتورة: $e');
    }
  }

  Widget _buildEditItemDialog(InvoiceItem currentItem, Item item) {
    final quantityController = TextEditingController(
      text: currentItem.quantity.toString(),
    );
    final priceController = TextEditingController(
      text: currentItem.unitPrice.toString(),
    );
    final discountController = TextEditingController(
      text: currentItem.discountPercentage.toString(),
    );
    final taxController = TextEditingController(
      text: currentItem.taxPercentage.toString(),
    );

    return AlertDialog(
      title: Text('تعديل ${item.name}'),
      content: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // معلومات الصنف
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: RevolutionaryColors.backgroundCard,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'الصنف: ${item.name}',
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                  Text('الكود: ${item.code}'),
                  Text('الوحدة: ${item.unit}'),
                  Text('الكمية المتاحة: ${item.quantity.toStringAsFixed(2)}'),
                ],
              ),
            ),
            const SizedBox(height: 16),
            // حقول الإدخال
            TextField(
              controller: quantityController,
              keyboardType: TextInputType.number,
              decoration: InputDecoration(
                labelText: 'الكمية',
                suffixText: item.unit,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
            const SizedBox(height: 8),
            TextField(
              controller: priceController,
              keyboardType: TextInputType.number,
              decoration: InputDecoration(
                labelText: 'سعر الوحدة',
                suffixText: 'ل.س',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Expanded(
                  child: TextField(
                    controller: discountController,
                    keyboardType: TextInputType.number,
                    decoration: InputDecoration(
                      labelText: 'الخصم %',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: TextField(
                    controller: taxController,
                    keyboardType: TextInputType.number,
                    decoration: InputDecoration(
                      labelText: 'الضريبة %',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('إلغاء'),
        ),
        ElevatedButton(
          onPressed: () {
            final quantity = double.tryParse(quantityController.text) ?? 0.0;
            final unitPrice = double.tryParse(priceController.text) ?? 0.0;
            final discountPercentage =
                double.tryParse(discountController.text) ?? 0.0;
            final taxPercentage = double.tryParse(taxController.text) ?? 0.0;

            if (quantity <= 0) {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('يرجى إدخال كمية صحيحة'),
                  backgroundColor: Colors.red,
                ),
              );
              return;
            }

            if (unitPrice < 0) {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('يرجى إدخال سعر صحيح'),
                  backgroundColor: Colors.red,
                ),
              );
              return;
            }

            // التحقق من الكمية المتاحة للمبيعات
            if ((widget.invoiceType == 'sale' ||
                    widget.invoiceType == 'purchase_return') &&
                quantity > item.quantity) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(
                    'الكمية المطلوبة (${quantity.toStringAsFixed(2)}) أكبر من المتاح (${item.quantity.toStringAsFixed(2)})',
                  ),
                  backgroundColor: Colors.red,
                ),
              );
              return;
            }

            // حساب المبالغ
            final totalPrice = quantity * unitPrice;
            final discountAmount = totalPrice * (discountPercentage / 100);
            final subtotal = totalPrice - discountAmount;
            final taxAmount = subtotal * (taxPercentage / 100);
            final netAmount = subtotal + taxAmount;

            Navigator.of(context).pop({
              'quantity': quantity,
              'unitPrice': unitPrice,
              'totalPrice': totalPrice,
              'discountPercentage': discountPercentage,
              'discountAmount': discountAmount,
              'taxPercentage': taxPercentage,
              'taxAmount': taxAmount,
              'netAmount': netAmount,
            });
          },
          style: ElevatedButton.styleFrom(
            backgroundColor: RevolutionaryColors.damascusSky,
            foregroundColor: Colors.white,
          ),
          child: const Text('تحديث'),
        ),
      ],
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message), backgroundColor: RevolutionaryColors.errorCoral),
    );
  }
}
