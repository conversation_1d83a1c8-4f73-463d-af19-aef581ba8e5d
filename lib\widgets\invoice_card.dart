import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../constants/revolutionary_design_colors.dart';
import '../constants/app_constants.dart';
import '../models/invoice.dart';
import '../models/invoice_status.dart';

class InvoiceCard extends StatelessWidget {
  final Invoice invoice;
  final VoidCallback onTap;
  final VoidCallback onEdit;
  final VoidCallback onDelete;
  final bool? isIntegrated; // حالة التكامل (اختيارية)

  const InvoiceCard({
    super.key,
    required this.invoice,
    required this.onTap,
    required this.onEdit,
    required this.onDelete,
    this.isIntegrated,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: InkWell(
        borderRadius: BorderRadius.circular(12),
        onTap: onTap,
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  // أيقونة نوع الفاتورة
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: _getInvoiceTypeColor().withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      _getInvoiceTypeIcon(),
                      color: _getInvoiceTypeColor(),
                      size: 20,
                    ),
                  ),
                  const SizedBox(width: 12),
                  // معلومات الفاتورة
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Expanded(
                              child: Text(
                                'فاتورة رقم: ${invoice.invoiceNumber}',
                                style: Theme.of(context).textTheme.titleMedium
                                    ?.copyWith(
                                      fontWeight: FontWeight.w600,
                                      color: RevolutionaryColors.textPrimary,
                                    ),
                              ),
                            ),
                            // مؤشر التكامل
                            if (isIntegrated != null) ...[
                              Container(
                                padding: const EdgeInsets.all(4),
                                decoration: BoxDecoration(
                                  color: isIntegrated!
                                      ? RevolutionaryColors.successGlow.withValues(alpha: 0.1)
                                      : RevolutionaryColors.warningAmber.withValues(
                                          alpha: 0.1,
                                        ),
                                  borderRadius: BorderRadius.circular(4),
                                ),
                                child: Icon(
                                  isIntegrated!
                                      ? Icons.check_circle_outline
                                      : Icons.warning_amber_outlined,
                                  color: isIntegrated!
                                      ? RevolutionaryColors.successGlow
                                      : RevolutionaryColors.warningAmber,
                                  size: 14,
                                ),
                              ),
                              const SizedBox(width: 8),
                            ],
                            // حالة الفاتورة
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 8,
                                vertical: 2,
                              ),
                              decoration: BoxDecoration(
                                color: _getStatusColor().withValues(alpha: 0.1),
                                borderRadius: BorderRadius.circular(4),
                              ),
                              child: Text(
                                invoice.statusArabic,
                                style: Theme.of(context).textTheme.bodySmall
                                    ?.copyWith(
                                      color: _getStatusColor(),
                                      fontSize: 10,
                                      fontWeight: FontWeight.w600,
                                    ),
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 4),
                        Row(
                          children: [
                            Text(
                              'التاريخ: ${DateFormat('dd/MM/yyyy').format(invoice.invoiceDate)}',
                              style: Theme.of(context).textTheme.bodySmall
                                  ?.copyWith(color: RevolutionaryColors.textSecondary),
                            ),
                            const SizedBox(width: 16),
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 6,
                                vertical: 2,
                              ),
                              decoration: BoxDecoration(
                                color: _getInvoiceTypeColor().withValues(
                                  alpha: 0.1,
                                ),
                                borderRadius: BorderRadius.circular(4),
                              ),
                              child: Text(
                                invoice.typeArabic,
                                style: Theme.of(context).textTheme.bodySmall
                                    ?.copyWith(
                                      color: _getInvoiceTypeColor(),
                                      fontSize: 10,
                                    ),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                  // قائمة الخيارات
                  PopupMenuButton<String>(
                    onSelected: (value) {
                      switch (value) {
                        case 'edit':
                          onEdit();
                          break;
                        case 'delete':
                          onDelete();
                          break;
                      }
                    },
                    itemBuilder: (context) => [
                      const PopupMenuItem(
                        value: 'edit',
                        child: Row(
                          children: [
                            Icon(Icons.edit, size: 18),
                            SizedBox(width: 8),
                            Text('تعديل'),
                          ],
                        ),
                      ),
                      const PopupMenuItem(
                        value: 'delete',
                        child: Row(
                          children: [
                            Icon(Icons.delete, size: 18, color: Colors.red),
                            SizedBox(width: 8),
                            Text('حذف', style: TextStyle(color: Colors.red)),
                          ],
                        ),
                      ),
                    ],
                    child: const Icon(
                      Icons.more_vert,
                      color: RevolutionaryColors.textSecondary,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              // المبالغ
              Row(
                children: [
                  Expanded(
                    child: _buildAmountInfo(
                      'المبلغ الإجمالي',
                      invoice.totalAmount,
                      RevolutionaryColors.damascusSky,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: _buildAmountInfo(
                      'المبلغ المدفوع',
                      invoice.paidAmount,
                      RevolutionaryColors.successGlow,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: _buildAmountInfo(
                      'المتبقي',
                      invoice.remainingAmount,
                      RevolutionaryColors.errorCoral,
                    ),
                  ),
                ],
              ),
              // معلومات إضافية
              if (invoice.items.isNotEmpty || invoice.notes != null) ...[
                const SizedBox(height: 12),
                Row(
                  children: [
                    if (invoice.items.isNotEmpty) ...[
                      Icon(
                        Icons.list,
                        size: 16,
                        color: RevolutionaryColors.textSecondary,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        'عدد الأصناف: ${invoice.items.length}',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: RevolutionaryColors.textSecondary,
                        ),
                      ),
                    ],
                    const Spacer(),
                    // مؤشر حالة الدفع
                    if (invoice.isPaid)
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 6,
                          vertical: 2,
                        ),
                        decoration: BoxDecoration(
                          color: RevolutionaryColors.successGlow.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              Icons.check_circle,
                              size: 12,
                              color: RevolutionaryColors.successGlow,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              'مدفوعة',
                              style: Theme.of(context).textTheme.bodySmall
                                  ?.copyWith(
                                    color: RevolutionaryColors.successGlow,
                                    fontSize: 10,
                                  ),
                            ),
                          ],
                        ),
                      )
                    else if (invoice.isPartiallyPaid)
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 6,
                          vertical: 2,
                        ),
                        decoration: BoxDecoration(
                          color: RevolutionaryColors.warningAmber.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              Icons.schedule,
                              size: 12,
                              color: RevolutionaryColors.warningAmber,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              'دفع جزئي',
                              style: Theme.of(context).textTheme.bodySmall
                                  ?.copyWith(
                                    color: RevolutionaryColors.warningAmber,
                                    fontSize: 10,
                                  ),
                            ),
                          ],
                        ),
                      ),
                  ],
                ),
              ],
              // الملاحظات
              if (invoice.notes != null && invoice.notes!.isNotEmpty) ...[
                const SizedBox(height: 8),
                Text(
                  invoice.notes!,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: RevolutionaryColors.textSecondary,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAmountInfo(String label, double amount, Color color) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(fontSize: 12, color: RevolutionaryColors.textSecondary),
        ),
        Text(
          '${NumberFormat('#,##0.00').format(amount)} ل.س',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: color,
          ),
        ),
      ],
    );
  }

  Color _getInvoiceTypeColor() {
    switch (invoice.type) {
      case AppConstants.invoiceTypeSale:
        return RevolutionaryColors.successGlow;
      case AppConstants.invoiceTypePurchase:
        return RevolutionaryColors.damascusSky;
      case AppConstants.invoiceTypeSaleReturn:
        return RevolutionaryColors.warningAmber;
      case AppConstants.invoiceTypePurchaseReturn:
        return RevolutionaryColors.infoTurquoise;
      default:
        return RevolutionaryColors.textSecondary;
    }
  }

  IconData _getInvoiceTypeIcon() {
    switch (invoice.type) {
      case AppConstants.invoiceTypeSale:
        return Icons.point_of_sale;
      case AppConstants.invoiceTypePurchase:
        return Icons.shopping_cart;
      case AppConstants.invoiceTypeSaleReturn:
        return Icons.keyboard_return;
      case AppConstants.invoiceTypePurchaseReturn:
        return Icons.undo;
      default:
        return Icons.receipt;
    }
  }

  Color _getStatusColor() {
    switch (invoice.status) {
      case InvoiceStatus.draft:
        return RevolutionaryColors.textSecondary;
      case InvoiceStatus.confirmed:
        return RevolutionaryColors.infoTurquoise;
      case InvoiceStatus.fullyPaid:
      case InvoiceStatus.partiallyPaid:
        return RevolutionaryColors.successGlow;
      case InvoiceStatus.overdue:
        return RevolutionaryColors.warningAmber;
      case InvoiceStatus.cancelled:
        return RevolutionaryColors.errorCoral;
    }
  }
}
