/// نموذج نقل المخزون بين المواقع والمستودعات
/// يحتوي على جميع البيانات المتعلقة بعمليات النقل والتتبع
library;

/// حالات طلب النقل
enum TransferStatus {
  /// معلق - في انتظار الموافقة
  pending('pending', 'معلق'),
  
  /// موافق عليه - جاهز للتنفيذ
  approved('approved', 'موافق عليه'),
  
  /// قيد التنفيذ - جاري النقل
  inProgress('in_progress', 'قيد التنفيذ'),
  
  /// مكتمل - تم النقل بنجاح
  completed('completed', 'مكتمل'),
  
  /// ملغي - تم إلغاء النقل
  cancelled('cancelled', 'ملغي');

  const TransferStatus(this.code, this.displayName);

  final String code;
  final String displayName;

  static TransferStatus fromCode(String code) {
    return TransferStatus.values.firstWhere(
      (status) => status.code == code,
      orElse: () => TransferStatus.pending,
    );
  }

  /// الحصول على لون الحالة
  String get colorCode {
    switch (this) {
      case TransferStatus.pending:
        return '#FF9800'; // برتقالي
      case TransferStatus.approved:
        return '#2196F3'; // أزرق
      case TransferStatus.inProgress:
        return '#9C27B0'; // بنفسجي
      case TransferStatus.completed:
        return '#4CAF50'; // أخضر
      case TransferStatus.cancelled:
        return '#F44336'; // أحمر
    }
  }

  /// التحقق من إمكانية التعديل
  bool get canEdit {
    return [TransferStatus.pending].contains(this);
  }

  /// التحقق من إمكانية الموافقة
  bool get canApprove {
    return this == TransferStatus.pending;
  }

  /// التحقق من إمكانية التنفيذ
  bool get canExecute {
    return this == TransferStatus.approved;
  }

  /// التحقق من إمكانية الإلغاء
  bool get canCancel {
    return [TransferStatus.pending, TransferStatus.approved].contains(this);
  }
}

/// نموذج صنف في طلب النقل
class InventoryTransferItem {
  final int? id;
  final int transferId;
  final int itemId;
  final double requestedQuantity;
  final double transferredQuantity;
  final double unitCost;
  final String? batchNumber;
  final DateTime? expiryDate;
  final String? serialNumber;
  final String? notes;
  final DateTime createdAt;

  InventoryTransferItem({
    this.id,
    required this.transferId,
    required this.itemId,
    required this.requestedQuantity,
    this.transferredQuantity = 0.0,
    this.unitCost = 0.0,
    this.batchNumber,
    this.expiryDate,
    this.serialNumber,
    this.notes,
    DateTime? createdAt,
  }) : createdAt = createdAt ?? DateTime.now();

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'transfer_id': transferId,
      'item_id': itemId,
      'requested_quantity': requestedQuantity,
      'transferred_quantity': transferredQuantity,
      'unit_cost': unitCost,
      'batch_number': batchNumber,
      'expiry_date': expiryDate?.toIso8601String(),
      'serial_number': serialNumber,
      'notes': notes,
      'created_at': createdAt.toIso8601String(),
    };
  }

  factory InventoryTransferItem.fromMap(Map<String, dynamic> map) {
    return InventoryTransferItem(
      id: map['id']?.toInt(),
      transferId: map['transfer_id']?.toInt() ?? 0,
      itemId: map['item_id']?.toInt() ?? 0,
      requestedQuantity: map['requested_quantity']?.toDouble() ?? 0.0,
      transferredQuantity: map['transferred_quantity']?.toDouble() ?? 0.0,
      unitCost: map['unit_cost']?.toDouble() ?? 0.0,
      batchNumber: map['batch_number'],
      expiryDate: map['expiry_date'] != null 
          ? DateTime.parse(map['expiry_date']) 
          : null,
      serialNumber: map['serial_number'],
      notes: map['notes'],
      createdAt: DateTime.parse(
        map['created_at'] ?? DateTime.now().toIso8601String(),
      ),
    );
  }

  InventoryTransferItem copyWith({
    int? id,
    int? transferId,
    int? itemId,
    double? requestedQuantity,
    double? transferredQuantity,
    double? unitCost,
    String? batchNumber,
    DateTime? expiryDate,
    String? serialNumber,
    String? notes,
    DateTime? createdAt,
  }) {
    return InventoryTransferItem(
      id: id ?? this.id,
      transferId: transferId ?? this.transferId,
      itemId: itemId ?? this.itemId,
      requestedQuantity: requestedQuantity ?? this.requestedQuantity,
      transferredQuantity: transferredQuantity ?? this.transferredQuantity,
      unitCost: unitCost ?? this.unitCost,
      batchNumber: batchNumber ?? this.batchNumber,
      expiryDate: expiryDate ?? this.expiryDate,
      serialNumber: serialNumber ?? this.serialNumber,
      notes: notes ?? this.notes,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  /// التحقق من اكتمال النقل
  bool get isFullyTransferred {
    return transferredQuantity >= requestedQuantity;
  }

  /// الكمية المتبقية للنقل
  double get remainingQuantity {
    return requestedQuantity - transferredQuantity;
  }

  /// نسبة الإكمال
  double get completionPercentage {
    if (requestedQuantity <= 0) return 0.0;
    return (transferredQuantity / requestedQuantity) * 100;
  }

  /// إجمالي التكلفة المطلوبة
  double get totalRequestedCost {
    return requestedQuantity * unitCost;
  }

  /// إجمالي التكلفة المنقولة
  double get totalTransferredCost {
    return transferredQuantity * unitCost;
  }
}

/// نموذج طلب النقل
class InventoryTransfer {
  final int? id;
  final String transferNumber;
  final int fromWarehouseId;
  final int toWarehouseId;
  final int? fromLocationId;
  final int? toLocationId;
  final TransferStatus status;
  final DateTime transferDate;
  final String? requestedBy;
  final String? approvedBy;
  final String? completedBy;
  final String? notes;
  final int totalItems;
  final double totalQuantity;
  final List<InventoryTransferItem> items;
  final DateTime createdAt;
  final DateTime updatedAt;

  InventoryTransfer({
    this.id,
    required this.transferNumber,
    required this.fromWarehouseId,
    required this.toWarehouseId,
    this.fromLocationId,
    this.toLocationId,
    this.status = TransferStatus.pending,
    DateTime? transferDate,
    this.requestedBy,
    this.approvedBy,
    this.completedBy,
    this.notes,
    int? totalItems,
    double? totalQuantity,
    this.items = const [],
    DateTime? createdAt,
    DateTime? updatedAt,
  }) : transferDate = transferDate ?? DateTime.now(),
       totalItems = totalItems ?? items.length,
       totalQuantity = totalQuantity ?? 
           items.fold(0.0, (sum, item) => sum + item.requestedQuantity),
       createdAt = createdAt ?? DateTime.now(),
       updatedAt = updatedAt ?? DateTime.now();

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'transfer_number': transferNumber,
      'from_warehouse_id': fromWarehouseId,
      'to_warehouse_id': toWarehouseId,
      'from_location_id': fromLocationId,
      'to_location_id': toLocationId,
      'status': status.code,
      'transfer_date': transferDate.toIso8601String().split('T')[0],
      'requested_by': requestedBy,
      'approved_by': approvedBy,
      'completed_by': completedBy,
      'notes': notes,
      'total_items': totalItems,
      'total_quantity': totalQuantity,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  factory InventoryTransfer.fromMap(Map<String, dynamic> map) {
    return InventoryTransfer(
      id: map['id']?.toInt(),
      transferNumber: map['transfer_number'] ?? '',
      fromWarehouseId: map['from_warehouse_id']?.toInt() ?? 0,
      toWarehouseId: map['to_warehouse_id']?.toInt() ?? 0,
      fromLocationId: map['from_location_id']?.toInt(),
      toLocationId: map['to_location_id']?.toInt(),
      status: TransferStatus.fromCode(map['status'] ?? 'pending'),
      transferDate: DateTime.parse(
        map['transfer_date'] ?? DateTime.now().toIso8601String().split('T')[0],
      ),
      requestedBy: map['requested_by'],
      approvedBy: map['approved_by'],
      completedBy: map['completed_by'],
      notes: map['notes'],
      totalItems: map['total_items']?.toInt() ?? 0,
      totalQuantity: map['total_quantity']?.toDouble() ?? 0.0,
      createdAt: DateTime.parse(
        map['created_at'] ?? DateTime.now().toIso8601String(),
      ),
      updatedAt: DateTime.parse(
        map['updated_at'] ?? DateTime.now().toIso8601String(),
      ),
    );
  }

  InventoryTransfer copyWith({
    int? id,
    String? transferNumber,
    int? fromWarehouseId,
    int? toWarehouseId,
    int? fromLocationId,
    int? toLocationId,
    TransferStatus? status,
    DateTime? transferDate,
    String? requestedBy,
    String? approvedBy,
    String? completedBy,
    String? notes,
    int? totalItems,
    double? totalQuantity,
    List<InventoryTransferItem>? items,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return InventoryTransfer(
      id: id ?? this.id,
      transferNumber: transferNumber ?? this.transferNumber,
      fromWarehouseId: fromWarehouseId ?? this.fromWarehouseId,
      toWarehouseId: toWarehouseId ?? this.toWarehouseId,
      fromLocationId: fromLocationId ?? this.fromLocationId,
      toLocationId: toLocationId ?? this.toLocationId,
      status: status ?? this.status,
      transferDate: transferDate ?? this.transferDate,
      requestedBy: requestedBy ?? this.requestedBy,
      approvedBy: approvedBy ?? this.approvedBy,
      completedBy: completedBy ?? this.completedBy,
      notes: notes ?? this.notes,
      totalItems: totalItems ?? this.totalItems,
      totalQuantity: totalQuantity ?? this.totalQuantity,
      items: items ?? this.items,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// التحقق من صحة النقل
  bool get isValid {
    return transferNumber.isNotEmpty &&
           fromWarehouseId > 0 &&
           toWarehouseId > 0 &&
           fromWarehouseId != toWarehouseId &&
           items.isNotEmpty;
  }

  /// التحقق من اكتمال النقل
  bool get isCompleted {
    return status == TransferStatus.completed;
  }

  /// التحقق من إمكانية التعديل
  bool get canEdit {
    return status.canEdit;
  }

  /// نسبة الإكمال الإجمالية
  double get overallCompletionPercentage {
    if (items.isEmpty) return 0.0;
    
    final totalCompletion = items.fold(
      0.0, 
      (sum, item) => sum + item.completionPercentage,
    );
    
    return totalCompletion / items.length;
  }

  /// عدد الأصناف المكتملة
  int get completedItemsCount {
    return items.where((item) => item.isFullyTransferred).length;
  }

  /// إجمالي التكلفة المطلوبة
  double get totalRequestedCost {
    return items.fold(0.0, (sum, item) => sum + item.totalRequestedCost);
  }

  /// إجمالي التكلفة المنقولة
  double get totalTransferredCost {
    return items.fold(0.0, (sum, item) => sum + item.totalTransferredCost);
  }
}
