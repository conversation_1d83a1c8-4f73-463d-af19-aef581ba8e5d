import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../services/optimized_reports_service.dart';
import '../services/report_performance_service.dart';
import '../constants/revolutionary_design_colors.dart';
import '../widgets/dashboard_card.dart';

/// شاشة مراقبة أداء التقارير
class ReportPerformanceScreen extends StatefulWidget {
  const ReportPerformanceScreen({super.key});

  @override
  State<ReportPerformanceScreen> createState() =>
      _ReportPerformanceScreenState();
}

class _ReportPerformanceScreenState extends State<ReportPerformanceScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  final OptimizedReportsService _reportsService = OptimizedReportsService();
  final ReportPerformanceService _performanceService =
      ReportPerformanceService();

  OptimizedReportsStatistics? _statistics;
  List<ReportExecutionRecord> _recentExecutions = [];
  Map<String, int> _failedReports = {};
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _loadData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    setState(() => _isLoading = true);

    try {
      final statistics = _reportsService.getPerformanceStatistics();
      final recentExecutions = _performanceService.getExecutionHistory(
        limit: 50,
      );
      final failedReports = _performanceService.getMostFailedReports();

      setState(() {
        _statistics = statistics;
        _recentExecutions = recentExecutions;
        _failedReports = failedReports;
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('خطأ في تحميل البيانات: $e')));
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('مراقبة أداء التقارير'),
        actions: [
          IconButton(icon: const Icon(Icons.refresh), onPressed: _loadData),
        ],
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'نظرة عامة', icon: Icon(Icons.dashboard)),
            Tab(text: 'التخزين المؤقت', icon: Icon(Icons.storage)),
            Tab(text: 'التنفيذ', icon: Icon(Icons.timeline)),
            Tab(text: 'المشاكل', icon: Icon(Icons.warning)),
          ],
        ),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : TabBarView(
              controller: _tabController,
              children: [
                _buildOverviewTab(),
                _buildCacheTab(),
                _buildExecutionTab(),
                _buildIssuesTab(),
              ],
            ),
    );
  }

  Widget _buildOverviewTab() {
    if (_statistics == null) {
      return const Center(child: Text('لا توجد بيانات'));
    }

    final performance = _statistics!.performanceAnalysis;

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'إحصائيات الأداء العامة',
            style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          GridView.count(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            crossAxisCount: 2,
            crossAxisSpacing: 16,
            mainAxisSpacing: 16,
            childAspectRatio: 1.2,
            children: [
              _buildStatCard(
                'إجمالي التنفيذات',
                '${performance.totalExecutions}',
                Icons.play_arrow,
                RevolutionaryColors.damascusSky,
              ),
              _buildStatCard(
                'معدل النجاح',
                '${(performance.successRate * 100).toStringAsFixed(1)}%',
                Icons.check_circle,
                RevolutionaryColors.successGlow,
              ),
              _buildStatCard(
                'متوسط الوقت',
                '${performance.averageExecutionTimeMs.toStringAsFixed(0)} مللي ثانية',
                Icons.timer,
                RevolutionaryColors.infoTurquoise,
              ),
              _buildStatCard(
                'التقارير البطيئة',
                '${performance.slowReports}',
                Icons.slow_motion_video,
                RevolutionaryColors.warningAmber,
              ),
            ],
          ),
          const SizedBox(height: 24),
          const Text(
            'أداء التقارير حسب النوع',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          ...performance.reportTypeMetrics.entries.map(
            (entry) => _buildReportTypeCard(entry.key, entry.value),
          ),
        ],
      ),
    );
  }

  Widget _buildCacheTab() {
    if (_statistics == null) {
      return const Center(child: Text('لا توجد بيانات'));
    }

    final cache = _statistics!.cacheStatistics;

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'إحصائيات التخزين المؤقت',
            style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          GridView.count(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            crossAxisCount: 2,
            crossAxisSpacing: 16,
            mainAxisSpacing: 16,
            childAspectRatio: 1.2,
            children: [
              _buildStatCard(
                'التقارير المخزنة',
                '${cache.totalCachedReports}',
                Icons.storage,
                RevolutionaryColors.damascusSky,
              ),
              _buildStatCard(
                'معدل الإصابة',
                '${(cache.cacheHitRate * 100).toStringAsFixed(1)}%',
                Icons.track_changes,
                RevolutionaryColors.successGlow,
              ),
              _buildStatCard(
                'استخدام الذاكرة',
                '${(cache.totalMemoryUsage / 1024 / 1024).toStringAsFixed(1)} MB',
                Icons.memory,
                RevolutionaryColors.infoTurquoise,
              ),
              _buildStatCard(
                'متوسط حجم التقرير',
                '${cache.averageReportSize.toStringAsFixed(0)} سجل',
                Icons.description,
                RevolutionaryColors.warningAmber,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildExecutionTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'سجل التنفيذ الأخير',
            style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          ListView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: _recentExecutions.length,
            itemBuilder: (context, index) {
              final execution = _recentExecutions[index];
              return _buildExecutionCard(execution);
            },
          ),
        ],
      ),
    );
  }

  Widget _buildIssuesTab() {
    final slowReports = _performanceService.getSlowestReports(limit: 10);

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'التقارير الأبطأ',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          ...slowReports.map((record) => _buildSlowReportCard(record)),
          const SizedBox(height: 24),
          const Text(
            'التقارير الأكثر فشلاً',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          ..._failedReports.entries.map(
            (entry) => _buildFailedReportCard(entry.key, entry.value),
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return DashboardCard(
      title: title,
      subtitle: value,
      icon: icon,
      color: color,
      onTap: () {},
    );
  }

  Widget _buildReportTypeCard(
    String reportType,
    ReportPerformanceMetrics metrics,
  ) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: RevolutionaryColors.damascusSky.withValues(alpha: 0.1),
          child: Icon(Icons.assessment, color: RevolutionaryColors.damascusSky),
        ),
        title: Text(_getReportTypeName(reportType)),
        subtitle: Text(
          'التنفيذات: ${metrics.totalExecutions} | '
          'النجاح: ${(metrics.successRate * 100).toStringAsFixed(1)}% | '
          'متوسط الوقت: ${metrics.averageExecutionTimeMs.toStringAsFixed(0)} مللي ثانية',
        ),
        trailing: Icon(
          metrics.successRate > 0.9 ? Icons.check_circle : Icons.warning,
          color: metrics.successRate > 0.9
              ? RevolutionaryColors.successGlow
              : RevolutionaryColors.warningAmber,
        ),
      ),
    );
  }

  Widget _buildExecutionCard(ReportExecutionRecord execution) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: execution.success
              ? RevolutionaryColors.successGlow.withValues(alpha: 0.1)
              : RevolutionaryColors.errorCoral.withValues(alpha: 0.1),
          child: Icon(
            execution.success ? Icons.check : Icons.error,
            color: execution.success
                ? RevolutionaryColors.successGlow
                : RevolutionaryColors.errorCoral,
          ),
        ),
        title: Text(_getReportTypeName(execution.reportType)),
        subtitle: Text(
          '${DateFormat('yyyy/MM/dd HH:mm').format(execution.endTime)} | '
          '${execution.executionTime.inMilliseconds} مللي ثانية'
          '${execution.recordCount != null ? ' | ${execution.recordCount} سجل' : ''}',
        ),
        trailing: execution.success
            ? null
            : const Icon(Icons.error_outline, color: RevolutionaryColors.errorCoral),
      ),
    );
  }

  Widget _buildSlowReportCard(ReportExecutionRecord record) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: const CircleAvatar(
          backgroundColor: RevolutionaryColors.warningAmber,
          child: Icon(Icons.slow_motion_video, color: Colors.white),
        ),
        title: Text(_getReportTypeName(record.reportType)),
        subtitle: Text(
          '${DateFormat('yyyy/MM/dd HH:mm').format(record.endTime)} | '
          '${(record.executionTime.inMilliseconds / 1000).toStringAsFixed(1)} ثانية',
        ),
        trailing: Text(
          '${record.recordCount ?? 0} سجل',
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
      ),
    );
  }

  Widget _buildFailedReportCard(String reportType, int failureCount) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: const CircleAvatar(
          backgroundColor: RevolutionaryColors.errorCoral,
          child: Icon(Icons.error, color: Colors.white),
        ),
        title: Text(_getReportTypeName(reportType)),
        subtitle: Text('عدد مرات الفشل: $failureCount'),
        trailing: const Icon(Icons.arrow_forward_ios),
        onTap: () {
          // يمكن إضافة تفاصيل أكثر عن الأخطاء
        },
      ),
    );
  }

  String _getReportTypeName(String reportType) {
    switch (reportType) {
      case 'trial_balance':
        return 'ميزان المراجعة';
      case 'profit_loss':
        return 'قائمة الدخل';
      case 'balance_sheet':
        return 'الميزانية العمومية';
      case 'inventory_report':
        return 'تقرير المخزون';
      case 'customer_aging':
        return 'أعمار الديون';
      case 'sales_analysis':
        return 'تحليل المبيعات';
      case 'purchase_analysis':
        return 'تحليل المشتريات';
      default:
        return reportType;
    }
  }
}
