/// أدوات مساعدة لتوليد الأيقونات
/// تحتوي على الأحجام المطلوبة لكل منصة
library;

import 'package:flutter/material.dart';

class IconGeneratorUtils {
  /// أحجام الأيقونات لنظام Android
  static const Map<String, int> androidIconSizes = {
    'mipmap-mdpi': 48,
    'mipmap-hdpi': 72,
    'mipmap-xhdpi': 96,
    'mipmap-xxhdpi': 144,
    'mipmap-xxxhdpi': 192,
  };

  /// أحجام الأيقونات لنظام iOS
  static const Map<String, int> iosIconSizes = {
    'Icon-App-20x20@1x': 20,
    'Icon-App-20x20@2x': 40,
    'Icon-App-20x20@3x': 60,
    'Icon-App-29x29@1x': 29,
    'Icon-App-29x29@2x': 58,
    'Icon-App-29x29@3x': 87,
    'Icon-App-40x40@1x': 40,
    'Icon-App-40x40@2x': 80,
    'Icon-App-40x40@3x': 120,
    'Icon-App-60x60@2x': 120,
    'Icon-App-60x60@3x': 180,
    'Icon-App-76x76@1x': 76,
    'Icon-App-76x76@2x': 152,
    'Icon-App-83.5x83.5@2x': 167,
    'Icon-App-1024x1024@1x': 1024,
  };

  /// أحجام الأيقونات للويب
  static const Map<String, int> webIconSizes = {
    'favicon-16x16': 16,
    'favicon-32x32': 32,
    'apple-touch-icon': 180,
    'android-chrome-192x192': 192,
    'android-chrome-512x512': 512,
  };

  /// أحجام الأيقونات لسطح المكتب
  static const Map<String, int> desktopIconSizes = {
    'icon-16': 16,
    'icon-24': 24,
    'icon-32': 32,
    'icon-48': 48,
    'icon-64': 64,
    'icon-96': 96,
    'icon-128': 128,
    'icon-256': 256,
    'icon-512': 512,
    'icon-1024': 1024,
  };

  /// الحصول على جميع الأحجام المطلوبة
  static Map<String, int> getAllRequiredSizes() {
    final allSizes = <String, int>{};
    
    allSizes.addAll(androidIconSizes);
    allSizes.addAll(iosIconSizes);
    allSizes.addAll(webIconSizes);
    allSizes.addAll(desktopIconSizes);
    
    return allSizes;
  }

  /// الحصول على الأحجام الأساسية
  static List<int> getBasicSizes() {
    return [16, 24, 32, 48, 64, 96, 128, 256, 512, 1024];
  }

  /// الحصول على معلومات الاستخدام لكل حجم
  static String getUsageInfo(int size) {
    switch (size) {
      case 16:
        return 'أيقونة صغيرة - شريط المهام، قوائم السياق';
      case 24:
        return 'أيقونة متوسطة - أشرطة الأدوات، القوائم';
      case 32:
        return 'أيقونة قياسية - سطح المكتب، المجلدات';
      case 48:
        return 'أيقونة كبيرة - قوائم التطبيقات، الإعدادات';
      case 64:
        return 'أيقونة كبيرة - عرض مفصل، معاينات';
      case 96:
        return 'أيقونة عالية الدقة - شاشات عالية الكثافة';
      case 128:
        return 'أيقونة كبيرة جداً - معاينات، عروض';
      case 256:
        return 'أيقونة عالية الجودة - معاينات كبيرة';
      case 512:
        return 'أيقونة فائقة الجودة - متاجر التطبيقات';
      case 1024:
        return 'أيقونة احترافية - App Store، تسويق';
      default:
        return 'استخدام خاص';
    }
  }

  /// الحصول على اللون المناسب للخلفية حسب الحجم
  static Color getBackgroundColor(int size) {
    if (size <= 32) {
      return Colors.transparent; // للأحجام الصغيرة
    } else if (size <= 128) {
      return const Color(0xFF2D5A87); // لون متوسط
    } else {
      return const Color(0xFF1A365D); // لون داكن للأحجام الكبيرة
    }
  }

  /// تحديد ما إذا كان يجب إظهار النص حسب الحجم
  static bool shouldShowText(int size) {
    return size >= 128; // إظهار النص فقط للأحجام الكبيرة
  }

  /// تحديد ما إذا كان يجب إظهار التفاصيل الزخرفية
  static bool shouldShowDecorations(int size) {
    return size >= 64; // إظهار الزخارف للأحجام المتوسطة والكبيرة
  }

  /// الحصول على سماكة الحدود المناسبة
  static double getBorderWidth(int size) {
    if (size <= 32) {
      return 1.0;
    } else if (size <= 128) {
      return 2.0;
    } else {
      return size * 0.01; // نسبة من الحجم
    }
  }

  /// الحصول على نصف قطر الزوايا المناسب
  static double getBorderRadius(int size) {
    return size * 0.1; // 10% من الحجم
  }

  /// تحديد حجم الخط المناسب للنص
  static double getFontSize(int size, {bool isTitle = true}) {
    if (isTitle) {
      return size * 0.08; // 8% من الحجم للعنوان
    } else {
      return size * 0.05; // 5% من الحجم للنص الفرعي
    }
  }

  /// الحصول على حجم الرمز الرئيسي
  static double getSymbolSize(int size) {
    return size * 0.4; // 40% من الحجم الكلي
  }

  /// الحصول على حجم العناصر الزخرفية
  static double getDecorationSize(int size) {
    return size * 0.03; // 3% من الحجم الكلي
  }

  /// تحديد جودة الرسم حسب الحجم
  static double getQualityMultiplier(int size) {
    if (size <= 64) {
      return 1.0; // جودة عادية للأحجام الصغيرة
    } else if (size <= 256) {
      return 1.5; // جودة محسنة للأحجام المتوسطة
    } else {
      return 2.0; // جودة عالية للأحجام الكبيرة
    }
  }

  /// الحصول على معلومات المنصة حسب الحجم
  static String getPlatformInfo(int size) {
    if (androidIconSizes.containsValue(size)) {
      return 'Android';
    } else if (iosIconSizes.containsValue(size)) {
      return 'iOS';
    } else if (webIconSizes.containsValue(size)) {
      return 'Web';
    } else if (desktopIconSizes.containsValue(size)) {
      return 'Desktop';
    } else {
      return 'عام';
    }
  }

  /// إنشاء قائمة بجميع الأحجام مع معلوماتها
  static List<Map<String, dynamic>> getIconSizesList() {
    final basicSizes = getBasicSizes();
    
    return basicSizes.map((size) {
      return {
        'size': size,
        'usage': getUsageInfo(size),
        'platform': getPlatformInfo(size),
        'showText': shouldShowText(size),
        'showDecorations': shouldShowDecorations(size),
        'borderWidth': getBorderWidth(size),
        'borderRadius': getBorderRadius(size),
        'fontSize': getFontSize(size),
        'symbolSize': getSymbolSize(size),
        'quality': getQualityMultiplier(size),
      };
    }).toList();
  }
}
