@echo off
echo ========================================
echo    Smart Ledger - Production Build
echo ========================================
echo.

REM Check if Flutter is installed
flutter --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Error: Flutter is not installed or not in PATH
    pause
    exit /b 1
)

REM Check if pubspec.yaml exists
if not exist "pubspec.yaml" (
    echo Error: This script must be run from the project root directory
    pause
    exit /b 1
)

echo [1/6] Cleaning project...
flutter clean
if %errorlevel% neq 0 (
    echo Error cleaning project
    pause
    exit /b 1
)

echo [2/6] Getting dependencies...
flutter pub get
if %errorlevel% neq 0 (
    echo Error getting dependencies
    pause
    exit /b 1
)

echo [3/6] Running tests...
flutter test
if %errorlevel% neq 0 (
    echo Warning: Some tests failed
    set /p continue="Do you want to continue? (y/n): "
    if /i not "%continue%"=="y" (
        echo Build cancelled
        pause
        exit /b 1
    )
)

REM Create build directory
if not exist "build\production" mkdir "build\production"

echo [4/6] Building Windows app...
flutter build windows --release
if %errorlevel% neq 0 (
    echo Error building Windows app
    pause
    exit /b 1
)

REM Copy Windows files
echo Copying Windows files...
xcopy "build\windows\x64\runner\Release\*" "build\production\windows\" /E /I /Y
if %errorlevel% neq 0 (
    echo Error copying Windows files
    pause
    exit /b 1
)

echo [5/6] Building Android APK...
flutter build apk --release --split-per-abi
if %errorlevel% neq 0 (
    echo Error building Android APK
    pause
    exit /b 1
)

REM Copy Android files
echo Copying Android files...
if not exist "build\production\android" mkdir "build\production\android"
copy "build\app\outputs\flutter-apk\*.apk" "build\production\android\"
if %errorlevel% neq 0 (
    echo Error copying Android files
    pause
    exit /b 1
)

echo [6/6] Building Android App Bundle...
flutter build appbundle --release
if %errorlevel% neq 0 (
    echo Error building Android App Bundle
    pause
    exit /b 1
)

REM Copy App Bundle
copy "build\app\outputs\bundle\release\*.aab" "build\production\android\"
if %errorlevel% neq 0 (
    echo Error copying App Bundle
    pause
    exit /b 1
)

echo.
echo ========================================
echo       Build completed successfully!
echo ========================================
echo.
echo Distribution files are available in:
echo - build\production\
echo.
echo Available files:
echo - windows\ (Windows application)
echo - android\*.apk (Android applications)
echo - android\*.aab (Android App Bundle)
echo.
echo You can now distribute these files to users.
echo.
pause
