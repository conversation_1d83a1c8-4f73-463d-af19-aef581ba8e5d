/// مركز الإشعارات والتنبيهات
/// يدير جميع الإشعارات والتنبيهات الذكية في النظام
library;

import 'package:flutter/material.dart';
import '../models/smart_notification.dart';
import '../constants/revolutionary_design_colors.dart';
import '../widgets/loading_widget.dart';
import '../responsive/responsive.dart';

class NotificationsCenterScreen extends StatefulWidget {
  const NotificationsCenterScreen({super.key});

  @override
  State<NotificationsCenterScreen> createState() =>
      _NotificationsCenterScreenState();
}

class _NotificationsCenterScreenState extends State<NotificationsCenterScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  List<SmartNotification> _allNotifications = [];
  List<SmartNotification> _unreadNotifications = [];
  bool _isLoading = true;
  String _selectedFilter = 'all';

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadNotifications();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadNotifications() async {
    setState(() => _isLoading = true);
    try {
      // محاكاة تحميل الإشعارات
      await Future.delayed(const Duration(seconds: 1));
      final notifications = <SmartNotification>[
        SmartNotification(
          id: 1,
          title: 'دفعة مستحقة',
          message: 'دفعة بقيمة 50,000 ل.س مستحقة للعميل أحمد محمد',
          type: 'payment',
          isRead: false,
          createdAt: DateTime.now().subtract(const Duration(hours: 2)),
          priority: 'high',
        ),
        SmartNotification(
          id: 2,
          title: 'مخزون منخفض',
          message: 'المخزون المتاح للصنف "قلم أزرق" هو 5 قطع',
          type: 'inventory',
          isRead: false,
          createdAt: DateTime.now().subtract(const Duration(hours: 4)),
          priority: 'medium',
        ),
        SmartNotification(
          id: 3,
          title: 'نسخة احتياطية مكتملة',
          message: 'تم إنشاء النسخة الاحتياطية بنجاح',
          type: 'backup',
          isRead: true,
          createdAt: DateTime.now().subtract(const Duration(days: 1)),
          priority: 'low',
        ),
      ];

      setState(() {
        _allNotifications = notifications;
        _unreadNotifications = notifications.where((n) => !n.isRead).toList();
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      _showErrorSnackBar('خطأ في تحميل الإشعارات: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const ResponsiveText.h2('مركز الإشعارات'),
        backgroundColor: RevolutionaryColors.damascusSky,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          // فلتر الإشعارات
          PopupMenuButton<String>(
            icon: const Icon(Icons.filter_list),
            onSelected: (value) => setState(() => _selectedFilter = value),
            itemBuilder: (context) => [
              const PopupMenuItem(value: 'all', child: Text('جميع الإشعارات')),
              const PopupMenuItem(value: 'payment', child: Text('الدفعات')),
              const PopupMenuItem(value: 'inventory', child: Text('المخزون')),
              const PopupMenuItem(
                value: 'backup',
                child: Text('النسخ الاحتياطية'),
              ),
              const PopupMenuItem(value: 'system', child: Text('النظام')),
            ],
          ),
          // تحديد الكل كمقروء
          IconButton(
            onPressed: _markAllAsRead,
            icon: const Icon(Icons.done_all),
            tooltip: 'تحديد الكل كمقروء',
          ),
          // حذف جميع الإشعارات المقروءة
          IconButton(
            onPressed: _clearReadNotifications,
            icon: const Icon(Icons.clear_all),
            tooltip: 'حذف المقروءة',
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          tabs: [
            Tab(
              icon: Badge(
                label: Text(_allNotifications.length.toString()),
                child: const Icon(Icons.notifications),
              ),
              text: 'الكل',
            ),
            Tab(
              icon: Badge(
                label: Text(_unreadNotifications.length.toString()),
                child: const Icon(Icons.notifications_active),
              ),
              text: 'غير مقروءة',
            ),
            const Tab(icon: Icon(Icons.settings), text: 'الإعدادات'),
          ],
        ),
      ),
      body: _isLoading
          ? const LoadingWidget()
          : TabBarView(
              controller: _tabController,
              children: [
                _buildNotificationsList(_getFilteredNotifications()),
                _buildNotificationsList(_unreadNotifications),
                _buildSettingsTab(),
              ],
            ),
    );
  }

  Widget _buildNotificationsList(List<SmartNotification> notifications) {
    final filteredNotifications = _filterNotifications(notifications);

    if (filteredNotifications.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.notifications_none, size: 64, color: Colors.grey[400]),
            const SizedBox(height: 16),
            Text(
              'لا توجد إشعارات',
              style: TextStyle(
                fontSize: 18,
                color: Colors.grey[600],
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'ستظهر الإشعارات هنا عند توفرها',
              style: TextStyle(color: Colors.grey[500]),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _loadNotifications,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: filteredNotifications.length,
        itemBuilder: (context, index) {
          final notification = filteredNotifications[index];
          return _buildNotificationCard(notification);
        },
      ),
    );
  }

  Widget _buildNotificationCard(SmartNotification notification) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: notification.isRead ? 1 : 3,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        borderRadius: BorderRadius.circular(12),
        onTap: () => _markAsRead(notification),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            border: notification.isRead
                ? null
                : Border.all(
                    color: _getNotificationColor(
                      notification.type,
                    ).withValues(alpha: 0.3),
                    width: 2,
                  ),
          ),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // أيقونة الإشعار
              Container(
                width: 48,
                height: 48,
                decoration: BoxDecoration(
                  color: _getNotificationColor(
                    notification.type,
                  ).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(24),
                ),
                child: Icon(
                  _getNotificationIcon(notification.type),
                  color: _getNotificationColor(notification.type),
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),

              // محتوى الإشعار
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // العنوان
                    Text(
                      notification.title,
                      style: TextStyle(
                        fontWeight: notification.isRead
                            ? FontWeight.w500
                            : FontWeight.bold,
                        fontSize: 16,
                        color: notification.isRead
                            ? Colors.grey[700]
                            : Colors.black87,
                      ),
                    ),
                    const SizedBox(height: 4),

                    // الرسالة
                    Text(
                      notification.message,
                      style: TextStyle(
                        color: notification.isRead
                            ? Colors.grey[600]
                            : Colors.grey[800],
                        fontSize: 14,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 8),

                    // التاريخ والوقت
                    Row(
                      children: [
                        Icon(
                          Icons.access_time,
                          size: 14,
                          color: Colors.grey[500],
                        ),
                        const SizedBox(width: 4),
                        Text(
                          _formatDateTime(notification.createdAt),
                          style: TextStyle(
                            color: Colors.grey[500],
                            fontSize: 12,
                          ),
                        ),
                        const Spacer(),

                        // نوع الإشعار
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 2,
                          ),
                          decoration: BoxDecoration(
                            color: _getNotificationColor(
                              notification.type,
                            ).withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Text(
                            _getNotificationTypeText(notification.type),
                            style: TextStyle(
                              color: _getNotificationColor(notification.type),
                              fontSize: 10,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),

              // قائمة الإجراءات
              PopupMenuButton<String>(
                onSelected: (value) =>
                    _handleNotificationAction(value, notification),
                itemBuilder: (context) => [
                  if (!notification.isRead)
                    const PopupMenuItem(
                      value: 'mark_read',
                      child: ListTile(
                        leading: Icon(Icons.done),
                        title: Text('تحديد كمقروء'),
                      ),
                    ),
                  if (notification.isRead)
                    const PopupMenuItem(
                      value: 'mark_unread',
                      child: ListTile(
                        leading: Icon(Icons.mark_email_unread),
                        title: Text('تحديد كغير مقروء'),
                      ),
                    ),
                  const PopupMenuItem(
                    value: 'delete',
                    child: ListTile(
                      leading: Icon(Icons.delete, color: Colors.red),
                      title: Text('حذف', style: TextStyle(color: Colors.red)),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSettingsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'إعدادات الإشعارات',
            style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),

          // إشعارات الدفعات
          _buildNotificationSetting(
            'إشعارات الدفعات المستحقة',
            'تنبيهات عند اقتراب موعد استحقاق الدفعات',
            Icons.payment,
            true,
          ),

          // إشعارات المخزون
          _buildNotificationSetting(
            'إشعارات المخزون المنخفض',
            'تنبيهات عند انخفاض مستوى المخزون',
            Icons.inventory,
            true,
          ),

          // إشعارات النسخ الاحتياطية
          _buildNotificationSetting(
            'إشعارات النسخ الاحتياطية',
            'تنبيهات حول حالة النسخ الاحتياطية',
            Icons.backup,
            true,
          ),

          // إشعارات النظام
          _buildNotificationSetting(
            'إشعارات النظام',
            'تنبيهات حول تحديثات وصيانة النظام',
            Icons.system_update,
            true,
          ),

          const SizedBox(height: 24),

          // إعدادات متقدمة
          const Text(
            'إعدادات متقدمة',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),

          Card(
            child: Column(
              children: [
                ListTile(
                  leading: const Icon(Icons.schedule),
                  title: const Text('ساعات العمل للإشعارات'),
                  subtitle: const Text('من 8:00 ص إلى 6:00 م'),
                  trailing: const Icon(Icons.arrow_forward_ios),
                  onTap: () {
                    // إعداد ساعات العمل - سيتم تطويره في الإصدار القادم
                  },
                ),
                const Divider(height: 1),
                ListTile(
                  leading: const Icon(Icons.volume_up),
                  title: const Text('صوت الإشعارات'),
                  subtitle: const Text('تشغيل صوت عند وصول إشعار جديد'),
                  trailing: Switch(
                    value: true,
                    onChanged: (value) {
                      // تفعيل/إلغاء الصوت - سيتم تطويره في الإصدار القادم
                    },
                    activeColor: RevolutionaryColors.damascusSky,
                  ),
                ),
                const Divider(height: 1),
                ListTile(
                  leading: const Icon(Icons.auto_delete),
                  title: const Text('حذف تلقائي للإشعارات القديمة'),
                  subtitle: const Text('حذف الإشعارات أقدم من 30 يوم'),
                  trailing: Switch(
                    value: true,
                    onChanged: (value) {
                      // الحذف التلقائي - سيتم تطويره في الإصدار القادم
                    },
                    activeColor: RevolutionaryColors.damascusSky,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNotificationSetting(
    String title,
    String subtitle,
    IconData icon,
    bool enabled,
  ) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: SwitchListTile(
        secondary: Icon(icon, color: RevolutionaryColors.damascusSky),
        title: Text(title),
        subtitle: Text(subtitle),
        value: enabled,
        onChanged: (value) {
          // تفعيل/إلغاء نوع الإشعار - سيتم تطويره في الإصدار القادم
        },
        activeColor: RevolutionaryColors.damascusSky,
      ),
    );
  }

  List<SmartNotification> _filterNotifications(
    List<SmartNotification> notifications,
  ) {
    if (_selectedFilter == 'all') return notifications;
    return notifications.where((n) => n.type == _selectedFilter).toList();
  }

  List<SmartNotification> _getFilteredNotifications() {
    if (_selectedFilter == 'all') return _allNotifications;
    return _allNotifications.where((n) => n.type == _selectedFilter).toList();
  }

  Color _getNotificationColor(String type) {
    switch (type) {
      case 'payment':
        return Colors.orange;
      case 'inventory':
        return Colors.red;
      case 'backup':
        return Colors.green;
      case 'system':
        return RevolutionaryColors.damascusSky;
      default:
        return Colors.grey;
    }
  }

  IconData _getNotificationIcon(String type) {
    switch (type) {
      case 'payment':
        return Icons.payment;
      case 'inventory':
        return Icons.inventory;
      case 'backup':
        return Icons.backup;
      case 'system':
        return Icons.system_update;
      default:
        return Icons.notifications;
    }
  }

  String _getNotificationTypeText(String type) {
    switch (type) {
      case 'payment':
        return 'دفعات';
      case 'inventory':
        return 'مخزون';
      case 'backup':
        return 'نسخ احتياطية';
      case 'system':
        return 'نظام';
      default:
        return 'عام';
    }
  }

  void _handleNotificationAction(
    String action,
    SmartNotification notification,
  ) {
    switch (action) {
      case 'mark_read':
        _markAsRead(notification);
        break;
      case 'mark_unread':
        _markAsUnread(notification);
        break;
      case 'delete':
        _deleteNotification(notification);
        break;
    }
  }

  Future<void> _markAsRead(SmartNotification notification) async {
    try {
      // محاكاة تحديث الإشعار
      await Future.delayed(const Duration(milliseconds: 500));
      await _loadNotifications();
    } catch (e) {
      _showErrorSnackBar('خطأ في تحديث الإشعار: $e');
    }
  }

  Future<void> _markAsUnread(SmartNotification notification) async {
    try {
      // محاكاة تحديث الإشعار
      await Future.delayed(const Duration(milliseconds: 500));
      await _loadNotifications();
    } catch (e) {
      _showErrorSnackBar('خطأ في تحديث الإشعار: $e');
    }
  }

  Future<void> _deleteNotification(SmartNotification notification) async {
    try {
      // محاكاة حذف الإشعار
      await Future.delayed(const Duration(milliseconds: 500));
      await _loadNotifications();
      _showSuccessSnackBar('تم حذف الإشعار');
    } catch (e) {
      _showErrorSnackBar('خطأ في حذف الإشعار: $e');
    }
  }

  Future<void> _markAllAsRead() async {
    try {
      // محاكاة تحديث جميع الإشعارات
      await Future.delayed(const Duration(seconds: 1));
      await _loadNotifications();
      _showSuccessSnackBar('تم تحديد جميع الإشعارات كمقروءة');
    } catch (e) {
      _showErrorSnackBar('خطأ في تحديث الإشعارات: $e');
    }
  }

  Future<void> _clearReadNotifications() async {
    try {
      // محاكاة حذف الإشعارات المقروءة
      await Future.delayed(const Duration(seconds: 1));
      await _loadNotifications();
      _showSuccessSnackBar('تم حذف الإشعارات المقروءة');
    } catch (e) {
      _showErrorSnackBar('خطأ في حذف الإشعارات: $e');
    }
  }

  String _formatDateTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inMinutes < 1) {
      return 'الآن';
    } else if (difference.inHours < 1) {
      return 'منذ ${difference.inMinutes} دقيقة';
    } else if (difference.inDays < 1) {
      return 'منذ ${difference.inHours} ساعة';
    } else if (difference.inDays < 7) {
      return 'منذ ${difference.inDays} يوم';
    } else {
      return '${dateTime.day}/${dateTime.month}/${dateTime.year}';
    }
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: RevolutionaryColors.successGlow,
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message), backgroundColor: Colors.red),
    );
  }
}
