/// شاشة إضافة جلسة جرد جديدة
/// تسمح بإنشاء جلسة جرد مع تحديد النوع والمخزن والإعدادات
library;

import 'package:flutter/material.dart';
import '../models/inventory_count.dart';
import '../models/warehouse.dart';
import '../models/warehouse_location.dart';
import '../services/inventory_count_service.dart';
import '../services/warehouse_service.dart';
import '../constants/revolutionary_design_colors.dart';
import '../widgets/loading_widget.dart';

class AddInventoryCountScreen extends StatefulWidget {
  const AddInventoryCountScreen({super.key});

  @override
  State<AddInventoryCountScreen> createState() =>
      _AddInventoryCountScreenState();
}

class _AddInventoryCountScreenState extends State<AddInventoryCountScreen> {
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  final _descriptionController = TextEditingController();

  final InventoryCountService _countService = InventoryCountService();
  final WarehouseService _warehouseService = WarehouseService();

  bool _isLoading = false;
  bool _isSaving = false;

  InventoryCountType _selectedType = InventoryCountType.partial;
  DateTime _scheduledDate = DateTime.now();
  int? _selectedWarehouseId;
  int? _selectedLocationId;

  List<Warehouse> _warehouses = [];
  List<WarehouseLocation> _locations = [];

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    setState(() => _isLoading = true);
    try {
      final warehouses = await _warehouseService.getAllWarehouses();
      setState(() {
        _warehouses = warehouses;
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      _showErrorSnackBar('خطأ في تحميل البيانات: $e');
    }
  }

  Future<void> _loadLocations(int warehouseId) async {
    try {
      final locations = await _warehouseService.getWarehouseLocations(
        warehouseId,
      );
      setState(() {
        _locations = locations;
        _selectedLocationId = null;
      });
    } catch (e) {
      _showErrorSnackBar('خطأ في تحميل المواقع: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إضافة جلسة جرد جديدة'),
        backgroundColor: RevolutionaryColors.damascusSky,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: _isLoading
          ? const LoadingWidget()
          : Form(
              key: _formKey,
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildBasicInfoSection(),
                    const SizedBox(height: 24),
                    _buildTypeSection(),
                    const SizedBox(height: 24),
                    _buildLocationSection(),
                    const SizedBox(height: 24),
                    _buildScheduleSection(),
                    const SizedBox(height: 32),
                    _buildActionButtons(),
                  ],
                ),
              ),
            ),
    );
  }

  Widget _buildBasicInfoSection() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'المعلومات الأساسية',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _titleController,
              decoration: const InputDecoration(
                labelText: 'عنوان الجرد *',
                hintText: 'أدخل عنوان وصفي للجرد',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.title),
              ),
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'يرجى إدخال عنوان الجرد';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _descriptionController,
              decoration: const InputDecoration(
                labelText: 'الوصف',
                hintText: 'وصف اختياري للجرد',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.description),
              ),
              maxLines: 3,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTypeSection() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'نوع الجرد',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            ...InventoryCountType.values.map(
              (type) => RadioListTile<InventoryCountType>(
                title: Text(type.displayName),
                subtitle: Text(_getTypeDescription(type)),
                value: type,
                groupValue: _selectedType,
                onChanged: (value) {
                  setState(() => _selectedType = value!);
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLocationSection() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'الموقع',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            DropdownButtonFormField<int>(
              decoration: const InputDecoration(
                labelText: 'المخزن',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.warehouse),
              ),
              value: _selectedWarehouseId,
              items: _warehouses
                  .map(
                    (warehouse) => DropdownMenuItem(
                      value: warehouse.id,
                      child: Text(warehouse.name),
                    ),
                  )
                  .toList(),
              onChanged: (value) {
                setState(() {
                  _selectedWarehouseId = value;
                  _selectedLocationId = null;
                  _locations.clear();
                });
                if (value != null) {
                  _loadLocations(value);
                }
              },
              validator: _selectedType == InventoryCountType.spot
                  ? (value) => value == null ? 'يرجى اختيار المخزن' : null
                  : null,
            ),
            if (_selectedType == InventoryCountType.spot) ...[
              const SizedBox(height: 16),
              DropdownButtonFormField<int>(
                decoration: const InputDecoration(
                  labelText: 'الموقع المحدد',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.location_on),
                ),
                value: _selectedLocationId,
                items: _locations
                    .map(
                      (location) => DropdownMenuItem(
                        value: location.id,
                        child: Text(location.displayName),
                      ),
                    )
                    .toList(),
                onChanged: (value) {
                  setState(() => _selectedLocationId = value);
                },
                validator: (value) =>
                    value == null ? 'يرجى اختيار الموقع' : null,
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildScheduleSection() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'الجدولة',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            ListTile(
              leading: const Icon(Icons.schedule),
              title: const Text('تاريخ الجرد المجدول'),
              subtitle: Text(_formatDate(_scheduledDate)),
              trailing: const Icon(Icons.calendar_today),
              onTap: _selectDate,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
                side: BorderSide(color: Colors.grey.shade300),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButtons() {
    return Row(
      children: [
        Expanded(
          child: OutlinedButton(
            onPressed: _isSaving ? null : () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: ElevatedButton(
            onPressed: _isSaving ? null : _saveCount,
            style: ElevatedButton.styleFrom(
              backgroundColor: RevolutionaryColors.damascusSky,
              foregroundColor: Colors.white,
            ),
            child: _isSaving
                ? const SizedBox(
                    height: 20,
                    width: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                : const Text('حفظ'),
          ),
        ),
      ],
    );
  }

  String _getTypeDescription(InventoryCountType type) {
    switch (type) {
      case InventoryCountType.full:
        return 'جرد شامل لجميع الأصناف في المخزن';
      case InventoryCountType.partial:
        return 'جرد جزئي لأصناف محددة';
      case InventoryCountType.cycle:
        return 'جرد دوري منتظم';
      case InventoryCountType.spot:
        return 'جرد فوري لموقع محدد';
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  Future<void> _selectDate() async {
    final picked = await showDatePicker(
      context: context,
      initialDate: _scheduledDate,
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );
    if (picked != null) {
      setState(() => _scheduledDate = picked);
    }
  }

  Future<void> _saveCount() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isSaving = true);
    try {
      final countNumber = await _countService.generateCountNumber();

      final count = InventoryCount(
        countNumber: countNumber,
        title: _titleController.text.trim(),
        description: _descriptionController.text.trim().isEmpty
            ? null
            : _descriptionController.text.trim(),
        countType: _selectedType,
        warehouseId: _selectedWarehouseId,
        locationId: _selectedLocationId,
        scheduledDate: _scheduledDate,
      );

      await _countService.createInventoryCount(count);

      if (mounted) {
        Navigator.pop(context, true);
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم إنشاء جلسة الجرد بنجاح'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      setState(() => _isSaving = false);
      _showErrorSnackBar('خطأ في حفظ الجرد: $e');
    }
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message), backgroundColor: Colors.red),
    );
  }
}
