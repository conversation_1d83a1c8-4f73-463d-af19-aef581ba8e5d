/// شاشة تفاصيل عرض السعر
/// تعرض تفاصيل عرض السعر مع إمكانية التعديل والتحويل إلى فاتورة
library;

import 'package:flutter/material.dart';
import '../models/quotation.dart';
import '../services/quotation_service.dart';
import '../services/logging_service.dart';
import '../constants/revolutionary_design_colors.dart';
import '../constants/app_constants.dart';
import '../widgets/loading_widget.dart';
import 'add_quotation_screen.dart';

class QuotationDetailsScreen extends StatefulWidget {
  final Quotation quotation;

  const QuotationDetailsScreen({super.key, required this.quotation});

  @override
  State<QuotationDetailsScreen> createState() => _QuotationDetailsScreenState();
}

class _QuotationDetailsScreenState extends State<QuotationDetailsScreen> {
  final QuotationService _quotationService = QuotationService();
  late Quotation _quotation;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _quotation = widget.quotation;
  }

  Future<void> _refreshQuotation() async {
    setState(() => _isLoading = true);

    try {
      final updatedQuotation = await _quotationService.getQuotationById(
        _quotation.id!,
      );
      if (updatedQuotation != null) {
        setState(() => _quotation = updatedQuotation);
      }
    } catch (e) {
      _showErrorSnackBar('خطأ في تحديث البيانات: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _convertToInvoice() async {
    if (!_quotation.canBeConverted) {
      _showErrorSnackBar(
        'لا يمكن تحويل عرض السعر. يجب أن يكون مقبولاً وغير محول مسبقاً',
      );
      return;
    }

    final confirmed = await _showConfirmDialog(
      'تحويل إلى فاتورة',
      'هل تريد تحويل عرض السعر ${_quotation.quotationNumber} إلى فاتورة؟',
    );

    if (!confirmed) return;

    setState(() => _isLoading = true);

    try {
      final invoiceId = await _quotationService.convertQuotationToInvoice(
        _quotation.id!,
      );

      _showSuccessSnackBar('تم تحويل عرض السعر إلى فاتورة بنجاح');
      await _refreshQuotation();

      LoggingService.info(
        'تم تحويل عرض السعر إلى فاتورة',
        category: 'QuotationDetailsScreen',
        data: {
          'quotation_id': _quotation.id,
          'quotation_number': _quotation.quotationNumber,
          'invoice_id': invoiceId,
        },
      );
    } catch (e) {
      _showErrorSnackBar('خطأ في تحويل عرض السعر: $e');

      LoggingService.error(
        'خطأ في تحويل عرض السعر إلى فاتورة',
        category: 'QuotationDetailsScreen',
        data: {'quotation_id': _quotation.id, 'error': e.toString()},
      );
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _updateStatus(QuotationStatus newStatus) async {
    setState(() => _isLoading = true);

    try {
      await _quotationService.updateQuotationStatus(_quotation.id!, newStatus);
      _showSuccessSnackBar('تم تحديث حالة عرض السعر بنجاح');
      await _refreshQuotation();
    } catch (e) {
      _showErrorSnackBar('خطأ في تحديث حالة عرض السعر: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<bool> _showConfirmDialog(String title, String content) async {
    return await showDialog<bool>(
          context: context,
          builder: (context) => AlertDialog(
            title: Text(title),
            content: Text(content),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: const Text('إلغاء'),
              ),
              ElevatedButton(
                onPressed: () => Navigator.of(context).pop(true),
                style: ElevatedButton.styleFrom(
                  backgroundColor: RevolutionaryColors.damascusSky,
                ),
                child: const Text('تأكيد'),
              ),
            ],
          ),
        ) ??
        false;
  }

  void _showStatusDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تغيير حالة عرض السعر'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: QuotationStatus.values
              .where((status) => status != QuotationStatus.converted)
              .map((status) {
                return ListTile(
                  title: Text(status.displayName),
                  leading: Radio<QuotationStatus>(
                    value: status,
                    groupValue: _quotation.status,
                    onChanged: (value) {
                      Navigator.of(context).pop();
                      if (value != null && value != _quotation.status) {
                        _updateStatus(value);
                      }
                    },
                  ),
                );
              })
              .toList(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
        ],
      ),
    );
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message), backgroundColor: RevolutionaryColors.successGlow),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message), backgroundColor: RevolutionaryColors.errorCoral),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('عرض السعر ${_quotation.quotationNumber}'),
        backgroundColor: RevolutionaryColors.damascusSky,
        foregroundColor: Colors.white,
        actions: [
          if (!_quotation.isConverted) ...[
            IconButton(
              icon: const Icon(Icons.edit),
              onPressed: () async {
                final result = await Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) =>
                        AddQuotationScreen(quotation: _quotation),
                  ),
                );
                if (result == true) {
                  await _refreshQuotation();
                  if (mounted) {
                    if (context.mounted) {
                      Navigator.of(context).pop(true);
                    }
                  }
                }
              },
              tooltip: 'تعديل',
            ),
            PopupMenuButton<String>(
              onSelected: (value) {
                switch (value) {
                  case 'status':
                    _showStatusDialog();
                    break;
                  case 'convert':
                    _convertToInvoice();
                    break;
                }
              },
              itemBuilder: (context) => [
                const PopupMenuItem(
                  value: 'status',
                  child: Row(
                    children: [
                      Icon(Icons.edit, size: 16),
                      SizedBox(width: 8),
                      Text('تغيير الحالة'),
                    ],
                  ),
                ),
                if (_quotation.canBeConverted)
                  const PopupMenuItem(
                    value: 'convert',
                    child: Row(
                      children: [
                        Icon(Icons.receipt_long, size: 16),
                        SizedBox(width: 8),
                        Text('تحويل إلى فاتورة'),
                      ],
                    ),
                  ),
              ],
            ),
          ],
        ],
      ),
      body: _isLoading
          ? const LoadingWidget()
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildHeader(),
                  const SizedBox(height: 24),
                  _buildCustomerSupplierInfo(),
                  const SizedBox(height: 24),
                  _buildItemsList(),
                  const SizedBox(height: 24),
                  _buildTotals(),
                  const SizedBox(height: 24),
                  _buildNotesAndTerms(),
                  if (_quotation.canBeConverted) ...[
                    const SizedBox(height: 24),
                    _buildConvertButton(),
                  ],
                ],
              ),
            ),
    );
  }

  Widget _buildHeader() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        _quotation.quotationNumber,
                        style: const TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                          color: RevolutionaryColors.damascusSky,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'تاريخ العرض: ${_formatDate(_quotation.quotationDate)}',
                        style: const TextStyle(fontSize: 16),
                      ),
                      if (_quotation.validUntil != null) ...[
                        const SizedBox(height: 4),
                        Text(
                          'صالح حتى: ${_formatDate(_quotation.validUntil!)}',
                          style: TextStyle(
                            fontSize: 16,
                            color: _quotation.isExpired ? Colors.red : null,
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
                _buildStatusChip(),
              ],
            ),
            if (_quotation.isConverted) ...[
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: RevolutionaryColors.successGlow.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: RevolutionaryColors.successGlow),
                ),
                child: Row(
                  children: [
                    const Icon(Icons.receipt, color: RevolutionaryColors.successGlow),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        'تم تحويل هذا العرض إلى فاتورة رقم: ${_quotation.convertedToInvoice}',
                        style: const TextStyle(
                          color: RevolutionaryColors.successGlow,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildStatusChip() {
    Color backgroundColor;
    Color textColor;
    String displayText = _quotation.displayStatus;

    switch (_quotation.status) {
      case QuotationStatus.draft:
        backgroundColor = Colors.grey[100]!;
        textColor = Colors.grey[700]!;
        break;
      case QuotationStatus.sent:
        backgroundColor = Colors.blue[100]!;
        textColor = Colors.blue[700]!;
        break;
      case QuotationStatus.accepted:
        backgroundColor = Colors.green[100]!;
        textColor = Colors.green[700]!;
        break;
      case QuotationStatus.rejected:
        backgroundColor = Colors.red[100]!;
        textColor = Colors.red[700]!;
        break;
      case QuotationStatus.expired:
        backgroundColor = Colors.orange[100]!;
        textColor = Colors.orange[700]!;
        break;
      case QuotationStatus.converted:
        backgroundColor = Colors.purple[100]!;
        textColor = Colors.purple[700]!;
        break;
    }

    if (_quotation.isExpired &&
        _quotation.status != QuotationStatus.converted) {
      backgroundColor = Colors.orange[100]!;
      textColor = Colors.orange[700]!;
      displayText = QuotationStatus.expired.displayName;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(20),
      ),
      child: Text(
        displayText,
        style: TextStyle(
          color: textColor,
          fontSize: 14,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  Widget _buildCustomerSupplierInfo() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'معلومات العميل/المورد',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            if (_quotation.customerName != null) ...[
              _buildInfoRow('العميل:', _quotation.customerName!),
            ] else if (_quotation.supplierName != null) ...[
              _buildInfoRow('المورد:', _quotation.supplierName!),
            ],
            if (_quotation.reference != null) ...[
              const SizedBox(height: 8),
              _buildInfoRow('المرجع:', _quotation.reference!),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: 100,
          child: Text(
            label,
            style: const TextStyle(fontWeight: FontWeight.w500),
          ),
        ),
        Expanded(child: Text(value)),
      ],
    );
  }

  Widget _buildItemsList() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'الأصناف',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            ListView.separated(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: _quotation.items.length,
              separatorBuilder: (context, index) => const Divider(),
              itemBuilder: (context, index) {
                final item = _quotation.items[index];
                return Padding(
                  padding: const EdgeInsets.symmetric(vertical: 8),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '${item.itemCode} - ${item.itemName}',
                        style: const TextStyle(
                          fontWeight: FontWeight.w500,
                          fontSize: 16,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Row(
                        children: [
                          Expanded(child: Text('الكمية: ${item.quantity}')),
                          Expanded(
                            child: Text(
                              'السعر: ${item.unitPrice.toStringAsFixed(2)}',
                            ),
                          ),
                          Expanded(
                            child: Text(
                              'الإجمالي: ${item.totalPrice.toStringAsFixed(2)}',
                              style: const TextStyle(
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTotals() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'الإجماليات',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            _buildTotalRow('المجموع الفرعي:', _quotation.subtotal),
            _buildTotalRow('الضريبة:', _quotation.taxAmount),
            _buildTotalRow('الخصم:', _quotation.discountAmount),
            const Divider(),
            _buildTotalRow(
              'الإجمالي النهائي:',
              _quotation.totalAmount,
              isTotal: true,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTotalRow(String label, double amount, {bool isTotal = false}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: isTotal ? 16 : 14,
              fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
            ),
          ),
          Text(
            '${amount.toStringAsFixed(2)} ${_getCurrencySymbol()}',
            style: TextStyle(
              fontSize: isTotal ? 16 : 14,
              fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
              color: isTotal ? RevolutionaryColors.damascusSky : null,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNotesAndTerms() {
    if (_quotation.notes == null && _quotation.terms == null) {
      return const SizedBox.shrink();
    }

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'ملاحظات وشروط',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            if (_quotation.notes != null) ...[
              _buildInfoRow('ملاحظات:', _quotation.notes!),
              const SizedBox(height: 8),
            ],
            if (_quotation.terms != null) ...[
              _buildInfoRow('شروط الدفع:', _quotation.terms!),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildConvertButton() {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton.icon(
        onPressed: _convertToInvoice,
        icon: const Icon(Icons.receipt_long),
        label: const Text('تحويل إلى فاتورة'),
        style: ElevatedButton.styleFrom(
          backgroundColor: RevolutionaryColors.successGlow,
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(vertical: 16),
        ),
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  String _getCurrencySymbol() {
    return AppConstants.currencySymbols['SYP'] ?? 'ل.س';
  }
}
