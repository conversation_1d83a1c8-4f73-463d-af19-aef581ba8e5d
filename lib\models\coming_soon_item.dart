/// نموذج صنف قريباً
/// يمثل الأصناف التي ستكون متوفرة قريباً ويمكن إضافتها للفواتير
library;

class ComingSoonItem {
  final int? id;
  final String name;
  final String description;
  final String? category;
  final double estimatedPrice;
  final String? unit;
  final DateTime expectedAvailabilityDate;
  final String? supplierInfo;
  final String? notes;
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;

  ComingSoonItem({
    this.id,
    required this.name,
    required this.description,
    this.category,
    this.estimatedPrice = 0.0,
    this.unit,
    required this.expectedAvailabilityDate,
    this.supplierInfo,
    this.notes,
    this.isActive = true,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) : createdAt = createdAt ?? DateTime.now(),
       updatedAt = updatedAt ?? DateTime.now();

  /// تحويل إلى Map لقاعدة البيانات
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'category': category,
      'estimated_price': estimatedPrice,
      'unit': unit,
      'expected_availability_date': expectedAvailabilityDate.toIso8601String(),
      'supplier_info': supplierInfo,
      'notes': notes,
      'is_active': isActive ? 1 : 0,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  /// إنشاء من Map
  factory ComingSoonItem.fromMap(Map<String, dynamic> map) {
    return ComingSoonItem(
      id: map['id'],
      name: map['name'] ?? '',
      description: map['description'] ?? '',
      category: map['category'],
      estimatedPrice: (map['estimated_price'] ?? 0.0).toDouble(),
      unit: map['unit'],
      expectedAvailabilityDate: DateTime.parse(map['expected_availability_date']),
      supplierInfo: map['supplier_info'],
      notes: map['notes'],
      isActive: (map['is_active'] ?? 1) == 1,
      createdAt: DateTime.parse(map['created_at']),
      updatedAt: DateTime.parse(map['updated_at']),
    );
  }

  /// نسخ مع تعديل
  ComingSoonItem copyWith({
    int? id,
    String? name,
    String? description,
    String? category,
    double? estimatedPrice,
    String? unit,
    DateTime? expectedAvailabilityDate,
    String? supplierInfo,
    String? notes,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return ComingSoonItem(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      category: category ?? this.category,
      estimatedPrice: estimatedPrice ?? this.estimatedPrice,
      unit: unit ?? this.unit,
      expectedAvailabilityDate: expectedAvailabilityDate ?? this.expectedAvailabilityDate,
      supplierInfo: supplierInfo ?? this.supplierInfo,
      notes: notes ?? this.notes,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// التحقق من انتهاء صلاحية التوقع
  bool get isExpired {
    return DateTime.now().isAfter(expectedAvailabilityDate);
  }

  /// عدد الأيام المتبقية
  int get daysRemaining {
    final difference = expectedAvailabilityDate.difference(DateTime.now());
    return difference.inDays;
  }

  /// حالة التوفر
  String get availabilityStatus {
    if (isExpired) {
      return 'متأخر';
    } else if (daysRemaining <= 7) {
      return 'قريب جداً';
    } else if (daysRemaining <= 30) {
      return 'قريب';
    } else {
      return 'بعيد';
    }
  }

  /// لون حالة التوفر
  String get statusColor {
    switch (availabilityStatus) {
      case 'متأخر':
        return '#F44336'; // أحمر
      case 'قريب جداً':
        return '#FF9800'; // برتقالي
      case 'قريب':
        return '#FFC107'; // أصفر
      case 'بعيد':
        return '#4CAF50'; // أخضر
      default:
        return '#9E9E9E'; // رمادي
    }
  }

  @override
  String toString() {
    return 'ComingSoonItem{id: $id, name: $name, expectedDate: $expectedAvailabilityDate}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ComingSoonItem && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

/// عنصر فاتورة لصنف قريباً
class ComingSoonInvoiceItem {
  final int? id;
  final int invoiceId;
  final int comingSoonItemId;
  final String itemName;
  final String itemDescription;
  final double quantity;
  final double estimatedPrice;
  final double totalEstimatedAmount;
  final String? unit;
  final String? notes;
  final DateTime expectedDeliveryDate;
  final DateTime createdAt;

  ComingSoonInvoiceItem({
    this.id,
    required this.invoiceId,
    required this.comingSoonItemId,
    required this.itemName,
    required this.itemDescription,
    required this.quantity,
    required this.estimatedPrice,
    required this.totalEstimatedAmount,
    this.unit,
    this.notes,
    required this.expectedDeliveryDate,
    DateTime? createdAt,
  }) : createdAt = createdAt ?? DateTime.now();

  /// تحويل إلى Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'invoice_id': invoiceId,
      'coming_soon_item_id': comingSoonItemId,
      'item_name': itemName,
      'item_description': itemDescription,
      'quantity': quantity,
      'estimated_price': estimatedPrice,
      'total_estimated_amount': totalEstimatedAmount,
      'unit': unit,
      'notes': notes,
      'expected_delivery_date': expectedDeliveryDate.toIso8601String(),
      'created_at': createdAt.toIso8601String(),
    };
  }

  /// إنشاء من Map
  factory ComingSoonInvoiceItem.fromMap(Map<String, dynamic> map) {
    return ComingSoonInvoiceItem(
      id: map['id'],
      invoiceId: map['invoice_id'],
      comingSoonItemId: map['coming_soon_item_id'],
      itemName: map['item_name'] ?? '',
      itemDescription: map['item_description'] ?? '',
      quantity: (map['quantity'] ?? 0.0).toDouble(),
      estimatedPrice: (map['estimated_price'] ?? 0.0).toDouble(),
      totalEstimatedAmount: (map['total_estimated_amount'] ?? 0.0).toDouble(),
      unit: map['unit'],
      notes: map['notes'],
      expectedDeliveryDate: DateTime.parse(map['expected_delivery_date']),
      createdAt: DateTime.parse(map['created_at']),
    );
  }

  /// إنشاء من صنف قريباً
  factory ComingSoonInvoiceItem.fromComingSoonItem({
    required int invoiceId,
    required ComingSoonItem comingSoonItem,
    required double quantity,
    DateTime? expectedDeliveryDate,
    String? notes,
  }) {
    final totalAmount = quantity * comingSoonItem.estimatedPrice;
    
    return ComingSoonInvoiceItem(
      invoiceId: invoiceId,
      comingSoonItemId: comingSoonItem.id!,
      itemName: comingSoonItem.name,
      itemDescription: comingSoonItem.description,
      quantity: quantity,
      estimatedPrice: comingSoonItem.estimatedPrice,
      totalEstimatedAmount: totalAmount,
      unit: comingSoonItem.unit,
      notes: notes,
      expectedDeliveryDate: expectedDeliveryDate ?? comingSoonItem.expectedAvailabilityDate,
    );
  }

  /// نسخ مع تعديل
  ComingSoonInvoiceItem copyWith({
    int? id,
    int? invoiceId,
    int? comingSoonItemId,
    String? itemName,
    String? itemDescription,
    double? quantity,
    double? estimatedPrice,
    double? totalEstimatedAmount,
    String? unit,
    String? notes,
    DateTime? expectedDeliveryDate,
    DateTime? createdAt,
  }) {
    return ComingSoonInvoiceItem(
      id: id ?? this.id,
      invoiceId: invoiceId ?? this.invoiceId,
      comingSoonItemId: comingSoonItemId ?? this.comingSoonItemId,
      itemName: itemName ?? this.itemName,
      itemDescription: itemDescription ?? this.itemDescription,
      quantity: quantity ?? this.quantity,
      estimatedPrice: estimatedPrice ?? this.estimatedPrice,
      totalEstimatedAmount: totalEstimatedAmount ?? this.totalEstimatedAmount,
      unit: unit ?? this.unit,
      notes: notes ?? this.notes,
      expectedDeliveryDate: expectedDeliveryDate ?? this.expectedDeliveryDate,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  @override
  String toString() {
    return 'ComingSoonInvoiceItem{id: $id, itemName: $itemName, quantity: $quantity}';
  }
}
