/// شاشة إدارة الأصناف القريبة
/// تتيح إدارة الأصناف التي ستكون متوفرة قريباً
library;

import 'package:flutter/material.dart';
import '../constants/revolutionary_design_colors.dart';
import '../widgets/revolutionary_ui_components.dart';
import '../models/coming_soon_item.dart';
import '../services/coming_soon_service.dart';
import '../services/logging_service.dart';
import '../widgets/loading_widget.dart';
import '../widgets/coming_soon_item_details_dialog.dart';
import '../widgets/add_coming_soon_item_dialog.dart';

class ComingSoonItemsScreen extends StatefulWidget {
  const ComingSoonItemsScreen({super.key});

  @override
  State<ComingSoonItemsScreen> createState() => _ComingSoonItemsScreenState();
}

class _ComingSoonItemsScreenState extends State<ComingSoonItemsScreen>
    with TickerProviderStateMixin {
  final ComingSoonService _comingSoonService = ComingSoonService();

  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  List<ComingSoonItem> _comingSoonItems = [];
  Map<String, dynamic> _statistics = {};
  bool _isLoading = true;
  String _searchQuery = '';
  String _selectedCategory = 'الكل';

  final List<String> _categories = [
    'الكل',
    'إلكترونيات',
    'ملابس',
    'أغذية',
    'أدوات',
    'أخرى',
  ];

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _loadData();
  }

  void _setupAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    _animationController.forward();
  }

  Future<void> _loadData() async {
    try {
      setState(() => _isLoading = true);

      final items = await _comingSoonService.getAllComingSoonItems();
      final stats = await _comingSoonService.getComingSoonStatistics();

      setState(() {
        _comingSoonItems = items;
        _statistics = stats;
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      LoggingService.error(
        'خطأ في تحميل الأصناف القريبة',
        category: 'ComingSoonItemsScreen',
        data: {'error': e.toString()},
      );
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: RevolutionaryColors.backgroundPrimary,
      appBar: _buildAppBar(),
      body: FadeTransition(opacity: _fadeAnimation, child: _buildBody()),
      floatingActionButton: _buildFloatingActionButton(),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      backgroundColor: RevolutionaryColors.oliveBranch,
      elevation: 0,
      title: Text(
        'الأصناف القريبة',
        style: TextStyle(
          color: RevolutionaryColors.textOnDark,
          fontSize: 20,
          fontWeight: FontWeight.bold,
          fontFamily: 'Cairo',
        ),
      ),
      leading: IconButton(
        icon: Icon(Icons.arrow_back_ios, color: RevolutionaryColors.textOnDark),
        onPressed: () => Navigator.pop(context),
      ),
      actions: [
        IconButton(
          icon: Icon(Icons.refresh, color: RevolutionaryColors.textOnDark),
          onPressed: _loadData,
        ),
      ],
    );
  }

  Widget _buildBody() {
    return Column(
      children: [
        _buildHeader(),
        _buildFilters(),
        Expanded(child: _buildContent()),
      ],
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            RevolutionaryColors.oliveBranch,
            RevolutionaryColors.oliveBranch.withValues(alpha: 0.8),
          ],
        ),
      ),
      child: Column(
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: RevolutionaryColors.jasmineWhite.withValues(
                    alpha: 0.2,
                  ),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  Icons.schedule,
                  color: RevolutionaryColors.syrianGold,
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'إدارة الأصناف القريبة',
                      style: TextStyle(
                        color: RevolutionaryColors.textOnDark,
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        fontFamily: 'Cairo',
                      ),
                    ),
                    Text(
                      'الأصناف التي ستكون متوفرة قريباً',
                      style: TextStyle(
                        color: RevolutionaryColors.textOnDark.withValues(
                          alpha: 0.8,
                        ),
                        fontSize: 14,
                        fontFamily: 'Cairo',
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          _buildStatistics(),
        ],
      ),
    );
  }

  Widget _buildStatistics() {
    return Row(
      children: [
        Expanded(
          child: _buildStatCard(
            'إجمالي الأصناف',
            _statistics['total']?.toString() ?? '0',
            Icons.inventory,
            RevolutionaryColors.jasmineWhite,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildStatCard(
            'قريب جداً',
            _statistics['comingSoon']?.toString() ?? '0',
            Icons.schedule,
            RevolutionaryColors.warningAmber,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildStatCard(
            'متأخر',
            _statistics['overdue']?.toString() ?? '0',
            Icons.warning,
            RevolutionaryColors.errorCoral,
          ),
        ),
      ],
    );
  }

  Widget _buildStatCard(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: RevolutionaryColors.jasmineWhite.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3), width: 1),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 20),
          const SizedBox(height: 4),
          Text(
            value,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: color,
              fontFamily: 'Cairo',
            ),
          ),
          Text(
            title,
            style: TextStyle(
              fontSize: 10,
              color: RevolutionaryColors.textOnDark.withValues(alpha: 0.7),
              fontFamily: 'Cairo',
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildFilters() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          // شريط البحث
          TextField(
            onChanged: (value) => setState(() => _searchQuery = value),
            decoration: InputDecoration(
              hintText: 'البحث في الأصناف القريبة...',
              hintStyle: TextStyle(
                color: RevolutionaryColors.textHint,
                fontFamily: 'Cairo',
              ),
              prefixIcon: Icon(
                Icons.search,
                color: RevolutionaryColors.oliveBranch,
              ),
              filled: true,
              fillColor: RevolutionaryColors.jasmineWhite,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide.none,
              ),
              contentPadding: const EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 12,
              ),
            ),
            style: TextStyle(
              color: RevolutionaryColors.textPrimary,
              fontFamily: 'Cairo',
            ),
          ),
          const SizedBox(height: 12),
          // فلتر الفئات
          SizedBox(
            height: 40,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: _categories.length,
              itemBuilder: (context, index) {
                final category = _categories[index];
                final isSelected = _selectedCategory == category;

                return Container(
                  margin: const EdgeInsets.only(right: 8),
                  child: FilterChip(
                    label: Text(
                      category,
                      style: TextStyle(
                        color: isSelected
                            ? RevolutionaryColors.textOnDark
                            : RevolutionaryColors.textPrimary,
                        fontFamily: 'Cairo',
                      ),
                    ),
                    selected: isSelected,
                    onSelected: (selected) {
                      setState(() => _selectedCategory = category);
                    },
                    backgroundColor: RevolutionaryColors.jasmineWhite,
                    selectedColor: RevolutionaryColors.oliveBranch,
                    checkmarkColor: RevolutionaryColors.textOnDark,
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildContent() {
    if (_isLoading) {
      return const LoadingWidget();
    }

    final filteredItems = _comingSoonItems.where((item) {
      final matchesSearch =
          _searchQuery.isEmpty ||
          item.name.toLowerCase().contains(_searchQuery.toLowerCase()) ||
          item.description.toLowerCase().contains(_searchQuery.toLowerCase());

      final matchesCategory =
          _selectedCategory == 'الكل' || item.category == _selectedCategory;

      return matchesSearch && matchesCategory;
    }).toList();

    if (filteredItems.isEmpty) {
      return _buildEmptyState();
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: filteredItems.length,
      itemBuilder: (context, index) {
        final item = filteredItems[index];
        return _buildComingSoonItemCard(item, index);
      },
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.schedule_outlined,
            size: 64,
            color: RevolutionaryColors.textHint,
          ),
          const SizedBox(height: 16),
          Text(
            'لا توجد أصناف قريبة',
            style: TextStyle(
              fontSize: 18,
              color: RevolutionaryColors.textHint,
              fontFamily: 'Cairo',
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'ابدأ بإضافة أول صنف قريب',
            style: TextStyle(
              fontSize: 14,
              color: RevolutionaryColors.textHint,
              fontFamily: 'Cairo',
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildComingSoonItemCard(ComingSoonItem item, int index) {
    final statusColor = Color(
      int.parse(item.statusColor.substring(1), radix: 16) + 0xFF000000,
    );

    return AnimatedContainer(
      duration: Duration(milliseconds: 300 + (index * 100)),
      margin: const EdgeInsets.only(bottom: 12),
      child: RevolutionaryUI.floating3DCard(
        title: item.name,
        icon: Icons.schedule,
        accentColor: statusColor,
        onTap: () => _showItemDetails(item),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          item.name,
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: RevolutionaryColors.textPrimary,
                            fontFamily: 'Cairo',
                          ),
                        ),
                        Text(
                          item.description,
                          style: TextStyle(
                            fontSize: 14,
                            color: RevolutionaryColors.textSecondary,
                            fontFamily: 'Cairo',
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: statusColor.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      item.availabilityStatus,
                      style: TextStyle(
                        fontSize: 12,
                        color: statusColor,
                        fontFamily: 'Cairo',
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              Row(
                children: [
                  Icon(
                    Icons.attach_money,
                    size: 16,
                    color: RevolutionaryColors.syrianGold,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    '${item.estimatedPrice.toStringAsFixed(0)} ل.س',
                    style: TextStyle(
                      fontSize: 12,
                      color: RevolutionaryColors.textSecondary,
                      fontFamily: 'Cairo',
                    ),
                  ),
                  const Spacer(),
                  Icon(
                    Icons.event,
                    size: 16,
                    color: RevolutionaryColors.damascusSky,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    _formatDate(item.expectedAvailabilityDate),
                    style: TextStyle(
                      fontSize: 12,
                      color: RevolutionaryColors.textSecondary,
                      fontFamily: 'Cairo',
                    ),
                  ),
                ],
              ),
              if (item.daysRemaining >= 0) ...[
                const SizedBox(height: 8),
                Row(
                  children: [
                    Icon(Icons.schedule, size: 16, color: statusColor),
                    const SizedBox(width: 4),
                    Text(
                      '${item.daysRemaining} يوم متبقي',
                      style: TextStyle(
                        fontSize: 12,
                        color: statusColor,
                        fontFamily: 'Cairo',
                      ),
                    ),
                  ],
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildFloatingActionButton() {
    return FloatingActionButton.extended(
      onPressed: _showAddItemDialog,
      backgroundColor: RevolutionaryColors.syrianGold,
      icon: Icon(Icons.add, color: RevolutionaryColors.textOnDark),
      label: Text(
        'إضافة صنف قريب',
        style: TextStyle(
          color: RevolutionaryColors.textOnDark,
          fontFamily: 'Cairo',
        ),
      ),
    );
  }

  void _showItemDetails(ComingSoonItem item) {
    showDialog(
      context: context,
      builder: (context) => ComingSoonItemDetailsDialog(
        item: item,
        onItemUpdated: _loadData,
        onItemDeleted: _loadData,
      ),
    );
  }

  void _showAddItemDialog() {
    showDialog(
      context: context,
      builder: (context) => AddComingSoonItemDialog(onItemAdded: _loadData),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}
