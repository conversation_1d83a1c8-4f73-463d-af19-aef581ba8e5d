import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import '../models/dashboard_models.dart';
import '../constants/revolutionary_design_colors.dart';
import '../responsive/responsive.dart';

/// بطاقة مؤشر الأداء الرئيسي (KPI)
class KPICard extends StatelessWidget {
  final KPIModel kpi;
  final VoidCallback? onTap;

  const KPICard({super.key, required this.kpi, this.onTap});

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 4,
      margin: const EdgeInsets.all(8),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            gradient: LinearGradient(
              colors: [
                RevolutionaryColors.damascusSky.withValues(alpha: 0.1),
                RevolutionaryColors.damascusSky.withValues(alpha: 0.05),
              ],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // العنوان والاتجاه
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: ResponsiveText.body(
                      kpi.title,
                      style: const TextStyle(
                        fontWeight: FontWeight.w600,
                        color: RevolutionaryColors.textSecondary,
                      ),
                    ),
                  ),
                  _buildTrendIcon(),
                ],
              ),

              const SizedBox(height: 8),

              // القيمة والوحدة
              Row(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Expanded(
                    child: ResponsiveText.h2(
                      kpi.value,
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        color: RevolutionaryColors.textPrimary,
                      ),
                    ),
                  ),
                  ResponsiveText.caption(
                    kpi.unit,
                    style: const TextStyle(
                      color: RevolutionaryColors.textSecondary,
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 8),

              // النسبة المئوية
              if (kpi.percentage != 0) ...[
                Row(
                  children: [
                    Icon(
                      kpi.trend == KPITrend.up
                          ? Icons.trending_up
                          : kpi.trend == KPITrend.down
                          ? Icons.trending_down
                          : Icons.trending_flat,
                      size: 16,
                      color: _getTrendColor(),
                    ),
                    const SizedBox(width: 4),
                    ResponsiveText.caption(
                      '${kpi.percentage.abs().toStringAsFixed(1)}%',
                      style: TextStyle(
                        color: _getTrendColor(),
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
              ],

              const SizedBox(height: 4),

              // الوصف
              ResponsiveText.caption(
                kpi.description,
                style: const TextStyle(
                  color: RevolutionaryColors.textSecondary,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTrendIcon() {
    return Container(
      padding: const EdgeInsets.all(4),
      decoration: BoxDecoration(
        color: _getTrendColor().withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Icon(
        kpi.trend == KPITrend.up
            ? Icons.arrow_upward
            : kpi.trend == KPITrend.down
            ? Icons.arrow_downward
            : Icons.remove,
        size: 16,
        color: _getTrendColor(),
      ),
    );
  }

  Color _getTrendColor() {
    switch (kpi.trend) {
      case KPITrend.up:
        return RevolutionaryColors.successGlow;
      case KPITrend.down:
        return RevolutionaryColors.errorCoral;
      case KPITrend.stable:
        return RevolutionaryColors.warningAmber;
    }
  }
}

/// بطاقة الملخص المالي
class FinancialSummaryCard extends StatelessWidget {
  final FinancialSummary summary;

  const FinancialSummaryCard({super.key, required this.summary});

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 6,
      margin: const EdgeInsets.all(8),
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          gradient: LinearGradient(
            colors: [
              RevolutionaryColors.damascusSky.withValues(alpha: 0.1),
              RevolutionaryColors.oliveBranch.withValues(alpha: 0.1),
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // العنوان
            ResponsiveText.h3(
              'الملخص المالي',
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                color: RevolutionaryColors.damascusSky,
              ),
            ),

            const SizedBox(height: 16),

            // الإيرادات والمصروفات
            _buildSummaryRow(
              'إجمالي الإيرادات',
              summary.totalRevenue,
              RevolutionaryColors.successGlow,
            ),
            _buildSummaryRow(
              'إجمالي المصروفات',
              summary.totalExpenses,
              RevolutionaryColors.errorCoral,
            ),
            _buildSummaryRow(
              'صافي الربح',
              summary.netProfit,
              summary.netProfit >= 0
                  ? RevolutionaryColors.successGlow
                  : RevolutionaryColors.errorCoral,
              isHighlighted: true,
            ),

            const Divider(height: 24),

            // الأصول والخصوم
            _buildSummaryRow(
              'إجمالي الأصول',
              summary.totalAssets,
              RevolutionaryColors.infoTurquoise,
            ),
            _buildSummaryRow(
              'إجمالي الخصوم',
              summary.totalLiabilities,
              RevolutionaryColors.warningAmber,
            ),
            _buildSummaryRow(
              'حقوق الملكية',
              summary.totalEquity,
              RevolutionaryColors.damascusSky,
            ),

            const Divider(height: 24),

            // النسب المالية
            Row(
              children: [
                Expanded(
                  child: _buildRatioCard(
                    'نسبة الربح',
                    '${summary.profitMargin.toStringAsFixed(1)}%',
                    RevolutionaryColors.successGlow,
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: _buildRatioCard(
                    'نسبة الخصوم',
                    '${summary.debtToAssetRatio.toStringAsFixed(1)}%',
                    RevolutionaryColors.warningAmber,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryRow(
    String title,
    double value,
    Color color, {
    bool isHighlighted = false,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          ResponsiveText.body(
            title,
            style: TextStyle(
              fontWeight: isHighlighted ? FontWeight.bold : FontWeight.normal,
              color: isHighlighted ? color : RevolutionaryColors.textPrimary,
            ),
          ),
          ResponsiveText.body(
            '${value.toStringAsFixed(2)} ل.س',
            style: TextStyle(fontWeight: FontWeight.w600, color: color),
          ),
        ],
      ),
    );
  }

  Widget _buildRatioCard(String title, String value, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          ResponsiveText.caption(title, style: TextStyle(color: color)),
          const SizedBox(height: 4),
          ResponsiveText.h3(
            value,
            style: TextStyle(fontWeight: FontWeight.bold, color: color),
          ),
        ],
      ),
    );
  }
}

/// رسم بياني خطي للبيانات المالية
class FinancialLineChart extends StatelessWidget {
  final List<ChartData> data;
  final String title;
  final Color color;
  final double height;

  const FinancialLineChart({
    super.key,
    required this.data,
    required this.title,
    required this.color,
    this.height = 200,
  });

  @override
  Widget build(BuildContext context) {
    if (data.isEmpty) {
      return _buildEmptyChart();
    }

    return Card(
      elevation: 4,
      margin: const EdgeInsets.all(8),
      child: Container(
        height: height,
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            ResponsiveText.h3(
              title,
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                color: RevolutionaryColors.textPrimary,
              ),
            ),
            const SizedBox(height: 16),
            Expanded(
              child: LineChart(
                LineChartData(
                  gridData: FlGridData(
                    show: true,
                    drawVerticalLine: false,
                    horizontalInterval: _calculateInterval(),
                    getDrawingHorizontalLine: (value) {
                      return FlLine(
                        color: RevolutionaryColors.borderLight.withValues(
                          alpha: 0.3,
                        ),
                        strokeWidth: 1,
                      );
                    },
                  ),
                  titlesData: FlTitlesData(
                    show: true,
                    rightTitles: const AxisTitles(
                      sideTitles: SideTitles(showTitles: false),
                    ),
                    topTitles: const AxisTitles(
                      sideTitles: SideTitles(showTitles: false),
                    ),
                    bottomTitles: AxisTitles(
                      sideTitles: SideTitles(
                        showTitles: true,
                        reservedSize: 30,
                        interval: 1,
                        getTitlesWidget: (value, meta) {
                          final index = value.toInt();
                          if (index >= 0 && index < data.length) {
                            final date = data[index].date;
                            return ResponsiveText.caption(
                              '${date.day}/${date.month}',
                              style: const TextStyle(
                                color: RevolutionaryColors.textSecondary,
                              ),
                            );
                          }
                          return const SizedBox.shrink();
                        },
                      ),
                    ),
                    leftTitles: AxisTitles(
                      sideTitles: SideTitles(
                        showTitles: true,
                        reservedSize: 60,
                        getTitlesWidget: (value, meta) {
                          return ResponsiveText.caption(
                            _formatValue(value),
                            style: const TextStyle(
                              color: RevolutionaryColors.textSecondary,
                            ),
                          );
                        },
                      ),
                    ),
                  ),
                  borderData: FlBorderData(
                    show: true,
                    border: Border.all(
                      color: RevolutionaryColors.borderLight.withValues(
                        alpha: 0.3,
                      ),
                    ),
                  ),
                  lineBarsData: [
                    LineChartBarData(
                      spots: data.asMap().entries.map((entry) {
                        return FlSpot(entry.key.toDouble(), entry.value.value);
                      }).toList(),
                      isCurved: true,
                      color: color,
                      barWidth: 3,
                      isStrokeCapRound: true,
                      dotData: FlDotData(
                        show: true,
                        getDotPainter: (spot, percent, barData, index) {
                          return FlDotCirclePainter(
                            radius: 4,
                            color: color,
                            strokeWidth: 2,
                            strokeColor: Colors.white,
                          );
                        },
                      ),
                      belowBarData: BarAreaData(
                        show: true,
                        color: color.withValues(alpha: 0.1),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyChart() {
    return Card(
      elevation: 4,
      margin: const EdgeInsets.all(8),
      child: Container(
        height: height,
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.show_chart,
              size: 48,
              color: RevolutionaryColors.textSecondary.withValues(alpha: 0.5),
            ),
            const SizedBox(height: 16),
            ResponsiveText.body(
              'لا توجد بيانات لعرضها',
              style: TextStyle(
                color: RevolutionaryColors.textSecondary.withValues(alpha: 0.7),
              ),
            ),
          ],
        ),
      ),
    );
  }

  double _calculateInterval() {
    if (data.isEmpty) return 1;
    final maxValue = data.map((e) => e.value).reduce((a, b) => a > b ? a : b);
    return maxValue / 5;
  }

  String _formatValue(double value) {
    if (value >= 1000000) {
      return '${(value / 1000000).toStringAsFixed(1)}م';
    } else if (value >= 1000) {
      return '${(value / 1000).toStringAsFixed(1)}ك';
    } else {
      return value.toStringAsFixed(0);
    }
  }
}
