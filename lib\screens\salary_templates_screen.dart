/// شاشة قوالب الرواتب
/// توفر واجهة لإنشاء وإدارة قوالب الرواتب المختلفة
library;

import 'package:flutter/material.dart';
import '../models/hr_models.dart';
import '../services/employee_service.dart';
import '../services/logging_service.dart';
import '../widgets/loading_widget.dart';
import '../widgets/custom_error_widget.dart' as custom_widgets;
import '../constants/app_constants.dart';

class SalaryTemplate {
  final int? id;
  final String name;
  final String description;
  final double basicSalary;
  final List<SalaryDetail> components;
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;

  const SalaryTemplate({
    this.id,
    required this.name,
    required this.description,
    required this.basicSalary,
    required this.components,
    this.isActive = true,
    required this.createdAt,
    required this.updatedAt,
  });

  SalaryTemplate copyWith({
    int? id,
    String? name,
    String? description,
    double? basicSalary,
    List<SalaryDetail>? components,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return SalaryTemplate(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      basicSalary: basicSalary ?? this.basicSalary,
      components: components ?? this.components,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  double get totalAllowances {
    return components
        .where((c) => c.isAllowance)
        .fold(0, (sum, c) => sum + c.amount);
  }

  double get totalDeductions {
    return components
        .where((c) => c.isDeduction)
        .fold(0, (sum, c) => sum + c.amount);
  }

  double get grossSalary => basicSalary + totalAllowances;
  double get netSalary => grossSalary - totalDeductions;
}

class SalaryTemplatesScreen extends StatefulWidget {
  const SalaryTemplatesScreen({super.key});

  @override
  State<SalaryTemplatesScreen> createState() => _SalaryTemplatesScreenState();
}

class _SalaryTemplatesScreenState extends State<SalaryTemplatesScreen> {
  final EmployeeService _employeeService = EmployeeService();

  List<SalaryTemplate> _templates = [];
  List<Employee> _employees = [];
  bool _isLoading = true;
  String? _error;
  bool _showActiveOnly = true;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final employees = await _employeeService.getAllEmployees();

      // بيانات وهمية لقوالب الرواتب
      await Future.delayed(const Duration(milliseconds: 500));

      _templates = [
        SalaryTemplate(
          id: 1,
          name: 'قالب الموظف الجديد',
          description: 'قالب راتب للموظفين الجدد',
          basicSalary: 500000,
          components: [
            SalaryDetail(
              id: 1,
              employeeId: 0,
              componentType: AppConstants.salaryComponentAllowance,
              componentName: 'بدل نقل',
              amount: 50000,
              effectiveFrom: DateTime.now(),
              createdAt: DateTime.now(),
              updatedAt: DateTime.now(),
            ),
            SalaryDetail(
              id: 2,
              employeeId: 0,
              componentType: AppConstants.salaryComponentDeduction,
              componentName: 'تأمينات اجتماعية',
              amount: 7,
              isPercentage: true,
              percentageOf: 'basic_salary',
              effectiveFrom: DateTime.now(),
              createdAt: DateTime.now(),
              updatedAt: DateTime.now(),
            ),
          ],
          createdAt: DateTime.now().subtract(const Duration(days: 30)),
          updatedAt: DateTime.now(),
        ),
        SalaryTemplate(
          id: 2,
          name: 'قالب المدير',
          description: 'قالب راتب للمدراء',
          basicSalary: 1000000,
          components: [
            SalaryDetail(
              id: 3,
              employeeId: 0,
              componentType: AppConstants.salaryComponentAllowance,
              componentName: 'بدل إدارة',
              amount: 200000,
              effectiveFrom: DateTime.now(),
              createdAt: DateTime.now(),
              updatedAt: DateTime.now(),
            ),
            SalaryDetail(
              id: 4,
              employeeId: 0,
              componentType: AppConstants.salaryComponentAllowance,
              componentName: 'بدل نقل',
              amount: 100000,
              effectiveFrom: DateTime.now(),
              createdAt: DateTime.now(),
              updatedAt: DateTime.now(),
            ),
            SalaryDetail(
              id: 5,
              employeeId: 0,
              componentType: AppConstants.salaryComponentDeduction,
              componentName: 'تأمينات اجتماعية',
              amount: 7,
              isPercentage: true,
              percentageOf: 'basic_salary',
              effectiveFrom: DateTime.now(),
              createdAt: DateTime.now(),
              updatedAt: DateTime.now(),
            ),
          ],
          createdAt: DateTime.now().subtract(const Duration(days: 15)),
          updatedAt: DateTime.now(),
        ),
      ];

      setState(() {
        _employees = employees;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });

      LoggingService.error(
        'خطأ في تحميل قوالب الرواتب',
        category: 'SalaryTemplatesScreen',
        data: {'error': e.toString()},
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('قوالب الرواتب'),
        backgroundColor: Colors.deepPurple[700],
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: Icon(
              _showActiveOnly ? Icons.visibility : Icons.visibility_off,
            ),
            onPressed: () {
              setState(() {
                _showActiveOnly = !_showActiveOnly;
              });
              _loadData();
            },
            tooltip: _showActiveOnly
                ? 'إظهار جميع القوالب'
                : 'إظهار القوالب النشطة فقط',
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadData,
            tooltip: 'تحديث',
          ),
        ],
      ),
      body: _buildBody(),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _showAddTemplateDialog(),
        backgroundColor: Colors.deepPurple[700],
        tooltip: 'إضافة قالب جديد',
        child: const Icon(Icons.add, color: Colors.white),
      ),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const LoadingWidget();
    }

    if (_error != null) {
      return custom_widgets.ErrorWidget(message: _error!, onRetry: _loadData);
    }

    final filteredTemplates = _showActiveOnly
        ? _templates.where((t) => t.isActive).toList()
        : _templates;

    if (filteredTemplates.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.description, size: 64, color: Colors.grey[400]),
            const SizedBox(height: 16),
            Text(
              _showActiveOnly ? 'لا توجد قوالب نشطة' : 'لا توجد قوالب',
              style: TextStyle(fontSize: 18, color: Colors.grey[600]),
            ),
            const SizedBox(height: 16),
            ElevatedButton.icon(
              onPressed: () => _showAddTemplateDialog(),
              icon: const Icon(Icons.add),
              label: const Text('إضافة قالب جديد'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.deepPurple[700],
                foregroundColor: Colors.white,
              ),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _loadData,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: filteredTemplates.length,
        itemBuilder: (context, index) {
          final template = filteredTemplates[index];
          return _buildTemplateCard(template);
        },
      ),
    );
  }

  Widget _buildTemplateCard(SalaryTemplate template) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        template.name,
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        template.description,
                        style: TextStyle(color: Colors.grey[600], fontSize: 14),
                      ),
                    ],
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: template.isActive
                        ? Colors.green.withValues(alpha: 0.1)
                        : Colors.grey.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: template.isActive ? Colors.green : Colors.grey,
                    ),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        template.isActive ? Icons.check_circle : Icons.cancel,
                        size: 16,
                        color: template.isActive ? Colors.green : Colors.grey,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        template.isActive ? 'نشط' : 'غير نشط',
                        style: TextStyle(
                          color: template.isActive ? Colors.green : Colors.grey,
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const Divider(height: 24),
            Row(
              children: [
                Expanded(
                  child: _buildSalaryInfo(
                    'الراتب الأساسي',
                    template.basicSalary,
                    Colors.blue,
                  ),
                ),
                Expanded(
                  child: _buildSalaryInfo(
                    'البدلات',
                    template.totalAllowances,
                    Colors.green,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: _buildSalaryInfo(
                    'الاستقطاعات',
                    template.totalDeductions,
                    Colors.red,
                  ),
                ),
                Expanded(
                  child: _buildSalaryInfo(
                    'الراتب الصافي',
                    template.netSalary,
                    Colors.deepPurple[700]!,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'عدد العناصر: ${template.components.length}',
                  style: TextStyle(color: Colors.grey[600], fontSize: 12),
                ),
                Row(
                  children: [
                    TextButton.icon(
                      onPressed: () => _showTemplateDetails(template),
                      icon: const Icon(Icons.visibility, size: 16),
                      label: const Text('عرض'),
                      style: TextButton.styleFrom(
                        foregroundColor: Colors.deepPurple[700],
                      ),
                    ),
                    TextButton.icon(
                      onPressed: () => _showEditTemplateDialog(template),
                      icon: const Icon(Icons.edit, size: 16),
                      label: const Text('تعديل'),
                      style: TextButton.styleFrom(
                        foregroundColor: Colors.orange,
                      ),
                    ),
                    TextButton.icon(
                      onPressed: () => _applyTemplate(template),
                      icon: const Icon(Icons.person_add, size: 16),
                      label: const Text('تطبيق'),
                      style: TextButton.styleFrom(
                        foregroundColor: Colors.green,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSalaryInfo(String label, double amount, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Text(
            label,
            style: TextStyle(fontSize: 12, color: Colors.grey[600]),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 4),
          Text(
            '${amount.toStringAsFixed(0)} ل.س',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: color,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  void _showAddTemplateDialog() {
    // يمكن إضافة نافذة إضافة قالب هنا
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('سيتم إضافة نافذة إضافة قالب قريباً')),
    );
  }

  void _showEditTemplateDialog(SalaryTemplate template) {
    // يمكن إضافة نافذة تعديل قالب هنا
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(SnackBar(content: Text('تعديل قالب ${template.name}')));
  }

  void _showTemplateDetails(SalaryTemplate template) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(template.name),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(template.description),
              const Divider(height: 24),
              Text(
                'تفاصيل الراتب:',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: Colors.deepPurple[700],
                ),
              ),
              const SizedBox(height: 8),
              _buildDetailRow(
                'الراتب الأساسي',
                '${template.basicSalary.toStringAsFixed(0)} ل.س',
              ),
              const SizedBox(height: 16),
              if (template.components
                  .where((c) => c.isAllowance)
                  .isNotEmpty) ...[
                Text(
                  'البدلات:',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: Colors.green,
                  ),
                ),
                const SizedBox(height: 8),
                ...template.components
                    .where((c) => c.isAllowance)
                    .map((component) => _buildComponentRow(component)),
                const SizedBox(height: 16),
              ],
              if (template.components
                  .where((c) => c.isDeduction)
                  .isNotEmpty) ...[
                Text(
                  'الاستقطاعات:',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: Colors.red,
                  ),
                ),
                const SizedBox(height: 8),
                ...template.components
                    .where((c) => c.isDeduction)
                    .map((component) => _buildComponentRow(component)),
                const SizedBox(height: 16),
              ],
              const Divider(),
              _buildDetailRow(
                'إجمالي البدلات',
                '${template.totalAllowances.toStringAsFixed(0)} ل.س',
              ),
              _buildDetailRow(
                'إجمالي الاستقطاعات',
                '${template.totalDeductions.toStringAsFixed(0)} ل.س',
              ),
              _buildDetailRow(
                'الراتب الإجمالي',
                '${template.grossSalary.toStringAsFixed(0)} ل.س',
              ),
              _buildDetailRow(
                'الراتب الصافي',
                '${template.netSalary.toStringAsFixed(0)} ل.س',
                isTotal: true,
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إغلاق'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _applyTemplate(template);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.deepPurple[700],
              foregroundColor: Colors.white,
            ),
            child: const Text('تطبيق على موظف'),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value, {bool isTotal = false}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
              fontSize: isTotal ? 14 : 13,
            ),
          ),
          Text(
            value,
            style: TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: isTotal ? 14 : 13,
              color: isTotal ? Colors.deepPurple[700] : null,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildComponentRow(SalaryDetail component) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2, horizontal: 16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
            child: Text(
              component.componentName,
              style: const TextStyle(fontSize: 13),
            ),
          ),
          Text(
            component.isPercentage
                ? '${component.amount}%'
                : '${component.amount.toStringAsFixed(0)} ل.س',
            style: TextStyle(
              fontSize: 13,
              fontWeight: FontWeight.bold,
              color: component.isAllowance ? Colors.green : Colors.red,
            ),
          ),
        ],
      ),
    );
  }

  void _applyTemplate(SalaryTemplate template) {
    if (_employees.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('لا يوجد موظفين لتطبيق القالب عليهم'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('تطبيق قالب ${template.name}'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('اختر الموظف لتطبيق القالب عليه:'),
            const SizedBox(height: 16),
            DropdownButtonFormField<int>(
              decoration: const InputDecoration(
                labelText: 'الموظف',
                border: OutlineInputBorder(),
              ),
              items: _employees.map((emp) {
                return DropdownMenuItem<int>(
                  value: emp.id,
                  child: Text('${emp.firstName} ${emp.lastName}'),
                );
              }).toList(),
              onChanged: (value) {
                // يمكن حفظ القيمة المختارة هنا
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text('تم تطبيق قالب ${template.name} بنجاح')),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.deepPurple[700],
              foregroundColor: Colors.white,
            ),
            child: const Text('تطبيق'),
          ),
        ],
      ),
    );
  }
}
