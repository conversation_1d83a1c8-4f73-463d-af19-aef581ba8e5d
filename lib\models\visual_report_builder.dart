/// نماذج منشئ التقارير المرئي
/// يتيح إنشاء تقارير مخصصة بواجهة سحب وإفلات
library;



/// نوع عنصر التقرير
enum ReportElementType {
  text,
  table,
  chart,
  image,
  spacer,
  divider,
  header,
  footer,
  calculation,
  filter,
}

/// نوع الرسم البياني
enum ChartType {
  pie,
  bar,
  line,
  area,
  donut,
  scatter,
}

/// نوع البيانات
enum DataSourceType {
  accounts,
  invoices,
  items,
  customers,
  suppliers,
  journalEntries,
  payments,
  inventory,
  custom,
}

/// عنصر التقرير
class ReportElement {
  final String id;
  final ReportElementType type;
  final String title;
  final Map<String, dynamic> properties;
  final Map<String, dynamic> styling;
  final int order;
  final bool isVisible;

  const ReportElement({
    required this.id,
    required this.type,
    required this.title,
    this.properties = const {},
    this.styling = const {},
    this.order = 0,
    this.isVisible = true,
  });

  ReportElement copyWith({
    String? id,
    ReportElementType? type,
    String? title,
    Map<String, dynamic>? properties,
    Map<String, dynamic>? styling,
    int? order,
    bool? isVisible,
  }) {
    return ReportElement(
      id: id ?? this.id,
      type: type ?? this.type,
      title: title ?? this.title,
      properties: properties ?? this.properties,
      styling: styling ?? this.styling,
      order: order ?? this.order,
      isVisible: isVisible ?? this.isVisible,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type.name,
      'title': title,
      'properties': properties,
      'styling': styling,
      'order': order,
      'isVisible': isVisible,
    };
  }

  factory ReportElement.fromJson(Map<String, dynamic> json) {
    return ReportElement(
      id: json['id'],
      type: ReportElementType.values.firstWhere(
        (e) => e.name == json['type'],
      ),
      title: json['title'],
      properties: Map<String, dynamic>.from(json['properties'] ?? {}),
      styling: Map<String, dynamic>.from(json['styling'] ?? {}),
      order: json['order'] ?? 0,
      isVisible: json['isVisible'] ?? true,
    );
  }
}

/// قالب التقرير
class ReportTemplate {
  final int? id;
  final String name;
  final String description;
  final String category;
  final List<ReportElement> elements;
  final Map<String, dynamic> settings;
  final DateTime createdAt;
  final DateTime? updatedAt;
  final bool isPublic;
  final String createdBy;

  const ReportTemplate({
    this.id,
    required this.name,
    required this.description,
    required this.category,
    required this.elements,
    this.settings = const {},
    required this.createdAt,
    this.updatedAt,
    this.isPublic = false,
    required this.createdBy,
  });

  ReportTemplate copyWith({
    int? id,
    String? name,
    String? description,
    String? category,
    List<ReportElement>? elements,
    Map<String, dynamic>? settings,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool? isPublic,
    String? createdBy,
  }) {
    return ReportTemplate(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      category: category ?? this.category,
      elements: elements ?? this.elements,
      settings: settings ?? this.settings,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isPublic: isPublic ?? this.isPublic,
      createdBy: createdBy ?? this.createdBy,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'category': category,
      'elements': elements.map((e) => e.toJson()).toList(),
      'settings': settings,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
      'isPublic': isPublic,
      'createdBy': createdBy,
    };
  }

  factory ReportTemplate.fromJson(Map<String, dynamic> json) {
    return ReportTemplate(
      id: json['id'],
      name: json['name'],
      description: json['description'],
      category: json['category'],
      elements: (json['elements'] as List)
          .map((e) => ReportElement.fromJson(e))
          .toList(),
      settings: Map<String, dynamic>.from(json['settings'] ?? {}),
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: json['updatedAt'] != null 
          ? DateTime.parse(json['updatedAt']) 
          : null,
      isPublic: json['isPublic'] ?? false,
      createdBy: json['createdBy'],
    );
  }
}

/// مصدر البيانات للتقرير
class ReportDataSource {
  final String id;
  final DataSourceType type;
  final String name;
  final String query;
  final Map<String, dynamic> parameters;
  final List<String> columns;
  final Map<String, String> columnTypes;

  const ReportDataSource({
    required this.id,
    required this.type,
    required this.name,
    required this.query,
    this.parameters = const {},
    this.columns = const [],
    this.columnTypes = const {},
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type.name,
      'name': name,
      'query': query,
      'parameters': parameters,
      'columns': columns,
      'columnTypes': columnTypes,
    };
  }

  factory ReportDataSource.fromJson(Map<String, dynamic> json) {
    return ReportDataSource(
      id: json['id'],
      type: DataSourceType.values.firstWhere(
        (e) => e.name == json['type'],
      ),
      name: json['name'],
      query: json['query'],
      parameters: Map<String, dynamic>.from(json['parameters'] ?? {}),
      columns: List<String>.from(json['columns'] ?? []),
      columnTypes: Map<String, String>.from(json['columnTypes'] ?? {}),
    );
  }
}

/// فلتر التقرير
class ReportFilter {
  final String id;
  final String column;
  final String operator;
  final dynamic value;
  final String dataType;

  const ReportFilter({
    required this.id,
    required this.column,
    required this.operator,
    required this.value,
    required this.dataType,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'column': column,
      'operator': operator,
      'value': value,
      'dataType': dataType,
    };
  }

  factory ReportFilter.fromJson(Map<String, dynamic> json) {
    return ReportFilter(
      id: json['id'],
      column: json['column'],
      operator: json['operator'],
      value: json['value'],
      dataType: json['dataType'],
    );
  }
}

/// إعدادات التقرير
class ReportSettings {
  final String pageSize;
  final String orientation;
  final Map<String, double> margins;
  final String headerText;
  final String footerText;
  final bool showPageNumbers;
  final String dateFormat;
  final String numberFormat;

  const ReportSettings({
    this.pageSize = 'A4',
    this.orientation = 'portrait',
    this.margins = const {
      'top': 20,
      'bottom': 20,
      'left': 20,
      'right': 20,
    },
    this.headerText = '',
    this.footerText = '',
    this.showPageNumbers = true,
    this.dateFormat = 'dd/MM/yyyy',
    this.numberFormat = '#,##0.00',
  });

  Map<String, dynamic> toJson() {
    return {
      'pageSize': pageSize,
      'orientation': orientation,
      'margins': margins,
      'headerText': headerText,
      'footerText': footerText,
      'showPageNumbers': showPageNumbers,
      'dateFormat': dateFormat,
      'numberFormat': numberFormat,
    };
  }

  factory ReportSettings.fromJson(Map<String, dynamic> json) {
    return ReportSettings(
      pageSize: json['pageSize'] ?? 'A4',
      orientation: json['orientation'] ?? 'portrait',
      margins: Map<String, double>.from(json['margins'] ?? {
        'top': 20,
        'bottom': 20,
        'left': 20,
        'right': 20,
      }),
      headerText: json['headerText'] ?? '',
      footerText: json['footerText'] ?? '',
      showPageNumbers: json['showPageNumbers'] ?? true,
      dateFormat: json['dateFormat'] ?? 'dd/MM/yyyy',
      numberFormat: json['numberFormat'] ?? '#,##0.00',
    );
  }
}
