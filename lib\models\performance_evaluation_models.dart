/// نماذج نظام تقييم الأداء
/// تحتوي على جميع النماذج المتعلقة بتقييم أداء الموظفين
library;

/// نموذج معايير التقييم
class EvaluationCriteria {
  final int? id;
  final String name;
  final String description;
  final String category;
  final double weight; // الوزن النسبي للمعيار (0-1)
  final int maxScore; // أقصى درجة للمعيار
  final String evaluationType; // numeric, rating, text
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;

  const EvaluationCriteria({
    this.id,
    required this.name,
    required this.description,
    required this.category,
    required this.weight,
    this.maxScore = 5,
    this.evaluationType = 'rating',
    this.isActive = true,
    required this.createdAt,
    required this.updatedAt,
  });

  factory EvaluationCriteria.fromMap(Map<String, dynamic> map) {
    return EvaluationCriteria(
      id: map['id'] as int?,
      name: map['name'] as String,
      description: map['description'] as String,
      category: map['category'] as String,
      weight: (map['weight'] as num).toDouble(),
      maxScore: map['max_score'] as int? ?? 5,
      evaluationType: map['evaluation_type'] as String? ?? 'rating',
      isActive: (map['is_active'] as int?) == 1,
      createdAt: DateTime.parse(map['created_at'] as String),
      updatedAt: DateTime.parse(map['updated_at'] as String),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      if (id != null) 'id': id,
      'name': name,
      'description': description,
      'category': category,
      'weight': weight,
      'max_score': maxScore,
      'evaluation_type': evaluationType,
      'is_active': isActive ? 1 : 0,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  EvaluationCriteria copyWith({
    int? id,
    String? name,
    String? description,
    String? category,
    double? weight,
    int? maxScore,
    String? evaluationType,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return EvaluationCriteria(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      category: category ?? this.category,
      weight: weight ?? this.weight,
      maxScore: maxScore ?? this.maxScore,
      evaluationType: evaluationType ?? this.evaluationType,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}

/// نموذج دورة التقييم
class EvaluationCycle {
  final int? id;
  final String name;
  final String description;
  final DateTime startDate;
  final DateTime endDate;
  final String status; // draft, active, completed, cancelled
  final String evaluationType; // annual, quarterly, monthly, project_based
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;

  const EvaluationCycle({
    this.id,
    required this.name,
    required this.description,
    required this.startDate,
    required this.endDate,
    this.status = 'draft',
    this.evaluationType = 'annual',
    this.isActive = true,
    required this.createdAt,
    required this.updatedAt,
  });

  factory EvaluationCycle.fromMap(Map<String, dynamic> map) {
    return EvaluationCycle(
      id: map['id'] as int?,
      name: map['name'] as String,
      description: map['description'] as String,
      startDate: DateTime.parse(map['start_date'] as String),
      endDate: DateTime.parse(map['end_date'] as String),
      status: map['status'] as String? ?? 'draft',
      evaluationType: map['evaluation_type'] as String? ?? 'annual',
      isActive: (map['is_active'] as int?) == 1,
      createdAt: DateTime.parse(map['created_at'] as String),
      updatedAt: DateTime.parse(map['updated_at'] as String),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      if (id != null) 'id': id,
      'name': name,
      'description': description,
      'start_date': startDate.toIso8601String().split('T')[0],
      'end_date': endDate.toIso8601String().split('T')[0],
      'status': status,
      'evaluation_type': evaluationType,
      'is_active': isActive ? 1 : 0,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  EvaluationCycle copyWith({
    int? id,
    String? name,
    String? description,
    DateTime? startDate,
    DateTime? endDate,
    String? status,
    String? evaluationType,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return EvaluationCycle(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      status: status ?? this.status,
      evaluationType: evaluationType ?? this.evaluationType,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  // خصائص مساعدة
  bool get isDraft => status == 'draft';
  bool get isActiveStatus => status == 'active';
  bool get isCompleted => status == 'completed';
  bool get isCancelled => status == 'cancelled';
  
  Duration get duration => endDate.difference(startDate);
  int get durationInDays => duration.inDays;
  bool get isOverdue => DateTime.now().isAfter(endDate) && !isCompleted;
}

/// نموذج تقييم الموظف
class EmployeeEvaluation {
  final int? id;
  final int employeeId;
  final int cycleId;
  final int? evaluatorId; // المقيم (المدير أو HR)
  final String status; // draft, submitted, reviewed, approved, rejected
  final double? overallScore;
  final String? overallRating; // excellent, good, satisfactory, needs_improvement, unsatisfactory
  final String? strengths;
  final String? weaknesses;
  final String? developmentAreas;
  final String? goals;
  final String? comments;
  final DateTime? submittedAt;
  final DateTime? reviewedAt;
  final DateTime createdAt;
  final DateTime updatedAt;

  const EmployeeEvaluation({
    this.id,
    required this.employeeId,
    required this.cycleId,
    this.evaluatorId,
    this.status = 'draft',
    this.overallScore,
    this.overallRating,
    this.strengths,
    this.weaknesses,
    this.developmentAreas,
    this.goals,
    this.comments,
    this.submittedAt,
    this.reviewedAt,
    required this.createdAt,
    required this.updatedAt,
  });

  factory EmployeeEvaluation.fromMap(Map<String, dynamic> map) {
    return EmployeeEvaluation(
      id: map['id'] as int?,
      employeeId: map['employee_id'] as int,
      cycleId: map['cycle_id'] as int,
      evaluatorId: map['evaluator_id'] as int?,
      status: map['status'] as String? ?? 'draft',
      overallScore: (map['overall_score'] as num?)?.toDouble(),
      overallRating: map['overall_rating'] as String?,
      strengths: map['strengths'] as String?,
      weaknesses: map['weaknesses'] as String?,
      developmentAreas: map['development_areas'] as String?,
      goals: map['goals'] as String?,
      comments: map['comments'] as String?,
      submittedAt: map['submitted_at'] != null
          ? DateTime.parse(map['submitted_at'] as String)
          : null,
      reviewedAt: map['reviewed_at'] != null
          ? DateTime.parse(map['reviewed_at'] as String)
          : null,
      createdAt: DateTime.parse(map['created_at'] as String),
      updatedAt: DateTime.parse(map['updated_at'] as String),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      if (id != null) 'id': id,
      'employee_id': employeeId,
      'cycle_id': cycleId,
      'evaluator_id': evaluatorId,
      'status': status,
      'overall_score': overallScore,
      'overall_rating': overallRating,
      'strengths': strengths,
      'weaknesses': weaknesses,
      'development_areas': developmentAreas,
      'goals': goals,
      'comments': comments,
      'submitted_at': submittedAt?.toIso8601String(),
      'reviewed_at': reviewedAt?.toIso8601String(),
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  EmployeeEvaluation copyWith({
    int? id,
    int? employeeId,
    int? cycleId,
    int? evaluatorId,
    String? status,
    double? overallScore,
    String? overallRating,
    String? strengths,
    String? weaknesses,
    String? developmentAreas,
    String? goals,
    String? comments,
    DateTime? submittedAt,
    DateTime? reviewedAt,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return EmployeeEvaluation(
      id: id ?? this.id,
      employeeId: employeeId ?? this.employeeId,
      cycleId: cycleId ?? this.cycleId,
      evaluatorId: evaluatorId ?? this.evaluatorId,
      status: status ?? this.status,
      overallScore: overallScore ?? this.overallScore,
      overallRating: overallRating ?? this.overallRating,
      strengths: strengths ?? this.strengths,
      weaknesses: weaknesses ?? this.weaknesses,
      developmentAreas: developmentAreas ?? this.developmentAreas,
      goals: goals ?? this.goals,
      comments: comments ?? this.comments,
      submittedAt: submittedAt ?? this.submittedAt,
      reviewedAt: reviewedAt ?? this.reviewedAt,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  // خصائص مساعدة
  bool get isDraft => status == 'draft';
  bool get isSubmitted => status == 'submitted';
  bool get isReviewed => status == 'reviewed';
  bool get isApproved => status == 'approved';
  bool get isRejected => status == 'rejected';
  
  String get ratingText {
    switch (overallRating) {
      case 'excellent': return 'ممتاز';
      case 'good': return 'جيد';
      case 'satisfactory': return 'مرضي';
      case 'needs_improvement': return 'يحتاج تحسين';
      case 'unsatisfactory': return 'غير مرضي';
      default: return 'غير محدد';
    }
  }
}

/// نموذج تفاصيل التقييم لكل معيار
class EvaluationDetail {
  final int? id;
  final int evaluationId;
  final int criteriaId;
  final double score;
  final String? rating;
  final String? comments;
  final DateTime createdAt;
  final DateTime updatedAt;

  const EvaluationDetail({
    this.id,
    required this.evaluationId,
    required this.criteriaId,
    required this.score,
    this.rating,
    this.comments,
    required this.createdAt,
    required this.updatedAt,
  });

  factory EvaluationDetail.fromMap(Map<String, dynamic> map) {
    return EvaluationDetail(
      id: map['id'] as int?,
      evaluationId: map['evaluation_id'] as int,
      criteriaId: map['criteria_id'] as int,
      score: (map['score'] as num).toDouble(),
      rating: map['rating'] as String?,
      comments: map['comments'] as String?,
      createdAt: DateTime.parse(map['created_at'] as String),
      updatedAt: DateTime.parse(map['updated_at'] as String),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      if (id != null) 'id': id,
      'evaluation_id': evaluationId,
      'criteria_id': criteriaId,
      'score': score,
      'rating': rating,
      'comments': comments,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  EvaluationDetail copyWith({
    int? id,
    int? evaluationId,
    int? criteriaId,
    double? score,
    String? rating,
    String? comments,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return EvaluationDetail(
      id: id ?? this.id,
      evaluationId: evaluationId ?? this.evaluationId,
      criteriaId: criteriaId ?? this.criteriaId,
      score: score ?? this.score,
      rating: rating ?? this.rating,
      comments: comments ?? this.comments,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}
