/// خدمة التنبيهات الذكية للموارد البشرية
/// توفر تنبيهات للمواعيد المهمة والمهام المعلقة
library;

import '../database/database_helper.dart';
import '../services/logging_service.dart';
import '../services/employee_service.dart';
import '../services/loan_service.dart';
import '../services/advanced_contract_service.dart';

/// نموذج التنبيه
class HRNotification {
  final String id;
  final String title;
  final String message;
  final String type;
  final String priority;
  final DateTime createdAt;
  final Map<String, dynamic>? data;
  final bool isRead;

  const HRNotification({
    required this.id,
    required this.title,
    required this.message,
    required this.type,
    this.priority = 'medium',
    required this.createdAt,
    this.data,
    this.isRead = false,
  });

  factory HRNotification.fromMap(Map<String, dynamic> map) {
    return HRNotification(
      id: map['id'] as String,
      title: map['title'] as String,
      message: map['message'] as String,
      type: map['type'] as String,
      priority: map['priority'] as String? ?? 'medium',
      createdAt: DateTime.parse(map['created_at'] as String),
      data: map['data'] != null ? Map<String, dynamic>.from(map['data']) : null,
      isRead: (map['is_read'] as int? ?? 0) == 1,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'title': title,
      'message': message,
      'type': type,
      'priority': priority,
      'created_at': createdAt.toIso8601String(),
      'data': data,
      'is_read': isRead ? 1 : 0,
    };
  }

  HRNotification copyWith({
    String? id,
    String? title,
    String? message,
    String? type,
    String? priority,
    DateTime? createdAt,
    Map<String, dynamic>? data,
    bool? isRead,
  }) {
    return HRNotification(
      id: id ?? this.id,
      title: title ?? this.title,
      message: message ?? this.message,
      type: type ?? this.type,
      priority: priority ?? this.priority,
      createdAt: createdAt ?? this.createdAt,
      data: data ?? this.data,
      isRead: isRead ?? this.isRead,
    );
  }
}

class HRNotificationsService {
  final DatabaseHelper _databaseHelper = DatabaseHelper();
  final EmployeeService _employeeService = EmployeeService();
  final LoanService _loanService = LoanService();

  /// إنشاء جدول التنبيهات
  Future<void> _createNotificationsTable() async {
    final db = await _databaseHelper.database;
    await db.execute('''
      CREATE TABLE IF NOT EXISTS hr_notifications (
        id TEXT PRIMARY KEY,
        title TEXT NOT NULL,
        message TEXT NOT NULL,
        type TEXT NOT NULL,
        priority TEXT DEFAULT 'medium',
        created_at TEXT NOT NULL,
        data TEXT,
        is_read INTEGER DEFAULT 0
      )
    ''');
  }

  /// إضافة تنبيه جديد
  Future<void> addNotification(HRNotification notification) async {
    try {
      await _createNotificationsTable();
      final db = await _databaseHelper.database;

      await db.insert('hr_notifications', notification.toMap());

      LoggingService.info(
        'تم إضافة تنبيه جديد',
        category: 'HRNotificationsService',
        data: {
          'type': notification.type,
          'priority': notification.priority,
          'title': notification.title,
        },
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في إضافة التنبيه',
        category: 'HRNotificationsService',
        data: {'error': e.toString()},
      );
      rethrow;
    }
  }

  /// الحصول على جميع التنبيهات
  Future<List<HRNotification>> getAllNotifications({
    bool unreadOnly = false,
  }) async {
    try {
      await _createNotificationsTable();
      final db = await _databaseHelper.database;

      String whereClause = '';
      List<dynamic> whereArgs = [];

      if (unreadOnly) {
        whereClause = 'WHERE is_read = ?';
        whereArgs = [0];
      }

      final result = await db.rawQuery('''
        SELECT * FROM hr_notifications 
        $whereClause
        ORDER BY created_at DESC
      ''', whereArgs);

      return result.map((map) => HRNotification.fromMap(map)).toList();
    } catch (e) {
      LoggingService.error(
        'خطأ في الحصول على التنبيهات',
        category: 'HRNotificationsService',
        data: {'error': e.toString()},
      );
      return [];
    }
  }

  /// تحديد التنبيه كمقروء
  Future<void> markAsRead(String notificationId) async {
    try {
      await _createNotificationsTable();
      final db = await _databaseHelper.database;

      await db.update(
        'hr_notifications',
        {'is_read': 1},
        where: 'id = ?',
        whereArgs: [notificationId],
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في تحديث التنبيه',
        category: 'HRNotificationsService',
        data: {'notificationId': notificationId, 'error': e.toString()},
      );
    }
  }

  /// فحص وإنشاء التنبيهات التلقائية
  Future<void> checkAndCreateNotifications() async {
    try {
      await _checkBirthdayNotifications();
      await _checkContractExpiryNotifications();
      await _checkOverdueLoansNotifications();
      await _checkAbsenteeismNotifications();
      await _checkProbationEndNotifications();
    } catch (e) {
      LoggingService.error(
        'خطأ في فحص التنبيهات التلقائية',
        category: 'HRNotificationsService',
        data: {'error': e.toString()},
      );
    }
  }

  /// فحص أعياد الميلاد
  Future<void> _checkBirthdayNotifications() async {
    try {
      final employees = await _employeeService.getAllEmployees(
        activeOnly: true,
      );
      final today = DateTime.now();

      for (final employee in employees) {
        if (employee.dateOfBirth != null) {
          final birthday = employee.dateOfBirth!;
          final thisYearBirthday = DateTime(
            today.year,
            birthday.month,
            birthday.day,
          );

          // التحقق من عيد الميلاد اليوم أو خلال 3 أيام
          final daysDifference = thisYearBirthday.difference(today).inDays;

          if (daysDifference >= 0 && daysDifference <= 3) {
            final notification = HRNotification(
              id: 'birthday_${employee.id}_${today.year}',
              title: 'عيد ميلاد موظف',
              message: daysDifference == 0
                  ? 'عيد ميلاد ${employee.displayName} اليوم!'
                  : 'عيد ميلاد ${employee.displayName} خلال $daysDifference أيام',
              type: 'birthday',
              priority: 'low',
              createdAt: DateTime.now(),
              data: {
                'employeeId': employee.id,
                'employeeName': employee.displayName,
                'birthdayDate': thisYearBirthday.toIso8601String(),
              },
            );

            await addNotification(notification);
          }
        }
      }
    } catch (e) {
      LoggingService.error(
        'خطأ في فحص أعياد الميلاد',
        category: 'HRNotificationsService',
        data: {'error': e.toString()},
      );
    }
  }

  /// فحص انتهاء العقود
  Future<void> _checkContractExpiryNotifications() async {
    try {
      // استيراد خدمة العقود المتقدمة
      final contractService = AdvancedContractService();
      final expiringContracts = await contractService
          .getExpiringSoonContracts();

      for (final contract in expiringContracts) {
        final employee = await _employeeService.getEmployeeById(
          contract.employeeId,
        );
        if (employee != null && contract.endDate != null) {
          final daysLeft = contract.endDate!.difference(DateTime.now()).inDays;

          final notification = HRNotification(
            id: 'contract_expiry_${contract.id}',
            title: 'عقد ينتهي قريباً',
            message: 'عقد ${employee.displayName} ينتهي خلال $daysLeft يوم',
            type: 'contract_expiry',
            priority: daysLeft <= 7 ? 'high' : 'medium',
            createdAt: DateTime.now(),
            data: {
              'employeeId': employee.id,
              'employeeName': employee.displayName,
              'contractId': contract.id,
              'endDate': contract.endDate!.toIso8601String(),
              'daysLeft': daysLeft,
            },
          );

          await addNotification(notification);
        }
      }
    } catch (e) {
      LoggingService.error(
        'خطأ في فحص انتهاء العقود',
        category: 'HRNotificationsService',
        data: {'error': e.toString()},
      );
    }
  }

  /// فحص القروض المتأخرة
  Future<void> _checkOverdueLoansNotifications() async {
    try {
      final overdueLoans = await _loanService.getOverdueInstallments();

      for (final installment in overdueLoans) {
        final loan = await _loanService.getLoanById(installment.loanId);
        if (loan != null) {
          final employee = await _employeeService.getEmployeeById(
            loan.employeeId,
          );
          if (employee != null) {
            final daysPastDue = DateTime.now()
                .difference(installment.dueDate)
                .inDays;

            final notification = HRNotification(
              id: 'overdue_loan_${installment.id}',
              title: 'قسط قرض متأخر',
              message:
                  'قسط قرض ${employee.displayName} متأخر بـ $daysPastDue يوم - المبلغ: ${installment.amount.toStringAsFixed(0)} ل.س',
              type: 'overdue_loan',
              priority: daysPastDue > 30 ? 'high' : 'medium',
              createdAt: DateTime.now(),
              data: {
                'employeeId': employee.id,
                'employeeName': employee.displayName,
                'loanId': loan.id,
                'installmentId': installment.id,
                'amount': installment.amount,
                'daysPastDue': daysPastDue,
              },
            );

            await addNotification(notification);
          }
        }
      }
    } catch (e) {
      LoggingService.error(
        'خطأ في فحص القروض المتأخرة',
        category: 'HRNotificationsService',
        data: {'error': e.toString()},
      );
    }
  }

  /// فحص الغياب المتكرر
  Future<void> _checkAbsenteeismNotifications() async {
    try {
      final employeeService = EmployeeService();

      // الحصول على جميع الموظفين النشطين
      final employees = await employeeService.getAllEmployees(activeOnly: true);

      final now = DateTime.now();
      final thirtyDaysAgo = now.subtract(const Duration(days: 30));

      for (final employee in employees) {
        // حساب أيام الغياب في آخر 30 يوم
        final absentDays = await _getAbsentDaysCount(
          employee.id!,
          thirtyDaysAgo,
          now,
        );

        // إذا كان الموظف غائب أكثر من 5 أيام في الشهر الماضي
        if (absentDays >= 5) {
          await addNotification(
            HRNotification(
              id: 'absenteeism_${employee.id}_${now.millisecondsSinceEpoch}',
              title: 'تحذير: غياب متكرر',
              message:
                  'الموظف ${employee.displayName} غائب $absentDays أيام في آخر 30 يوم',
              type: 'absenteeism',
              priority: absentDays >= 10 ? 'high' : 'medium',
              data: {
                'employeeId': employee.id,
                'employeeName': employee.displayName,
                'absentDays': absentDays,
                'period': '30 days',
              },
              createdAt: now,
            ),
          );
        }

        // فحص الغياب المتتالي (3 أيام متتالية)
        final consecutiveAbsentDays = await _getConsecutiveAbsentDays(
          employee.id!,
        );
        if (consecutiveAbsentDays >= 3) {
          await addNotification(
            HRNotification(
              id: 'consecutive_absence_${employee.id}_${now.millisecondsSinceEpoch}',
              title: 'تحذير: غياب متتالي',
              message:
                  'الموظف ${employee.displayName} غائب $consecutiveAbsentDays أيام متتالية',
              type: 'consecutive_absence',
              priority: 'high',
              data: {
                'employeeId': employee.id,
                'employeeName': employee.displayName,
                'consecutiveDays': consecutiveAbsentDays,
              },
              createdAt: now,
            ),
          );
        }
      }

      LoggingService.info(
        'تم فحص الغياب المتكرر لـ ${employees.length} موظف',
        category: 'HRNotificationsService',
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في فحص الغياب المتكرر',
        category: 'HRNotificationsService',
        data: {'error': e.toString()},
      );
    }
  }

  /// فحص انتهاء فترة التجربة
  Future<void> _checkProbationEndNotifications() async {
    try {
      final db = await _databaseHelper.database;
      final employeeService = EmployeeService();

      // الحصول على العقود النشطة من جدول employee_contracts
      final result = await db.query(
        'employee_contracts',
        where: 'is_active = 1 AND probation_period_months > 0',
      );

      final now = DateTime.now();

      for (final contractMap in result) {
        final employeeId = contractMap['employee_id'] as int;
        final startDate = DateTime.parse(contractMap['start_date'] as String);
        final probationPeriodMonths =
            contractMap['probation_period_months'] as int;

        // الحصول على بيانات الموظف
        final employee = await employeeService.getEmployeeById(employeeId);
        if (employee == null) continue;

        if (probationPeriodMonths > 0) {
          // حساب تاريخ انتهاء فترة التجربة
          final probationEndDate = DateTime(
            startDate.year,
            startDate.month + probationPeriodMonths,
            startDate.day,
          );

          final daysUntilEnd = probationEndDate.difference(now).inDays;

          // تنبيه قبل 30 يوم من انتهاء فترة التجربة
          if (daysUntilEnd <= 30 && daysUntilEnd > 0) {
            await addNotification(
              HRNotification(
                id: 'probation_end_${employeeId}_${now.millisecondsSinceEpoch}',
                title: 'انتهاء فترة التجربة قريباً',
                message:
                    'فترة التجربة للموظف ${employee.displayName} ستنتهي خلال $daysUntilEnd يوم',
                type: 'probation_end',
                priority: daysUntilEnd <= 7 ? 'high' : 'medium',
                data: {
                  'employeeId': employeeId,
                  'employeeName': employee.displayName,
                  'contractId': contractMap['id'],
                  'probationEndDate': probationEndDate.toIso8601String(),
                  'daysRemaining': daysUntilEnd,
                },
                createdAt: now,
              ),
            );
          }

          // تنبيه إذا انتهت فترة التجربة ولم يتم اتخاذ إجراء
          if (daysUntilEnd <= 0 && daysUntilEnd >= -7) {
            await addNotification(
              HRNotification(
                id: 'probation_expired_${employeeId}_${now.millisecondsSinceEpoch}',
                title: 'انتهت فترة التجربة',
                message:
                    'انتهت فترة التجربة للموظف ${employee.displayName} منذ ${-daysUntilEnd} يوم - يتطلب اتخاذ إجراء',
                type: 'probation_expired',
                priority: 'high',
                data: {
                  'employeeId': employeeId,
                  'employeeName': employee.displayName,
                  'contractId': contractMap['id'],
                  'probationEndDate': probationEndDate.toIso8601String(),
                  'daysOverdue': -daysUntilEnd,
                },
                createdAt: now,
              ),
            );
          }
        }
      }

      LoggingService.info(
        'تم فحص انتهاء فترة التجربة لـ ${result.length} عقد',
        category: 'HRNotificationsService',
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في فحص انتهاء فترة التجربة',
        category: 'HRNotificationsService',
        data: {'error': e.toString()},
      );
    }
  }

  /// حذف التنبيهات القديمة (أكثر من 30 يوم)
  Future<void> cleanupOldNotifications() async {
    try {
      await _createNotificationsTable();
      final db = await _databaseHelper.database;

      final thirtyDaysAgo = DateTime.now().subtract(const Duration(days: 30));

      await db.delete(
        'hr_notifications',
        where: 'created_at < ?',
        whereArgs: [thirtyDaysAgo.toIso8601String()],
      );

      LoggingService.info(
        'تم حذف التنبيهات القديمة',
        category: 'HRNotificationsService',
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في حذف التنبيهات القديمة',
        category: 'HRNotificationsService',
        data: {'error': e.toString()},
      );
    }
  }

  /// الحصول على عدد التنبيهات غير المقروءة
  Future<int> getUnreadNotificationsCount() async {
    try {
      final unreadNotifications = await getAllNotifications(unreadOnly: true);
      return unreadNotifications.length;
    } catch (e) {
      LoggingService.error(
        'خطأ في الحصول على عدد التنبيهات غير المقروءة',
        category: 'HRNotificationsService',
        data: {'error': e.toString()},
      );
      return 0;
    }
  }

  /// حساب أيام الغياب في فترة معينة
  Future<int> _getAbsentDaysCount(
    int employeeId,
    DateTime startDate,
    DateTime endDate,
  ) async {
    try {
      final db = await _databaseHelper.database;

      final result = await db.rawQuery(
        '''
        SELECT COUNT(*) as absent_days
        FROM attendance
        WHERE employee_id = ?
        AND date BETWEEN ? AND ?
        AND status = 'absent'
        ''',
        [
          employeeId,
          startDate.toIso8601String().split('T')[0],
          endDate.toIso8601String().split('T')[0],
        ],
      );

      return (result.first['absent_days'] as int?) ?? 0;
    } catch (e) {
      LoggingService.error(
        'خطأ في حساب أيام الغياب',
        category: 'HRNotificationsService',
        data: {'employeeId': employeeId, 'error': e.toString()},
      );
      return 0;
    }
  }

  /// حساب أيام الغياب المتتالية
  Future<int> _getConsecutiveAbsentDays(int employeeId) async {
    try {
      final db = await _databaseHelper.database;

      // الحصول على آخر 30 يوم من سجلات الحضور مرتبة بالتاريخ تنازلياً
      final result = await db.query(
        'attendance',
        where: 'employee_id = ? AND date >= ?',
        whereArgs: [
          employeeId,
          DateTime.now()
              .subtract(const Duration(days: 30))
              .toIso8601String()
              .split('T')[0],
        ],
        orderBy: 'date DESC',
        limit: 30,
      );

      int consecutiveDays = 0;

      // عد الأيام المتتالية من الأحدث للأقدم
      for (final record in result) {
        if (record['status'] == 'absent') {
          consecutiveDays++;
        } else {
          break; // توقف عند أول يوم حضور
        }
      }

      return consecutiveDays;
    } catch (e) {
      LoggingService.error(
        'خطأ في حساب أيام الغياب المتتالية',
        category: 'HRNotificationsService',
        data: {'employeeId': employeeId, 'error': e.toString()},
      );
      return 0;
    }
  }
}
