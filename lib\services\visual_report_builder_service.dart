/// خدمة منشئ التقارير المرئي
/// تدير إنشاء وحفظ وتحرير القوالب المخصصة
library;

import 'dart:convert';
import 'package:sqflite/sqflite.dart';
import '../database/database_helper.dart';
import '../models/visual_report_builder.dart';

class VisualReportBuilderService {
  static final VisualReportBuilderService _instance =
      VisualReportBuilderService._internal();
  factory VisualReportBuilderService() => _instance;
  VisualReportBuilderService._internal();

  final DatabaseHelper _databaseHelper = DatabaseHelper();

  /// إنشاء جداول منشئ التقارير
  Future<void> createTables(Database db) async {
    // جدول قوالب التقارير
    await db.execute('''
      CREATE TABLE IF NOT EXISTS report_templates (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        description TEXT,
        category TEXT NOT NULL,
        elements TEXT NOT NULL,
        settings TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT,
        is_public INTEGER DEFAULT 0,
        created_by TEXT NOT NULL
      )
    ''');

    // جدول مصادر البيانات
    await db.execute('''
      CREATE TABLE IF NOT EXISTS report_data_sources (
        id TEXT PRIMARY KEY,
        type TEXT NOT NULL,
        name TEXT NOT NULL,
        query_text TEXT NOT NULL,
        parameters TEXT,
        columns TEXT,
        column_types TEXT
      )
    ''');

    // جدول التقارير المحفوظة
    await db.execute('''
      CREATE TABLE IF NOT EXISTS saved_reports (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        template_id INTEGER NOT NULL,
        name TEXT NOT NULL,
        generated_data TEXT NOT NULL,
        filters TEXT,
        created_at TEXT NOT NULL,
        FOREIGN KEY (template_id) REFERENCES report_templates (id)
      )
    ''');
  }

  /// حفظ قالب تقرير جديد
  Future<int> saveTemplate(ReportTemplate template) async {
    final db = await _databaseHelper.database;

    final templateData = {
      'name': template.name,
      'description': template.description,
      'category': template.category,
      'elements': jsonEncode(template.elements.map((e) => e.toJson()).toList()),
      'settings': jsonEncode(template.settings),
      'created_at': template.createdAt.toIso8601String(),
      'updated_at': template.updatedAt?.toIso8601String(),
      'is_public': template.isPublic ? 1 : 0,
      'created_by': template.createdBy,
    };

    if (template.id != null) {
      await db.update(
        'report_templates',
        templateData,
        where: 'id = ?',
        whereArgs: [template.id],
      );
      return template.id!;
    } else {
      return await db.insert('report_templates', templateData);
    }
  }

  /// الحصول على جميع القوالب
  Future<List<ReportTemplate>> getAllTemplates() async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'report_templates',
      orderBy: 'created_at DESC',
    );

    return maps.map((map) => _mapToTemplate(map)).toList();
  }

  /// الحصول على القوالب حسب الفئة
  Future<List<ReportTemplate>> getTemplatesByCategory(String category) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'report_templates',
      where: 'category = ?',
      whereArgs: [category],
      orderBy: 'created_at DESC',
    );

    return maps.map((map) => _mapToTemplate(map)).toList();
  }

  /// الحصول على قالب بالمعرف
  Future<ReportTemplate?> getTemplateById(int id) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'report_templates',
      where: 'id = ?',
      whereArgs: [id],
    );

    if (maps.isNotEmpty) {
      return _mapToTemplate(maps.first);
    }
    return null;
  }

  /// حذف قالب
  Future<void> deleteTemplate(int id) async {
    final db = await _databaseHelper.database;
    await db.delete('report_templates', where: 'id = ?', whereArgs: [id]);
  }

  /// إنشاء قوالب افتراضية
  Future<void> createDefaultTemplates() async {
    final defaultTemplates = [
      _createTrialBalanceTemplate(),
      _createIncomeStatementTemplate(),
      _createInventoryReportTemplate(),
      _createCustomerStatementTemplate(),
      _createSalesReportTemplate(),
    ];

    for (final template in defaultTemplates) {
      await saveTemplate(template);
    }
  }

  /// إنشاء قالب ميزان المراجعة
  ReportTemplate _createTrialBalanceTemplate() {
    return ReportTemplate(
      name: 'ميزان المراجعة',
      description: 'تقرير ميزان المراجعة الشامل',
      category: 'المحاسبة',
      elements: [
        ReportElement(
          id: 'header',
          type: ReportElementType.header,
          title: 'رأس التقرير',
          properties: {
            'text': 'ميزان المراجعة',
            'fontSize': 18,
            'alignment': 'center',
          },
        ),
        ReportElement(
          id: 'date_filter',
          type: ReportElementType.filter,
          title: 'فلتر التاريخ',
          properties: {
            'filterType': 'dateRange',
            'label': 'من تاريخ - إلى تاريخ',
          },
        ),
        ReportElement(
          id: 'trial_balance_table',
          type: ReportElementType.table,
          title: 'جدول ميزان المراجعة',
          properties: {
            'dataSource': 'accounts',
            'columns': ['account_name', 'debit', 'credit', 'balance'],
            'showTotals': true,
          },
        ),
        ReportElement(
          id: 'balance_chart',
          type: ReportElementType.chart,
          title: 'رسم بياني للأرصدة',
          properties: {
            'chartType': 'pie',
            'dataSource': 'accounts',
            'valueColumn': 'balance',
            'labelColumn': 'account_name',
          },
        ),
      ],
      createdAt: DateTime.now(),
      createdBy: 'system',
    );
  }

  /// إنشاء قالب قائمة الدخل
  ReportTemplate _createIncomeStatementTemplate() {
    return ReportTemplate(
      name: 'قائمة الدخل',
      description: 'تقرير قائمة الدخل التفصيلي',
      category: 'المحاسبة',
      elements: [
        ReportElement(
          id: 'header',
          type: ReportElementType.header,
          title: 'رأس التقرير',
          properties: {
            'text': 'قائمة الدخل',
            'fontSize': 18,
            'alignment': 'center',
          },
        ),
        ReportElement(
          id: 'revenue_section',
          type: ReportElementType.table,
          title: 'الإيرادات',
          properties: {
            'dataSource': 'accounts',
            'accountType': 'revenue',
            'showSubtotal': true,
          },
        ),
        ReportElement(
          id: 'expense_section',
          type: ReportElementType.table,
          title: 'المصروفات',
          properties: {
            'dataSource': 'accounts',
            'accountType': 'expense',
            'showSubtotal': true,
          },
        ),
        ReportElement(
          id: 'net_income',
          type: ReportElementType.calculation,
          title: 'صافي الدخل',
          properties: {
            'formula': 'revenue_total - expense_total',
            'fontSize': 16,
            'fontWeight': 'bold',
          },
        ),
      ],
      createdAt: DateTime.now(),
      createdBy: 'system',
    );
  }

  /// إنشاء قالب تقرير المخزون
  ReportTemplate _createInventoryReportTemplate() {
    return ReportTemplate(
      name: 'تقرير المخزون',
      description: 'تقرير شامل للمخزون والحركات',
      category: 'المخزون',
      elements: [
        ReportElement(
          id: 'header',
          type: ReportElementType.header,
          title: 'رأس التقرير',
          properties: {
            'text': 'تقرير المخزون',
            'fontSize': 18,
            'alignment': 'center',
          },
        ),
        ReportElement(
          id: 'inventory_table',
          type: ReportElementType.table,
          title: 'جدول المخزون',
          properties: {
            'dataSource': 'items',
            'columns': ['name', 'quantity', 'unit_price', 'total_value'],
            'showTotals': true,
          },
        ),
        ReportElement(
          id: 'inventory_chart',
          type: ReportElementType.chart,
          title: 'توزيع قيمة المخزون',
          properties: {
            'chartType': 'bar',
            'dataSource': 'items',
            'valueColumn': 'total_value',
            'labelColumn': 'name',
          },
        ),
      ],
      createdAt: DateTime.now(),
      createdBy: 'system',
    );
  }

  /// إنشاء قالب كشف حساب العميل
  ReportTemplate _createCustomerStatementTemplate() {
    return ReportTemplate(
      name: 'كشف حساب العميل',
      description: 'كشف حساب تفصيلي للعميل',
      category: 'العملاء',
      elements: [
        ReportElement(
          id: 'customer_info',
          type: ReportElementType.text,
          title: 'معلومات العميل',
          properties: {
            'dataSource': 'customers',
            'fields': ['name', 'phone', 'address'],
          },
        ),
        ReportElement(
          id: 'transactions_table',
          type: ReportElementType.table,
          title: 'المعاملات',
          properties: {
            'dataSource': 'invoices',
            'columns': ['date', 'invoice_number', 'amount', 'balance'],
            'showRunningBalance': true,
          },
        ),
      ],
      createdAt: DateTime.now(),
      createdBy: 'system',
    );
  }

  /// إنشاء قالب تقرير المبيعات
  ReportTemplate _createSalesReportTemplate() {
    return ReportTemplate(
      name: 'تقرير المبيعات',
      description: 'تقرير شامل للمبيعات والأداء',
      category: 'المبيعات',
      elements: [
        ReportElement(
          id: 'sales_summary',
          type: ReportElementType.table,
          title: 'ملخص المبيعات',
          properties: {
            'dataSource': 'invoices',
            'groupBy': 'month',
            'aggregation': 'sum',
            'valueColumn': 'total_amount',
          },
        ),
        ReportElement(
          id: 'sales_trend',
          type: ReportElementType.chart,
          title: 'اتجاه المبيعات',
          properties: {
            'chartType': 'line',
            'dataSource': 'invoices',
            'xAxis': 'date',
            'yAxis': 'total_amount',
          },
        ),
      ],
      createdAt: DateTime.now(),
      createdBy: 'system',
    );
  }

  /// تحويل البيانات إلى قالب
  ReportTemplate _mapToTemplate(Map<String, dynamic> map) {
    return ReportTemplate(
      id: map['id'],
      name: map['name'],
      description: map['description'] ?? '',
      category: map['category'],
      elements: (jsonDecode(map['elements']) as List)
          .map((e) => ReportElement.fromJson(e))
          .toList(),
      settings: map['settings'] != null ? jsonDecode(map['settings']) : {},
      createdAt: DateTime.parse(map['created_at']),
      updatedAt: map['updated_at'] != null
          ? DateTime.parse(map['updated_at'])
          : null,
      isPublic: map['is_public'] == 1,
      createdBy: map['created_by'],
    );
  }
}
