/// خدمة تذكيرات الدفعات الذكية
/// تتعامل مع إرسال التذكيرات والإشعارات للدفعات المستحقة
library;

import 'dart:async';
import '../database/database_helper.dart';
import '../models/payment_schedule.dart';
import '../models/invoice.dart';
import '../services/payment_schedule_service.dart';
import '../services/invoice_service.dart';
import '../services/logging_service.dart';
import '../constants/app_constants.dart';

/// نوع الإشعار
enum NotificationType {
  /// تذكير عادي
  reminder('reminder', 'تذكير'),

  /// تحذير
  warning('warning', 'تحذير'),

  /// عاجل
  urgent('urgent', 'عاجل'),

  /// متأخر
  overdue('overdue', 'متأخر');

  const NotificationType(this.code, this.displayName);

  final String code;
  final String displayName;

  static NotificationType fromCode(String code) {
    return NotificationType.values.firstWhere(
      (type) => type.code == code,
      orElse: () => NotificationType.reminder,
    );
  }
}

/// نموذج إشعار الدفع
class PaymentNotification {
  final int? id;
  final int scheduleId;
  final int invoiceId;
  final NotificationType type;
  final String title;
  final String message;
  final DateTime dueDate;
  final bool isRead;
  final bool isSent;
  final DateTime? sentAt;
  final DateTime createdAt;

  PaymentNotification({
    this.id,
    required this.scheduleId,
    required this.invoiceId,
    required this.type,
    required this.title,
    required this.message,
    required this.dueDate,
    this.isRead = false,
    this.isSent = false,
    this.sentAt,
    DateTime? createdAt,
  }) : createdAt = createdAt ?? DateTime.now();

  /// تحويل من Map
  factory PaymentNotification.fromMap(Map<String, dynamic> map) {
    return PaymentNotification(
      id: map['id'] as int?,
      scheduleId: map['schedule_id'] as int,
      invoiceId: map['invoice_id'] as int,
      type: NotificationType.fromCode(map['type'] as String),
      title: map['title'] as String,
      message: map['message'] as String,
      dueDate: DateTime.parse(map['due_date'] as String),
      isRead: (map['is_read'] as int) == 1,
      isSent: (map['is_sent'] as int) == 1,
      sentAt: map['sent_at'] != null
          ? DateTime.parse(map['sent_at'] as String)
          : null,
      createdAt: DateTime.parse(map['created_at'] as String),
    );
  }

  /// تحويل إلى Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'schedule_id': scheduleId,
      'invoice_id': invoiceId,
      'type': type.code,
      'title': title,
      'message': message,
      'due_date': dueDate.toIso8601String(),
      'is_read': isRead ? 1 : 0,
      'is_sent': isSent ? 1 : 0,
      'sent_at': sentAt?.toIso8601String(),
      'created_at': createdAt.toIso8601String(),
    };
  }

  /// نسخ مع تعديل
  PaymentNotification copyWith({
    int? id,
    int? scheduleId,
    int? invoiceId,
    NotificationType? type,
    String? title,
    String? message,
    DateTime? dueDate,
    bool? isRead,
    bool? isSent,
    DateTime? sentAt,
    DateTime? createdAt,
  }) {
    return PaymentNotification(
      id: id ?? this.id,
      scheduleId: scheduleId ?? this.scheduleId,
      invoiceId: invoiceId ?? this.invoiceId,
      type: type ?? this.type,
      title: title ?? this.title,
      message: message ?? this.message,
      dueDate: dueDate ?? this.dueDate,
      isRead: isRead ?? this.isRead,
      isSent: isSent ?? this.isSent,
      sentAt: sentAt ?? this.sentAt,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  /// هل الإشعار متأخر
  bool get isOverdue => DateTime.now().isAfter(dueDate);

  /// عدد الأيام حتى الاستحقاق
  int get daysUntilDue {
    final now = DateTime.now();
    if (now.isAfter(dueDate)) return 0;
    return dueDate.difference(now).inDays;
  }

  /// عدد الأيام منذ التأخير
  int get daysOverdue {
    final now = DateTime.now();
    if (now.isBefore(dueDate)) return 0;
    return now.difference(dueDate).inDays;
  }
}

/// خدمة تذكيرات الدفعات
class PaymentReminderService {
  final DatabaseHelper _databaseHelper = DatabaseHelper();
  final PaymentScheduleService _scheduleService = PaymentScheduleService();
  final InvoiceService _invoiceService = InvoiceService();

  /// إنشاء تذكير جديد
  Future<int> createReminder(PaymentNotification notification) async {
    try {
      final db = await _databaseHelper.database;

      final notificationData = notification.toMap();
      notificationData.remove('id');

      final id = await db.insert(
        AppConstants.paymentNotificationsTable,
        notificationData,
      );

      LoggingService.info(
        'تم إنشاء تذكير دفع جديد',
        category: 'PaymentReminderService',
        data: {
          'notification_id': id,
          'schedule_id': notification.scheduleId,
          'type': notification.type.code,
        },
      );

      return id;
    } catch (e) {
      LoggingService.error(
        'خطأ في إنشاء التذكير',
        category: 'PaymentReminderService',
        data: {'error': e.toString()},
      );
      rethrow;
    }
  }

  /// الحصول على جميع التذكيرات
  Future<List<PaymentNotification>> getAllNotifications({
    bool? isRead,
    NotificationType? type,
    int? limit,
    int? offset,
  }) async {
    try {
      final db = await _databaseHelper.database;

      String whereClause = '1=1';
      List<dynamic> whereArgs = [];

      if (isRead != null) {
        whereClause += ' AND is_read = ?';
        whereArgs.add(isRead ? 1 : 0);
      }

      if (type != null) {
        whereClause += ' AND type = ?';
        whereArgs.add(type.code);
      }

      final maps = await db.query(
        AppConstants.paymentNotificationsTable,
        where: whereClause,
        whereArgs: whereArgs.isNotEmpty ? whereArgs : null,
        orderBy: 'created_at DESC',
        limit: limit,
        offset: offset,
      );

      return maps.map((map) => PaymentNotification.fromMap(map)).toList();
    } catch (e) {
      LoggingService.error(
        'خطأ في الحصول على التذكيرات',
        category: 'PaymentReminderService',
        data: {'error': e.toString()},
      );
      return [];
    }
  }

  /// الحصول على التذكيرات غير المقروءة
  Future<List<PaymentNotification>> getUnreadNotifications() async {
    return await getAllNotifications(isRead: false);
  }

  /// الحصول على التذكيرات المستحقة
  Future<List<PaymentNotification>> getDueNotifications() async {
    try {
      final db = await _databaseHelper.database;
      final now = DateTime.now();

      final maps = await db.query(
        AppConstants.paymentNotificationsTable,
        where: 'due_date <= ? AND is_sent = ?',
        whereArgs: [now.toIso8601String(), 0],
        orderBy: 'due_date ASC',
      );

      return maps.map((map) => PaymentNotification.fromMap(map)).toList();
    } catch (e) {
      LoggingService.error(
        'خطأ في الحصول على التذكيرات المستحقة',
        category: 'PaymentReminderService',
        data: {'error': e.toString()},
      );
      return [];
    }
  }

  /// تحديث حالة القراءة
  Future<void> markAsRead(int notificationId) async {
    try {
      final db = await _databaseHelper.database;

      await db.update(
        AppConstants.paymentNotificationsTable,
        {'is_read': 1},
        where: 'id = ?',
        whereArgs: [notificationId],
      );

      LoggingService.info(
        'تم تحديد التذكير كمقروء',
        category: 'PaymentReminderService',
        data: {'notification_id': notificationId},
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في تحديث حالة القراءة',
        category: 'PaymentReminderService',
        data: {'notification_id': notificationId, 'error': e.toString()},
      );
      rethrow;
    }
  }

  /// تحديث حالة الإرسال
  Future<void> markAsSent(int notificationId) async {
    try {
      final db = await _databaseHelper.database;

      await db.update(
        AppConstants.paymentNotificationsTable,
        {'is_sent': 1, 'sent_at': DateTime.now().toIso8601String()},
        where: 'id = ?',
        whereArgs: [notificationId],
      );

      LoggingService.info(
        'تم تحديد التذكير كمرسل',
        category: 'PaymentReminderService',
        data: {'notification_id': notificationId},
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في تحديث حالة الإرسال',
        category: 'PaymentReminderService',
        data: {'notification_id': notificationId, 'error': e.toString()},
      );
      rethrow;
    }
  }

  /// إنشاء تذكيرات للجداول المستحقة
  Future<void> generateScheduleReminders() async {
    try {
      final upcomingSchedules = await _scheduleService.getUpcomingSchedules(
        daysAhead: 30,
      );

      for (final schedule in upcomingSchedules) {
        await _createScheduleNotifications(schedule);
      }

      LoggingService.info(
        'تم إنشاء تذكيرات الجداول',
        category: 'PaymentReminderService',
        data: {'schedules_processed': upcomingSchedules.length},
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في إنشاء تذكيرات الجداول',
        category: 'PaymentReminderService',
        data: {'error': e.toString()},
      );
    }
  }

  /// إنشاء إشعارات لجدولة محددة
  Future<void> _createScheduleNotifications(PaymentSchedule schedule) async {
    try {
      final invoice = await _invoiceService.getInvoiceById(schedule.invoiceId);
      if (invoice == null) return;

      // التحقق من وجود إشعارات سابقة
      final existingNotifications = await _getScheduleNotifications(
        schedule.id!,
      );
      if (existingNotifications.isNotEmpty) return;

      // إنشاء إشعارات حسب التذكيرات المحددة
      for (final reminder in schedule.activeReminders) {
        final reminderDate = schedule.nextPaymentDate.subtract(
          Duration(days: reminder.daysBefore),
        );

        // تحديد نوع الإشعار
        NotificationType notificationType;
        if (reminder.daysBefore <= 1) {
          notificationType = NotificationType.urgent;
        } else if (reminder.daysBefore <= 3) {
          notificationType = NotificationType.warning;
        } else {
          notificationType = NotificationType.reminder;
        }

        final notification = PaymentNotification(
          scheduleId: schedule.id!,
          invoiceId: schedule.invoiceId,
          type: notificationType,
          title: _generateNotificationTitle(schedule, reminder.daysBefore),
          message: _generateNotificationMessage(
            schedule,
            invoice,
            reminder.daysBefore,
          ),
          dueDate: reminderDate,
        );

        await createReminder(notification);
      }

      // إنشاء إشعار التأخير إذا لزم الأمر
      if (schedule.isNextPaymentDue) {
        final overdueNotification = PaymentNotification(
          scheduleId: schedule.id!,
          invoiceId: schedule.invoiceId,
          type: NotificationType.overdue,
          title: 'دفعة متأخرة - ${schedule.scheduleName}',
          message: _generateOverdueMessage(schedule, invoice),
          dueDate: schedule.nextPaymentDate,
        );

        await createReminder(overdueNotification);
      }
    } catch (e) {
      LoggingService.error(
        'خطأ في إنشاء إشعارات الجدولة',
        category: 'PaymentReminderService',
        data: {'schedule_id': schedule.id, 'error': e.toString()},
      );
    }
  }

  /// الحصول على إشعارات جدولة محددة
  Future<List<PaymentNotification>> _getScheduleNotifications(
    int scheduleId,
  ) async {
    try {
      final db = await _databaseHelper.database;

      final maps = await db.query(
        AppConstants.paymentNotificationsTable,
        where: 'schedule_id = ?',
        whereArgs: [scheduleId],
      );

      return maps.map((map) => PaymentNotification.fromMap(map)).toList();
    } catch (e) {
      LoggingService.error(
        'خطأ في الحصول على إشعارات الجدولة',
        category: 'PaymentReminderService',
        data: {'schedule_id': scheduleId, 'error': e.toString()},
      );
      return [];
    }
  }

  /// إنشاء عنوان الإشعار
  String _generateNotificationTitle(PaymentSchedule schedule, int daysBefore) {
    if (daysBefore == 0) {
      return 'دفعة مستحقة اليوم - ${schedule.scheduleName}';
    } else if (daysBefore == 1) {
      return 'دفعة مستحقة غداً - ${schedule.scheduleName}';
    } else {
      return 'دفعة مستحقة خلال $daysBefore أيام - ${schedule.scheduleName}';
    }
  }

  /// إنشاء رسالة الإشعار
  String _generateNotificationMessage(
    PaymentSchedule schedule,
    Invoice invoice,
    int daysBefore,
  ) {
    final amount = schedule.installmentAmount.toStringAsFixed(2);
    final dueDate = _formatDate(schedule.nextPaymentDate);

    String timeText;
    if (daysBefore == 0) {
      timeText = 'اليوم';
    } else if (daysBefore == 1) {
      timeText = 'غداً';
    } else {
      timeText = 'خلال $daysBefore أيام';
    }

    return 'لديك دفعة مستحقة $timeText بمبلغ $amount ل.س '
        'للفاتورة رقم ${invoice.invoiceNumber} '
        'بتاريخ الاستحقاق $dueDate';
  }

  /// إنشاء رسالة التأخير
  String _generateOverdueMessage(PaymentSchedule schedule, Invoice invoice) {
    final amount = schedule.installmentAmount.toStringAsFixed(2);
    final daysOverdue = DateTime.now()
        .difference(schedule.nextPaymentDate)
        .inDays;

    return 'دفعة متأخرة منذ $daysOverdue يوم بمبلغ $amount ل.س '
        'للفاتورة رقم ${invoice.invoiceNumber}. '
        'يرجى المتابعة في أقرب وقت ممكن.';
  }

  /// تنسيق التاريخ
  String _formatDate(DateTime date) {
    return '${date.day.toString().padLeft(2, '0')}/'
        '${date.month.toString().padLeft(2, '0')}/'
        '${date.year}';
  }

  /// معالجة التذكيرات المستحقة
  Future<void> processReminders() async {
    try {
      final dueNotifications = await getDueNotifications();

      for (final notification in dueNotifications) {
        await _sendNotification(notification);
        await markAsSent(notification.id!);
      }

      LoggingService.info(
        'تم معالجة التذكيرات المستحقة',
        category: 'PaymentReminderService',
        data: {'processed_count': dueNotifications.length},
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في معالجة التذكيرات',
        category: 'PaymentReminderService',
        data: {'error': e.toString()},
      );
    }
  }

  /// إرسال إشعار
  Future<void> _sendNotification(PaymentNotification notification) async {
    try {
      // هنا يمكن إضافة منطق إرسال الإشعارات الفعلي
      // مثل الإشعارات المحلية أو البريد الإلكتروني

      LoggingService.info(
        'تم إرسال إشعار',
        category: 'PaymentReminderService',
        data: {
          'notification_id': notification.id,
          'type': notification.type.code,
          'title': notification.title,
        },
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في إرسال الإشعار',
        category: 'PaymentReminderService',
        data: {'notification_id': notification.id, 'error': e.toString()},
      );
    }
  }

  /// حذف الإشعارات القديمة
  Future<void> cleanupOldNotifications({int daysOld = 30}) async {
    try {
      final db = await _databaseHelper.database;
      final cutoffDate = DateTime.now().subtract(Duration(days: daysOld));

      final deletedCount = await db.delete(
        AppConstants.paymentNotificationsTable,
        where: 'created_at < ? AND is_read = ?',
        whereArgs: [cutoffDate.toIso8601String(), 1],
      );

      LoggingService.info(
        'تم تنظيف الإشعارات القديمة',
        category: 'PaymentReminderService',
        data: {'deleted_count': deletedCount},
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في تنظيف الإشعارات القديمة',
        category: 'PaymentReminderService',
        data: {'error': e.toString()},
      );
    }
  }

  /// الحصول على إحصائيات التذكيرات
  Future<Map<String, dynamic>> getNotificationStatistics() async {
    try {
      final db = await _databaseHelper.database;

      // إجمالي الإشعارات
      final totalResult = await db.rawQuery(
        'SELECT COUNT(*) as total FROM ${AppConstants.paymentNotificationsTable}',
      );
      final total = totalResult.first['total'] as int;

      // الإشعارات غير المقروءة
      final unreadResult = await db.rawQuery(
        'SELECT COUNT(*) as unread FROM ${AppConstants.paymentNotificationsTable} WHERE is_read = 0',
      );
      final unread = unreadResult.first['unread'] as int;

      // الإشعارات المستحقة
      final dueResult = await db.rawQuery(
        'SELECT COUNT(*) as due FROM ${AppConstants.paymentNotificationsTable} WHERE due_date <= ? AND is_sent = 0',
        [DateTime.now().toIso8601String()],
      );
      final due = dueResult.first['due'] as int;

      // الإشعارات المتأخرة
      final overdueResult = await db.rawQuery(
        'SELECT COUNT(*) as overdue FROM ${AppConstants.paymentNotificationsTable} WHERE type = ?',
        [NotificationType.overdue.code],
      );
      final overdue = overdueResult.first['overdue'] as int;

      return {
        'total_notifications': total,
        'unread_notifications': unread,
        'due_notifications': due,
        'overdue_notifications': overdue,
      };
    } catch (e) {
      LoggingService.error(
        'خطأ في الحصول على إحصائيات التذكيرات',
        category: 'PaymentReminderService',
        data: {'error': e.toString()},
      );
      return {};
    }
  }
}
