/// شاشة تقييم الأداء
/// توفر واجهة شاملة لإدارة تقييم أداء الموظفين
library;

import 'package:flutter/material.dart';
import '../models/performance_evaluation_models.dart';
import '../models/hr_models.dart';
import '../services/performance_evaluation_service.dart';
import '../services/employee_service.dart';
import '../services/logging_service.dart';
import '../widgets/loading_widget.dart';
import '../widgets/custom_error_widget.dart' as custom_widgets;
import '../constants/revolutionary_design_colors.dart';

class PerformanceEvaluationScreen extends StatefulWidget {
  const PerformanceEvaluationScreen({super.key});

  @override
  State<PerformanceEvaluationScreen> createState() =>
      _PerformanceEvaluationScreenState();
}

class _PerformanceEvaluationScreenState
    extends State<PerformanceEvaluationScreen>
    with TickerProviderStateMixin {
  final PerformanceEvaluationService _evaluationService =
      PerformanceEvaluationService();
  final EmployeeService _employeeService = EmployeeService();

  late TabController _tabController;
  List<EvaluationCycle> _cycles = [];
  List<EmployeeEvaluation> _evaluations = [];
  List<EvaluationCriteria> _criteria = [];
  List<Employee> _employees = [];
  Map<String, dynamic> _statistics = {};

  bool _isLoading = true;
  String? _error;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 5, vsync: this);
    _loadData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final results = await Future.wait([
        _evaluationService.getAllEvaluationCycles(),
        _evaluationService.getEmployeeEvaluations(),
        _evaluationService.getAllEvaluationCriteria(),
        _evaluationService.getEvaluationStatistics(),
        _employeeService.getAllEmployees(),
      ]);

      setState(() {
        _cycles = results[0] as List<EvaluationCycle>;
        _evaluations = results[1] as List<EmployeeEvaluation>;
        _criteria = results[2] as List<EvaluationCriteria>;
        _statistics = results[3] as Map<String, dynamic>;
        _employees = results[4] as List<Employee>;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });

      LoggingService.error(
        'خطأ في تحميل بيانات تقييم الأداء',
        category: 'PerformanceEvaluationScreen',
        data: {'error': e.toString()},
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('تقييم الأداء'),
        backgroundColor: RevolutionaryColors.damascusSky,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadData,
            tooltip: 'تحديث',
          ),
          PopupMenuButton<String>(
            icon: const Icon(Icons.more_vert),
            onSelected: (value) {
              switch (value) {
                case 'add_cycle':
                  _showAddCycleDialog();
                  break;
                case 'add_criteria':
                  _showAddCriteriaDialog();
                  break;
                case 'create_defaults':
                  _createDefaultCriteria();
                  break;
                case 'export':
                  _exportEvaluationData();
                  break;
              }
            },
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'add_cycle',
                child: Text('إضافة دورة تقييم'),
              ),
              const PopupMenuItem(
                value: 'add_criteria',
                child: Text('إضافة معيار تقييم'),
              ),
              const PopupMenuItem(
                value: 'create_defaults',
                child: Text('إنشاء المعايير الافتراضية'),
              ),
              const PopupMenuItem(
                value: 'export',
                child: Text('تصدير البيانات'),
              ),
            ],
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          indicatorColor: Colors.white,
          isScrollable: true,
          tabs: const [
            Tab(icon: Icon(Icons.dashboard), text: 'نظرة عامة'),
            Tab(icon: Icon(Icons.refresh), text: 'دورات التقييم'),
            Tab(icon: Icon(Icons.assessment), text: 'التقييمات'),
            Tab(icon: Icon(Icons.rule), text: 'معايير التقييم'),
            Tab(icon: Icon(Icons.analytics), text: 'التقارير'),
          ],
        ),
      ),
      body: _buildBody(),
      floatingActionButton: _buildFloatingActionButton(),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const LoadingWidget();
    }

    if (_error != null) {
      return custom_widgets.ErrorWidget(message: _error!, onRetry: _loadData);
    }

    return TabBarView(
      controller: _tabController,
      children: [
        _buildOverviewTab(),
        _buildCyclesTab(),
        _buildEvaluationsTab(),
        _buildCriteriaTab(),
        _buildReportsTab(),
      ],
    );
  }

  Widget _buildOverviewTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildStatisticsCards(),
          const SizedBox(height: 24),
          _buildQuickActions(),
          const SizedBox(height: 24),
          _buildRecentEvaluations(),
        ],
      ),
    );
  }

  Widget _buildStatisticsCards() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'إحصائيات تقييم الأداء',
          style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 12),
        GridView.count(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisCount: 2,
          crossAxisSpacing: 12,
          mainAxisSpacing: 12,
          childAspectRatio: 1.5,
          children: [
            _buildStatCard(
              'دورات التقييم',
              '${_statistics['totalCycles'] ?? 0}',
              Icons.cycle,
              RevolutionaryColors.damascusSky,
            ),
            _buildStatCard(
              'التقييمات المكتملة',
              '${_statistics['completedEvaluations'] ?? 0}',
              Icons.check_circle,
              RevolutionaryColors.successGlow,
            ),
            _buildStatCard(
              'معدل الإكمال',
              '${_statistics['completionRate'] ?? '0.0'}%',
              Icons.trending_up,
              RevolutionaryColors.infoTurquoise,
            ),
            _buildStatCard(
              'متوسط النتائج',
              '${(_statistics['averageScore'] as double? ?? 0.0).toStringAsFixed(1)}',
              Icons.star,
              RevolutionaryColors.warningAmber,
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildStatCard(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, size: 32, color: color),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: const TextStyle(fontSize: 12),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildQuickActions() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'الإجراءات السريعة',
          style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 12),
        Wrap(
          spacing: 12,
          runSpacing: 12,
          children: [
            _buildActionCard(
              'إضافة دورة تقييم',
              Icons.add_circle,
              RevolutionaryColors.successGlow,
              _showAddCycleDialog,
            ),
            _buildActionCard(
              'إنشاء تقييم',
              Icons.assessment,
              RevolutionaryColors.infoTurquoise,
              _showCreateEvaluationDialog,
            ),
            _buildActionCard(
              'إضافة معيار',
              Icons.rule,
              RevolutionaryColors.warningAmber,
              _showAddCriteriaDialog,
            ),
            _buildActionCard(
              'تقرير الأداء',
              Icons.analytics,
              RevolutionaryColors.damascusSky,
              _generatePerformanceReport,
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildActionCard(
    String title,
    IconData icon,
    Color color,
    VoidCallback onTap,
  ) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        width: (MediaQuery.of(context).size.width - 56) / 2,
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: color.withOpacity(0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: color.withOpacity(0.3)),
        ),
        child: Row(
          children: [
            Icon(icon, color: color, size: 24),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                title,
                style: TextStyle(fontWeight: FontWeight.w600, color: color),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRecentEvaluations() {
    final recentEvaluations = _evaluations.take(5).toList();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'التقييمات الحديثة',
          style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 12),
        if (recentEvaluations.isEmpty)
          const Center(
            child: Padding(
              padding: EdgeInsets.all(32),
              child: Text(
                'لا توجد تقييمات',
                style: TextStyle(color: Colors.grey),
              ),
            ),
          )
        else
          ...recentEvaluations.map((evaluation) {
            final employee = _employees.firstWhere(
              (emp) => emp.id == evaluation.employeeId,
              orElse: () => Employee(
                employeeNumber: 'غير معروف',
                nationalId: '',
                firstName: 'غير معروف',
                lastName: '',
                fullName: 'غير معروف',
                email: '',
                phone: '',
                hireDate: DateTime.now(),
                basicSalary: 0,
                createdAt: DateTime.now(),
                updatedAt: DateTime.now(),
              ),
            );

            return Card(
              margin: const EdgeInsets.only(bottom: 8),
              child: ListTile(
                leading: CircleAvatar(
                  backgroundColor: RevolutionaryColors.damascusSky.withValues(
                    0.1,
                  ),
                  child: Text(
                    employee.firstName.isNotEmpty ? employee.firstName[0] : '؟',
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      color: RevolutionaryColors.damascusSky,
                    ),
                  ),
                ),
                title: Text('${employee.firstName} ${employee.lastName}'),
                subtitle: Text(evaluation.ratingText),
                trailing: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    if (evaluation.overallScore != null)
                      Text(
                        '${evaluation.overallScore!.toStringAsFixed(1)}%',
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          color: RevolutionaryColors.successGlow,
                        ),
                      ),
                    Text(
                      _getStatusText(evaluation.status),
                      style: TextStyle(
                        fontSize: 12,
                        color: _getStatusColor(evaluation.status),
                      ),
                    ),
                  ],
                ),
                onTap: () => _showEvaluationDetails(evaluation, employee),
              ),
            );
          }),
      ],
    );
  }

  Widget _buildCyclesTab() {
    return Column(
      children: [
        // شريط البحث والفلترة
        Container(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Expanded(
                child: TextField(
                  decoration: const InputDecoration(
                    hintText: 'البحث في دورات التقييم...',
                    prefixIcon: Icon(Icons.search),
                    border: OutlineInputBorder(),
                  ),
                  onChanged: (value) {
                    // TODO: تطبيق البحث
                  },
                ),
              ),
              const SizedBox(width: 12),
              ElevatedButton.icon(
                onPressed: _showAddCycleDialog,
                icon: const Icon(Icons.add),
                label: const Text('إضافة دورة'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: RevolutionaryColors.successGlow,
                  foregroundColor: Colors.white,
                ),
              ),
            ],
          ),
        ),
        // قائمة الدورات
        Expanded(
          child: _cycles.isEmpty
              ? const Center(
                  child: Text(
                    'لا توجد دورات تقييم',
                    style: TextStyle(color: Colors.grey),
                  ),
                )
              : ListView.builder(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  itemCount: _cycles.length,
                  itemBuilder: (context, index) {
                    final cycle = _cycles[index];
                    return _buildCycleCard(cycle);
                  },
                ),
        ),
      ],
    );
  }

  Widget _buildCycleCard(EvaluationCycle cycle) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: ExpansionTile(
        leading: Icon(
          _getCycleIcon(cycle.status),
          color: _getStatusColor(cycle.status),
        ),
        title: Text(
          cycle.name,
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        subtitle: Text(
          '${cycle.startDate.day}/${cycle.startDate.month}/${cycle.startDate.year} - ${cycle.endDate.day}/${cycle.endDate.month}/${cycle.endDate.year}',
        ),
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(cycle.description, style: const TextStyle(fontSize: 14)),
                const SizedBox(height: 12),
                Row(
                  children: [
                    Expanded(
                      child: _buildCycleDetail(
                        'النوع',
                        _getEvaluationTypeText(cycle.evaluationType),
                      ),
                    ),
                    Expanded(
                      child: _buildCycleDetail(
                        'المدة',
                        '${cycle.durationInDays} يوم',
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    Expanded(
                      child: _buildCycleDetail(
                        'الحالة',
                        _getStatusText(cycle.status),
                      ),
                    ),
                    Expanded(
                      child: _buildCycleDetail(
                        'نشط',
                        cycle.isActive ? 'نعم' : 'لا',
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    ElevatedButton.icon(
                      onPressed: () => _createEvaluationsForCycle(cycle),
                      icon: const Icon(Icons.add),
                      label: const Text('إنشاء تقييمات'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: RevolutionaryColors.successGlow,
                        foregroundColor: Colors.white,
                      ),
                    ),
                    const SizedBox(width: 12),
                    OutlinedButton.icon(
                      onPressed: () => _editCycle(cycle),
                      icon: const Icon(Icons.edit),
                      label: const Text('تعديل'),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCycleDetail(String label, String value) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[600],
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 2),
        Text(
          value,
          style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w600),
        ),
      ],
    );
  }

  Widget _buildEvaluationsTab() {
    return const Center(child: Text('تبويب التقييمات - قيد التطوير'));
  }

  Widget _buildCriteriaTab() {
    return const Center(child: Text('تبويب معايير التقييم - قيد التطوير'));
  }

  Widget _buildReportsTab() {
    return const Center(child: Text('تبويب التقارير - قيد التطوير'));
  }

  Widget? _buildFloatingActionButton() {
    switch (_tabController.index) {
      case 1: // دورات التقييم
        return FloatingActionButton(
          onPressed: _showAddCycleDialog,
          backgroundColor: RevolutionaryColors.successGlow,
          child: const Icon(Icons.add, color: Colors.white),
        );
      case 2: // التقييمات
        return FloatingActionButton(
          onPressed: _showCreateEvaluationDialog,
          backgroundColor: RevolutionaryColors.infoTurquoise,
          child: const Icon(Icons.assessment, color: Colors.white),
        );
      case 3: // معايير التقييم
        return FloatingActionButton(
          onPressed: _showAddCriteriaDialog,
          backgroundColor: RevolutionaryColors.warningAmber,
          child: const Icon(Icons.rule, color: Colors.white),
        );
      default:
        return null;
    }
  }

  // دوال مساعدة
  IconData _getCycleIcon(String status) {
    switch (status) {
      case 'active':
        return Icons.play_circle;
      case 'completed':
        return Icons.check_circle;
      case 'cancelled':
        return Icons.cancel;
      default:
        return Icons.draft;
    }
  }

  Color _getStatusColor(String status) {
    switch (status) {
      case 'active':
        return RevolutionaryColors.successGlow;
      case 'completed':
        return RevolutionaryColors.infoTurquoise;
      case 'cancelled':
        return RevolutionaryColors.errorCoral;
      case 'approved':
        return RevolutionaryColors.successGlow;
      case 'submitted':
        return RevolutionaryColors.warningAmber;
      case 'reviewed':
        return RevolutionaryColors.infoTurquoise;
      case 'rejected':
        return RevolutionaryColors.errorCoral;
      default:
        return Colors.grey;
    }
  }

  String _getStatusText(String status) {
    switch (status) {
      case 'draft':
        return 'مسودة';
      case 'active':
        return 'نشط';
      case 'completed':
        return 'مكتمل';
      case 'cancelled':
        return 'ملغي';
      case 'submitted':
        return 'مرسل';
      case 'reviewed':
        return 'تمت المراجعة';
      case 'approved':
        return 'معتمد';
      case 'rejected':
        return 'مرفوض';
      default:
        return status;
    }
  }

  String _getEvaluationTypeText(String type) {
    switch (type) {
      case 'annual':
        return 'سنوي';
      case 'quarterly':
        return 'ربع سنوي';
      case 'monthly':
        return 'شهري';
      case 'project_based':
        return 'حسب المشروع';
      default:
        return type;
    }
  }

  // دوال الأحداث
  void _showAddCycleDialog() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('حوار إضافة دورة تقييم - قيد التطوير'),
        backgroundColor: Colors.orange,
      ),
    );
  }

  void _showAddCriteriaDialog() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('حوار إضافة معيار تقييم - قيد التطوير'),
        backgroundColor: Colors.orange,
      ),
    );
  }

  void _showCreateEvaluationDialog() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('حوار إنشاء تقييم - قيد التطوير'),
        backgroundColor: Colors.orange,
      ),
    );
  }

  void _createDefaultCriteria() async {
    try {
      await _evaluationService.createDefaultCriteria();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم إنشاء معايير التقييم الافتراضية بنجاح'),
            backgroundColor: Colors.green,
          ),
        );
        _loadData();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في إنشاء المعايير: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _createEvaluationsForCycle(EvaluationCycle cycle) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('إنشاء تقييمات للدورة: ${cycle.name}'),
        backgroundColor: Colors.blue,
      ),
    );
  }

  void _editCycle(EvaluationCycle cycle) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('تعديل الدورة: ${cycle.name}'),
        backgroundColor: Colors.blue,
      ),
    );
  }

  void _generatePerformanceReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تم تحديث تقرير الأداء'),
        backgroundColor: Colors.blue,
      ),
    );
    _loadData();
  }

  void _exportEvaluationData() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تصدير بيانات التقييم - قيد التطوير'),
        backgroundColor: Colors.orange,
      ),
    );
  }

  void _showEvaluationDetails(
    EmployeeEvaluation evaluation,
    Employee employee,
  ) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('تقييم ${employee.firstName} ${employee.lastName}'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (evaluation.overallScore != null)
                _buildDetailRow(
                  'النتيجة الإجمالية',
                  '${evaluation.overallScore!.toStringAsFixed(1)}%',
                ),
              _buildDetailRow('التقدير', evaluation.ratingText),
              _buildDetailRow('الحالة', _getStatusText(evaluation.status)),
              if (evaluation.strengths != null) ...[
                const Divider(),
                const Text(
                  'نقاط القوة:',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                Text(evaluation.strengths!),
              ],
              if (evaluation.weaknesses != null) ...[
                const Divider(),
                const Text(
                  'نقاط الضعف:',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                Text(evaluation.weaknesses!),
              ],
              if (evaluation.developmentAreas != null) ...[
                const Divider(),
                const Text(
                  'مجالات التطوير:',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                Text(evaluation.developmentAreas!),
              ],
              if (evaluation.goals != null) ...[
                const Divider(),
                const Text(
                  'الأهداف:',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                Text(evaluation.goals!),
              ],
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label, style: const TextStyle(fontWeight: FontWeight.w500)),
          Text(value, style: const TextStyle(fontWeight: FontWeight.bold)),
        ],
      ),
    );
  }
}
