/// خدمة إدارة الملفات
/// توفر وظائف رفع وإدارة وحفظ الملفات للموظفين
library;

import 'dart:io';
import 'dart:typed_data';
import 'package:path/path.dart' as path;
import 'package:path_provider/path_provider.dart';
import 'package:file_picker/file_picker.dart';
import '../services/logging_service.dart';

class FileManagementService {
  static final FileManagementService _instance = FileManagementService._internal();
  factory FileManagementService() => _instance;
  FileManagementService._internal();

  /// الحصول على مجلد الوثائق الرئيسي
  Future<Directory> get _documentsDirectory async {
    final appDir = await getApplicationDocumentsDirectory();
    final documentsDir = Directory(path.join(appDir.path, 'SmartLedger', 'EmployeeDocuments'));
    
    if (!await documentsDir.exists()) {
      await documentsDir.create(recursive: true);
    }
    
    return documentsDir;
  }

  /// الحصول على مجلد وثائق موظف معين
  Future<Directory> getEmployeeDocumentsDirectory(int employeeId) async {
    final documentsDir = await _documentsDirectory;
    final employeeDir = Directory(path.join(documentsDir.path, 'Employee_$employeeId'));
    
    if (!await employeeDir.exists()) {
      await employeeDir.create(recursive: true);
    }
    
    return employeeDir;
  }

  /// رفع ملف جديد
  Future<String?> uploadFile({
    required int employeeId,
    required String documentType,
    String? customFileName,
  }) async {
    try {
      // اختيار الملف
      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['pdf', 'doc', 'docx', 'jpg', 'jpeg', 'png', 'txt'],
        allowMultiple: false,
      );

      if (result == null || result.files.isEmpty) {
        return null; // المستخدم ألغى العملية
      }

      final file = result.files.first;
      
      // التحقق من حجم الملف (أقصى 10 ميجابايت)
      if (file.size > 10 * 1024 * 1024) {
        throw Exception('حجم الملف كبير جداً. الحد الأقصى 10 ميجابايت');
      }

      // إنشاء اسم ملف فريد
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final extension = path.extension(file.name);
      final fileName = customFileName != null 
          ? '${customFileName}_$timestamp$extension'
          : '${documentType}_$timestamp$extension';

      // الحصول على مجلد الموظف
      final employeeDir = await getEmployeeDocumentsDirectory(employeeId);
      final filePath = path.join(employeeDir.path, fileName);

      // نسخ الملف
      if (file.bytes != null) {
        // للويب
        await File(filePath).writeAsBytes(file.bytes!);
      } else if (file.path != null) {
        // للموبايل/ديسكتوب
        await File(file.path!).copy(filePath);
      } else {
        throw Exception('لا يمكن الوصول لبيانات الملف');
      }

      LoggingService.info(
        'تم رفع الملف بنجاح',
        category: 'FileManagementService',
        data: {
          'employeeId': employeeId,
          'fileName': fileName,
          'fileSize': file.size,
          'documentType': documentType,
        },
      );

      return filePath;
    } catch (e) {
      LoggingService.error(
        'خطأ في رفع الملف',
        category: 'FileManagementService',
        data: {'error': e.toString()},
      );
      rethrow;
    }
  }

  /// حفظ ملف من البيانات
  Future<String> saveFileFromBytes({
    required int employeeId,
    required String fileName,
    required Uint8List bytes,
    required String documentType,
  }) async {
    try {
      // إنشاء اسم ملف فريد
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final extension = path.extension(fileName);
      final uniqueFileName = '${documentType}_$timestamp$extension';

      // الحصول على مجلد الموظف
      final employeeDir = await getEmployeeDocumentsDirectory(employeeId);
      final filePath = path.join(employeeDir.path, uniqueFileName);

      // حفظ الملف
      await File(filePath).writeAsBytes(bytes);

      LoggingService.info(
        'تم حفظ الملف بنجاح',
        category: 'FileManagementService',
        data: {
          'employeeId': employeeId,
          'fileName': uniqueFileName,
          'fileSize': bytes.length,
        },
      );

      return filePath;
    } catch (e) {
      LoggingService.error(
        'خطأ في حفظ الملف',
        category: 'FileManagementService',
        data: {'error': e.toString()},
      );
      rethrow;
    }
  }

  /// حذف ملف
  Future<bool> deleteFile(String filePath) async {
    try {
      final file = File(filePath);
      if (await file.exists()) {
        await file.delete();
        
        LoggingService.info(
          'تم حذف الملف بنجاح',
          category: 'FileManagementService',
          data: {'filePath': filePath},
        );
        
        return true;
      }
      return false;
    } catch (e) {
      LoggingService.error(
        'خطأ في حذف الملف',
        category: 'FileManagementService',
        data: {'filePath': filePath, 'error': e.toString()},
      );
      return false;
    }
  }

  /// التحقق من وجود ملف
  Future<bool> fileExists(String filePath) async {
    try {
      return await File(filePath).exists();
    } catch (e) {
      return false;
    }
  }

  /// الحصول على حجم ملف
  Future<int> getFileSize(String filePath) async {
    try {
      final file = File(filePath);
      if (await file.exists()) {
        return await file.length();
      }
      return 0;
    } catch (e) {
      return 0;
    }
  }

  /// الحصول على معلومات ملف
  Future<Map<String, dynamic>> getFileInfo(String filePath) async {
    try {
      final file = File(filePath);
      if (!await file.exists()) {
        return {'exists': false};
      }

      final stat = await file.stat();
      final fileName = path.basename(filePath);
      final extension = path.extension(filePath);

      return {
        'exists': true,
        'name': fileName,
        'extension': extension,
        'size': stat.size,
        'modified': stat.modified,
        'type': _getFileType(extension),
      };
    } catch (e) {
      LoggingService.error(
        'خطأ في الحصول على معلومات الملف',
        category: 'FileManagementService',
        data: {'filePath': filePath, 'error': e.toString()},
      );
      return {'exists': false, 'error': e.toString()};
    }
  }

  /// تحديد نوع الملف
  String _getFileType(String extension) {
    switch (extension.toLowerCase()) {
      case '.pdf':
        return 'PDF';
      case '.doc':
      case '.docx':
        return 'Word Document';
      case '.jpg':
      case '.jpeg':
        return 'JPEG Image';
      case '.png':
        return 'PNG Image';
      case '.txt':
        return 'Text File';
      default:
        return 'Unknown';
    }
  }

  /// الحصول على قائمة الملفات المدعومة
  List<String> get supportedExtensions => [
    'pdf', 'doc', 'docx', 'jpg', 'jpeg', 'png', 'txt'
  ];

  /// تنظيف الملفات القديمة (أكثر من سنة)
  Future<void> cleanupOldFiles() async {
    try {
      final documentsDir = await _documentsDirectory;
      final oneYearAgo = DateTime.now().subtract(const Duration(days: 365));

      await for (final entity in documentsDir.list(recursive: true)) {
        if (entity is File) {
          final stat = await entity.stat();
          if (stat.modified.isBefore(oneYearAgo)) {
            await entity.delete();
            LoggingService.info(
              'تم حذف ملف قديم',
              category: 'FileManagementService',
              data: {'filePath': entity.path},
            );
          }
        }
      }
    } catch (e) {
      LoggingService.error(
        'خطأ في تنظيف الملفات القديمة',
        category: 'FileManagementService',
        data: {'error': e.toString()},
      );
    }
  }

  /// الحصول على إحصائيات التخزين
  Future<Map<String, dynamic>> getStorageStatistics() async {
    try {
      final documentsDir = await _documentsDirectory;
      int totalFiles = 0;
      int totalSize = 0;
      final Map<String, int> fileTypes = {};

      await for (final entity in documentsDir.list(recursive: true)) {
        if (entity is File) {
          totalFiles++;
          final stat = await entity.stat();
          totalSize += stat.size;
          
          final extension = path.extension(entity.path).toLowerCase();
          fileTypes[extension] = (fileTypes[extension] ?? 0) + 1;
        }
      }

      return {
        'totalFiles': totalFiles,
        'totalSize': totalSize,
        'totalSizeMB': (totalSize / (1024 * 1024)).toStringAsFixed(2),
        'fileTypes': fileTypes,
      };
    } catch (e) {
      LoggingService.error(
        'خطأ في الحصول على إحصائيات التخزين',
        category: 'FileManagementService',
        data: {'error': e.toString()},
      );
      return {
        'totalFiles': 0,
        'totalSize': 0,
        'totalSizeMB': '0.00',
        'fileTypes': {},
      };
    }
  }
}
