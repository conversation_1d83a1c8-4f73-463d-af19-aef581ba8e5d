import 'package:flutter/material.dart';
import '../constants/revolutionary_design_colors.dart';
import '../models/customer.dart';
import '../models/supplier.dart';
import '../services/customer_service.dart';
import '../services/performance_service.dart';
import '../services/progressive_loading_service.dart';
import '../services/supplier_service.dart';
import '../widgets/customer_card.dart';
import '../widgets/supplier_card.dart';
import '../widgets/loading_widget.dart';
import '../widgets/progressive_list_view.dart';

class CustomersSuppliersScreen extends StatefulWidget {
  const CustomersSuppliersScreen({super.key});

  @override
  State<CustomersSuppliersScreen> createState() =>
      _CustomersSuppliersScreenState();
}

class _CustomersSuppliersScreenState extends State<CustomersSuppliersScreen>
    with TickerProviderStateMixin {
  final CustomerService _customerService = CustomerService();
  final SupplierService _supplierService = SupplierService();
  final PerformanceService _performanceService = PerformanceService();
  final ProgressiveLoadingService _progressiveLoadingService =
      ProgressiveLoadingService();
  final TextEditingController _searchController = TextEditingController();

  late TabController _tabController;

  List<Customer> _customers = [];
  List<Customer> _filteredCustomers = [];
  List<Supplier> _suppliers = [];
  List<Supplier> _filteredSuppliers = [];

  bool _isLoading = true;
  String _selectedFilter = 'all';
  bool _useProgressiveLoading = false;

  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);

    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    _loadData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _animationController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    setState(() => _isLoading = true);

    try {
      // استخدام خدمة الأداء لقياس وقت التحميل
      final customers = await _performanceService.measureOperation(
        'load_all_customers',
        () => _customerService.getAllCustomers(),
        category: 'customers',
        metadata: {'screen': 'customers_suppliers_screen'},
      );

      final suppliers = await _performanceService.measureOperation(
        'load_all_suppliers',
        () => _supplierService.getAllSuppliers(),
        category: 'suppliers',
        metadata: {'screen': 'customers_suppliers_screen'},
      );

      setState(() {
        _customers = customers;
        _filteredCustomers = customers;
        _suppliers = suppliers;
        _filteredSuppliers = suppliers;
        _isLoading = false;
      });
      _animationController.forward();

      // تسجيل إحصائيات التحميل
      _performanceService.updateMetric(
        'customers_loaded_count',
        customers.length.toDouble(),
      );
      _performanceService.updateMetric(
        'suppliers_loaded_count',
        suppliers.length.toDouble(),
      );

      // تسجيل إحصائيات العملاء النشطين
      final activeCustomers = customers.where((c) => c.isActive).length;
      final activeSuppliers = suppliers.where((s) => s.isActive).length;
      _performanceService.updateMetric(
        'active_customers_count',
        activeCustomers.toDouble(),
      );
      _performanceService.updateMetric(
        'active_suppliers_count',
        activeSuppliers.toDouble(),
      );
    } catch (e) {
      setState(() => _isLoading = false);
      _showErrorSnackBar('حدث خطأ في تحميل البيانات: $e');
    }
  }

  void _filterData() {
    setState(() {
      // فلترة العملاء
      _filteredCustomers = _customers.where((customer) {
        final matchesSearch =
            customer.name.toLowerCase().contains(
              _searchController.text.toLowerCase(),
            ) ||
            customer.code.contains(_searchController.text);

        final matchesFilter =
            _selectedFilter == 'all' ||
            (_selectedFilter == 'active' && customer.isActive) ||
            (_selectedFilter == 'inactive' && !customer.isActive) ||
            (_selectedFilter == 'debt' && customer.hasDebt) ||
            (_selectedFilter == 'credit' && customer.hasCredit);

        return matchesSearch && matchesFilter;
      }).toList();

      // فلترة الموردين
      _filteredSuppliers = _suppliers.where((supplier) {
        final matchesSearch =
            supplier.name.toLowerCase().contains(
              _searchController.text.toLowerCase(),
            ) ||
            supplier.code.contains(_searchController.text);

        final matchesFilter =
            _selectedFilter == 'all' ||
            (_selectedFilter == 'active' && supplier.isActive) ||
            (_selectedFilter == 'inactive' && !supplier.isActive) ||
            (_selectedFilter == 'debt' && supplier.hasDebt) ||
            (_selectedFilter == 'credit' && supplier.hasCredit);

        return matchesSearch && matchesFilter;
      }).toList();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('العملاء والموردين'),
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'العملاء', icon: Icon(Icons.person)),
            Tab(text: 'الموردين', icon: Icon(Icons.business)),
          ],
        ),
        actions: [
          IconButton(
            icon: Icon(
              _useProgressiveLoading ? Icons.view_stream : Icons.view_list,
            ),
            onPressed: _toggleLoadingMode,
            tooltip: _useProgressiveLoading
                ? 'التحميل العادي'
                : 'التحميل التدريجي',
          ),
          IconButton(icon: const Icon(Icons.add), onPressed: _addNew),
          IconButton(icon: const Icon(Icons.refresh), onPressed: _loadData),
        ],
      ),
      body: Column(
        children: [
          _buildSearchAndFilter(),
          _buildSummaryCards(),
          Expanded(
            child: _isLoading
                ? const LoadingWidget()
                : TabBarView(
                    controller: _tabController,
                    children: [_buildCustomersList(), _buildSuppliersList()],
                  ),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchAndFilter() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: RevolutionaryColors.backgroundCard,
        boxShadow: [
          BoxShadow(
            color: RevolutionaryColors.shadowMedium,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // شريط البحث
          TextField(
            controller: _searchController,
            decoration: InputDecoration(
              hintText: 'البحث...',
              prefixIcon: const Icon(Icons.search),
              suffixIcon: _searchController.text.isNotEmpty
                  ? IconButton(
                      icon: const Icon(Icons.clear),
                      onPressed: () {
                        _searchController.clear();
                        _filterData();
                      },
                    )
                  : null,
            ),
            onChanged: (_) => _filterData(),
          ),
          const SizedBox(height: 12),
          // فلاتر
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              children: [
                _buildFilterChip('all', 'الكل'),
                _buildFilterChip('active', 'نشط'),
                _buildFilterChip('inactive', 'غير نشط'),
                _buildFilterChip('debt', 'مدين'),
                _buildFilterChip('credit', 'دائن'),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterChip(String filter, String label) {
    final isSelected = _selectedFilter == filter;
    return Padding(
      padding: const EdgeInsets.only(left: 8),
      child: FilterChip(
        label: Text(label),
        selected: isSelected,
        onSelected: (selected) {
          setState(() {
            _selectedFilter = filter;
            _filterData();
          });
        },
        selectedColor: RevolutionaryColors.damascusSky.withValues(alpha: 0.2),
        checkmarkColor: RevolutionaryColors.damascusSky,
      ),
    );
  }

  Widget _buildSummaryCards() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          Expanded(
            child: _buildSummaryCard(
              'العملاء',
              _customers.length.toString(),
              Icons.person,
              RevolutionaryColors.damascusSky,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: _buildSummaryCard(
              'الموردين',
              _suppliers.length.toString(),
              Icons.business,
              RevolutionaryColors.syrianGold,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: _buildSummaryCard(
              'مدينين',
              _customers.where((c) => c.hasDebt).length.toString(),
              Icons.trending_up,
              RevolutionaryColors.syrianGold,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: _buildSummaryCard(
              'دائنين',
              _suppliers.where((s) => s.hasDebt).length.toString(),
              Icons.trending_down,
              RevolutionaryColors.errorCoral,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryCard(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.2)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 20),
          const SizedBox(height: 4),
          Text(
            value,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: color,
              fontWeight: FontWeight.bold,
            ),
          ),
          Text(
            title,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: RevolutionaryColors.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildCustomersList() {
    // استخدام التحميل التدريجي إذا كان مفعلاً
    if (_useProgressiveLoading) {
      return _buildProgressiveCustomersList();
    }

    // التحميل العادي
    if (_filteredCustomers.isEmpty) {
      return _buildEmptyState('لا يوجد عملاء', 'اضغط على + لإضافة عميل جديد');
    }

    return FadeTransition(
      opacity: _fadeAnimation,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: _filteredCustomers.length,
        itemBuilder: (context, index) {
          final customer = _filteredCustomers[index];
          return CustomerCard(
            customer: customer,
            onTap: () => _viewCustomerDetails(customer),
            onEdit: () => _editCustomer(customer),
            onDelete: () => _deleteCustomer(customer),
          );
        },
      ),
    );
  }

  Widget _buildSuppliersList() {
    // استخدام التحميل التدريجي إذا كان مفعلاً
    if (_useProgressiveLoading) {
      return _buildProgressiveSuppliersList();
    }

    // التحميل العادي
    if (_filteredSuppliers.isEmpty) {
      return _buildEmptyState('لا يوجد موردين', 'اضغط على + لإضافة مورد جديد');
    }

    return FadeTransition(
      opacity: _fadeAnimation,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: _filteredSuppliers.length,
        itemBuilder: (context, index) {
          final supplier = _filteredSuppliers[index];
          return SupplierCard(
            supplier: supplier,
            onTap: () => _viewSupplierDetails(supplier),
            onEdit: () => _editSupplier(supplier),
            onDelete: () => _deleteSupplier(supplier),
          );
        },
      ),
    );
  }

  Widget _buildEmptyState(String title, String subtitle) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.people_outline,
            size: 64,
            color: RevolutionaryColors.textHint,
          ),
          const SizedBox(height: 16),
          Text(
            title,
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              color: RevolutionaryColors.textHint,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            subtitle,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: RevolutionaryColors.textHint,
            ),
          ),
        ],
      ),
    );
  }

  void _addNew() {
    final isCustomerTab = _tabController.index == 0;
    if (isCustomerTab) {
      _showAddCustomerDialog();
    } else {
      _showAddSupplierDialog();
    }
  }

  void _viewCustomerDetails(Customer customer) {
    _showCustomerDetailsDialog(customer);
  }

  void _editCustomer(Customer customer) {
    _showEditCustomerDialog(customer);
  }

  void _deleteCustomer(Customer customer) {
    _showDeleteDialog('العميل', customer.name, () async {
      await _performDeleteCustomer(customer);
    });
  }

  void _viewSupplierDetails(Supplier supplier) {
    _showSupplierDetailsDialog(supplier);
  }

  void _editSupplier(Supplier supplier) {
    _showEditSupplierDialog(supplier);
  }

  void _deleteSupplier(Supplier supplier) {
    _showDeleteDialog('المورد', supplier.name, () async {
      await _performDeleteSupplier(supplier);
    });
  }

  void _showDeleteDialog(String type, String name, VoidCallback onConfirm) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('تأكيد حذف $type'),
        content: Text('هل أنت متأكد من حذف $type "$name"؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              onConfirm();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: RevolutionaryColors.errorCoral,
            ),
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }

  Future<void> _performDeleteCustomer(Customer customer) async {
    try {
      await _customerService.deleteCustomer(customer.id!);
      _showSuccessSnackBar('تم حذف العميل بنجاح');
      _loadData();
    } catch (e) {
      _showErrorSnackBar('حدث خطأ في حذف العميل: $e');
    }
  }

  Future<void> _performDeleteSupplier(Supplier supplier) async {
    try {
      await _supplierService.deleteSupplier(supplier.id!);
      _showSuccessSnackBar('تم حذف المورد بنجاح');
      _loadData();
    } catch (e) {
      _showErrorSnackBar('حدث خطأ في حذف المورد: $e');
    }
  }

  // ===============================
  // حوارات العملاء
  // ===============================

  void _showAddCustomerDialog() {
    _showCustomerDialog();
  }

  void _showEditCustomerDialog(Customer customer) {
    _showCustomerDialog(customer: customer);
  }

  void _showCustomerDetailsDialog(Customer customer) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('تفاصيل العميل: ${customer.name}'),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildDetailRow('الكود', customer.code),
              _buildDetailRow('الاسم', customer.name),
              _buildDetailRow('الهاتف', customer.phone ?? 'غير محدد'),
              _buildDetailRow('العنوان', customer.address ?? 'غير محدد'),
              _buildDetailRow(
                'الرصيد',
                '${customer.balance.toStringAsFixed(2)} ل.س',
              ),
              _buildDetailRow(
                'حد الائتمان',
                '${customer.creditLimit.toStringAsFixed(2)} ل.س',
              ),
              _buildDetailRow('الحالة', customer.isActive ? 'نشط' : 'غير نشط'),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _editCustomer(customer);
            },
            child: const Text('تعديل'),
          ),
        ],
      ),
    );
  }

  void _showCustomerDialog({Customer? customer}) {
    final isEdit = customer != null;
    final codeController = TextEditingController(text: customer?.code ?? '');
    final nameController = TextEditingController(text: customer?.name ?? '');
    final phoneController = TextEditingController(text: customer?.phone ?? '');
    final addressController = TextEditingController(
      text: customer?.address ?? '',
    );
    final balanceController = TextEditingController(
      text: customer?.balance.toString() ?? '0',
    );
    final creditLimitController = TextEditingController(
      text: customer?.creditLimit.toString() ?? '0',
    );
    bool isActive = customer?.isActive ?? true;

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          title: Text(isEdit ? 'تعديل العميل' : 'إضافة عميل جديد'),
          content: SingleChildScrollView(
            child: SizedBox(
              width: 400,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  TextField(
                    controller: codeController,
                    decoration: const InputDecoration(
                      labelText: 'كود العميل',
                      border: OutlineInputBorder(),
                    ),
                  ),
                  const SizedBox(height: 16),
                  TextField(
                    controller: nameController,
                    decoration: const InputDecoration(
                      labelText: 'اسم العميل *',
                      border: OutlineInputBorder(),
                    ),
                  ),
                  const SizedBox(height: 16),
                  TextField(
                    controller: phoneController,
                    decoration: const InputDecoration(
                      labelText: 'رقم الهاتف',
                      border: OutlineInputBorder(),
                    ),
                  ),
                  const SizedBox(height: 16),
                  TextField(
                    controller: addressController,
                    decoration: const InputDecoration(
                      labelText: 'العنوان',
                      border: OutlineInputBorder(),
                    ),
                    maxLines: 2,
                  ),
                  const SizedBox(height: 16),
                  TextField(
                    controller: balanceController,
                    decoration: const InputDecoration(
                      labelText: 'الرصيد الافتتاحي',
                      border: OutlineInputBorder(),
                    ),
                    keyboardType: TextInputType.number,
                  ),
                  const SizedBox(height: 16),
                  TextField(
                    controller: creditLimitController,
                    decoration: const InputDecoration(
                      labelText: 'حد الائتمان',
                      border: OutlineInputBorder(),
                    ),
                    keyboardType: TextInputType.number,
                  ),
                  const SizedBox(height: 16),
                  CheckboxListTile(
                    title: const Text('نشط'),
                    value: isActive,
                    onChanged: (value) =>
                        setState(() => isActive = value ?? true),
                  ),
                ],
              ),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('إلغاء'),
            ),
            ElevatedButton(
              onPressed: () => _saveCustomer(
                context,
                customer,
                codeController.text,
                nameController.text,
                phoneController.text,
                addressController.text,
                balanceController.text,
                creditLimitController.text,
                isActive,
              ),
              child: Text(isEdit ? 'تحديث' : 'حفظ'),
            ),
          ],
        ),
      ),
    );
  }

  void _showSupplierDetailsDialog(Supplier supplier) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('تفاصيل المورد: ${supplier.name}'),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildDetailRow('الكود', supplier.code),
              _buildDetailRow('الاسم', supplier.name),
              _buildDetailRow('الهاتف', supplier.phone ?? 'غير محدد'),
              _buildDetailRow(
                'البريد الإلكتروني',
                supplier.email ?? 'غير محدد',
              ),
              _buildDetailRow('العنوان', supplier.address ?? 'غير محدد'),
              _buildDetailRow(
                'الرصيد',
                '${supplier.balance.toStringAsFixed(2)} ل.س',
              ),
              _buildDetailRow('الحالة', supplier.isActive ? 'نشط' : 'غير نشط'),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _editSupplier(supplier);
            },
            child: const Text('تعديل'),
          ),
        ],
      ),
    );
  }

  // ===============================
  // حوارات الموردين
  // ===============================

  void _showAddSupplierDialog() {
    _showSupplierDialog();
  }

  void _showEditSupplierDialog(Supplier supplier) {
    _showSupplierDialog(supplier: supplier);
  }

  void _showSupplierDialog({Supplier? supplier}) {
    final isEdit = supplier != null;
    final codeController = TextEditingController(text: supplier?.code ?? '');
    final nameController = TextEditingController(text: supplier?.name ?? '');
    final phoneController = TextEditingController(text: supplier?.phone ?? '');
    final emailController = TextEditingController(text: supplier?.email ?? '');
    final addressController = TextEditingController(
      text: supplier?.address ?? '',
    );
    final balanceController = TextEditingController(
      text: supplier?.balance.toString() ?? '0',
    );
    bool isActive = supplier?.isActive ?? true;

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          title: Text(isEdit ? 'تعديل المورد' : 'إضافة مورد جديد'),
          content: SingleChildScrollView(
            child: SizedBox(
              width: 400,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  TextField(
                    controller: codeController,
                    decoration: const InputDecoration(
                      labelText: 'كود المورد',
                      border: OutlineInputBorder(),
                    ),
                  ),
                  const SizedBox(height: 16),
                  TextField(
                    controller: nameController,
                    decoration: const InputDecoration(
                      labelText: 'اسم المورد *',
                      border: OutlineInputBorder(),
                    ),
                  ),
                  const SizedBox(height: 16),
                  TextField(
                    controller: phoneController,
                    decoration: const InputDecoration(
                      labelText: 'رقم الهاتف',
                      border: OutlineInputBorder(),
                    ),
                  ),
                  const SizedBox(height: 16),
                  TextField(
                    controller: emailController,
                    decoration: const InputDecoration(
                      labelText: 'البريد الإلكتروني',
                      border: OutlineInputBorder(),
                    ),
                  ),
                  const SizedBox(height: 16),
                  TextField(
                    controller: addressController,
                    decoration: const InputDecoration(
                      labelText: 'العنوان',
                      border: OutlineInputBorder(),
                    ),
                    maxLines: 2,
                  ),
                  const SizedBox(height: 16),
                  TextField(
                    controller: balanceController,
                    decoration: const InputDecoration(
                      labelText: 'الرصيد الافتتاحي',
                      border: OutlineInputBorder(),
                    ),
                    keyboardType: TextInputType.number,
                  ),
                  const SizedBox(height: 16),
                  CheckboxListTile(
                    title: const Text('نشط'),
                    value: isActive,
                    onChanged: (value) =>
                        setState(() => isActive = value ?? true),
                  ),
                ],
              ),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('إلغاء'),
            ),
            ElevatedButton(
              onPressed: () => _saveSupplier(
                context,
                supplier,
                codeController.text,
                nameController.text,
                phoneController.text,
                emailController.text,
                addressController.text,
                balanceController.text,
                isActive,
              ),
              child: Text(isEdit ? 'تحديث' : 'حفظ'),
            ),
          ],
        ),
      ),
    );
  }

  // ===============================
  // دوال مساعدة
  // ===============================

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(child: Text(value)),
        ],
      ),
    );
  }

  Future<void> _saveCustomer(
    BuildContext context,
    Customer? existingCustomer,
    String code,
    String name,
    String phone,
    String address,
    String balance,
    String creditLimit,
    bool isActive,
  ) async {
    if (name.trim().isEmpty) {
      _showErrorSnackBar('يرجى إدخال اسم العميل');
      return;
    }

    try {
      final customer = Customer(
        id: existingCustomer?.id,
        code: code.trim().isEmpty ? '' : code.trim(),
        name: name.trim(),
        phone: phone.trim().isEmpty ? null : phone.trim(),
        address: address.trim().isEmpty ? null : address.trim(),
        balance: double.tryParse(balance) ?? 0.0,
        creditLimit: double.tryParse(creditLimit) ?? 0.0,
        currencyId: 1,
        isActive: isActive,
      );

      if (existingCustomer != null) {
        await _customerService.updateCustomer(customer);
        _showSuccessSnackBar('تم تحديث العميل بنجاح');
      } else {
        await _customerService.insertCustomer(customer);
        _showSuccessSnackBar('تم إضافة العميل بنجاح');
      }

      if (context.mounted) {
        Navigator.pop(context);
      }
      _loadData();
    } catch (e) {
      _showErrorSnackBar('حدث خطأ في حفظ العميل: $e');
    }
  }

  Future<void> _saveSupplier(
    BuildContext context,
    Supplier? existingSupplier,
    String code,
    String name,
    String phone,
    String email,
    String address,
    String balance,
    bool isActive,
  ) async {
    if (name.trim().isEmpty) {
      _showErrorSnackBar('يرجى إدخال اسم المورد');
      return;
    }

    try {
      final supplier = Supplier(
        id: existingSupplier?.id,
        code: code.trim().isEmpty ? '' : code.trim(),
        name: name.trim(),
        phone: phone.trim().isEmpty ? null : phone.trim(),
        email: email.trim().isEmpty ? null : email.trim(),
        address: address.trim().isEmpty ? null : address.trim(),
        balance: double.tryParse(balance) ?? 0.0,
        currencyId: 1,
        isActive: isActive,
      );

      if (existingSupplier != null) {
        await _supplierService.updateSupplier(supplier);
        _showSuccessSnackBar('تم تحديث المورد بنجاح');
      } else {
        await _supplierService.insertSupplier(supplier);
        _showSuccessSnackBar('تم إضافة المورد بنجاح');
      }

      if (context.mounted) {
        Navigator.pop(context);
      }
      _loadData();
    } catch (e) {
      _showErrorSnackBar('حدث خطأ في حفظ المورد: $e');
    }
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: RevolutionaryColors.successGlow,
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: RevolutionaryColors.errorCoral,
      ),
    );
  }

  /// التبديل بين التحميل العادي والتدريجي
  void _toggleLoadingMode() {
    setState(() {
      _useProgressiveLoading = !_useProgressiveLoading;
    });

    // إعادة تحميل البيانات بالطريقة الجديدة
    _loadData();

    _showSuccessSnackBar(
      _useProgressiveLoading
          ? 'تم تفعيل التحميل التدريجي'
          : 'تم تفعيل التحميل العادي',
    );
  }

  /// بناء واجهة التحميل التدريجي للعملاء
  Widget _buildProgressiveCustomersList() {
    return SearchableProgressiveListView<Map<String, dynamic>>(
      loadStreamBuilder: (searchTerm) =>
          _progressiveLoadingService.loadCustomersProgressive(
            searchTerm: searchTerm,
            isActive: _selectedFilter == 'active'
                ? true
                : _selectedFilter == 'inactive'
                ? false
                : null,
          ),
      itemBuilder: (context, customerData, index) {
        final customer = Customer.fromMap(customerData);
        return CustomerCard(
          customer: customer,
          onTap: () => _viewCustomerDetails(customer),
          onEdit: () => _editCustomer(customer),
          onDelete: () => _deleteCustomer(customer),
        );
      },
      searchHint: 'البحث في العملاء...',
      emptyMessage: 'لا يوجد عملاء',
      padding: const EdgeInsets.all(16),
    );
  }

  /// بناء واجهة التحميل التدريجي للموردين
  Widget _buildProgressiveSuppliersList() {
    return SearchableProgressiveListView<Map<String, dynamic>>(
      loadStreamBuilder: (searchTerm) =>
          _progressiveLoadingService.loadSuppliersProgressive(
            searchTerm: searchTerm,
            isActive: _selectedFilter == 'active'
                ? true
                : _selectedFilter == 'inactive'
                ? false
                : null,
          ),
      itemBuilder: (context, supplierData, index) {
        final supplier = Supplier.fromMap(supplierData);
        return SupplierCard(
          supplier: supplier,
          onTap: () => _viewSupplierDetails(supplier),
          onEdit: () => _editSupplier(supplier),
          onDelete: () => _deleteSupplier(supplier),
        );
      },
      searchHint: 'البحث في الموردين...',
      emptyMessage: 'لا يوجد موردين',
      padding: const EdgeInsets.all(16),
    );
  }
}
