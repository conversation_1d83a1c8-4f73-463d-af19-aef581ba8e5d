/// خدمة إدارة المرتجعات المتقدمة
/// توفر نظام شامل لإدارة مرتجعات المبيعات والمشتريات
library;

import '../database/database_helper.dart';
import '../models/advanced_return.dart';
import '../models/invoice_status.dart';
import '../models/journal_entry.dart';
import '../services/logging_service.dart';
import '../services/invoice_service.dart';
import '../services/journal_entry_service.dart';
import '../services/item_location_stock_service.dart';
import '../services/account_service.dart';
import '../services/settings_service.dart';
import '../services/warehouse_service.dart';
import '../constants/app_constants.dart';

class AdvancedReturnService {
  final DatabaseHelper _databaseHelper = DatabaseHelper();
  final InvoiceService _invoiceService = InvoiceService();
  final ItemLocationStockService _itemLocationStockService =
      ItemLocationStockService();
  final JournalEntryService _journalEntryService = JournalEntryService();
  final AccountService _accountService = AccountService();
  final SettingsService _settingsService = SettingsService();
  final WarehouseService _warehouseService = WarehouseService();

  /// إنشاء مرتجع جديد
  Future<int> createReturn(AdvancedReturn returnItem) async {
    try {
      final db = await _databaseHelper.database;

      // التحقق من صحة الفاتورة الأصلية
      final originalInvoice = await _invoiceService.getInvoiceById(
        returnItem.originalInvoiceId,
      );
      if (originalInvoice == null) {
        throw Exception('الفاتورة الأصلية غير موجودة');
      }

      // التحقق من أن الفاتورة مؤكدة
      if (originalInvoice.status != InvoiceStatus.confirmed) {
        throw Exception('لا يمكن إنشاء مرتجع لفاتورة غير مؤكدة');
      }

      LoggingService.info(
        'إنشاء مرتجع جديد',
        category: 'AdvancedReturnService',
        data: {
          'returnNumber': returnItem.returnNumber,
          'type': returnItem.type.name,
          'originalInvoiceId': returnItem.originalInvoiceId,
        },
      );

      return await db.transaction((txn) async {
        // إدراج المرتجع الرئيسي
        final returnData = returnItem.toMap();
        returnData.remove('id');
        final returnId = await txn.insert('advanced_returns', returnData);

        // إدراج عناصر المرتجع
        for (final item in returnItem.items) {
          final itemData = item.copyWith(returnId: returnId).toMap();
          itemData.remove('id');
          await txn.insert('advanced_return_items', itemData);
        }

        LoggingService.info(
          'تم إنشاء المرتجع بنجاح',
          category: 'AdvancedReturnService',
          data: {'returnId': returnId},
        );

        return returnId;
      });
    } catch (e) {
      LoggingService.error(
        'خطأ في إنشاء المرتجع',
        category: 'AdvancedReturnService',
        data: {'error': e.toString()},
      );
      rethrow;
    }
  }

  /// تحديث مرتجع
  Future<void> updateReturn(AdvancedReturn returnItem) async {
    try {
      final db = await _databaseHelper.database;

      await db.transaction((txn) async {
        // تحديث المرتجع الرئيسي
        final returnData = returnItem
            .copyWith(updatedAt: DateTime.now())
            .toMap();
        await txn.update(
          'advanced_returns',
          returnData,
          where: 'id = ?',
          whereArgs: [returnItem.id],
        );

        // حذف العناصر القديمة
        await txn.delete(
          'advanced_return_items',
          where: 'return_id = ?',
          whereArgs: [returnItem.id],
        );

        // إدراج العناصر الجديدة
        for (final item in returnItem.items) {
          final itemData = item.copyWith(returnId: returnItem.id!).toMap();
          itemData.remove('id');
          await txn.insert('advanced_return_items', itemData);
        }
      });

      LoggingService.info(
        'تم تحديث المرتجع',
        category: 'AdvancedReturnService',
        data: {'returnId': returnItem.id},
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في تحديث المرتجع',
        category: 'AdvancedReturnService',
        data: {'error': e.toString()},
      );
      rethrow;
    }
  }

  /// الموافقة على مرتجع
  Future<void> approveReturn(int returnId, int approvedBy) async {
    try {
      final db = await _databaseHelper.database;

      await db.update(
        'advanced_returns',
        {
          'status': ReturnStatus.approved.name,
          'approval_date': DateTime.now().toIso8601String(),
          'approved_by': approvedBy,
          'updated_at': DateTime.now().toIso8601String(),
        },
        where: 'id = ?',
        whereArgs: [returnId],
      );

      LoggingService.info(
        'تم الموافقة على المرتجع',
        category: 'AdvancedReturnService',
        data: {'returnId': returnId, 'approvedBy': approvedBy},
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في الموافقة على المرتجع',
        category: 'AdvancedReturnService',
        data: {'error': e.toString()},
      );
      rethrow;
    }
  }

  /// رفض مرتجع
  Future<void> rejectReturn(int returnId, String reason) async {
    try {
      final db = await _databaseHelper.database;

      await db.update(
        'advanced_returns',
        {
          'status': ReturnStatus.rejected.name,
          'internal_notes': reason,
          'updated_at': DateTime.now().toIso8601String(),
        },
        where: 'id = ?',
        whereArgs: [returnId],
      );

      LoggingService.info(
        'تم رفض المرتجع',
        category: 'AdvancedReturnService',
        data: {'returnId': returnId, 'reason': reason},
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في رفض المرتجع',
        category: 'AdvancedReturnService',
        data: {'error': e.toString()},
      );
      rethrow;
    }
  }

  /// معالجة مرتجع
  Future<void> processReturn(
    int returnId,
    ReturnProcessingType processingType,
    int processedBy, {
    double? processedAmount,
  }) async {
    try {
      final returnItem = await getReturnById(returnId);
      if (returnItem == null) {
        throw Exception('المرتجع غير موجود');
      }

      if (!returnItem.canBeProcessed) {
        throw Exception('لا يمكن معالجة هذا المرتجع');
      }

      final db = await _databaseHelper.database;

      await db.transaction((txn) async {
        // تحديث حالة المرتجع
        final finalProcessedAmount = processedAmount ?? returnItem.totalAmount;
        final isFullyProcessed = finalProcessedAmount >= returnItem.totalAmount;

        await txn.update(
          'advanced_returns',
          {
            'status': isFullyProcessed
                ? ReturnStatus.processed.name
                : ReturnStatus.approved.name,
            'processing_type': processingType.name,
            'processed_amount': finalProcessedAmount,
            'remaining_amount': returnItem.totalAmount - finalProcessedAmount,
            'processed_date': DateTime.now().toIso8601String(),
            'processed_by': processedBy,
            'updated_at': DateTime.now().toIso8601String(),
          },
          where: 'id = ?',
          whereArgs: [returnId],
        );

        // معالجة حسب النوع
        switch (processingType) {
          case ReturnProcessingType.refund:
            await _processRefund(returnItem, finalProcessedAmount, txn);
            break;
          case ReturnProcessingType.exchange:
            await _processExchange(returnItem, txn);
            break;
          case ReturnProcessingType.credit:
            await _processCredit(returnItem, finalProcessedAmount, txn);
            break;
          case ReturnProcessingType.repair:
            await _processRepair(returnItem, txn);
            break;
        }

        // تحديث المخزون إذا لزم الأمر
        if (processingType != ReturnProcessingType.repair) {
          await _updateInventoryForReturn(returnItem, txn);
        }
      });

      LoggingService.info(
        'تم معالجة المرتجع',
        category: 'AdvancedReturnService',
        data: {
          'returnId': returnId,
          'processingType': processingType.name,
          'processedBy': processedBy,
        },
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في معالجة المرتجع',
        category: 'AdvancedReturnService',
        data: {'error': e.toString()},
      );
      rethrow;
    }
  }

  /// الحصول على مرتجع بالمعرف
  Future<AdvancedReturn?> getReturnById(int id) async {
    try {
      final db = await _databaseHelper.database;

      final result = await db.query(
        'advanced_returns',
        where: 'id = ?',
        whereArgs: [id],
      );

      if (result.isEmpty) return null;

      final returnItem = AdvancedReturn.fromMap(result.first);

      // جلب العناصر
      final itemsResult = await db.query(
        'advanced_return_items',
        where: 'return_id = ?',
        whereArgs: [id],
      );

      final items = itemsResult
          .map((map) => AdvancedReturnItem.fromMap(map))
          .toList();

      return returnItem.copyWith(items: items);
    } catch (e) {
      LoggingService.error(
        'خطأ في جلب المرتجع',
        category: 'AdvancedReturnService',
        data: {'error': e.toString()},
      );
      return null;
    }
  }

  /// الحصول على جميع المرتجعات
  Future<List<AdvancedReturn>> getAllReturns({
    ReturnStatus? status,
    ReturnType? type,
    DateTime? fromDate,
    DateTime? toDate,
  }) async {
    try {
      final db = await _databaseHelper.database;

      String whereClause = '1=1';
      List<dynamic> whereArgs = [];

      if (status != null) {
        whereClause += ' AND status = ?';
        whereArgs.add(status.name);
      }

      if (type != null) {
        whereClause += ' AND type = ?';
        whereArgs.add(type.name);
      }

      if (fromDate != null) {
        whereClause += ' AND return_date >= ?';
        whereArgs.add(fromDate.toIso8601String());
      }

      if (toDate != null) {
        whereClause += ' AND return_date <= ?';
        whereArgs.add(toDate.toIso8601String());
      }

      final result = await db.query(
        'advanced_returns',
        where: whereClause,
        whereArgs: whereArgs,
        orderBy: 'created_at DESC',
      );

      return result.map((map) => AdvancedReturn.fromMap(map)).toList();
    } catch (e) {
      LoggingService.error(
        'خطأ في جلب المرتجعات',
        category: 'AdvancedReturnService',
        data: {'error': e.toString()},
      );
      return [];
    }
  }

  /// الحصول على المرتجعات المعلقة
  Future<List<AdvancedReturn>> getPendingReturns() async {
    return getAllReturns(status: ReturnStatus.pending);
  }

  /// الحصول على إحصائيات المرتجعات
  Future<Map<String, dynamic>> getReturnStatistics() async {
    try {
      final db = await _databaseHelper.database;

      // إجمالي المرتجعات
      final totalResult = await db.rawQuery(
        'SELECT COUNT(*) as total FROM advanced_returns',
      );
      final total = totalResult.first['total'] as int;

      // المرتجعات المعلقة
      final pendingResult = await db.rawQuery(
        'SELECT COUNT(*) as pending FROM advanced_returns WHERE status = ?',
        [ReturnStatus.pending.name],
      );
      final pending = pendingResult.first['pending'] as int;

      // المرتجعات المعالجة
      final processedResult = await db.rawQuery(
        'SELECT COUNT(*) as processed FROM advanced_returns WHERE status = ?',
        [ReturnStatus.processed.name],
      );
      final processed = processedResult.first['processed'] as int;

      // إجمالي قيمة المرتجعات
      final valueResult = await db.rawQuery(
        'SELECT SUM(total_amount) as total_value FROM advanced_returns',
      );
      final totalValue = valueResult.first['total_value'] as double? ?? 0.0;

      return {
        'total': total,
        'pending': pending,
        'processed': processed,
        'rejected': total - pending - processed,
        'totalValue': totalValue,
      };
    } catch (e) {
      LoggingService.error(
        'خطأ في جلب إحصائيات المرتجعات',
        category: 'AdvancedReturnService',
        data: {'error': e.toString()},
      );
      return {
        'total': 0,
        'pending': 0,
        'processed': 0,
        'rejected': 0,
        'totalValue': 0.0,
      };
    }
  }

  /// معالجة الاسترداد النقدي
  Future<void> _processRefund(
    AdvancedReturn returnItem,
    double amount,
    dynamic txn,
  ) async {
    try {
      // إنشاء قيد محاسبي للاسترداد
      final entryNumber = await _journalEntryService.generateEntryNumber();
      final details = <JournalEntryDetail>[];

      // حساب النقدية (مدين) - خروج نقدية
      final cashAccountId = await _getCashAccountId();
      details.add(
        JournalEntryDetail(
          journalEntryId: 0,
          accountId: cashAccountId,
          debitAmount: amount,
          creditAmount: 0.0,
          description: 'استرداد نقدي - مرتجع ${returnItem.returnNumber}',
        ),
      );

      // حساب العميل/المورد (دائن) - تقليل الرصيد
      final accountId = returnItem.type == ReturnType.salesReturn
          ? await _getCustomersAccountId()
          : await _getSuppliersAccountId();

      details.add(
        JournalEntryDetail(
          journalEntryId: 0,
          accountId: accountId,
          debitAmount: 0.0,
          creditAmount: amount,
          description: 'استرداد نقدي - مرتجع ${returnItem.returnNumber}',
        ),
      );

      final currencyId = await _getDefaultCurrencyId();
      final journalEntry = JournalEntry(
        entryNumber: entryNumber,
        entryDate: DateTime.now(),
        description: 'قيد استرداد نقدي - مرتجع ${returnItem.returnNumber}',
        type: 'return_refund',
        totalDebit: amount,
        totalCredit: amount,
        currencyId: currencyId,
        referenceType: 'advanced_return',
        referenceId: returnItem.id,
        details: details,
      );

      await _journalEntryService.insertJournalEntryWithTransaction(
        journalEntry,
        txn,
      );

      LoggingService.info(
        'تم إنشاء قيد الاسترداد النقدي',
        category: 'AdvancedReturnService',
        data: {
          'returnId': returnItem.id,
          'amount': amount,
          'entryNumber': entryNumber,
        },
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في إنشاء قيد الاسترداد النقدي',
        category: 'AdvancedReturnService',
        data: {'returnId': returnItem.id, 'error': e.toString()},
      );
      rethrow;
    }
  }

  /// معالجة الاستبدال
  Future<void> _processExchange(AdvancedReturn returnItem, dynamic txn) async {
    try {
      // إنشاء قيد محاسبي للاستبدال
      final entryNumber = await _journalEntryService.generateEntryNumber();
      final details = <JournalEntryDetail>[];

      // حساب المخزون (مدين) - استلام البضاعة المرتجعة
      final inventoryAccountId = await _getInventoryAccountId();
      details.add(
        JournalEntryDetail(
          journalEntryId: 0,
          accountId: inventoryAccountId,
          debitAmount: returnItem.totalAmount,
          creditAmount: 0.0,
          description: 'استبدال بضاعة - مرتجع ${returnItem.returnNumber}',
        ),
      );

      // حساب المبيعات/المشتريات (دائن) - عكس العملية الأصلية
      final salesAccountId = returnItem.type == ReturnType.salesReturn
          ? await _getSalesAccountId()
          : await _getPurchasesAccountId();

      details.add(
        JournalEntryDetail(
          journalEntryId: 0,
          accountId: salesAccountId,
          debitAmount: 0.0,
          creditAmount: returnItem.totalAmount,
          description: 'استبدال بضاعة - مرتجع ${returnItem.returnNumber}',
        ),
      );

      final currencyId = await _getDefaultCurrencyId();
      final journalEntry = JournalEntry(
        entryNumber: entryNumber,
        entryDate: DateTime.now(),
        description: 'قيد استبدال بضاعة - مرتجع ${returnItem.returnNumber}',
        type: 'return_exchange',
        totalDebit: returnItem.totalAmount,
        totalCredit: returnItem.totalAmount,
        currencyId: currencyId,
        referenceType: 'advanced_return',
        referenceId: returnItem.id,
        details: details,
      );

      await _journalEntryService.insertJournalEntryWithTransaction(
        journalEntry,
        txn,
      );

      LoggingService.info(
        'تم إنشاء قيد الاستبدال',
        category: 'AdvancedReturnService',
        data: {'returnId': returnItem.id, 'entryNumber': entryNumber},
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في إنشاء قيد الاستبدال',
        category: 'AdvancedReturnService',
        data: {'returnId': returnItem.id, 'error': e.toString()},
      );
      rethrow;
    }
  }

  /// معالجة الرصيد الدائن
  Future<void> _processCredit(
    AdvancedReturn returnItem,
    double amount,
    dynamic txn,
  ) async {
    try {
      // إنشاء قيد محاسبي للرصيد الدائن
      final entryNumber = await _journalEntryService.generateEntryNumber();
      final details = <JournalEntryDetail>[];

      // حساب المبيعات/المشتريات (مدين) - عكس العملية الأصلية
      final salesAccountId = returnItem.type == ReturnType.salesReturn
          ? await _getSalesAccountId()
          : await _getPurchasesAccountId();

      details.add(
        JournalEntryDetail(
          journalEntryId: 0,
          accountId: salesAccountId,
          debitAmount: amount,
          creditAmount: 0.0,
          description: 'رصيد دائن - مرتجع ${returnItem.returnNumber}',
        ),
      );

      // حساب العميل/المورد (دائن) - إضافة رصيد دائن
      final accountId = returnItem.type == ReturnType.salesReturn
          ? await _getCustomersAccountId()
          : await _getSuppliersAccountId();

      details.add(
        JournalEntryDetail(
          journalEntryId: 0,
          accountId: accountId,
          debitAmount: 0.0,
          creditAmount: amount,
          description: 'رصيد دائن - مرتجع ${returnItem.returnNumber}',
        ),
      );

      final currencyId = await _getDefaultCurrencyId();
      final journalEntry = JournalEntry(
        entryNumber: entryNumber,
        entryDate: DateTime.now(),
        description: 'قيد رصيد دائن - مرتجع ${returnItem.returnNumber}',
        type: 'return_credit',
        totalDebit: amount,
        totalCredit: amount,
        currencyId: currencyId,
        referenceType: 'advanced_return',
        referenceId: returnItem.id,
        details: details,
      );

      await _journalEntryService.insertJournalEntryWithTransaction(
        journalEntry,
        txn,
      );

      LoggingService.info(
        'تم إنشاء قيد الرصيد الدائن',
        category: 'AdvancedReturnService',
        data: {
          'returnId': returnItem.id,
          'amount': amount,
          'entryNumber': entryNumber,
        },
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في إنشاء قيد الرصيد الدائن',
        category: 'AdvancedReturnService',
        data: {'returnId': returnItem.id, 'error': e.toString()},
      );
      rethrow;
    }
  }

  /// معالجة الإصلاح
  Future<void> _processRepair(AdvancedReturn returnItem, dynamic txn) async {
    try {
      // إنشاء قيد محاسبي لتسجيل طلب الإصلاح
      final entryNumber = await _journalEntryService.generateEntryNumber();
      final details = <JournalEntryDetail>[];

      // حساب الإصلاحات تحت التنفيذ (مدين) - تسجيل قيمة البضاعة تحت الإصلاح
      final repairsAccountId = await _getRepairsInProgressAccountId();
      details.add(
        JournalEntryDetail(
          journalEntryId: 0,
          accountId: repairsAccountId,
          debitAmount: returnItem.totalAmount,
          creditAmount: 0.0,
          description: 'طلب إصلاح - مرتجع ${returnItem.returnNumber}',
        ),
      );

      // حساب العميل/المورد (دائن) - تقليل الرصيد مؤقتاً
      final accountId = returnItem.type == ReturnType.salesReturn
          ? await _getCustomersAccountId()
          : await _getSuppliersAccountId();

      details.add(
        JournalEntryDetail(
          journalEntryId: 0,
          accountId: accountId,
          debitAmount: 0.0,
          creditAmount: returnItem.totalAmount,
          description: 'طلب إصلاح - مرتجع ${returnItem.returnNumber}',
        ),
      );

      final currencyId = await _getDefaultCurrencyId();
      final journalEntry = JournalEntry(
        entryNumber: entryNumber,
        entryDate: DateTime.now(),
        description: 'قيد طلب إصلاح - مرتجع ${returnItem.returnNumber}',
        type: 'return_repair',
        totalDebit: returnItem.totalAmount,
        totalCredit: returnItem.totalAmount,
        currencyId: currencyId,
        referenceType: 'advanced_return',
        referenceId: returnItem.id,
        details: details,
      );

      await _journalEntryService.insertJournalEntryWithTransaction(
        journalEntry,
        txn,
      );

      LoggingService.info(
        'تم إنشاء قيد طلب الإصلاح',
        category: 'AdvancedReturnService',
        data: {'returnId': returnItem.id, 'entryNumber': entryNumber},
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في إنشاء قيد طلب الإصلاح',
        category: 'AdvancedReturnService',
        data: {'returnId': returnItem.id, 'error': e.toString()},
      );
      rethrow;
    }
  }

  /// تحديث المخزون للمرتجع
  Future<void> _updateInventoryForReturn(
    AdvancedReturn returnItem,
    dynamic txn,
  ) async {
    try {
      // الحصول على معلومات المستودع والموقع من الفاتورة الأصلية
      final warehouseInfo = await _getWarehouseAndLocationFromInvoice(
        returnItem.originalInvoiceId,
      );

      // معالجة كل عنصر في المرتجع
      for (final item in returnItem.items) {
        if (item.isProcessed) {
          // تحديد نوع الحركة حسب نوع المرتجع
          if (returnItem.type == ReturnType.salesReturn) {
            // مرتجع مبيعات: إضافة الكمية للمخزون
            await _itemLocationStockService.addItemToLocation(
              itemId: item.itemId,
              warehouseId: warehouseInfo['warehouseId']!,
              locationId: warehouseInfo['locationId']!,
              quantity: item.returnQuantity,
              unitCost: item.unitPrice,
              description: 'مرتجع مبيعات - ${returnItem.returnNumber}',
            );
          } else if (returnItem.type == ReturnType.purchaseReturn) {
            // مرتجع مشتريات: خصم الكمية من المخزون
            await _itemLocationStockService.subtractItemFromLocation(
              itemId: item.itemId,
              locationId: warehouseInfo['locationId']!,
              quantity: item.returnQuantity,
              description: 'مرتجع مشتريات - ${returnItem.returnNumber}',
            );
          }

          LoggingService.info(
            'تم تحديث المخزون لعنصر المرتجع',
            category: 'AdvancedReturnService',
            data: {
              'returnId': returnItem.id,
              'itemId': item.itemId,
              'quantity': item.returnQuantity,
              'type': returnItem.type.name,
            },
          );
        }
      }
    } catch (e) {
      LoggingService.error(
        'خطأ في تحديث المخزون للمرتجع',
        category: 'AdvancedReturnService',
        data: {'returnId': returnItem.id, 'error': e.toString()},
      );
      rethrow;
    }
  }

  // ===============================
  // الطرق المساعدة
  // ===============================

  /// الحصول على حساب النقدية الافتراضي
  Future<int> _getCashAccountId() async {
    try {
      final cashAccounts = await _accountService.getAccountsByType(
        AppConstants.accountTypeAsset,
      );

      // البحث عن حساب النقدية (عادة يحتوي على كلمة "نقد" أو "صندوق")
      final cashAccount = cashAccounts.firstWhere(
        (account) =>
            account.name.contains('نقد') ||
            account.name.contains('صندوق') ||
            account.code.startsWith('1001'),
        orElse: () => cashAccounts.isNotEmpty
            ? cashAccounts.first
            : throw Exception('لا يوجد حساب نقدية'),
      );

      return cashAccount.id!;
    } catch (e) {
      LoggingService.error(
        'خطأ في الحصول على حساب النقدية',
        category: 'AdvancedReturnService',
        data: {'error': e.toString()},
      );
      return 1; // حساب افتراضي
    }
  }

  /// الحصول على حساب العملاء الافتراضي
  Future<int> _getCustomersAccountId() async {
    try {
      final assetAccounts = await _accountService.getAccountsByType(
        AppConstants.accountTypeAsset,
      );

      // البحث عن حساب العملاء
      final customersAccount = assetAccounts.firstWhere(
        (account) =>
            account.name.contains('عملاء') ||
            account.name.contains('مدينون') ||
            account.code.startsWith('1201'),
        orElse: () => assetAccounts.isNotEmpty
            ? assetAccounts.first
            : throw Exception('لا يوجد حساب عملاء'),
      );

      return customersAccount.id!;
    } catch (e) {
      LoggingService.error(
        'خطأ في الحصول على حساب العملاء',
        category: 'AdvancedReturnService',
        data: {'error': e.toString()},
      );
      return 2; // حساب افتراضي
    }
  }

  /// الحصول على حساب الموردين الافتراضي
  Future<int> _getSuppliersAccountId() async {
    try {
      final liabilityAccounts = await _accountService.getAccountsByType(
        AppConstants.accountTypeLiability,
      );

      // البحث عن حساب الموردين
      final suppliersAccount = liabilityAccounts.firstWhere(
        (account) =>
            account.name.contains('موردين') ||
            account.name.contains('دائنون') ||
            account.code.startsWith('2201'),
        orElse: () => liabilityAccounts.isNotEmpty
            ? liabilityAccounts.first
            : throw Exception('لا يوجد حساب موردين'),
      );

      return suppliersAccount.id!;
    } catch (e) {
      LoggingService.error(
        'خطأ في الحصول على حساب الموردين',
        category: 'AdvancedReturnService',
        data: {'error': e.toString()},
      );
      return 3; // حساب افتراضي
    }
  }

  /// الحصول على حساب المخزون الافتراضي
  Future<int> _getInventoryAccountId() async {
    try {
      final inventoryAccounts = await _accountService.getAccountsByType(
        AppConstants.accountTypeInventory,
      );

      // البحث عن حساب المخزون
      final inventoryAccount = inventoryAccounts.firstWhere(
        (account) =>
            account.name.contains('مخزون') ||
            account.name.contains('بضاعة') ||
            account.code.startsWith('1301'),
        orElse: () => inventoryAccounts.isNotEmpty
            ? inventoryAccounts.first
            : throw Exception('لا يوجد حساب مخزون'),
      );

      return inventoryAccount.id!;
    } catch (e) {
      LoggingService.error(
        'خطأ في الحصول على حساب المخزون',
        category: 'AdvancedReturnService',
        data: {'error': e.toString()},
      );
      return 4; // حساب افتراضي
    }
  }

  /// الحصول على حساب المبيعات الافتراضي
  Future<int> _getSalesAccountId() async {
    try {
      final salesAccounts = await _accountService.getAccountsByType(
        AppConstants.accountTypeSale,
      );

      // البحث عن حساب المبيعات
      final salesAccount = salesAccounts.firstWhere(
        (account) =>
            account.name.contains('مبيعات') || account.code.startsWith('4001'),
        orElse: () => salesAccounts.isNotEmpty
            ? salesAccounts.first
            : throw Exception('لا يوجد حساب مبيعات'),
      );

      return salesAccount.id!;
    } catch (e) {
      LoggingService.error(
        'خطأ في الحصول على حساب المبيعات',
        category: 'AdvancedReturnService',
        data: {'error': e.toString()},
      );
      return 5; // حساب افتراضي
    }
  }

  /// الحصول على حساب المشتريات الافتراضي
  Future<int> _getPurchasesAccountId() async {
    try {
      final purchaseAccounts = await _accountService.getAccountsByType(
        AppConstants.accountTypePurchase,
      );

      // البحث عن حساب المشتريات
      final purchaseAccount = purchaseAccounts.firstWhere(
        (account) =>
            account.name.contains('مشتريات') || account.code.startsWith('5001'),
        orElse: () => purchaseAccounts.isNotEmpty
            ? purchaseAccounts.first
            : throw Exception('لا يوجد حساب مشتريات'),
      );

      return purchaseAccount.id!;
    } catch (e) {
      LoggingService.error(
        'خطأ في الحصول على حساب المشتريات',
        category: 'AdvancedReturnService',
        data: {'error': e.toString()},
      );
      return 6; // حساب افتراضي
    }
  }

  /// الحصول على حساب الإصلاحات تحت التنفيذ
  Future<int> _getRepairsInProgressAccountId() async {
    try {
      final assetAccounts = await _accountService.getAccountsByType(
        AppConstants.accountTypeAsset,
      );

      // البحث عن حساب الإصلاحات تحت التنفيذ
      final repairsAccount = assetAccounts.firstWhere(
        (account) =>
            account.name.contains('إصلاح') ||
            account.name.contains('صيانة') ||
            account.code.startsWith('1401'),
        orElse: () => assetAccounts.isNotEmpty
            ? assetAccounts.first
            : throw Exception('لا يوجد حساب إصلاحات'),
      );

      return repairsAccount.id!;
    } catch (e) {
      LoggingService.error(
        'خطأ في الحصول على حساب الإصلاحات',
        category: 'AdvancedReturnService',
        data: {'error': e.toString()},
      );
      return 7; // حساب افتراضي
    }
  }

  /// الحصول على معرف العملة الافتراضية
  Future<int> _getDefaultCurrencyId() async {
    try {
      // الحصول على رمز العملة الافتراضية من الإعدادات
      final defaultCurrencyCode = await _settingsService.getDefaultCurrency();

      // البحث عن معرف العملة في قاعدة البيانات
      final db = await _databaseHelper.database;
      final result = await db.query(
        'currencies',
        columns: ['id'],
        where: 'code = ?',
        whereArgs: [defaultCurrencyCode],
        limit: 1,
      );

      if (result.isNotEmpty) {
        return result.first['id'] as int;
      }

      // إذا لم توجد العملة، إرجاع العملة الافتراضية (الليرة السورية)
      return 1;
    } catch (e) {
      LoggingService.error(
        'خطأ في الحصول على العملة الافتراضية',
        category: 'AdvancedReturnService',
        data: {'error': e.toString()},
      );
      return 1; // العملة الافتراضية
    }
  }

  /// الحصول على معلومات المستودع والموقع من الفاتورة الأصلية
  Future<Map<String, int>> _getWarehouseAndLocationFromInvoice(
    int invoiceId,
  ) async {
    try {
      final invoice = await _invoiceService.getInvoiceById(invoiceId);
      if (invoice != null && invoice.items.isNotEmpty) {
        // يمكن إضافة منطق للحصول على المستودع والموقع من عناصر الفاتورة
        // حالياً سنستخدم المستودع الافتراضي
        final defaultWarehouse = await _warehouseService.getDefaultWarehouse();
        if (defaultWarehouse != null) {
          return {
            'warehouseId': defaultWarehouse.id!,
            'locationId': 1, // الموقع الافتراضي في المستودع
          };
        }
      }

      return {
        'warehouseId': 1, // المستودع الافتراضي
        'locationId': 1, // الموقع الافتراضي
      };
    } catch (e) {
      LoggingService.error(
        'خطأ في الحصول على معلومات المستودع',
        category: 'AdvancedReturnService',
        data: {'error': e.toString()},
      );
      return {'warehouseId': 1, 'locationId': 1};
    }
  }
}
