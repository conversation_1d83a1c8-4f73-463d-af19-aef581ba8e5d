/// مولد أيقونة التطبيق
/// ينشئ أيقونة احترافية لتطبيق Smart Ledger
library;

import 'package:flutter/material.dart';
import '../constants/revolutionary_design_colors.dart';

class AppIconGenerator {
  /// إنشاء أيقونة التطبيق
  static Widget createAppIcon({double size = 512, bool showBackground = true}) {
    return CustomPaint(
      size: Si<PERSON>(size, size),
      painter: AppIconPainter(showBackground: showBackground),
    );
  }

  /// إنشاء أيقونة مبسطة للاستخدام في الواجهات
  static Widget createSimpleIcon({double size = 48, Color? color}) {
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            color ?? RevolutionaryColors.damascusSky,
            (color ?? RevolutionaryColors.damascusSky).withValues(alpha: 0.8),
          ],
        ),
        borderRadius: BorderRadius.circular(size * 0.2),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.2),
            blurRadius: size * 0.1,
            offset: Offset(0, size * 0.05),
          ),
        ],
      ),
      child: Icon(
        Icons.account_balance_wallet,
        size: size * 0.6,
        color: Colors.white,
      ),
    );
  }
}

class AppIconPainter extends CustomPainter {
  final bool showBackground;

  AppIconPainter({this.showBackground = true});

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width / 2;

    // الخلفية الرئيسية
    if (showBackground) {
      _drawBackground(canvas, size, center, radius);
    }

    // الرمز الرئيسي
    _drawMainSymbol(canvas, size, center);

    // العناصر الزخرفية
    _drawDecorativeElements(canvas, size, center);

    // النص (اختياري)
    _drawText(canvas, size, center);
  }

  /// رسم الخلفية
  void _drawBackground(Canvas canvas, Size size, Offset center, double radius) {
    // الخلفية المتدرجة
    final backgroundPaint = Paint()
      ..shader = RadialGradient(
        colors: [
          RevolutionaryColors.damascusSky,
          RevolutionaryColors.damascusSky.withValues(alpha: 0.9),
          const Color(0xFF1A365D),
        ],
        stops: const [0.0, 0.7, 1.0],
      ).createShader(Rect.fromCircle(center: center, radius: radius));

    canvas.drawCircle(center, radius, backgroundPaint);

    // حدود ذهبية
    final borderPaint = Paint()
      ..color = RevolutionaryColors.syrianGold
      ..style = PaintingStyle.stroke
      ..strokeWidth = size.width * 0.02;

    canvas.drawCircle(center, radius - size.width * 0.01, borderPaint);
  }

  /// رسم الرمز الرئيسي
  void _drawMainSymbol(Canvas canvas, Size size, Offset center) {
    final symbolSize = size.width * 0.4;
    final symbolRect = Rect.fromCenter(
      center: center,
      width: symbolSize,
      height: symbolSize,
    );

    // رسم دفتر الحسابات
    _drawLedgerBook(canvas, symbolRect);

    // رسم رموز المال
    _drawMoneySymbols(canvas, size, center);
  }

  /// رسم دفتر الحسابات
  void _drawLedgerBook(Canvas canvas, Rect rect) {
    final bookPaint = Paint()
      ..color = Colors.white
      ..style = PaintingStyle.fill;

    final shadowPaint = Paint()
      ..color = Colors.black.withValues(alpha: 0.3)
      ..style = PaintingStyle.fill;

    // ظل الكتاب
    final shadowRect = rect.translate(rect.width * 0.05, rect.height * 0.05);
    canvas.drawRRect(
      RRect.fromRectAndRadius(shadowRect, Radius.circular(rect.width * 0.1)),
      shadowPaint,
    );

    // الكتاب الرئيسي
    canvas.drawRRect(
      RRect.fromRectAndRadius(rect, Radius.circular(rect.width * 0.1)),
      bookPaint,
    );

    // خطوط الصفحات
    final linePaint = Paint()
      ..color = RevolutionaryColors.damascusSky.withValues(alpha: 0.3)
      ..strokeWidth = 2;

    for (int i = 1; i <= 4; i++) {
      final y = rect.top + (rect.height * i / 5);
      canvas.drawLine(
        Offset(rect.left + rect.width * 0.1, y),
        Offset(rect.right - rect.width * 0.1, y),
        linePaint,
      );
    }

    // خط عمودي (هامش)
    canvas.drawLine(
      Offset(rect.left + rect.width * 0.25, rect.top + rect.height * 0.1),
      Offset(rect.left + rect.width * 0.25, rect.bottom - rect.height * 0.1),
      linePaint,
    );
  }

  /// رسم رموز المال
  void _drawMoneySymbols(Canvas canvas, Size size, Offset center) {
    final coinPaint = Paint()
      ..color = RevolutionaryColors.syrianGold
      ..style = PaintingStyle.fill;

    final coinRadius = size.width * 0.08;

    // عملات ذهبية حول الرمز الرئيسي
    final positions = [
      Offset(center.dx - size.width * 0.25, center.dy - size.width * 0.15),
      Offset(center.dx + size.width * 0.25, center.dy - size.width * 0.15),
      Offset(center.dx + size.width * 0.3, center.dy + size.width * 0.1),
      Offset(center.dx - size.width * 0.3, center.dy + size.width * 0.1),
    ];

    for (final position in positions) {
      // العملة الذهبية
      canvas.drawCircle(position, coinRadius, coinPaint);

      // رمز الليرة السورية
      final textPainter = TextPainter(
        text: TextSpan(
          text: 'ل.س',
          style: TextStyle(
            color: Colors.white,
            fontSize: coinRadius * 0.8,
            fontWeight: FontWeight.bold,
          ),
        ),
        textDirection: TextDirection.rtl,
      );
      textPainter.layout();
      textPainter.paint(
        canvas,
        Offset(
          position.dx - textPainter.width / 2,
          position.dy - textPainter.height / 2,
        ),
      );
    }
  }

  /// رسم العناصر الزخرفية
  void _drawDecorativeElements(Canvas canvas, Size size, Offset center) {
    final decorPaint = Paint()
      ..color = RevolutionaryColors.syrianGold.withValues(alpha: 0.6)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2;

    // دوائر زخرفية
    for (int i = 1; i <= 3; i++) {
      final radius = size.width * (0.35 + i * 0.05);
      canvas.drawCircle(center, radius, decorPaint);
    }

    // نجوم صغيرة
    _drawStars(canvas, size, center);
  }

  /// رسم النجوم
  void _drawStars(Canvas canvas, Size size, Offset center) {
    final starPaint = Paint()
      ..color = RevolutionaryColors.syrianGold.withValues(alpha: 0.8)
      ..style = PaintingStyle.fill;

    final starPositions = [
      Offset(center.dx, center.dy - size.width * 0.4),
      Offset(center.dx + size.width * 0.35, center.dy - size.width * 0.2),
      Offset(center.dx + size.width * 0.35, center.dy + size.width * 0.2),
      Offset(center.dx, center.dy + size.width * 0.4),
      Offset(center.dx - size.width * 0.35, center.dy + size.width * 0.2),
      Offset(center.dx - size.width * 0.35, center.dy - size.width * 0.2),
    ];

    for (final position in starPositions) {
      _drawStar(canvas, position, size.width * 0.03, starPaint);
    }
  }

  /// رسم نجمة واحدة
  void _drawStar(Canvas canvas, Offset center, double radius, Paint paint) {
    final path = Path();
    const numPoints = 5;

    for (int i = 0; i < numPoints; i++) {
      final x =
          center.dx +
          radius *
              0.5 *
              (i % 2 == 0 ? 1 : 0.5) *
              (i == 0
                  ? 0
                  : (i % 2 == 0
                        ? (i ~/ 2).isEven
                              ? 1
                              : -1
                        : (i ~/ 2).isEven
                        ? 0.7
                        : -0.7));
      final y =
          center.dy +
          radius *
              0.5 *
              (i % 2 == 0 ? 1 : 0.5) *
              (i == 0
                  ? -1
                  : (i % 2 == 0
                        ? (i ~/ 2).isEven
                              ? -0.3
                              : 0.8
                        : (i ~/ 2).isEven
                        ? -0.9
                        : 0.9));

      if (i == 0) {
        path.moveTo(x, y);
      } else {
        path.lineTo(x, y);
      }
    }
    path.close();
    canvas.drawPath(path, paint);
  }

  /// رسم النص
  void _drawText(Canvas canvas, Size size, Offset center) {
    // النص العربي
    final textPainter = TextPainter(
      text: TextSpan(
        text: 'Smart Ledger',
        style: TextStyle(
          color: Colors.white,
          fontSize: size.width * 0.08,
          fontWeight: FontWeight.bold,
          shadows: [
            Shadow(
              color: Colors.black.withValues(alpha: 0.5),
              blurRadius: 4,
              offset: const Offset(2, 2),
            ),
          ],
        ),
      ),
      textDirection: TextDirection.ltr,
    );

    textPainter.layout();
    textPainter.paint(
      canvas,
      Offset(center.dx - textPainter.width / 2, center.dy + size.width * 0.3),
    );

    // النص العربي
    final arabicTextPainter = TextPainter(
      text: TextSpan(
        text: 'دفتر الحسابات الذكي',
        style: TextStyle(
          color: RevolutionaryColors.syrianGold,
          fontSize: size.width * 0.05,
          fontWeight: FontWeight.w600,
        ),
      ),
      textDirection: TextDirection.rtl,
    );

    arabicTextPainter.layout();
    arabicTextPainter.paint(
      canvas,
      Offset(
        center.dx - arabicTextPainter.width / 2,
        center.dy + size.width * 0.38,
      ),
    );
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
