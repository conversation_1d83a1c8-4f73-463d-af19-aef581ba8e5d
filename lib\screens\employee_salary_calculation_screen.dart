/// شاشة حساب راتب الموظف
/// حساب تفصيلي لراتب موظف معين مع إمكانية التعديل والحفظ
library;

import 'package:flutter/material.dart';
import '../constants/revolutionary_design_colors.dart';
import '../models/hr_models.dart';
import '../services/payroll_service.dart';
import '../services/logging_service.dart';
import '../widgets/loading_widget.dart';

class EmployeeSalaryCalculationScreen extends StatefulWidget {
  final Employee employee;
  final int? month;
  final int? year;

  const EmployeeSalaryCalculationScreen({
    super.key,
    required this.employee,
    this.month,
    this.year,
  });

  @override
  State<EmployeeSalaryCalculationScreen> createState() =>
      _EmployeeSalaryCalculationScreenState();
}

class _EmployeeSalaryCalculationScreenState
    extends State<EmployeeSalaryCalculationScreen> {
  final PayrollService _payrollService = PayrollService();
  final _formKey = GlobalKey<FormState>();

  // Controllers
  final _basicSalaryController = TextEditingController();
  final _allowancesController = TextEditingController();
  final _overtimeHoursController = TextEditingController();
  final _bonusController = TextEditingController();
  final _deductionsController = TextEditingController();
  final _notesController = TextEditingController();

  // Data
  late int _selectedMonth;
  late int _selectedYear;
  PayrollRecord? _existingRecord;
  bool _isLoading = false;
  bool _isCalculating = false;

  // Calculated values
  double _grossSalary = 0;
  double _taxAmount = 0;
  double _insuranceAmount = 0;
  double _loanDeductions = 0;
  double _netSalary = 0;

  @override
  void initState() {
    super.initState();
    _selectedMonth = widget.month ?? DateTime.now().month;
    _selectedYear = widget.year ?? DateTime.now().year;
    _initializeControllers();
    _loadExistingRecord();
  }

  void _initializeControllers() {
    _basicSalaryController.text = widget.employee.basicSalary.toString();
    _allowancesController.text = '0';
    _overtimeHoursController.text = '0';
    _bonusController.text = '0';
    _deductionsController.text = '0';
  }

  @override
  void dispose() {
    _basicSalaryController.dispose();
    _allowancesController.dispose();
    _overtimeHoursController.dispose();
    _bonusController.dispose();
    _deductionsController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('حساب راتب: ${widget.employee.displayName}'),
        backgroundColor: RevolutionaryColors.damascusSky,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.calculate),
            onPressed: _calculateSalary,
            tooltip: 'حساب الراتب',
          ),
          if (_existingRecord != null)
            IconButton(
              icon: const Icon(Icons.save),
              onPressed: _saveSalary,
              tooltip: 'حفظ الراتب',
            ),
        ],
      ),
      body: _isLoading
          ? const LoadingWidget(message: 'جاري تحميل البيانات...')
          : Form(
              key: _formKey,
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildEmployeeInfoCard(),
                    const SizedBox(height: 16),
                    _buildPeriodSelectionCard(),
                    const SizedBox(height: 16),
                    _buildSalaryInputCard(),
                    const SizedBox(height: 16),
                    _buildCalculationResultCard(),
                    const SizedBox(height: 24),
                    _buildActionButtons(),
                  ],
                ),
              ),
            ),
    );
  }

  Widget _buildEmployeeInfoCard() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            CircleAvatar(
              radius: 30,
              backgroundColor: RevolutionaryColors.damascusSky.withValues(
                alpha: 0.1,
              ),
              backgroundImage: widget.employee.photoPath != null
                  ? AssetImage(widget.employee.photoPath!)
                  : null,
              child: widget.employee.photoPath == null
                  ? Icon(
                      Icons.person,
                      size: 30,
                      color: RevolutionaryColors.damascusSky,
                    )
                  : null,
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    widget.employee.displayName,
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'رقم الموظف: ${widget.employee.employeeNumber}',
                    style: TextStyle(fontSize: 14, color: Colors.grey[600]),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'الراتب الأساسي: ${widget.employee.basicSalary.toStringAsFixed(0)} ل.س',
                    style: TextStyle(fontSize: 14, color: Colors.grey[600]),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPeriodSelectionCard() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.calendar_month,
                  color: RevolutionaryColors.damascusSky,
                ),
                const SizedBox(width: 8),
                const Text(
                  'فترة الراتب',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: DropdownButtonFormField<int>(
                    value: _selectedMonth,
                    decoration: const InputDecoration(
                      labelText: 'الشهر',
                      border: OutlineInputBorder(),
                    ),
                    items: List.generate(12, (index) {
                      final month = index + 1;
                      return DropdownMenuItem(
                        value: month,
                        child: Text(_getMonthName(month)),
                      );
                    }),
                    onChanged: (value) {
                      setState(() {
                        _selectedMonth = value!;
                      });
                      _loadExistingRecord();
                    },
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: DropdownButtonFormField<int>(
                    value: _selectedYear,
                    decoration: const InputDecoration(
                      labelText: 'السنة',
                      border: OutlineInputBorder(),
                    ),
                    items: List.generate(5, (index) {
                      final year = DateTime.now().year - 2 + index;
                      return DropdownMenuItem(
                        value: year,
                        child: Text(year.toString()),
                      );
                    }),
                    onChanged: (value) {
                      setState(() {
                        _selectedYear = value!;
                      });
                      _loadExistingRecord();
                    },
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSalaryInputCard() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.edit, color: RevolutionaryColors.damascusSky),
                const SizedBox(width: 8),
                const Text(
                  'بيانات الراتب',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: TextFormField(
                    controller: _basicSalaryController,
                    decoration: const InputDecoration(
                      labelText: 'الراتب الأساسي',
                      border: OutlineInputBorder(),
                      suffixText: 'ل.س',
                    ),
                    keyboardType: TextInputType.number,
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'مطلوب';
                      }
                      if (double.tryParse(value) == null) {
                        return 'رقم غير صحيح';
                      }
                      return null;
                    },
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: TextFormField(
                    controller: _allowancesController,
                    decoration: const InputDecoration(
                      labelText: 'البدلات',
                      border: OutlineInputBorder(),
                      suffixText: 'ل.س',
                    ),
                    keyboardType: TextInputType.number,
                    validator: (value) {
                      if (value != null && value.isNotEmpty) {
                        if (double.tryParse(value) == null) {
                          return 'رقم غير صحيح';
                        }
                      }
                      return null;
                    },
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: TextFormField(
                    controller: _overtimeHoursController,
                    decoration: const InputDecoration(
                      labelText: 'ساعات إضافية',
                      border: OutlineInputBorder(),
                      suffixText: 'ساعة',
                    ),
                    keyboardType: TextInputType.number,
                    validator: (value) {
                      if (value != null && value.isNotEmpty) {
                        if (double.tryParse(value) == null) {
                          return 'رقم غير صحيح';
                        }
                      }
                      return null;
                    },
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: TextFormField(
                    controller: _bonusController,
                    decoration: const InputDecoration(
                      labelText: 'المكافآت',
                      border: OutlineInputBorder(),
                      suffixText: 'ل.س',
                    ),
                    keyboardType: TextInputType.number,
                    validator: (value) {
                      if (value != null && value.isNotEmpty) {
                        if (double.tryParse(value) == null) {
                          return 'رقم غير صحيح';
                        }
                      }
                      return null;
                    },
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _deductionsController,
              decoration: const InputDecoration(
                labelText: 'خصومات أخرى',
                border: OutlineInputBorder(),
                suffixText: 'ل.س',
              ),
              keyboardType: TextInputType.number,
              validator: (value) {
                if (value != null && value.isNotEmpty) {
                  if (double.tryParse(value) == null) {
                    return 'رقم غير صحيح';
                  }
                }
                return null;
              },
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _notesController,
              decoration: const InputDecoration(
                labelText: 'ملاحظات',
                border: OutlineInputBorder(),
              ),
              maxLines: 2,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCalculationResultCard() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.calculate, color: RevolutionaryColors.damascusSky),
                const SizedBox(width: 8),
                const Text(
                  'نتيجة الحساب',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildCalculationRow(
              'الراتب الإجمالي',
              _grossSalary,
              RevolutionaryColors.infoTurquoise,
            ),
            _buildCalculationRow(
              'ضريبة الدخل',
              _taxAmount,
              RevolutionaryColors.warningAmber,
            ),
            _buildCalculationRow(
              'الضمان الاجتماعي',
              _insuranceAmount,
              RevolutionaryColors.warningAmber,
            ),
            _buildCalculationRow(
              'خصم القروض',
              _loanDeductions,
              RevolutionaryColors.errorCoral,
            ),
            const Divider(thickness: 2),
            _buildCalculationRow(
              'الراتب الصافي',
              _netSalary,
              RevolutionaryColors.successGlow,
              isTotal: true,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCalculationRow(
    String label,
    double amount,
    Color color, {
    bool isTotal = false,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: isTotal ? 16 : 14,
              fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
            ),
          ),
          Text(
            '${amount.toStringAsFixed(0)} ل.س',
            style: TextStyle(
              fontSize: isTotal ? 16 : 14,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons() {
    return Row(
      children: [
        Expanded(
          child: OutlinedButton.icon(
            onPressed: _isCalculating ? null : _calculateSalary,
            icon: _isCalculating
                ? const SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : const Icon(Icons.calculate),
            label: Text(_isCalculating ? 'جاري الحساب...' : 'حساب الراتب'),
            style: OutlinedButton.styleFrom(
              padding: const EdgeInsets.all(16),
              side: BorderSide(color: RevolutionaryColors.damascusSky),
            ),
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: ElevatedButton.icon(
            onPressed: (_existingRecord != null && !_isCalculating)
                ? _saveSalary
                : null,
            icon: const Icon(Icons.save),
            label: const Text('حفظ الراتب'),
            style: ElevatedButton.styleFrom(
              backgroundColor: RevolutionaryColors.successGlow,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.all(16),
            ),
          ),
        ),
      ],
    );
  }

  String _getMonthName(int month) {
    const months = [
      'يناير',
      'فبراير',
      'مارس',
      'أبريل',
      'مايو',
      'يونيو',
      'يوليو',
      'أغسطس',
      'سبتمبر',
      'أكتوبر',
      'نوفمبر',
      'ديسمبر',
    ];
    return months[month - 1];
  }

  Future<void> _loadExistingRecord() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final record = await _payrollService.getPayrollRecord(
        widget.employee.id!,
        _selectedMonth,
        _selectedYear,
      );

      if (record != null) {
        setState(() {
          _existingRecord = record;
          _basicSalaryController.text = record.basicSalary.toString();
          _allowancesController.text = record.allowances.toString();
          _overtimeHoursController.text = record.overtimeHours.toString();
          _bonusController.text = record.bonuses.toString();
          _deductionsController.text = record.otherDeductions.toString();
          _notesController.text = record.notes ?? '';

          // تحديث القيم المحسوبة
          _grossSalary = record.grossSalary;
          _taxAmount = record.incomeTax;
          _insuranceAmount = record.socialInsurance;
          _loanDeductions = record.loanDeductions;
          _netSalary = record.netSalary;
        });
      } else {
        setState(() {
          _existingRecord = null;
          _resetCalculations();
        });
      }
    } catch (e) {
      LoggingService.error(
        'خطأ في تحميل سجل الراتب',
        category: 'EmployeeSalaryCalculationScreen',
        data: {
          'employeeId': widget.employee.id,
          'month': _selectedMonth,
          'year': _selectedYear,
          'error': e.toString(),
        },
      );
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _resetCalculations() {
    _grossSalary = 0;
    _taxAmount = 0;
    _insuranceAmount = 0;
    _loanDeductions = 0;
    _netSalary = 0;
  }

  Future<void> _calculateSalary() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isCalculating = true;
    });

    try {
      final record = await _payrollService.calculatePayroll(
        employeeId: widget.employee.id!,
        month: _selectedMonth,
        year: _selectedYear,
        allowances: double.tryParse(_allowancesController.text) ?? 0,
        bonuses: double.tryParse(_bonusController.text) ?? 0,
        otherDeductions: double.tryParse(_deductionsController.text) ?? 0,
        notes: _notesController.text.trim().isNotEmpty
            ? _notesController.text.trim()
            : null,
      );

      setState(() {
        _existingRecord = record;
        _grossSalary = record.grossSalary;
        _taxAmount = record.incomeTax;
        _insuranceAmount = record.socialInsurance;
        _loanDeductions = record.loanDeductions;
        _netSalary = record.netSalary;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'تم حساب راتب "${widget.employee.displayName}" بنجاح',
            ),
            backgroundColor: RevolutionaryColors.successGlow,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في حساب الراتب: ${e.toString()}'),
            backgroundColor: RevolutionaryColors.errorCoral,
          ),
        );
      }

      LoggingService.error(
        'خطأ في حساب الراتب',
        category: 'EmployeeSalaryCalculationScreen',
        data: {
          'employeeId': widget.employee.id,
          'month': _selectedMonth,
          'year': _selectedYear,
          'error': e.toString(),
        },
      );
    } finally {
      setState(() {
        _isCalculating = false;
      });
    }
  }

  Future<void> _saveSalary() async {
    if (_existingRecord == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يجب حساب الراتب أولاً'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    try {
      // هنا يمكن إضافة منطق حفظ إضافي إذا لزم الأمر
      // مثل تغيير حالة الراتب إلى "معتمد"

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('تم حفظ راتب "${widget.employee.displayName}" بنجاح'),
            backgroundColor: RevolutionaryColors.successGlow,
          ),
        );
        Navigator.pop(context, true);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في حفظ الراتب: ${e.toString()}'),
            backgroundColor: RevolutionaryColors.errorCoral,
          ),
        );
      }

      LoggingService.error(
        'خطأ في حفظ الراتب',
        category: 'EmployeeSalaryCalculationScreen',
        data: {
          'employeeId': widget.employee.id,
          'month': _selectedMonth,
          'year': _selectedYear,
          'error': e.toString(),
        },
      );
    }
  }
}
