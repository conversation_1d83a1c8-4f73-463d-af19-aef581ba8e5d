import 'package:flutter/material.dart';
import '../constants/revolutionary_design_colors.dart';
import '../constants/app_constants.dart';
import '../models/invoice.dart';
import '../models/invoice_status.dart';
import '../services/enhanced_invoice_service.dart';
import '../services/invoice_service.dart';
import '../services/performance_service.dart';
import '../services/progressive_loading_service.dart';

import '../services/recurring_invoice_service.dart';
import '../widgets/invoice_card.dart';
import '../widgets/loading_widget.dart';
import '../widgets/progressive_list_view.dart';
import 'add_invoice_screen.dart';
import 'quotations_screen.dart';
import 'recurring_invoices_screen.dart';
import 'invoice_templates_screen.dart';

class InvoicesScreen extends StatefulWidget {
  const InvoicesScreen({super.key});

  @override
  State<InvoicesScreen> createState() => _InvoicesScreenState();
}

class _InvoicesScreenState extends State<InvoicesScreen>
    with TickerProviderStateMixin {
  final EnhancedInvoiceService _enhancedInvoiceService =
      EnhancedInvoiceService();
  final InvoiceService _invoiceService = InvoiceService(); // للعمليات البسيطة
  final PerformanceService _performanceService = PerformanceService();
  final ProgressiveLoadingService _progressiveLoadingService =
      ProgressiveLoadingService();

  final RecurringInvoiceService _recurringService = RecurringInvoiceService();
  final TextEditingController _searchController = TextEditingController();

  late TabController _tabController;

  List<Invoice> _allInvoices = [];
  List<Invoice> _salesInvoices = [];
  List<Invoice> _purchaseInvoices = [];
  List<Invoice> _filteredInvoices = [];

  bool _isLoading = true;
  String _selectedStatus = 'all';
  bool _useProgressiveLoading = false; // يمكن تفعيله من الإعدادات

  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _tabController.addListener(_onTabChanged);

    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    // بدء جدولة الفواتير المتكررة
    _recurringService.startScheduler();

    _loadInvoices();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _animationController.dispose();
    _searchController.dispose();
    // إيقاف جدولة الفواتير المتكررة
    _recurringService.dispose();
    super.dispose();
  }

  void _onTabChanged() {
    _filterInvoices();
  }

  Future<void> _loadInvoices() async {
    setState(() => _isLoading = true);

    try {
      // استخدام خدمة الأداء لقياس وقت التحميل
      final allInvoices = await _performanceService.measureOperation(
        'load_all_invoices',
        () => _invoiceService.getAllInvoices(),
        category: 'invoices',
        metadata: {'screen': 'invoices_screen'},
      );

      final salesInvoices = allInvoices
          .where(
            (i) =>
                i.type == AppConstants.invoiceTypeSale ||
                i.type == AppConstants.invoiceTypeSaleReturn,
          )
          .toList();
      final purchaseInvoices = allInvoices
          .where(
            (i) =>
                i.type == AppConstants.invoiceTypePurchase ||
                i.type == AppConstants.invoiceTypePurchaseReturn,
          )
          .toList();

      setState(() {
        _allInvoices = allInvoices;
        _salesInvoices = salesInvoices;
        _purchaseInvoices = purchaseInvoices;
        _isLoading = false;
      });

      _filterInvoices();
      _animationController.forward();

      // تسجيل إحصائيات التحميل
      _performanceService.updateMetric(
        'invoices_loaded_count',
        allInvoices.length.toDouble(),
      );
      _performanceService.updateMetric(
        'sales_invoices_count',
        salesInvoices.length.toDouble(),
      );
      _performanceService.updateMetric(
        'purchase_invoices_count',
        purchaseInvoices.length.toDouble(),
      );
    } catch (e) {
      setState(() => _isLoading = false);
      _showErrorSnackBar('حدث خطأ في تحميل الفواتير: $e');
    }
  }

  void _filterInvoices() {
    setState(() {
      List<Invoice> sourceList;
      switch (_tabController.index) {
        case 0:
          sourceList = _allInvoices;
          break;
        case 1:
          sourceList = _salesInvoices;
          break;
        case 2:
          sourceList = _purchaseInvoices;
          break;
        default:
          sourceList = _allInvoices;
      }

      _filteredInvoices = sourceList.where((invoice) {
        final matchesSearch =
            invoice.invoiceNumber.contains(_searchController.text) ||
            (invoice.notes?.toLowerCase().contains(
                  _searchController.text.toLowerCase(),
                ) ??
                false);

        final matchesStatus =
            _selectedStatus == 'all' ||
            (_selectedStatus == 'draft' &&
                invoice.status == InvoiceStatus.draft) ||
            (_selectedStatus == 'confirmed' &&
                invoice.status == InvoiceStatus.confirmed) ||
            (_selectedStatus == 'paid' &&
                (invoice.status == InvoiceStatus.fullyPaid ||
                    invoice.status == InvoiceStatus.partiallyPaid)) ||
            (_selectedStatus == 'unpaid' &&
                !invoice.isPaid &&
                invoice.status != InvoiceStatus.cancelled);

        return matchesSearch && matchesStatus;
      }).toList();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('الفواتير'),
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'الكل', icon: Icon(Icons.receipt_long)),
            Tab(text: 'المبيعات', icon: Icon(Icons.point_of_sale)),
            Tab(text: 'المشتريات', icon: Icon(Icons.shopping_cart)),
          ],
        ),
        actions: [
          // عروض الأسعار
          IconButton(
            icon: const Icon(Icons.request_quote),
            onPressed: _navigateToQuotations,
            tooltip: 'عروض الأسعار',
          ),
          // الفواتير المتكررة
          IconButton(
            icon: const Icon(Icons.repeat),
            onPressed: _navigateToRecurringInvoices,
            tooltip: 'الفواتير المتكررة',
          ),
          // قوالب الفواتير
          IconButton(
            icon: const Icon(Icons.design_services),
            onPressed: _navigateToTemplates,
            tooltip: 'قوالب الفواتير',
          ),
          // تبديل نمط التحميل
          IconButton(
            icon: Icon(
              _useProgressiveLoading ? Icons.view_stream : Icons.view_list,
            ),
            onPressed: _toggleLoadingMode,
            tooltip: _useProgressiveLoading
                ? 'التحميل العادي'
                : 'التحميل التدريجي',
          ),
          IconButton(icon: const Icon(Icons.add), onPressed: _addNewInvoice),
          IconButton(icon: const Icon(Icons.refresh), onPressed: _loadInvoices),
        ],
      ),
      body: Column(
        children: [
          _buildSearchAndFilter(),
          _buildSummaryCards(),
          Expanded(
            child: _isLoading ? const LoadingWidget() : _buildInvoicesList(),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchAndFilter() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: RevolutionaryColors.backgroundCard,
        boxShadow: [
          BoxShadow(
            color: RevolutionaryColors.shadowMedium,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // شريط البحث
          TextField(
            controller: _searchController,
            decoration: InputDecoration(
              hintText: 'البحث في الفواتير...',
              prefixIcon: const Icon(Icons.search),
              suffixIcon: _searchController.text.isNotEmpty
                  ? IconButton(
                      icon: const Icon(Icons.clear),
                      onPressed: () {
                        _searchController.clear();
                        _filterInvoices();
                      },
                    )
                  : null,
            ),
            onChanged: (_) => _filterInvoices(),
          ),
          const SizedBox(height: 12),
          // فلاتر الحالة
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              children: [
                _buildFilterChip('all', 'الكل'),
                _buildFilterChip('draft', 'مسودة'),
                _buildFilterChip('confirmed', 'مؤكدة'),
                _buildFilterChip('paid', 'مدفوعة'),
                _buildFilterChip('unpaid', 'غير مدفوعة'),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterChip(String status, String label) {
    final isSelected = _selectedStatus == status;
    return Padding(
      padding: const EdgeInsets.only(left: 8),
      child: FilterChip(
        label: Text(label),
        selected: isSelected,
        onSelected: (selected) {
          setState(() {
            _selectedStatus = status;
            _filterInvoices();
          });
        },
        selectedColor: RevolutionaryColors.damascusSky.withValues(alpha: 0.2),
        checkmarkColor: RevolutionaryColors.damascusSky,
      ),
    );
  }

  Widget _buildSummaryCards() {
    final currentList = _getCurrentInvoiceList();
    final totalInvoices = currentList.length;
    final totalAmount = currentList.fold<double>(
      0.0,
      (sum, invoice) => sum + invoice.totalAmount,
    );
    final paidAmount = currentList.fold<double>(
      0.0,
      (sum, invoice) => sum + invoice.paidAmount,
    );
    final unpaidCount = currentList
        .where((i) => !i.isPaid && i.status != InvoiceStatus.cancelled)
        .length;

    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          Expanded(
            child: _buildSummaryCard(
              'إجمالي الفواتير',
              totalInvoices.toString(),
              Icons.receipt_long,
              RevolutionaryColors.damascusSky,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: _buildSummaryCard(
              'المبلغ الإجمالي',
              '${totalAmount.toStringAsFixed(0)} ل.س',
              Icons.attach_money,
              RevolutionaryColors.successGlow,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: _buildSummaryCard(
              'المدفوع',
              '${paidAmount.toStringAsFixed(0)} ل.س',
              Icons.check_circle,
              RevolutionaryColors.infoTurquoise,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: _buildSummaryCard(
              'غير مدفوعة',
              unpaidCount.toString(),
              Icons.pending,
              RevolutionaryColors.warningAmber,
            ),
          ),
        ],
      ),
    );
  }

  List<Invoice> _getCurrentInvoiceList() {
    switch (_tabController.index) {
      case 0:
        return _allInvoices;
      case 1:
        return _salesInvoices;
      case 2:
        return _purchaseInvoices;
      default:
        return _allInvoices;
    }
  }

  Widget _buildSummaryCard(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.2)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 20),
          const SizedBox(height: 4),
          Text(
            value,
            style: Theme.of(context).textTheme.titleSmall?.copyWith(
              color: color,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
          Text(
            title,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: RevolutionaryColors.textSecondary,
            ),
            textAlign: TextAlign.center,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  Widget _buildInvoicesList() {
    // استخدام التحميل التدريجي إذا كان مفعلاً
    if (_useProgressiveLoading) {
      return _buildProgressiveInvoicesList();
    }

    // التحميل العادي
    if (_filteredInvoices.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.receipt_long_outlined,
              size: 64,
              color: RevolutionaryColors.textHint,
            ),
            const SizedBox(height: 16),
            Text(
              'لا توجد فواتير',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                color: RevolutionaryColors.textHint,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'اضغط على + لإضافة فاتورة جديدة',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: RevolutionaryColors.textHint,
              ),
            ),
          ],
        ),
      );
    }

    return FadeTransition(
      opacity: _fadeAnimation,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: _filteredInvoices.length,
        itemBuilder: (context, index) {
          final invoice = _filteredInvoices[index];
          return InvoiceCard(
            invoice: invoice,
            onTap: () => _viewInvoiceDetails(invoice),
            onEdit: () => _editInvoice(invoice),
            onDelete: () => _deleteInvoice(invoice),
          );
        },
      ),
    );
  }

  void _addNewInvoice() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('نوع الفاتورة'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(
                Icons.point_of_sale,
                color: RevolutionaryColors.successGlow,
              ),
              title: const Text('فاتورة مبيعات'),
              onTap: () {
                Navigator.pop(context);
                _navigateToAddInvoice(AppConstants.invoiceTypeSale);
              },
            ),
            ListTile(
              leading: const Icon(
                Icons.shopping_cart,
                color: RevolutionaryColors.damascusSky,
              ),
              title: const Text('فاتورة مشتريات'),
              onTap: () {
                Navigator.pop(context);
                _navigateToAddInvoice(AppConstants.invoiceTypePurchase);
              },
            ),
            ListTile(
              leading: const Icon(
                Icons.keyboard_return,
                color: RevolutionaryColors.warningAmber,
              ),
              title: const Text('مردود مبيعات'),
              onTap: () {
                Navigator.pop(context);
                _navigateToAddInvoice(AppConstants.invoiceTypeSaleReturn);
              },
            ),
            ListTile(
              leading: const Icon(
                Icons.undo,
                color: RevolutionaryColors.infoTurquoise,
              ),
              title: const Text('مردود مشتريات'),
              onTap: () {
                Navigator.pop(context);
                _navigateToAddInvoice(AppConstants.invoiceTypePurchaseReturn);
              },
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _navigateToAddInvoice(String invoiceType) async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => AddInvoiceScreen(invoiceType: invoiceType),
      ),
    );

    if (result == true) {
      _loadInvoices();
    }
  }

  void _viewInvoiceDetails(Invoice invoice) {
    _showInvoiceIntegrationDetails(invoice);
  }

  Future<void> _showInvoiceIntegrationDetails(Invoice invoice) async {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('تفاصيل الفاتورة ${invoice.invoiceNumber}'),
        content: FutureBuilder<Map<String, dynamic>>(
          future: _enhancedInvoiceService.getInvoiceIntegrationDetails(
            invoice.id!,
          ),
          builder: (context, snapshot) {
            if (snapshot.connectionState == ConnectionState.waiting) {
              return const SizedBox(
                height: 100,
                child: Center(child: CircularProgressIndicator()),
              );
            }

            if (snapshot.hasError) {
              return Text('خطأ: ${snapshot.error}');
            }

            final details = snapshot.data!;
            final integrationStatus =
                details['integrationStatus'] as Map<String, dynamic>;
            final journalEntries = details['journalEntries'] as List;
            final inventoryMovements = details['inventoryMovements'] as List;
            final costOfGoodsSold = details['costOfGoodsSold'] as double;

            return SizedBox(
              width: double.maxFinite,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // حالة التكامل
                  Card(
                    color: integrationStatus['isFullyIntegrated']
                        ? RevolutionaryColors.successGlow.withValues(alpha: 0.1)
                        : RevolutionaryColors.warningAmber.withValues(
                            alpha: 0.1,
                          ),
                    child: Padding(
                      padding: const EdgeInsets.all(12),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'حالة التكامل',
                            style: Theme.of(context).textTheme.titleMedium
                                ?.copyWith(fontWeight: FontWeight.bold),
                          ),
                          const SizedBox(height: 8),
                          Row(
                            children: [
                              Icon(
                                integrationStatus['hasJournalEntries']
                                    ? Icons.check_circle
                                    : Icons.error,
                                color: integrationStatus['hasJournalEntries']
                                    ? RevolutionaryColors.successGlow
                                    : RevolutionaryColors.errorCoral,
                                size: 16,
                              ),
                              const SizedBox(width: 8),
                              Text(
                                'القيود المحاسبية: ${journalEntries.length}',
                              ),
                            ],
                          ),
                          const SizedBox(height: 4),
                          Row(
                            children: [
                              Icon(
                                integrationStatus['hasInventoryMovements']
                                    ? Icons.check_circle
                                    : Icons.error,
                                color:
                                    integrationStatus['hasInventoryMovements']
                                    ? RevolutionaryColors.successGlow
                                    : RevolutionaryColors.errorCoral,
                                size: 16,
                              ),
                              const SizedBox(width: 8),
                              Text(
                                'حركات المخزون: ${inventoryMovements.length}',
                              ),
                            ],
                          ),
                          if (costOfGoodsSold > 0) ...[
                            const SizedBox(height: 4),
                            Text(
                              'تكلفة البضاعة المباعة: ${costOfGoodsSold.toStringAsFixed(2)}',
                            ),
                          ],
                        ],
                      ),
                    ),
                  ),

                  // معلومات الفاتورة الأساسية
                  const SizedBox(height: 12),
                  Text(
                    'معلومات الفاتورة',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text('النوع: ${_getInvoiceTypeText(invoice.type)}'),
                  Text('التاريخ: ${_formatDate(invoice.invoiceDate)}'),
                  Text(
                    'المبلغ الإجمالي: ${invoice.totalAmount.toStringAsFixed(2)}',
                  ),
                  Text('الحالة: ${_getInvoiceStatusText(invoice.status)}'),
                ],
              ),
            );
          },
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
          if (invoice.status != InvoiceStatus.cancelled)
            ElevatedButton(
              onPressed: () {
                Navigator.pop(context);
                _reapplyInvoiceIntegration(invoice);
              },
              child: const Text('إعادة تطبيق التكامل'),
            ),
        ],
      ),
    );
  }

  void _editInvoice(Invoice invoice) {
    _showComingSoon('تعديل الفاتورة');
  }

  void _deleteInvoice(Invoice invoice) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text(
          'هل أنت متأكد من حذف الفاتورة "${invoice.invoiceNumber}"؟',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.pop(context);
              await _performDeleteInvoice(invoice);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: RevolutionaryColors.errorCoral,
            ),
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }

  Future<void> _performDeleteInvoice(Invoice invoice) async {
    try {
      // استخدام الخدمة المحسنة للحذف مع إلغاء التأثيرات
      await _enhancedInvoiceService.deleteInvoiceWithIntegration(invoice.id!);
      _showSuccessSnackBar('تم حذف الفاتورة وإلغاء تأثيراتها بنجاح');
      _loadInvoices();
    } catch (e) {
      _showErrorSnackBar('حدث خطأ في حذف الفاتورة: $e');
    }
  }

  void _showComingSoon(String feature) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('$feature - قريباً إن شاء الله'),
        backgroundColor: RevolutionaryColors.damascusSky,
      ),
    );
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: RevolutionaryColors.successGlow,
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: RevolutionaryColors.errorCoral,
      ),
    );
  }

  // دوال مساعدة للتنسيق والعرض
  String _getInvoiceTypeText(String type) {
    switch (type) {
      case AppConstants.invoiceTypeSale:
        return 'فاتورة مبيعات';
      case AppConstants.invoiceTypePurchase:
        return 'فاتورة مشتريات';
      case AppConstants.invoiceTypeSaleReturn:
        return 'مردود مبيعات';
      case AppConstants.invoiceTypePurchaseReturn:
        return 'مردود مشتريات';
      default:
        return 'غير محدد';
    }
  }

  String _getInvoiceStatusText(InvoiceStatus status) {
    return status.displayName;
  }

  String _formatDate(DateTime date) {
    return '${date.day.toString().padLeft(2, '0')}/'
        '${date.month.toString().padLeft(2, '0')}/'
        '${date.year}';
  }

  Future<void> _reapplyInvoiceIntegration(Invoice invoice) async {
    try {
      await _enhancedInvoiceService.reapplyInvoiceIntegration(invoice.id!);
      _showSuccessSnackBar('تم إعادة تطبيق التكامل بنجاح');
      _loadInvoices();
    } catch (e) {
      _showErrorSnackBar('حدث خطأ في إعادة تطبيق التكامل: $e');
    }
  }

  /// التبديل بين التحميل العادي والتدريجي
  void _toggleLoadingMode() {
    setState(() {
      _useProgressiveLoading = !_useProgressiveLoading;
    });

    // إعادة تحميل البيانات بالطريقة الجديدة
    _loadInvoices();

    _showSuccessSnackBar(
      _useProgressiveLoading
          ? 'تم تفعيل التحميل التدريجي'
          : 'تم تفعيل التحميل العادي',
    );
  }

  /// بناء واجهة التحميل التدريجي للفواتير
  Widget _buildProgressiveInvoicesList() {
    return ProgressiveListView<Map<String, dynamic>>(
      loadStream: _progressiveLoadingService.loadInvoicesProgressive(
        searchTerm: _searchController.text,
        status: _selectedStatus,
      ),
      itemBuilder: (context, invoiceData, index) {
        final invoice = Invoice.fromMap(invoiceData);
        return InvoiceCard(
          invoice: invoice,
          onTap: () => _viewInvoiceDetails(invoice),
          onEdit: () => _editInvoice(invoice),
          onDelete: () => _deleteInvoice(invoice),
        );
      },
      emptyMessage: 'لا توجد فواتير',
      loadingMessage: 'جاري تحميل الفواتير...',
      padding: const EdgeInsets.all(16),
      onRefresh: _loadInvoices,
    );
  }

  void _navigateToQuotations() {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const QuotationsScreen()),
    );
  }

  void _navigateToRecurringInvoices() {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const RecurringInvoicesScreen()),
    );
  }

  void _navigateToTemplates() {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const InvoiceTemplatesScreen()),
    );
  }
}
