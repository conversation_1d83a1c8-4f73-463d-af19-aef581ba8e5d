import 'package:flutter/material.dart';
import 'revolutionary_design_colors.dart';

/// أنماط النصوص المحسنة مع تباين أفضل ووضوح أكبر
class EnhancedTextStyles {
  // ==================== العناوين الرئيسية ====================

  /// عنوان رئيسي كبير مع تباين عالي
  static TextStyle get displayLarge => const TextStyle(
    fontSize: 36,
    fontWeight: FontWeight.bold,
    color: RevolutionaryColors.textPrimary,
    letterSpacing: -0.5,
    height: 1.2,
  );

  /// عنوان رئيسي متوسط
  static TextStyle get displayMedium => const TextStyle(
    fontSize: 32,
    fontWeight: FontWeight.bold,
    color: RevolutionaryColors.textPrimary,
    letterSpacing: -0.25,
    height: 1.25,
  );

  /// عنوان رئيسي صغير
  static TextStyle get displaySmall => const TextStyle(
    fontSize: 28,
    fontWeight: FontWeight.w600,
    color: RevolutionaryColors.textPrimary,
    letterSpacing: 0,
    height: 1.3,
  );

  // ==================== العناوين الفرعية ====================

  /// عنوان فرعي كبير
  static TextStyle get headlineLarge => const TextStyle(
    fontSize: 24,
    fontWeight: FontWeight.w600,
    color: RevolutionaryColors.textPrimary,
    letterSpacing: 0,
    height: 1.33,
  );

  /// عنوان فرعي متوسط
  static TextStyle get headlineMedium => const TextStyle(
    fontSize: 20,
    fontWeight: FontWeight.w600,
    color: RevolutionaryColors.textPrimary,
    letterSpacing: 0.15,
    height: 1.4,
  );

  /// عنوان فرعي صغير
  static TextStyle get headlineSmall => const TextStyle(
    fontSize: 18,
    fontWeight: FontWeight.w500,
    color: RevolutionaryColors.textPrimary,
    letterSpacing: 0.15,
    height: 1.44,
  );

  // ==================== عناوين الأقسام ====================

  /// عنوان قسم كبير
  static TextStyle get titleLarge => const TextStyle(
    fontSize: 16,
    fontWeight: FontWeight.w600,
    color: RevolutionaryColors.textPrimary,
    letterSpacing: 0.15,
    height: 1.5,
  );

  /// عنوان قسم متوسط
  static TextStyle get titleMedium => const TextStyle(
    fontSize: 14,
    fontWeight: FontWeight.w600,
    color: RevolutionaryColors.textPrimary,
    letterSpacing: 0.1,
    height: 1.43,
  );

  /// عنوان قسم صغير
  static TextStyle get titleSmall => const TextStyle(
    fontSize: 12,
    fontWeight: FontWeight.w500,
    color: RevolutionaryColors.textPrimary,
    letterSpacing: 0.1,
    height: 1.33,
  );

  // ==================== النصوص العادية ====================

  /// نص عادي كبير
  static TextStyle get bodyLarge => const TextStyle(
    fontSize: 16,
    fontWeight: FontWeight.w400,
    color: RevolutionaryColors.textPrimary,
    letterSpacing: 0.5,
    height: 1.5,
  );

  /// نص عادي متوسط
  static TextStyle get bodyMedium => const TextStyle(
    fontSize: 14,
    fontWeight: FontWeight.w400,
    color: RevolutionaryColors.textPrimary,
    letterSpacing: 0.25,
    height: 1.43,
  );

  /// نص عادي صغير
  static TextStyle get bodySmall => const TextStyle(
    fontSize: 12,
    fontWeight: FontWeight.w400,
    color: RevolutionaryColors.textSecondary,
    letterSpacing: 0.4,
    height: 1.33,
  );

  // ==================== التسميات والتلميحات ====================

  /// تسمية كبيرة
  static TextStyle get labelLarge => const TextStyle(
    fontSize: 14,
    fontWeight: FontWeight.w500,
    color: RevolutionaryColors.textPrimary,
    letterSpacing: 0.1,
    height: 1.43,
  );

  /// تسمية متوسطة
  static TextStyle get labelMedium => const TextStyle(
    fontSize: 12,
    fontWeight: FontWeight.w500,
    color: RevolutionaryColors.textSecondary,
    letterSpacing: 0.5,
    height: 1.33,
  );

  /// تسمية صغيرة
  static TextStyle get labelSmall => const TextStyle(
    fontSize: 10,
    fontWeight: FontWeight.w500,
    color: RevolutionaryColors.textHint,
    letterSpacing: 0.5,
    height: 1.2,
  );

  // ==================== أنماط خاصة ====================

  /// نص الأزرار
  static TextStyle get button => const TextStyle(
    fontSize: 16,
    fontWeight: FontWeight.w600,
    letterSpacing: 0.5,
    height: 1.25,
  );

  /// نص الأزرار الصغيرة
  static TextStyle get buttonSmall => const TextStyle(
    fontSize: 14,
    fontWeight: FontWeight.w500,
    letterSpacing: 0.25,
    height: 1.43,
  );

  /// نص التنبيهات
  static TextStyle get alert => const TextStyle(
    fontSize: 14,
    fontWeight: FontWeight.w500,
    letterSpacing: 0.25,
    height: 1.43,
  );

  /// نص الأرقام والمبالغ
  static TextStyle get numeric => const TextStyle(
    fontSize: 16,
    fontWeight: FontWeight.w600,
    letterSpacing: 0.5,
    height: 1.5,
    fontFeatures: [FontFeature.tabularFigures()],
  );

  /// نص الأرقام الكبيرة
  static TextStyle get numericLarge => const TextStyle(
    fontSize: 20,
    fontWeight: FontWeight.bold,
    letterSpacing: 0.15,
    height: 1.4,
    fontFeatures: [FontFeature.tabularFigures()],
  );

  // ==================== أنماط ملونة ====================

  /// نص أساسي ملون
  static TextStyle get primaryText => TextStyle(
    fontSize: 16,
    fontWeight: FontWeight.w600,
    color: RevolutionaryColors.damascusSky,
    letterSpacing: 0.15,
    height: 1.5,
  );

  /// نص ثانوي ملون
  static TextStyle get secondaryText => TextStyle(
    fontSize: 14,
    fontWeight: FontWeight.w500,
    color: RevolutionaryColors.damascusSky,
    letterSpacing: 0.25,
    height: 1.43,
  );

  /// نص النجاح
  static TextStyle get successText => TextStyle(
    fontSize: 14,
    fontWeight: FontWeight.w500,
    color: RevolutionaryColors.successGlow,
    letterSpacing: 0.25,
    height: 1.43,
  );

  /// نص التحذير
  static TextStyle get warningText => TextStyle(
    fontSize: 14,
    fontWeight: FontWeight.w500,
    color: RevolutionaryColors.warningAmber,
    letterSpacing: 0.25,
    height: 1.43,
  );

  /// نص الخطأ
  static TextStyle get errorText => TextStyle(
    fontSize: 14,
    fontWeight: FontWeight.w500,
    color: RevolutionaryColors.errorCoral,
    letterSpacing: 0.25,
    height: 1.43,
  );

  /// نص المعلومات
  static TextStyle get infoText => TextStyle(
    fontSize: 14,
    fontWeight: FontWeight.w500,
    color: RevolutionaryColors.infoTurquoise,
    letterSpacing: 0.25,
    height: 1.43,
  );

  // ==================== دوال مساعدة ====================

  /// إنشاء نمط نص مخصص مع تباين محسن
  static TextStyle createCustomStyle({
    required double fontSize,
    FontWeight fontWeight = FontWeight.w400,
    Color? color,
    double? letterSpacing,
    double? height,
    bool highContrast = false,
  }) {
    return TextStyle(
      fontSize: fontSize,
      fontWeight: fontWeight,
      color:
          color ??
          (highContrast
              ? RevolutionaryColors.textPrimary
              : RevolutionaryColors.textSecondary),
      letterSpacing: letterSpacing ?? (fontSize > 16 ? 0.15 : 0.25),
      height: height ?? (fontSize > 20 ? 1.3 : 1.43),
    );
  }

  /// تطبيق تباين عالي على نمط موجود
  static TextStyle withHighContrast(TextStyle style) {
    return style.copyWith(
      color: RevolutionaryColors.textPrimary,
      fontWeight: FontWeight
          .values[(style.fontWeight?.index ?? FontWeight.w400.index) + 1],
    );
  }

  /// تطبيق شفافية على نمط موجود
  static TextStyle withOpacity(TextStyle style, double opacity) {
    return style.copyWith(
      color: (style.color ?? RevolutionaryColors.textPrimary).withValues(
        alpha: opacity,
      ),
    );
  }
}
