/// حوار إضافة برنامج تدريب جديد
/// يوفر واجهة لإدخال تفاصيل برنامج التدريب
library;

import 'package:flutter/material.dart';
import '../models/training_models.dart';
import '../constants/revolutionary_design_colors.dart';

class AddTrainingProgramDialog extends StatefulWidget {
  final Function(TrainingProgram) onSave;

  const AddTrainingProgramDialog({
    super.key,
    required this.onSave,
  });

  @override
  State<AddTrainingProgramDialog> createState() => _AddTrainingProgramDialogState();
}

class _AddTrainingProgramDialogState extends State<AddTrainingProgramDialog> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _durationController = TextEditingController();
  final _costController = TextEditingController();
  final _providerController = TextEditingController();
  final _locationController = TextEditingController();
  final _maxParticipantsController = TextEditingController(text: '20');
  final _prerequisitesController = TextEditingController();
  final _objectivesController = TextEditingController();
  final _materialsController = TextEditingController();

  String _selectedCategory = 'technical';
  String _selectedLevel = 'beginner';
  String _selectedDeliveryMethod = 'offline';

  final List<Map<String, String>> _categories = [
    {'value': 'technical', 'label': 'تقني'},
    {'value': 'management', 'label': 'إداري'},
    {'value': 'soft_skills', 'label': 'مهارات شخصية'},
    {'value': 'safety', 'label': 'السلامة'},
    {'value': 'language', 'label': 'لغات'},
    {'value': 'finance', 'label': 'مالي'},
    {'value': 'marketing', 'label': 'تسويق'},
    {'value': 'other', 'label': 'أخرى'},
  ];

  final List<Map<String, String>> _levels = [
    {'value': 'beginner', 'label': 'مبتدئ'},
    {'value': 'intermediate', 'label': 'متوسط'},
    {'value': 'advanced', 'label': 'متقدم'},
    {'value': 'expert', 'label': 'خبير'},
  ];

  final List<Map<String, String>> _deliveryMethods = [
    {'value': 'offline', 'label': 'حضوري'},
    {'value': 'online', 'label': 'عبر الإنترنت'},
    {'value': 'hybrid', 'label': 'مختلط'},
  ];

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    _durationController.dispose();
    _costController.dispose();
    _providerController.dispose();
    _locationController.dispose();
    _maxParticipantsController.dispose();
    _prerequisitesController.dispose();
    _objectivesController.dispose();
    _materialsController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('إضافة برنامج تدريب جديد'),
      content: SizedBox(
        width: MediaQuery.of(context).size.width * 0.9,
        height: MediaQuery.of(context).size.height * 0.8,
        child: Form(
          key: _formKey,
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // اسم البرنامج
                TextFormField(
                  controller: _nameController,
                  decoration: const InputDecoration(
                    labelText: 'اسم البرنامج *',
                    border: OutlineInputBorder(),
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'يرجى إدخال اسم البرنامج';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),

                // الوصف
                TextFormField(
                  controller: _descriptionController,
                  decoration: const InputDecoration(
                    labelText: 'الوصف *',
                    border: OutlineInputBorder(),
                  ),
                  maxLines: 3,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'يرجى إدخال وصف البرنامج';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),

                // الفئة والمستوى
                Row(
                  children: [
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        value: _selectedCategory,
                        decoration: const InputDecoration(
                          labelText: 'الفئة',
                          border: OutlineInputBorder(),
                        ),
                        items: _categories.map((category) {
                          return DropdownMenuItem<String>(
                            value: category['value'],
                            child: Text(category['label']!),
                          );
                        }).toList(),
                        onChanged: (value) {
                          setState(() {
                            _selectedCategory = value!;
                          });
                        },
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        value: _selectedLevel,
                        decoration: const InputDecoration(
                          labelText: 'المستوى',
                          border: OutlineInputBorder(),
                        ),
                        items: _levels.map((level) {
                          return DropdownMenuItem<String>(
                            value: level['value'],
                            child: Text(level['label']!),
                          );
                        }).toList(),
                        onChanged: (value) {
                          setState(() {
                            _selectedLevel = value!;
                          });
                        },
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),

                // المدة والتكلفة
                Row(
                  children: [
                    Expanded(
                      child: TextFormField(
                        controller: _durationController,
                        decoration: const InputDecoration(
                          labelText: 'المدة (ساعة) *',
                          border: OutlineInputBorder(),
                        ),
                        keyboardType: TextInputType.number,
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'يرجى إدخال المدة';
                          }
                          if (int.tryParse(value) == null) {
                            return 'يرجى إدخال رقم صحيح';
                          }
                          return null;
                        },
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: TextFormField(
                        controller: _costController,
                        decoration: const InputDecoration(
                          labelText: 'التكلفة (ل.س)',
                          border: OutlineInputBorder(),
                        ),
                        keyboardType: TextInputType.number,
                        validator: (value) {
                          if (value != null && value.isNotEmpty) {
                            if (double.tryParse(value) == null) {
                              return 'يرجى إدخال رقم صحيح';
                            }
                          }
                          return null;
                        },
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),

                // مقدم الخدمة والموقع
                Row(
                  children: [
                    Expanded(
                      child: TextFormField(
                        controller: _providerController,
                        decoration: const InputDecoration(
                          labelText: 'مقدم الخدمة',
                          border: OutlineInputBorder(),
                        ),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: TextFormField(
                        controller: _locationController,
                        decoration: const InputDecoration(
                          labelText: 'الموقع',
                          border: OutlineInputBorder(),
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),

                // طريقة التقديم والحد الأقصى للمشاركين
                Row(
                  children: [
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        value: _selectedDeliveryMethod,
                        decoration: const InputDecoration(
                          labelText: 'طريقة التقديم',
                          border: OutlineInputBorder(),
                        ),
                        items: _deliveryMethods.map((method) {
                          return DropdownMenuItem<String>(
                            value: method['value'],
                            child: Text(method['label']!),
                          );
                        }).toList(),
                        onChanged: (value) {
                          setState(() {
                            _selectedDeliveryMethod = value!;
                          });
                        },
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: TextFormField(
                        controller: _maxParticipantsController,
                        decoration: const InputDecoration(
                          labelText: 'الحد الأقصى للمشاركين',
                          border: OutlineInputBorder(),
                        ),
                        keyboardType: TextInputType.number,
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'يرجى إدخال الحد الأقصى';
                          }
                          if (int.tryParse(value) == null) {
                            return 'يرجى إدخال رقم صحيح';
                          }
                          return null;
                        },
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),

                // المتطلبات المسبقة
                TextFormField(
                  controller: _prerequisitesController,
                  decoration: const InputDecoration(
                    labelText: 'المتطلبات المسبقة',
                    border: OutlineInputBorder(),
                  ),
                  maxLines: 2,
                ),
                const SizedBox(height: 16),

                // الأهداف
                TextFormField(
                  controller: _objectivesController,
                  decoration: const InputDecoration(
                    labelText: 'أهداف البرنامج',
                    border: OutlineInputBorder(),
                  ),
                  maxLines: 3,
                ),
                const SizedBox(height: 16),

                // المواد التدريبية
                TextFormField(
                  controller: _materialsController,
                  decoration: const InputDecoration(
                    labelText: 'المواد التدريبية',
                    border: OutlineInputBorder(),
                  ),
                  maxLines: 2,
                ),
              ],
            ),
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('إلغاء'),
        ),
        ElevatedButton(
          onPressed: () {
            if (_formKey.currentState!.validate()) {
              final program = TrainingProgram(
                name: _nameController.text.trim(),
                description: _descriptionController.text.trim(),
                category: _selectedCategory,
                level: _selectedLevel,
                durationHours: int.parse(_durationController.text),
                cost: _costController.text.isEmpty ? 0 : double.parse(_costController.text),
                provider: _providerController.text.trim().isEmpty ? null : _providerController.text.trim(),
                location: _locationController.text.trim().isEmpty ? null : _locationController.text.trim(),
                deliveryMethod: _selectedDeliveryMethod,
                maxParticipants: int.parse(_maxParticipantsController.text),
                prerequisites: _prerequisitesController.text.trim().isEmpty ? null : _prerequisitesController.text.trim(),
                objectives: _objectivesController.text.trim().isEmpty ? null : _objectivesController.text.trim(),
                materials: _materialsController.text.trim().isEmpty ? null : _materialsController.text.trim(),
                createdAt: DateTime.now(),
                updatedAt: DateTime.now(),
              );
              
              widget.onSave(program);
              Navigator.of(context).pop();
            }
          },
          style: ElevatedButton.styleFrom(
            backgroundColor: RevolutionaryColors.successGlow,
            foregroundColor: Colors.white,
          ),
          child: const Text('إضافة البرنامج'),
        ),
      ],
    );
  }
}
