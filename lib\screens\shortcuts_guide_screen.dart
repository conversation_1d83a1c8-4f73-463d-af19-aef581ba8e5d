import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../services/keyboard_shortcuts_service.dart';
import '../constants/revolutionary_design_colors.dart';

/// شاشة دليل اختصارات لوحة المفاتيح
class ShortcutsGuideScreen extends StatefulWidget {
  const ShortcutsGuideScreen({super.key});

  @override
  State<ShortcutsGuideScreen> createState() => _ShortcutsGuideScreenState();
}

class _ShortcutsGuideScreenState extends State<ShortcutsGuideScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  String _searchQuery = '';
  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    final categories = KeyboardShortcutsService.getShortcutsByCategory();
    _tabController = TabController(length: categories.length, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final categories = KeyboardShortcutsService.getShortcutsByCategory();
    final filteredCategories = _filterCategories(categories);

    return Scaffold(
      appBar: AppBar(
        title: const Text('دليل اختصارات لوحة المفاتيح'),
        backgroundColor: RevolutionaryColors.damascusSky,
        foregroundColor: Colors.white,
        elevation: 0,
        bottom: PreferredSize(
          preferredSize: const Size.fromHeight(100),
          child: Column(
            children: [
              // شريط البحث
              Container(
                padding: const EdgeInsets.all(16),
                child: TextField(
                  controller: _searchController,
                  decoration: InputDecoration(
                    hintText: 'البحث في الاختصارات...',
                    prefixIcon: const Icon(Icons.search),
                    suffixIcon: _searchQuery.isNotEmpty
                        ? IconButton(
                            icon: const Icon(Icons.clear),
                            onPressed: () {
                              _searchController.clear();
                              setState(() {
                                _searchQuery = '';
                              });
                            },
                          )
                        : null,
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide.none,
                    ),
                    filled: true,
                    fillColor: Colors.white,
                  ),
                  onChanged: (value) {
                    setState(() {
                      _searchQuery = value.toLowerCase();
                    });
                  },
                ),
              ),
              // التبويبات
              if (filteredCategories.isNotEmpty)
                TabBar(
                  controller: _tabController,
                  isScrollable: true,
                  indicatorColor: Colors.white,
                  labelColor: Colors.white,
                  unselectedLabelColor: Colors.white70,
                  tabs: filteredCategories.keys
                      .map((category) => Tab(text: category))
                      .toList(),
                ),
            ],
          ),
        ),
      ),
      body: filteredCategories.isEmpty
          ? _buildEmptyState()
          : TabBarView(
              controller: _tabController,
              children: filteredCategories.entries
                  .map((category) => _buildCategoryView(category))
                  .toList(),
            ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _showQuickReference,
        backgroundColor: RevolutionaryColors.damascusSky,
        icon: const Icon(Icons.help_outline, color: Colors.white),
        label: const Text('مرجع سريع', style: TextStyle(color: Colors.white)),
      ),
    );
  }

  Map<String, Map<LogicalKeySet, String>> _filterCategories(
      Map<String, Map<LogicalKeySet, String>> categories) {
    if (_searchQuery.isEmpty) return categories;

    final filtered = <String, Map<LogicalKeySet, String>>{};
    
    for (final category in categories.entries) {
      final filteredShortcuts = <LogicalKeySet, String>{};
      
      for (final shortcut in category.value.entries) {
        if (shortcut.value.toLowerCase().contains(_searchQuery) ||
            _formatShortcut(shortcut.key).toLowerCase().contains(_searchQuery)) {
          filteredShortcuts[shortcut.key] = shortcut.value;
        }
      }
      
      if (filteredShortcuts.isNotEmpty) {
        filtered[category.key] = filteredShortcuts;
      }
    }
    
    return filtered;
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.search_off,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'لم يتم العثور على اختصارات',
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'جرب البحث بكلمات مختلفة',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[500],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCategoryView(MapEntry<String, Map<LogicalKeySet, String>> category) {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: category.value.length,
      itemBuilder: (context, index) {
        final shortcut = category.value.entries.elementAt(index);
        return _buildShortcutCard(shortcut.key, shortcut.value);
      },
    );
  }

  Widget _buildShortcutCard(LogicalKeySet keySet, String description) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: ListTile(
        contentPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
        leading: Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
          decoration: BoxDecoration(
            color: RevolutionaryColors.damascusSky.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: RevolutionaryColors.damascusSky.withValues(alpha: 0.3)),
          ),
          child: Text(
            _formatShortcut(keySet),
            style: TextStyle(
              fontFamily: 'monospace',
              fontWeight: FontWeight.bold,
              color: RevolutionaryColors.damascusSky,
              fontSize: 12,
            ),
          ),
        ),
        title: Text(
          description,
          style: const TextStyle(
            fontWeight: FontWeight.w500,
            fontSize: 16,
          ),
        ),
        trailing: IconButton(
          icon: const Icon(Icons.copy, size: 20),
          onPressed: () => _copyShortcut(keySet),
          tooltip: 'نسخ الاختصار',
        ),
        onTap: () => _showShortcutDetails(keySet, description),
      ),
    );
  }

  void _copyShortcut(LogicalKeySet keySet) {
    final shortcutText = _formatShortcut(keySet);
    Clipboard.setData(ClipboardData(text: shortcutText));
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('تم نسخ الاختصار: $shortcutText'),
        duration: const Duration(seconds: 2),
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _showShortcutDetails(LogicalKeySet keySet, String description) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(description),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('الاختصار: ${_formatShortcut(keySet)}'),
            const SizedBox(height: 16),
            const Text('كيفية الاستخدام:'),
            const SizedBox(height: 8),
            Text(
              _getUsageInstructions(keySet, description),
              style: TextStyle(color: Colors.grey[600]),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إغلاق'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _copyShortcut(keySet);
            },
            child: const Text('نسخ'),
          ),
        ],
      ),
    );
  }

  void _showQuickReference() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('المرجع السريع'),
        content: SizedBox(
          width: double.maxFinite,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildQuickReferenceItem('Ctrl + N', 'جديد'),
              _buildQuickReferenceItem('Ctrl + S', 'حفظ'),
              _buildQuickReferenceItem('Ctrl + Z', 'تراجع'),
              _buildQuickReferenceItem('Ctrl + Y', 'إعادة'),
              _buildQuickReferenceItem('Ctrl + F', 'بحث'),
              _buildQuickReferenceItem('F1', 'مساعدة'),
              _buildQuickReferenceItem('Esc', 'إلغاء'),
              const Divider(),
              const Text(
                'نصائح:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              const Text('• استخدم Tab للتنقل بين الحقول'),
              const Text('• استخدم Enter للتأكيد'),
              const Text('• استخدم Esc للإلغاء'),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickReferenceItem(String shortcut, String description) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        children: [
          SizedBox(
            width: 80,
            child: Text(
              shortcut,
              style: const TextStyle(
                fontFamily: 'monospace',
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          Text(description),
        ],
      ),
    );
  }

  String _formatShortcut(LogicalKeySet keySet) {
    final keys = <String>[];
    
    if (keySet.keys.contains(LogicalKeyboardKey.control) ||
        keySet.keys.contains(LogicalKeyboardKey.controlLeft) ||
        keySet.keys.contains(LogicalKeyboardKey.controlRight)) {
      keys.add('Ctrl');
    }
    
    if (keySet.keys.contains(LogicalKeyboardKey.shift) ||
        keySet.keys.contains(LogicalKeyboardKey.shiftLeft) ||
        keySet.keys.contains(LogicalKeyboardKey.shiftRight)) {
      keys.add('Shift');
    }
    
    if (keySet.keys.contains(LogicalKeyboardKey.alt) ||
        keySet.keys.contains(LogicalKeyboardKey.altLeft) ||
        keySet.keys.contains(LogicalKeyboardKey.altRight)) {
      keys.add('Alt');
    }
    
    // إضافة المفتاح الأساسي
    for (final key in keySet.keys) {
      if (!_isModifierKey(key)) {
        keys.add(_getKeyName(key));
      }
    }
    
    return keys.join(' + ');
  }

  bool _isModifierKey(LogicalKeyboardKey key) {
    return key == LogicalKeyboardKey.control ||
           key == LogicalKeyboardKey.controlLeft ||
           key == LogicalKeyboardKey.controlRight ||
           key == LogicalKeyboardKey.shift ||
           key == LogicalKeyboardKey.shiftLeft ||
           key == LogicalKeyboardKey.shiftRight ||
           key == LogicalKeyboardKey.alt ||
           key == LogicalKeyboardKey.altLeft ||
           key == LogicalKeyboardKey.altRight;
  }

  String _getKeyName(LogicalKeyboardKey key) {
    // نفس منطق _getKeyName من KeyboardShortcutsWrapper
    if (key == LogicalKeyboardKey.keyA) return 'A';
    if (key == LogicalKeyboardKey.keyB) return 'B';
    if (key == LogicalKeyboardKey.keyC) return 'C';
    if (key == LogicalKeyboardKey.keyD) return 'D';
    if (key == LogicalKeyboardKey.keyE) return 'E';
    if (key == LogicalKeyboardKey.keyF) return 'F';
    if (key == LogicalKeyboardKey.keyG) return 'G';
    if (key == LogicalKeyboardKey.keyH) return 'H';
    if (key == LogicalKeyboardKey.keyI) return 'I';
    if (key == LogicalKeyboardKey.keyJ) return 'J';
    if (key == LogicalKeyboardKey.keyK) return 'K';
    if (key == LogicalKeyboardKey.keyL) return 'L';
    if (key == LogicalKeyboardKey.keyM) return 'M';
    if (key == LogicalKeyboardKey.keyN) return 'N';
    if (key == LogicalKeyboardKey.keyO) return 'O';
    if (key == LogicalKeyboardKey.keyP) return 'P';
    if (key == LogicalKeyboardKey.keyQ) return 'Q';
    if (key == LogicalKeyboardKey.keyR) return 'R';
    if (key == LogicalKeyboardKey.keyS) return 'S';
    if (key == LogicalKeyboardKey.keyT) return 'T';
    if (key == LogicalKeyboardKey.keyU) return 'U';
    if (key == LogicalKeyboardKey.keyV) return 'V';
    if (key == LogicalKeyboardKey.keyW) return 'W';
    if (key == LogicalKeyboardKey.keyX) return 'X';
    if (key == LogicalKeyboardKey.keyY) return 'Y';
    if (key == LogicalKeyboardKey.keyZ) return 'Z';
    if (key == LogicalKeyboardKey.f1) return 'F1';
    if (key == LogicalKeyboardKey.f2) return 'F2';
    if (key == LogicalKeyboardKey.f3) return 'F3';
    if (key == LogicalKeyboardKey.f4) return 'F4';
    if (key == LogicalKeyboardKey.f5) return 'F5';
    if (key == LogicalKeyboardKey.f6) return 'F6';
    if (key == LogicalKeyboardKey.f7) return 'F7';
    if (key == LogicalKeyboardKey.f8) return 'F8';
    if (key == LogicalKeyboardKey.f9) return 'F9';
    if (key == LogicalKeyboardKey.f10) return 'F10';
    if (key == LogicalKeyboardKey.f11) return 'F11';
    if (key == LogicalKeyboardKey.f12) return 'F12';
    if (key == LogicalKeyboardKey.enter) return 'Enter';
    if (key == LogicalKeyboardKey.escape) return 'Esc';
    if (key == LogicalKeyboardKey.tab) return 'Tab';
    if (key == LogicalKeyboardKey.space) return 'Space';
    if (key == LogicalKeyboardKey.delete) return 'Del';
    if (key == LogicalKeyboardKey.insert) return 'Ins';
    if (key == LogicalKeyboardKey.pageUp) return 'PgUp';
    if (key == LogicalKeyboardKey.pageDown) return 'PgDn';
    if (key == LogicalKeyboardKey.arrowUp) return '↑';
    if (key == LogicalKeyboardKey.arrowDown) return '↓';
    if (key == LogicalKeyboardKey.arrowLeft) return '←';
    if (key == LogicalKeyboardKey.arrowRight) return '→';
    
    return key.keyLabel;
  }

  String _getUsageInstructions(LogicalKeySet keySet, String description) {
    // إرشادات مخصصة حسب نوع الاختصار
    if (description.contains('جديد')) {
      return 'اضغط على هذا الاختصار لإنشاء عنصر جديد في الشاشة الحالية.';
    } else if (description.contains('حفظ')) {
      return 'اضغط على هذا الاختصار لحفظ التغييرات الحالية.';
    } else if (description.contains('بحث')) {
      return 'اضغط على هذا الاختصار لفتح مربع البحث.';
    } else if (description.contains('تراجع')) {
      return 'اضغط على هذا الاختصار للتراجع عن آخر عملية.';
    } else if (description.contains('إعادة')) {
      return 'اضغط على هذا الاختصار لإعادة آخر عملية تم التراجع عنها.';
    }
    
    return 'اضغط على هذا الاختصار لتنفيذ العملية المطلوبة.';
  }
}
