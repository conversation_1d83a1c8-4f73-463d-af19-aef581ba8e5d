/// لوحة التحكم المتقدمة للموارد البشرية
/// واجهة تفاعلية مع إحصائيات ومؤشرات الأداء
library;

import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import '../constants/revolutionary_design_colors.dart';
import '../services/hr_reports_service.dart';
import '../services/hr_notifications_service.dart';
import '../services/logging_service.dart';
import '../widgets/loading_widget.dart';

class HRDashboardScreen extends StatefulWidget {
  const HRDashboardScreen({super.key});

  @override
  State<HRDashboardScreen> createState() => _HRDashboardScreenState();
}

class _HRDashboardScreenState extends State<HRDashboardScreen> {
  final HRReportsService _reportsService = HRReportsService();
  final HRNotificationsService _notificationsService = HRNotificationsService();

  // البيانات
  EmployeeReport? _employeeReport;
  AttendanceReport? _attendanceReport;
  PayrollReport? _payrollReport;
  LoansReport? _loansReport;
  List<HRNotification> _notifications = [];

  bool _isLoading = true;
  String? _error;

  @override
  void initState() {
    super.initState();
    _loadDashboardData();
  }

  Future<void> _loadDashboardData() async {
    try {
      setState(() {
        _isLoading = true;
        _error = null;
      });

      // تحديث التنبيهات التلقائية
      await _notificationsService.checkAndCreateNotifications();

      // تحميل البيانات بشكل متوازي
      final results = await Future.wait([
        _reportsService.generateEmployeeReport(),
        _reportsService.generateAttendanceReport(),
        _reportsService.generatePayrollReport(),
        _reportsService.generateLoansReport(),
        _notificationsService.getAllNotifications(unreadOnly: true),
      ]);

      setState(() {
        _employeeReport = results[0] as EmployeeReport;
        _attendanceReport = results[1] as AttendanceReport;
        _payrollReport = results[2] as PayrollReport;
        _loansReport = results[3] as LoansReport;
        _notifications = results[4] as List<HRNotification>;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });

      LoggingService.error(
        'خطأ في تحميل بيانات لوحة التحكم',
        category: 'HRDashboardScreen',
        data: {'error': e.toString()},
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('لوحة تحكم الموارد البشرية'),
        backgroundColor: RevolutionaryColors.damascusSky,
        foregroundColor: Colors.white,
        actions: [
          // أيقونة التنبيهات
          Stack(
            children: [
              IconButton(
                icon: const Icon(Icons.notifications),
                onPressed: _showNotifications,
              ),
              if (_notifications.isNotEmpty)
                Positioned(
                  right: 8,
                  top: 8,
                  child: Container(
                    padding: const EdgeInsets.all(2),
                    decoration: BoxDecoration(
                      color: RevolutionaryColors.errorCoral,
                      borderRadius: BorderRadius.circular(10),
                    ),
                    constraints: const BoxConstraints(
                      minWidth: 16,
                      minHeight: 16,
                    ),
                    child: Text(
                      '${_notifications.length}',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 10,
                        fontWeight: FontWeight.bold,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ),
            ],
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadDashboardData,
          ),
        ],
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const LoadingWidget(message: 'جاري تحميل لوحة التحكم...');
    }

    if (_error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 64, color: Colors.red[400]),
            const SizedBox(height: 16),
            Text(
              'حدث خطأ',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.red[600],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              _error!,
              style: TextStyle(fontSize: 14, color: Colors.grey[600]),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadDashboardData,
              child: const Text('إعادة المحاولة'),
            ),
          ],
        ),
      );
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildWelcomeCard(),
          const SizedBox(height: 16),
          _buildQuickStats(),
          const SizedBox(height: 24),
          _buildChartsSection(),
          const SizedBox(height: 24),
          _buildNotificationsSection(),
          const SizedBox(height: 24),
          _buildQuickActions(),
        ],
      ),
    );
  }

  Widget _buildWelcomeCard() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            RevolutionaryColors.damascusSky,
            RevolutionaryColors.damascusSky.withValues(alpha: 0.8),
          ],
        ),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'مرحباً بك في لوحة تحكم الموارد البشرية',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'نظرة شاملة على حالة الموارد البشرية في ${DateTime.now().day}/${DateTime.now().month}/${DateTime.now().year}',
            style: const TextStyle(fontSize: 14, color: Colors.white70),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Icon(Icons.trending_up, color: Colors.white, size: 20),
              const SizedBox(width: 8),
              Text(
                'جميع الأنظمة تعمل بكفاءة',
                style: const TextStyle(
                  fontSize: 14,
                  color: Colors.white,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildQuickStats() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'الإحصائيات السريعة',
          style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: _buildStatCard(
                'إجمالي الموظفين',
                _employeeReport?.totalEmployees.toString() ?? '0',
                Icons.people,
                RevolutionaryColors.infoTurquoise,
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: _buildStatCard(
                'معدل الحضور',
                '${_attendanceReport?.attendanceRate.toStringAsFixed(1) ?? '0'}%',
                Icons.check_circle,
                RevolutionaryColors.successGlow,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            Expanded(
              child: _buildStatCard(
                'إجمالي الرواتب',
                '${_payrollReport?.totalGrossSalary.toStringAsFixed(0) ?? '0'} ل.س',
                Icons.account_balance_wallet,
                RevolutionaryColors.syrianGold,
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: _buildStatCard(
                'القروض النشطة',
                _loansReport?.activeLoans.toString() ?? '0',
                Icons.account_balance,
                RevolutionaryColors.warningAmber,
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: _buildStatCard(
                'التنبيهات',
                _notifications.length.toString(),
                Icons.notifications,
                RevolutionaryColors.errorCoral,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildStatCard(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Icon(icon, color: color, size: 32),
            const SizedBox(height: 8),
            Text(
              title,
              style: const TextStyle(fontSize: 12, color: Colors.grey),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 4),
            Text(
              value,
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: color,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildChartsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'الرسوم البيانية',
          style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(child: _buildEmployeeStatusChart()),
            const SizedBox(width: 16),
            Expanded(child: _buildAttendanceChart()),
          ],
        ),
      ],
    );
  }

  Widget _buildEmployeeStatusChart() {
    if (_employeeReport == null) return const SizedBox();

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'حالة الموظفين',
              style: TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            SizedBox(
              height: 150,
              child: PieChart(
                PieChartData(
                  sections: [
                    PieChartSectionData(
                      value: _employeeReport!.activeEmployees.toDouble(),
                      title: 'نشط',
                      color: RevolutionaryColors.successGlow,
                      radius: 50,
                      titleStyle: const TextStyle(
                        fontSize: 10,
                        color: Colors.white,
                      ),
                    ),
                    PieChartSectionData(
                      value: _employeeReport!.inactiveEmployees.toDouble(),
                      title: 'غير نشط',
                      color: RevolutionaryColors.warningAmber,
                      radius: 50,
                      titleStyle: const TextStyle(
                        fontSize: 10,
                        color: Colors.white,
                      ),
                    ),
                    PieChartSectionData(
                      value: _employeeReport!.terminatedEmployees.toDouble(),
                      title: 'منتهي',
                      color: RevolutionaryColors.errorCoral,
                      radius: 50,
                      titleStyle: const TextStyle(
                        fontSize: 10,
                        color: Colors.white,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAttendanceChart() {
    if (_attendanceReport == null) return const SizedBox();

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'الحضور والغياب',
              style: TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            SizedBox(
              height: 150,
              child: PieChart(
                PieChartData(
                  sections: [
                    PieChartSectionData(
                      value: _attendanceReport!.totalPresentDays.toDouble(),
                      title: 'حضور',
                      color: RevolutionaryColors.successGlow,
                      radius: 50,
                      titleStyle: const TextStyle(
                        fontSize: 10,
                        color: Colors.white,
                      ),
                    ),
                    PieChartSectionData(
                      value: _attendanceReport!.totalAbsentDays.toDouble(),
                      title: 'غياب',
                      color: RevolutionaryColors.errorCoral,
                      radius: 50,
                      titleStyle: const TextStyle(
                        fontSize: 10,
                        color: Colors.white,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNotificationsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Text(
              'التنبيهات الحديثة',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            if (_notifications.isNotEmpty)
              TextButton(
                onPressed: _showNotifications,
                child: const Text('عرض الكل'),
              ),
          ],
        ),
        const SizedBox(height: 12),
        if (_notifications.isEmpty)
          Card(
            child: Padding(
              padding: const EdgeInsets.all(20),
              child: Row(
                children: [
                  Icon(
                    Icons.check_circle,
                    color: RevolutionaryColors.successGlow,
                    size: 32,
                  ),
                  const SizedBox(width: 12),
                  const Expanded(
                    child: Text(
                      'لا توجد تنبيهات جديدة',
                      style: TextStyle(fontSize: 16),
                    ),
                  ),
                ],
              ),
            ),
          )
        else
          Column(
            children: _notifications.take(3).map((notification) {
              return Card(
                margin: const EdgeInsets.only(bottom: 8),
                child: ListTile(
                  leading: CircleAvatar(
                    backgroundColor: _getNotificationColor(notification.type),
                    child: Icon(
                      _getNotificationIcon(notification.type),
                      color: Colors.white,
                      size: 20,
                    ),
                  ),
                  title: Text(
                    notification.title,
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                  subtitle: Text(notification.message),
                  trailing: Text(
                    _formatNotificationTime(notification.createdAt),
                    style: const TextStyle(fontSize: 12, color: Colors.grey),
                  ),
                  onTap: () => _markNotificationAsRead(notification),
                ),
              );
            }).toList(),
          ),
      ],
    );
  }

  Widget _buildQuickActions() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'الإجراءات السريعة',
          style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 12),
        GridView.count(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisCount: 2,
          crossAxisSpacing: 12,
          mainAxisSpacing: 12,
          childAspectRatio: 2.5,
          children: [
            _buildActionCard(
              'إضافة موظف',
              Icons.person_add,
              RevolutionaryColors.successGlow,
              () => _navigateToAddEmployee(),
            ),
            _buildActionCard(
              'تسجيل حضور',
              Icons.access_time,
              RevolutionaryColors.infoTurquoise,
              () => _navigateToAttendance(),
            ),
            _buildActionCard(
              'حساب الرواتب',
              Icons.account_balance_wallet,
              RevolutionaryColors.syrianGold,
              () => _navigateToPayroll(),
            ),
            _buildActionCard(
              'عرض التقارير',
              Icons.analytics,
              RevolutionaryColors.damascusSky,
              () => _navigateToReports(),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildActionCard(
    String title,
    IconData icon,
    Color color,
    VoidCallback onTap,
  ) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Icon(icon, color: color, size: 24),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  title,
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Color _getNotificationColor(String type) {
    switch (type) {
      case 'birthday':
        return RevolutionaryColors.successGlow;
      case 'contract_expiry':
        return RevolutionaryColors.warningAmber;
      case 'overdue_loan':
        return RevolutionaryColors.errorCoral;
      case 'absenteeism':
        return RevolutionaryColors.warningAmber;
      case 'probation_end':
        return RevolutionaryColors.infoTurquoise;
      default:
        return RevolutionaryColors.damascusSky;
    }
  }

  IconData _getNotificationIcon(String type) {
    switch (type) {
      case 'birthday':
        return Icons.cake;
      case 'contract_expiry':
        return Icons.description;
      case 'overdue_loan':
        return Icons.money_off;
      case 'absenteeism':
        return Icons.person_off;
      case 'probation_end':
        return Icons.schedule;
      default:
        return Icons.notifications;
    }
  }

  String _formatNotificationTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inDays > 0) {
      return '${difference.inDays} يوم';
    } else if (difference.inHours > 0) {
      return '${difference.inHours} ساعة';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes} دقيقة';
    } else {
      return 'الآن';
    }
  }

  void _showNotifications() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.7,
        maxChildSize: 0.9,
        minChildSize: 0.5,
        builder: (context, scrollController) => Column(
          children: [
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: RevolutionaryColors.damascusSky,
                borderRadius: const BorderRadius.vertical(
                  top: Radius.circular(20),
                ),
              ),
              child: Row(
                children: [
                  const Icon(Icons.notifications, color: Colors.white),
                  const SizedBox(width: 8),
                  const Text(
                    'التنبيهات',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                  const Spacer(),
                  IconButton(
                    icon: const Icon(Icons.close, color: Colors.white),
                    onPressed: () => Navigator.pop(context),
                  ),
                ],
              ),
            ),
            Expanded(
              child: ListView.builder(
                controller: scrollController,
                itemCount: _notifications.length,
                itemBuilder: (context, index) {
                  final notification = _notifications[index];
                  return ListTile(
                    leading: CircleAvatar(
                      backgroundColor: _getNotificationColor(notification.type),
                      child: Icon(
                        _getNotificationIcon(notification.type),
                        color: Colors.white,
                        size: 20,
                      ),
                    ),
                    title: Text(notification.title),
                    subtitle: Text(notification.message),
                    trailing: Text(
                      _formatNotificationTime(notification.createdAt),
                      style: const TextStyle(fontSize: 12, color: Colors.grey),
                    ),
                    onTap: () {
                      _markNotificationAsRead(notification);
                      Navigator.pop(context);
                    },
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _markNotificationAsRead(HRNotification notification) async {
    await _notificationsService.markAsRead(notification.id);
    setState(() {
      _notifications.removeWhere((n) => n.id == notification.id);
    });
  }

  void _navigateToAddEmployee() {
    Navigator.pushNamed(context, '/add_employee');
  }

  void _navigateToAttendance() {
    // التنقل إلى شاشة الحضور
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('الانتقال إلى شاشة الحضور')));
  }

  void _navigateToPayroll() {
    // التنقل إلى شاشة الرواتب
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('الانتقال إلى شاشة الرواتب')));
  }

  void _navigateToReports() {
    // التنقل إلى شاشة التقارير
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('الانتقال إلى شاشة التقارير')));
  }
}
