import 'package:flutter/material.dart';
import '../constants/revolutionary_design_colors.dart';
import '../constants/app_constants.dart';

class AppLogo extends StatefulWidget {
  final double size;
  final bool showText;

  const AppLogo({super.key, this.size = 60, this.showText = false});

  @override
  State<AppLogo> createState() => _AppLogoState();
}

class _AppLogoState extends State<AppLogo> with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _rotationAnimation;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(seconds: 3),
      vsync: this,
    );

    _rotationAnimation = Tween<double>(begin: 0, end: 1).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    _scaleAnimation = Tween<double>(begin: 0.8, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.elasticOut),
    );

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: widget.showText
              ? Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    _buildLogoIcon(),
                    const SizedBox(height: 8),
                    Text(
                      AppConstants.appNameArabic,
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        color: RevolutionaryColors.damascusSky,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                )
              : _buildLogoIcon(),
        );
      },
    );
  }

  Widget _buildLogoIcon() {
    return Container(
      width: widget.size,
      height: widget.size,
      decoration: BoxDecoration(
        gradient: RevolutionaryColors.interactiveGradient,
        borderRadius: BorderRadius.circular(widget.size * 0.2),
        boxShadow: [
          BoxShadow(
            color: RevolutionaryColors.damascusSky.withValues(alpha: 0.3),
            blurRadius: 12,
            offset: const Offset(0, 6),
          ),
        ],
      ),
      child: Stack(
        alignment: Alignment.center,
        children: [
          // الخلفية الدائرية
          Container(
            width: widget.size * 0.8,
            height: widget.size * 0.8,
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.2),
              shape: BoxShape.circle,
            ),
          ),
          // الأيقونة الرئيسية
          Transform.rotate(
            angle: _rotationAnimation.value * 0.1,
            child: Icon(
              Icons.account_balance,
              size: widget.size * 0.5,
              color: Colors.white,
            ),
          ),
          // العناصر الزخرفية
          Positioned(
            top: widget.size * 0.15,
            right: widget.size * 0.15,
            child: Transform.rotate(
              angle: _rotationAnimation.value * 2,
              child: Container(
                width: widget.size * 0.1,
                height: widget.size * 0.1,
                decoration: const BoxDecoration(
                  color: Colors.white,
                  shape: BoxShape.circle,
                ),
              ),
            ),
          ),
          Positioned(
            bottom: widget.size * 0.2,
            left: widget.size * 0.2,
            child: Transform.rotate(
              angle: -_rotationAnimation.value * 1.5,
              child: Container(
                width: widget.size * 0.08,
                height: widget.size * 0.08,
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.8),
                  shape: BoxShape.circle,
                ),
              ),
            ),
          ),
          // خط زخرفي
          Positioned(
            bottom: widget.size * 0.25,
            child: Transform.rotate(
              angle: _rotationAnimation.value * 0.5,
              child: Container(
                width: widget.size * 0.3,
                height: 2,
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.6),
                  borderRadius: BorderRadius.circular(1),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
