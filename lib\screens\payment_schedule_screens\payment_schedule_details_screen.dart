/// شاشة تفاصيل جدولة الدفعات
/// تعرض تفاصيل شاملة لجدولة الدفعات مع إمكانية إدارة الدفعات والتذكيرات
library;

import 'package:flutter/material.dart';
import '../../models/payment_schedule.dart';
import '../../models/payment.dart';
import '../../models/invoice.dart';
import '../../services/payment_schedule_service.dart';
import '../../services/payment_service.dart';
import '../../services/invoice_service.dart';
import '../../constants/revolutionary_design_colors.dart';
import '../../widgets/loading_widget.dart';

class PaymentScheduleDetailsScreen extends StatefulWidget {
  final PaymentSchedule schedule;
  final VoidCallback onScheduleUpdated;

  const PaymentScheduleDetailsScreen({
    super.key,
    required this.schedule,
    required this.onScheduleUpdated,
  });

  @override
  State<PaymentScheduleDetailsScreen> createState() =>
      _PaymentScheduleDetailsScreenState();
}

class _PaymentScheduleDetailsScreenState
    extends State<PaymentScheduleDetailsScreen>
    with TickerProviderStateMixin {
  final PaymentScheduleService _scheduleService = PaymentScheduleService();
  final PaymentService _paymentService = PaymentService();
  final InvoiceService _invoiceService = InvoiceService();

  late TabController _tabController;
  bool _isLoading = true;
  PaymentSchedule? _currentSchedule;
  Invoice? _invoice;
  List<Payment> _payments = [];
  List<PaymentReminder> _reminders = [];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    setState(() => _isLoading = true);
    try {
      // تحميل بيانات الجدولة المحدثة
      final schedule = await _scheduleService.getPaymentScheduleById(
        widget.schedule.id!,
      );

      // تحميل بيانات الفاتورة
      final invoice = await _invoiceService.getInvoiceById(
        widget.schedule.invoiceId,
      );

      // تحميل الدفعات المرتبطة بالجدولة
      final payments = await _paymentService.getInvoicePayments(
        widget.schedule.invoiceId,
      );

      // تحميل التذكيرات
      final reminders = schedule?.reminders ?? [];

      setState(() {
        _currentSchedule = schedule;
        _invoice = invoice;
        _payments = payments;
        _reminders = reminders;
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      _showError('خطأ في تحميل البيانات: $e');
    }
  }

  void _showError(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message), backgroundColor: Colors.red),
    );
  }

  void _showSuccess(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: RevolutionaryColors.successGlow,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: RevolutionaryColors.backgroundPrimary,
      appBar: AppBar(
        title: Text('تفاصيل الجدولة: ${widget.schedule.scheduleName}'),
        backgroundColor: RevolutionaryColors.damascusSky,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          PopupMenuButton<String>(
            onSelected: _handleAction,
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'edit',
                child: Row(
                  children: [
                    Icon(Icons.edit),
                    SizedBox(width: 8),
                    Text('تعديل'),
                  ],
                ),
              ),
              if (_currentSchedule?.status == ScheduleStatus.active)
                const PopupMenuItem(
                  value: 'pause',
                  child: Row(
                    children: [
                      Icon(Icons.pause),
                      SizedBox(width: 8),
                      Text('إيقاف مؤقت'),
                    ],
                  ),
                ),
              if (_currentSchedule?.status == ScheduleStatus.paused)
                const PopupMenuItem(
                  value: 'resume',
                  child: Row(
                    children: [
                      Icon(Icons.play_arrow),
                      SizedBox(width: 8),
                      Text('استئناف'),
                    ],
                  ),
                ),
              const PopupMenuItem(
                value: 'cancel',
                child: Row(
                  children: [
                    Icon(Icons.cancel),
                    SizedBox(width: 8),
                    Text('إلغاء'),
                  ],
                ),
              ),
            ],
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          indicatorColor: Colors.white,
          tabs: const [
            Tab(text: 'التفاصيل'),
            Tab(text: 'الدفعات'),
            Tab(text: 'التذكيرات'),
          ],
        ),
      ),
      body: _isLoading
          ? const LoadingWidget()
          : TabBarView(
              controller: _tabController,
              children: [
                _buildDetailsTab(),
                _buildPaymentsTab(),
                _buildRemindersTab(),
              ],
            ),
    );
  }

  Widget _buildDetailsTab() {
    if (_currentSchedule == null || _invoice == null) {
      return const Center(child: Text('لا توجد بيانات'));
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          _buildScheduleInfoCard(),
          const SizedBox(height: 16),
          _buildInvoiceInfoCard(),
          const SizedBox(height: 16),
          _buildProgressCard(),
          const SizedBox(height: 16),
          _buildStatusCard(),
        ],
      ),
    );
  }

  Widget _buildScheduleInfoCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'معلومات الجدولة',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: RevolutionaryColors.damascusSky,
              ),
            ),
            const SizedBox(height: 16),
            _buildInfoRow('اسم الجدولة', _currentSchedule!.scheduleName),
            _buildInfoRow(
              'تكرار الدفع',
              _currentSchedule!.frequency.displayName,
            ),
            _buildInfoRow(
              'مبلغ القسط',
              '${_currentSchedule!.installmentAmount.toStringAsFixed(2)} ل.س',
            ),
            _buildInfoRow(
              'عدد الأقساط',
              '${_currentSchedule!.totalInstallments}',
            ),
            _buildInfoRow(
              'الأقساط المكتملة',
              '${_currentSchedule!.completedInstallments}',
            ),
            _buildInfoRow(
              'تاريخ البداية',
              _formatDate(_currentSchedule!.startDate),
            ),
            _buildInfoRow(
              'تاريخ الدفعة التالية',
              _formatDate(_currentSchedule!.nextPaymentDate),
            ),
            if (_currentSchedule!.endDate != null)
              _buildInfoRow(
                'تاريخ النهاية',
                _formatDate(_currentSchedule!.endDate!),
              ),
            _buildInfoRow(
              'الدفع التلقائي',
              _currentSchedule!.autoPayment ? 'مفعل' : 'غير مفعل',
            ),
            if (_currentSchedule!.notes?.isNotEmpty == true)
              _buildInfoRow('ملاحظات', _currentSchedule!.notes!),
          ],
        ),
      ),
    );
  }

  Widget _buildInvoiceInfoCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'معلومات الفاتورة',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: RevolutionaryColors.damascusSky,
              ),
            ),
            const SizedBox(height: 16),
            _buildInfoRow('رقم الفاتورة', _invoice!.invoiceNumber),
            _buildInfoRow(
              'المبلغ الإجمالي',
              '${_invoice!.totalAmount.toStringAsFixed(2)} ل.س',
            ),
            _buildInfoRow(
              'المبلغ المدفوع',
              '${_invoice!.paidAmount.toStringAsFixed(2)} ل.س',
            ),
            _buildInfoRow(
              'المبلغ المتبقي',
              '${_invoice!.remainingAmount.toStringAsFixed(2)} ل.س',
            ),
            _buildInfoRow('تاريخ الفاتورة', _formatDate(_invoice!.invoiceDate)),
          ],
        ),
      ),
    );
  }

  Widget _buildProgressCard() {
    final progress =
        _currentSchedule!.completedInstallments /
        _currentSchedule!.totalInstallments;
    final remainingAmount =
        _currentSchedule!.totalAmount - _currentSchedule!.paidAmount;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'تقدم الجدولة',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: RevolutionaryColors.damascusSky,
              ),
            ),
            const SizedBox(height: 16),
            LinearProgressIndicator(
              value: progress,
              backgroundColor: RevolutionaryColors.backgroundSecondary,
              valueColor: AlwaysStoppedAnimation<Color>(
                RevolutionaryColors.successGlow,
              ),
            ),
            const SizedBox(height: 8),
            Text('${(progress * 100).toStringAsFixed(1)}% مكتمل'),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildProgressItem(
                    'المبلغ المدفوع',
                    '${_currentSchedule!.paidAmount.toStringAsFixed(2)} ل.س',
                    RevolutionaryColors.successGlow,
                  ),
                ),
                Expanded(
                  child: _buildProgressItem(
                    'المبلغ المتبقي',
                    '${remainingAmount.toStringAsFixed(2)} ل.س',
                    RevolutionaryColors.warningAmber,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'حالة الجدولة',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: RevolutionaryColors.damascusSky,
              ),
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              decoration: BoxDecoration(
                color: _getStatusColor(_currentSchedule!.status),
                borderRadius: BorderRadius.circular(20),
              ),
              child: Text(
                _currentSchedule!.status.displayName,
                style: const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            const SizedBox(height: 8),
            Text(_currentSchedule!.status.description),
          ],
        ),
      ),
    );
  }

  Widget _buildPaymentsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          if (_currentSchedule?.status == ScheduleStatus.active)
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Row(
                  children: [
                    Expanded(
                      child: Text(
                        'الدفعة التالية: ${_currentSchedule!.installmentAmount.toStringAsFixed(2)} ل.س',
                        style: const TextStyle(fontWeight: FontWeight.bold),
                      ),
                    ),
                    ElevatedButton(
                      onPressed: _recordPayment,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: RevolutionaryColors.successGlow,
                        foregroundColor: Colors.white,
                      ),
                      child: const Text('تسجيل دفعة'),
                    ),
                  ],
                ),
              ),
            ),
          const SizedBox(height: 16),
          if (_payments.isEmpty)
            const Card(
              child: Padding(
                padding: EdgeInsets.all(32),
                child: Center(child: Text('لا توجد دفعات مسجلة')),
              ),
            )
          else
            ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: _payments.length,
              itemBuilder: (context, index) {
                final payment = _payments[index];
                return Card(
                  child: ListTile(
                    leading: CircleAvatar(
                      backgroundColor: RevolutionaryColors.successGlow,
                      child: const Icon(Icons.payment, color: Colors.white),
                    ),
                    title: Text('${payment.amount.toStringAsFixed(2)} ل.س'),
                    subtitle: Text(_formatDate(payment.paymentDate)),
                    trailing: Text(payment.method.displayName),
                  ),
                );
              },
            ),
        ],
      ),
    );
  }

  Widget _buildRemindersTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          if (_reminders.isEmpty)
            const Card(
              child: Padding(
                padding: EdgeInsets.all(32),
                child: Center(child: Text('لا توجد تذكيرات مضافة')),
              ),
            )
          else
            ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: _reminders.length,
              itemBuilder: (context, index) {
                final reminder = _reminders[index];
                return Card(
                  child: ListTile(
                    leading: CircleAvatar(
                      backgroundColor: reminder.isActive
                          ? RevolutionaryColors.damascusSky
                          : RevolutionaryColors.textSecondary,
                      child: Icon(
                        _getReminderIcon(reminder.type),
                        color: Colors.white,
                      ),
                    ),
                    title: Text(reminder.title),
                    subtitle: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(reminder.message),
                        Text('${reminder.daysBefore} أيام قبل الاستحقاق'),
                      ],
                    ),
                    trailing: Switch(
                      value: reminder.isActive,
                      onChanged: (value) => _toggleReminder(reminder, value),
                    ),
                  ),
                );
              },
            ),
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: RevolutionaryColors.textSecondary,
              ),
            ),
          ),
          Expanded(child: Text(value)),
        ],
      ),
    );
  }

  Widget _buildProgressItem(String label, String value, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: RevolutionaryColors.textSecondary,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: TextStyle(fontWeight: FontWeight.bold, color: color),
          ),
        ],
      ),
    );
  }

  Color _getStatusColor(ScheduleStatus status) {
    switch (status) {
      case ScheduleStatus.active:
        return RevolutionaryColors.successGlow;
      case ScheduleStatus.paused:
        return RevolutionaryColors.warningAmber;
      case ScheduleStatus.completed:
        return RevolutionaryColors.damascusSky;
      case ScheduleStatus.cancelled:
        return Colors.red;
      case ScheduleStatus.expired:
        return RevolutionaryColors.textSecondary;
    }
  }

  IconData _getReminderIcon(ReminderType type) {
    switch (type) {
      case ReminderType.notification:
        return Icons.notifications;
      case ReminderType.email:
        return Icons.email;
      case ReminderType.sms:
        return Icons.sms;
      case ReminderType.inApp:
        return Icons.app_registration;
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day.toString().padLeft(2, '0')}/'
        '${date.month.toString().padLeft(2, '0')}/'
        '${date.year}';
  }

  Future<void> _recordPayment() async {
    try {
      await _scheduleService.recordScheduledPayment(
        _currentSchedule!.id!,
        _currentSchedule!.installmentAmount,
      );
      _showSuccess('تم تسجيل الدفعة بنجاح');
      widget.onScheduleUpdated();
      _loadData();
    } catch (e) {
      _showError('خطأ في تسجيل الدفعة: $e');
    }
  }

  Future<void> _toggleReminder(PaymentReminder reminder, bool isActive) async {
    try {
      // تحديث حالة التذكير
      final updatedReminder = reminder.copyWith(isActive: isActive);
      await _scheduleService.updateReminder(updatedReminder);

      // تحديث البيانات المحلية
      setState(() {
        final index = _reminders.indexWhere((r) => r.id == reminder.id);
        if (index != -1) {
          _reminders[index] = updatedReminder;
        }

        // تحديث الجدولة الحالية أيضاً
        if (_currentSchedule != null) {
          final scheduleReminderIndex = _currentSchedule!.reminders.indexWhere(
            (r) => r.id == reminder.id,
          );
          if (scheduleReminderIndex != -1) {
            _currentSchedule = _currentSchedule!.copyWith(
              reminders: [
                ..._currentSchedule!.reminders.take(scheduleReminderIndex),
                updatedReminder,
                ..._currentSchedule!.reminders.skip(scheduleReminderIndex + 1),
              ],
            );
          }
        }
      });

      _showSuccess(isActive ? 'تم تفعيل التذكير' : 'تم إلغاء تفعيل التذكير');
    } catch (e) {
      _showError('خطأ في تحديث التذكير: $e');
    }
  }

  Future<void> _handleAction(String action) async {
    try {
      switch (action) {
        case 'edit':
          // سيتم تنفيذه في شاشة التعديل
          break;
        case 'pause':
          await _scheduleService.pauseSchedule(_currentSchedule!.id!);
          _showSuccess('تم إيقاف الجدولة مؤقتاً');
          widget.onScheduleUpdated();
          _loadData();
          break;
        case 'resume':
          await _scheduleService.resumeSchedule(_currentSchedule!.id!);
          _showSuccess('تم استئناف الجدولة');
          widget.onScheduleUpdated();
          _loadData();
          break;
        case 'cancel':
          final confirmed = await _showCancelConfirmation();
          if (confirmed) {
            await _scheduleService.cancelSchedule(_currentSchedule!.id!);
            _showSuccess('تم إلغاء الجدولة');
            widget.onScheduleUpdated();
            if (mounted) {
              Navigator.of(context).pop();
            }
          }
          break;
      }
    } catch (e) {
      _showError('خطأ في تنفيذ العملية: $e');
    }
  }

  Future<bool> _showCancelConfirmation() async {
    return await showDialog<bool>(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('تأكيد الإلغاء'),
            content: const Text('هل أنت متأكد من إلغاء هذه الجدولة؟'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: const Text('لا'),
              ),
              ElevatedButton(
                onPressed: () => Navigator.of(context).pop(true),
                style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
                child: const Text('نعم، إلغاء'),
              ),
            ],
          ),
        ) ??
        false;
  }
}
