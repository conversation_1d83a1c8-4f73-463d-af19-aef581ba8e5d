/// خدمة إدارة العقود المتقدمة
/// توفر إدارة شاملة لعقود الموظفين مع التنبيهات والتجديد التلقائي
library;

import '../database/database_helper.dart';
import '../constants/app_constants.dart';
import '../services/logging_service.dart';
import '../services/hr_notifications_service.dart';
import '../models/hr_models.dart';

class AdvancedContractService {
  final DatabaseHelper _databaseHelper = DatabaseHelper();
  final HRNotificationsService _notificationsService = HRNotificationsService();

  /// إنشاء جدول العقود المتقدم
  Future<void> _createContractsTable() async {
    final db = await _databaseHelper.database;
    await db.execute('''
      CREATE TABLE IF NOT EXISTS employee_contracts (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        employee_id INTEGER NOT NULL,
        contract_type TEXT NOT NULL DEFAULT 'permanent',
        start_date TEXT NOT NULL,
        end_date TEXT,
        salary REAL NOT NULL,
        working_hours TEXT DEFAULT '8 hours',
        vacation_days INTEGER DEFAULT 21,
        benefits TEXT,
        terms TEXT,
        status TEXT NOT NULL DEFAULT 'active',
        notes TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        FOREIGN KEY (employee_id) REFERENCES employees (id)
      )
    ''');
  }

  /// إضافة عقد جديد
  Future<EmployeeContract> addContract(EmployeeContract contract) async {
    try {
      await _createContractsTable();
      final db = await _databaseHelper.database;
      
      final contractData = contract.toMap();
      contractData.remove('id'); // إزالة المعرف للإدراج التلقائي
      
      final id = await db.insert('employee_contracts', contractData);
      
      final newContract = contract.copyWith(id: id);
      
      // إنشاء تنبيه للعقد الجديد
      await _createContractNotification(newContract, 'contract_created');
      
      LoggingService.info(
        'تم إضافة عقد جديد',
        category: 'AdvancedContractService',
        data: {
          'contractId': id,
          'employeeId': contract.employeeId,
          'contractType': contract.contractType,
        },
      );
      
      return newContract;
    } catch (e) {
      LoggingService.error(
        'خطأ في إضافة العقد',
        category: 'AdvancedContractService',
        data: {'error': e.toString()},
      );
      rethrow;
    }
  }

  /// الحصول على جميع العقود
  Future<List<EmployeeContract>> getAllContracts() async {
    try {
      await _createContractsTable();
      final db = await _databaseHelper.database;
      
      final result = await db.query(
        'employee_contracts',
        orderBy: 'created_at DESC',
      );
      
      return result.map((map) => EmployeeContract.fromMap(map)).toList();
    } catch (e) {
      LoggingService.error(
        'خطأ في الحصول على العقود',
        category: 'AdvancedContractService',
        data: {'error': e.toString()},
      );
      return [];
    }
  }

  /// الحصول على عقود موظف معين
  Future<List<EmployeeContract>> getEmployeeContracts(int employeeId) async {
    try {
      await _createContractsTable();
      final db = await _databaseHelper.database;
      
      final result = await db.query(
        'employee_contracts',
        where: 'employee_id = ?',
        whereArgs: [employeeId],
        orderBy: 'created_at DESC',
      );
      
      return result.map((map) => EmployeeContract.fromMap(map)).toList();
    } catch (e) {
      LoggingService.error(
        'خطأ في الحصول على عقود الموظف',
        category: 'AdvancedContractService',
        data: {'employeeId': employeeId, 'error': e.toString()},
      );
      return [];
    }
  }

  /// الحصول على العقود النشطة
  Future<List<EmployeeContract>> getActiveContracts() async {
    try {
      final allContracts = await getAllContracts();
      return allContracts.where((contract) => contract.isActive).toList();
    } catch (e) {
      LoggingService.error(
        'خطأ في الحصول على العقود النشطة',
        category: 'AdvancedContractService',
        data: {'error': e.toString()},
      );
      return [];
    }
  }

  /// الحصول على العقود المنتهية قريباً
  Future<List<EmployeeContract>> getExpiringSoonContracts({int daysAhead = 30}) async {
    try {
      final allContracts = await getAllContracts();
      final now = DateTime.now();
      final futureDate = now.add(Duration(days: daysAhead));
      
      return allContracts.where((contract) {
        if (contract.endDate == null) return false;
        return contract.endDate!.isAfter(now) && 
               contract.endDate!.isBefore(futureDate) &&
               contract.status == AppConstants.contractStatusActive;
      }).toList();
    } catch (e) {
      LoggingService.error(
        'خطأ في الحصول على العقود المنتهية قريباً',
        category: 'AdvancedContractService',
        data: {'error': e.toString()},
      );
      return [];
    }
  }

  /// تحديث عقد
  Future<EmployeeContract> updateContract(EmployeeContract contract) async {
    try {
      await _createContractsTable();
      final db = await _databaseHelper.database;
      
      final contractData = contract.toMap();
      contractData['updated_at'] = DateTime.now().toIso8601String();
      
      await db.update(
        'employee_contracts',
        contractData,
        where: 'id = ?',
        whereArgs: [contract.id],
      );
      
      final updatedContract = contract.copyWith(updatedAt: DateTime.now());
      
      LoggingService.info(
        'تم تحديث العقد',
        category: 'AdvancedContractService',
        data: {
          'contractId': contract.id,
          'employeeId': contract.employeeId,
        },
      );
      
      return updatedContract;
    } catch (e) {
      LoggingService.error(
        'خطأ في تحديث العقد',
        category: 'AdvancedContractService',
        data: {'contractId': contract.id, 'error': e.toString()},
      );
      rethrow;
    }
  }

  /// تجديد عقد
  Future<EmployeeContract> renewContract(
    int contractId,
    DateTime newEndDate, {
    double? newSalary,
    String? notes,
  }) async {
    try {
      await _createContractsTable();
      final db = await _databaseHelper.database;
      
      // الحصول على العقد الحالي
      final result = await db.query(
        'employee_contracts',
        where: 'id = ?',
        whereArgs: [contractId],
      );
      
      if (result.isEmpty) {
        throw Exception('العقد غير موجود');
      }
      
      final currentContract = EmployeeContract.fromMap(result.first);
      
      // إنشاء عقد جديد بناءً على العقد الحالي
      final renewedContract = currentContract.copyWith(
        id: null, // سيتم إنشاء معرف جديد
        endDate: newEndDate,
        salary: newSalary ?? currentContract.salary,
        notes: notes ?? currentContract.notes,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );
      
      // إضافة العقد المجدد
      final newContract = await addContract(renewedContract);
      
      // تحديث العقد القديم كمنتهي
      await updateContract(currentContract.copyWith(
        status: AppConstants.contractStatusExpired,
        updatedAt: DateTime.now(),
      ));
      
      // إنشاء تنبيه للتجديد
      await _createContractNotification(newContract, 'contract_renewed');
      
      LoggingService.info(
        'تم تجديد العقد',
        category: 'AdvancedContractService',
        data: {
          'oldContractId': contractId,
          'newContractId': newContract.id,
          'employeeId': currentContract.employeeId,
        },
      );
      
      return newContract;
    } catch (e) {
      LoggingService.error(
        'خطأ في تجديد العقد',
        category: 'AdvancedContractService',
        data: {'contractId': contractId, 'error': e.toString()},
      );
      rethrow;
    }
  }

  /// إنهاء عقد
  Future<void> terminateContract(int contractId, String reason) async {
    try {
      await _createContractsTable();
      final db = await _databaseHelper.database;
      
      await db.update(
        'employee_contracts',
        {
          'status': AppConstants.contractStatusTerminated,
          'notes': reason,
          'updated_at': DateTime.now().toIso8601String(),
        },
        where: 'id = ?',
        whereArgs: [contractId],
      );
      
      LoggingService.info(
        'تم إنهاء العقد',
        category: 'AdvancedContractService',
        data: {
          'contractId': contractId,
          'reason': reason,
        },
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في إنهاء العقد',
        category: 'AdvancedContractService',
        data: {'contractId': contractId, 'error': e.toString()},
      );
      rethrow;
    }
  }

  /// فحص العقود المنتهية وإنشاء التنبيهات
  Future<void> checkExpiringContracts() async {
    try {
      final expiringContracts = await getExpiringSoonContracts();
      
      for (final contract in expiringContracts) {
        await _createContractNotification(contract, 'contract_expiring');
      }
      
      LoggingService.info(
        'تم فحص العقود المنتهية',
        category: 'AdvancedContractService',
        data: {'expiringContractsCount': expiringContracts.length},
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في فحص العقود المنتهية',
        category: 'AdvancedContractService',
        data: {'error': e.toString()},
      );
    }
  }

  /// إنشاء تنبيه للعقد
  Future<void> _createContractNotification(
    EmployeeContract contract,
    String notificationType,
  ) async {
    try {
      String title = '';
      String message = '';
      String priority = 'medium';
      
      switch (notificationType) {
        case 'contract_created':
          title = 'عقد جديد';
          message = 'تم إنشاء عقد جديد للموظف رقم ${contract.employeeId}';
          priority = 'low';
          break;
        case 'contract_renewed':
          title = 'تجديد عقد';
          message = 'تم تجديد عقد الموظف رقم ${contract.employeeId}';
          priority = 'medium';
          break;
        case 'contract_expiring':
          final daysLeft = contract.endDate?.difference(DateTime.now()).inDays ?? 0;
          title = 'عقد ينتهي قريباً';
          message = 'عقد الموظف رقم ${contract.employeeId} ينتهي خلال $daysLeft يوم';
          priority = daysLeft <= 7 ? 'high' : 'medium';
          break;
      }
      
      final notification = HRNotification(
        id: '${notificationType}_${contract.id}_${DateTime.now().millisecondsSinceEpoch}',
        title: title,
        message: message,
        type: 'contract_expiry',
        priority: priority,
        createdAt: DateTime.now(),
        data: {
          'contractId': contract.id,
          'employeeId': contract.employeeId,
          'contractType': contract.contractType,
          'endDate': contract.endDate?.toIso8601String(),
        },
      );
      
      await _notificationsService.addNotification(notification);
    } catch (e) {
      LoggingService.error(
        'خطأ في إنشاء تنبيه العقد',
        category: 'AdvancedContractService',
        data: {'contractId': contract.id, 'error': e.toString()},
      );
    }
  }

  /// الحصول على إحصائيات العقود
  Future<Map<String, dynamic>> getContractsStatistics() async {
    try {
      final allContracts = await getAllContracts();
      final activeContracts = allContracts.where((c) => c.isActive).length;
      final expiredContracts = allContracts.where((c) => c.isExpired).length;
      final terminatedContracts = allContracts.where((c) => c.status == AppConstants.contractStatusTerminated).length;
      final expiringContracts = await getExpiringSoonContracts();
      
      return {
        'totalContracts': allContracts.length,
        'activeContracts': activeContracts,
        'expiredContracts': expiredContracts,
        'terminatedContracts': terminatedContracts,
        'expiringSoonContracts': expiringContracts.length,
        'averageSalary': allContracts.isNotEmpty 
            ? allContracts.map((c) => c.salary).reduce((a, b) => a + b) / allContracts.length
            : 0.0,
      };
    } catch (e) {
      LoggingService.error(
        'خطأ في الحصول على إحصائيات العقود',
        category: 'AdvancedContractService',
        data: {'error': e.toString()},
      );
      return {
        'totalContracts': 0,
        'activeContracts': 0,
        'expiredContracts': 0,
        'terminatedContracts': 0,
        'expiringSoonContracts': 0,
        'averageSalary': 0.0,
      };
    }
  }
}
