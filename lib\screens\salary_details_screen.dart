/// شاشة تفاصيل الراتب
/// توفر واجهة شاملة لعرض وإدارة تفاصيل راتب الموظف
library;

import 'package:flutter/material.dart';
import '../models/hr_models.dart';
import '../services/logging_service.dart';
import '../widgets/loading_widget.dart';
import '../widgets/custom_error_widget.dart' as custom_widgets;
import '../constants/app_constants.dart';

class SalaryDetailsScreen extends StatefulWidget {
  final Employee employee;

  const SalaryDetailsScreen({super.key, required this.employee});

  @override
  State<SalaryDetailsScreen> createState() => _SalaryDetailsScreenState();
}

class _SalaryDetailsScreenState extends State<SalaryDetailsScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  Salary? _currentSalary;
  List<SalaryDetail> _salaryDetails = [];
  List<Salary> _salaryHistory = [];
  bool _isLoading = true;
  String? _error;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadSalaryData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadSalaryData() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      // في التطبيق الحقيقي، ستكون هناك خدمات منفصلة للراتب
      // هنا سنستخدم بيانات وهمية للعرض
      await Future.delayed(const Duration(milliseconds: 500));

      _currentSalary = Salary(
        id: 1,
        employeeId: widget.employee.id!,
        basicSalary: widget.employee.basicSalary,
        totalAllowances: widget.employee.basicSalary * 0.3,
        totalDeductions: widget.employee.basicSalary * 0.15,
        grossSalary: widget.employee.basicSalary * 1.3,
        netSalary: widget.employee.basicSalary * 1.15,
        effectiveFrom: DateTime.now().subtract(const Duration(days: 365)),
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      _salaryDetails = [
        SalaryDetail(
          id: 1,
          employeeId: widget.employee.id!,
          componentType: AppConstants.salaryComponentAllowance,
          componentName: 'بدل نقل',
          amount: widget.employee.basicSalary * 0.1,
          effectiveFrom: DateTime.now().subtract(const Duration(days: 365)),
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
        SalaryDetail(
          id: 2,
          employeeId: widget.employee.id!,
          componentType: AppConstants.salaryComponentAllowance,
          componentName: 'بدل طعام',
          amount: widget.employee.basicSalary * 0.15,
          effectiveFrom: DateTime.now().subtract(const Duration(days: 365)),
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
        SalaryDetail(
          id: 3,
          employeeId: widget.employee.id!,
          componentType: AppConstants.salaryComponentAllowance,
          componentName: 'بدل سكن',
          amount: widget.employee.basicSalary * 0.05,
          effectiveFrom: DateTime.now().subtract(const Duration(days: 365)),
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
        SalaryDetail(
          id: 4,
          employeeId: widget.employee.id!,
          componentType: AppConstants.salaryComponentDeduction,
          componentName: 'تأمينات اجتماعية',
          amount: widget.employee.basicSalary * 0.07,
          effectiveFrom: DateTime.now().subtract(const Duration(days: 365)),
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
        SalaryDetail(
          id: 5,
          employeeId: widget.employee.id!,
          componentType: AppConstants.salaryComponentDeduction,
          componentName: 'ضريبة دخل',
          amount: widget.employee.basicSalary * 0.08,
          effectiveFrom: DateTime.now().subtract(const Duration(days: 365)),
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
      ];

      _salaryHistory = [
        _currentSalary!,
        // يمكن إضافة المزيد من التاريخ هنا
      ];

      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });

      LoggingService.error(
        'خطأ في تحميل تفاصيل الراتب',
        category: 'SalaryDetailsScreen',
        data: {'employeeId': widget.employee.id, 'error': e.toString()},
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'راتب ${widget.employee.firstName} ${widget.employee.lastName}',
        ),
        backgroundColor: Colors.teal[700],
        foregroundColor: Colors.white,
        bottom: TabBar(
          controller: _tabController,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          indicatorColor: Colors.white,
          tabs: const [
            Tab(
              icon: Icon(Icons.account_balance_wallet),
              text: 'الراتب الحالي',
            ),
            Tab(icon: Icon(Icons.list_alt), text: 'تفاصيل الراتب'),
            Tab(icon: Icon(Icons.history), text: 'تاريخ الرواتب'),
          ],
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.edit),
            onPressed: () => _showEditSalaryDialog(),
            tooltip: 'تعديل الراتب',
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadSalaryData,
            tooltip: 'تحديث',
          ),
        ],
      ),
      body: _buildBody(),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _showAddSalaryComponentDialog(),
        backgroundColor: Colors.teal[700],
        tooltip: 'إضافة عنصر راتب',
        child: const Icon(Icons.add, color: Colors.white),
      ),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const LoadingWidget();
    }

    if (_error != null) {
      return custom_widgets.ErrorWidget(
        message: _error!,
        onRetry: _loadSalaryData,
      );
    }

    return TabBarView(
      controller: _tabController,
      children: [
        _buildCurrentSalaryTab(),
        _buildSalaryDetailsTab(),
        _buildSalaryHistoryTab(),
      ],
    );
  }

  Widget _buildCurrentSalaryTab() {
    if (_currentSalary == null) {
      return const Center(child: Text('لا توجد بيانات راتب'));
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          _buildSalarySummaryCard(),
          const SizedBox(height: 16),
          _buildSalaryBreakdownCard(),
          const SizedBox(height: 16),
          _buildSalaryCalculationCard(),
        ],
      ),
    );
  }

  Widget _buildSalarySummaryCard() {
    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          children: [
            Row(
              children: [
                Icon(
                  Icons.account_balance_wallet,
                  color: Colors.teal[700],
                  size: 32,
                ),
                const SizedBox(width: 12),
                const Text(
                  'ملخص الراتب',
                  style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const Divider(height: 24),
            _buildSummaryRow(
              'الراتب الأساسي',
              _currentSalary!.basicSalary,
              Colors.blue,
            ),
            _buildSummaryRow(
              'إجمالي البدلات',
              _currentSalary!.totalAllowances,
              Colors.green,
            ),
            _buildSummaryRow(
              'إجمالي الاستقطاعات',
              _currentSalary!.totalDeductions,
              Colors.red,
            ),
            const Divider(),
            _buildSummaryRow(
              'الراتب الإجمالي',
              _currentSalary!.grossSalary,
              Colors.orange,
              isTotal: true,
            ),
            _buildSummaryRow(
              'الراتب الصافي',
              _currentSalary!.netSalary,
              Colors.teal[700]!,
              isTotal: true,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryRow(
    String label,
    double amount,
    Color color, {
    bool isTotal = false,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: isTotal ? 16 : 14,
              fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
            ),
          ),
          Text(
            '${amount.toStringAsFixed(0)} ل.س',
            style: TextStyle(
              fontSize: isTotal ? 16 : 14,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSalaryBreakdownCard() {
    final allowances = _salaryDetails.where((d) => d.isAllowance).toList();
    final deductions = _salaryDetails.where((d) => d.isDeduction).toList();

    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.pie_chart, color: Colors.teal[700], size: 32),
                const SizedBox(width: 12),
                const Text(
                  'تفصيل الراتب',
                  style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const Divider(height: 24),

            // البدلات
            if (allowances.isNotEmpty) ...[
              Row(
                children: [
                  Icon(Icons.add_circle, color: Colors.green, size: 20),
                  const SizedBox(width: 8),
                  const Text(
                    'البدلات',
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              ...allowances.map(
                (detail) => _buildDetailRow(detail, Colors.green),
              ),
              const SizedBox(height: 16),
            ],

            // الاستقطاعات
            if (deductions.isNotEmpty) ...[
              Row(
                children: [
                  Icon(Icons.remove_circle, color: Colors.red, size: 20),
                  const SizedBox(width: 8),
                  const Text(
                    'الاستقطاعات',
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              ...deductions.map(
                (detail) => _buildDetailRow(detail, Colors.red),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildDetailRow(SalaryDetail detail, Color color) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
            child: Text(
              detail.componentName,
              style: const TextStyle(fontSize: 14),
            ),
          ),
          Text(
            '${detail.amount.toStringAsFixed(0)} ل.س',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSalaryCalculationCard() {
    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.calculate, color: Colors.teal[700], size: 32),
                const SizedBox(width: 12),
                const Text(
                  'حساب الراتب',
                  style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const Divider(height: 24),
            _buildCalculationStep(
              'الراتب الأساسي',
              _currentSalary!.basicSalary,
              '+',
            ),
            _buildCalculationStep(
              'البدلات',
              _currentSalary!.totalAllowances,
              '+',
            ),
            _buildCalculationStep(
              'الاستقطاعات',
              _currentSalary!.totalDeductions,
              '-',
            ),
            const Divider(),
            _buildCalculationStep(
              'الراتب الصافي',
              _currentSalary!.netSalary,
              '=',
              isResult: true,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCalculationStep(
    String label,
    double amount,
    String operator, {
    bool isResult = false,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          if (operator != '+')
            Text(
              operator,
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: operator == '-' ? Colors.red : Colors.teal[700],
              ),
            ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              label,
              style: TextStyle(
                fontSize: isResult ? 16 : 14,
                fontWeight: isResult ? FontWeight.bold : FontWeight.normal,
              ),
            ),
          ),
          Text(
            '${amount.toStringAsFixed(0)} ل.س',
            style: TextStyle(
              fontSize: isResult ? 16 : 14,
              fontWeight: FontWeight.bold,
              color: isResult ? Colors.teal[700] : null,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSalaryDetailsTab() {
    return RefreshIndicator(
      onRefresh: _loadSalaryData,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: _salaryDetails.length,
        itemBuilder: (context, index) {
          final detail = _salaryDetails[index];
          return _buildSalaryDetailCard(detail);
        },
      ),
    );
  }

  Widget _buildSalaryDetailCard(SalaryDetail detail) {
    Color cardColor;
    IconData icon;

    if (detail.isAllowance) {
      cardColor = Colors.green[50]!;
      icon = Icons.add_circle;
    } else if (detail.isDeduction) {
      cardColor = Colors.red[50]!;
      icon = Icons.remove_circle;
    } else {
      cardColor = Colors.blue[50]!;
      icon = Icons.star;
    }

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      color: cardColor,
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: detail.isAllowance ? Colors.green : Colors.red,
          child: Icon(icon, color: Colors.white),
        ),
        title: Text(
          detail.componentName,
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('النوع: ${detail.isAllowance ? 'بدل' : 'استقطاع'}'),
            Text('ساري من: ${_formatDate(detail.effectiveFrom)}'),
            if (detail.isPercentage)
              Text(
                'نسبة مئوية: ${detail.amount}% من ${detail.percentageOf ?? 'الراتب الأساسي'}',
              ),
          ],
        ),
        trailing: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            Text(
              '${detail.amount.toStringAsFixed(0)} ل.س',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: detail.isAllowance ? Colors.green : Colors.red,
                fontSize: 16,
              ),
            ),
            if (detail.isTaxable)
              const Text(
                'خاضع للضريبة',
                style: TextStyle(fontSize: 10, color: Colors.grey),
              ),
          ],
        ),
        onTap: () => _showEditSalaryDetailDialog(detail),
      ),
    );
  }

  Widget _buildSalaryHistoryTab() {
    return RefreshIndicator(
      onRefresh: _loadSalaryData,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: _salaryHistory.length,
        itemBuilder: (context, index) {
          final salary = _salaryHistory[index];
          return _buildSalaryHistoryCard(salary);
        },
      ),
    );
  }

  Widget _buildSalaryHistoryCard(Salary salary) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: salary.isActive ? Colors.teal[700] : Colors.grey,
          child: const Icon(Icons.history, color: Colors.white),
        ),
        title: Text(
          'راتب ${_formatDate(salary.effectiveFrom)}',
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'الراتب الأساسي: ${salary.basicSalary.toStringAsFixed(0)} ل.س',
            ),
            Text('الراتب الصافي: ${salary.netSalary.toStringAsFixed(0)} ل.س'),
            if (salary.effectiveTo != null)
              Text('انتهى في: ${_formatDate(salary.effectiveTo!)}'),
          ],
        ),
        trailing: salary.isActive
            ? const Chip(
                label: Text('نشط', style: TextStyle(color: Colors.white)),
                backgroundColor: Colors.green,
              )
            : const Chip(label: Text('منتهي'), backgroundColor: Colors.grey),
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  void _showEditSalaryDialog() {
    // يمكن إضافة نافذة تعديل الراتب هنا
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('سيتم إضافة نافذة تعديل الراتب قريباً')),
    );
  }

  void _showAddSalaryComponentDialog() {
    // يمكن إضافة نافذة إضافة عنصر راتب هنا
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('سيتم إضافة نافذة إضافة عنصر راتب قريباً')),
    );
  }

  void _showEditSalaryDetailDialog(SalaryDetail detail) {
    // يمكن إضافة نافذة تعديل عنصر الراتب هنا
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(SnackBar(content: Text('تعديل ${detail.componentName}')));
  }
}
