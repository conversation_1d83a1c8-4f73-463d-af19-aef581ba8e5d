import 'package:flutter/material.dart';
import '../constants/revolutionary_design_colors.dart';
import '../models/supplier.dart';

class SupplierCard extends StatelessWidget {
  final Supplier supplier;
  final VoidCallback onTap;
  final VoidCallback onEdit;
  final VoidCallback onDelete;

  const SupplierCard({
    super.key,
    required this.supplier,
    required this.onTap,
    required this.onEdit,
    required this.onDelete,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: InkWell(
        borderRadius: BorderRadius.circular(12),
        onTap: onTap,
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  // أيقونة المورد
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: RevolutionaryColors.syrianGold.withValues(
                        alpha: 0.1,
                      ),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Icon(
                      Icons.business,
                      color: RevolutionaryColors.syrianGold,
                      size: 20,
                    ),
                  ),
                  const SizedBox(width: 12),
                  // معلومات المورد
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Expanded(
                              child: Text(
                                supplier.name,
                                style: Theme.of(context).textTheme.titleMedium
                                    ?.copyWith(
                                      fontWeight: FontWeight.w600,
                                      color: RevolutionaryColors.textPrimary,
                                    ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                            if (!supplier.isActive)
                              Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 8,
                                  vertical: 2,
                                ),
                                decoration: BoxDecoration(
                                  color: RevolutionaryColors.warningAmber
                                      .withValues(alpha: 0.1),
                                  borderRadius: BorderRadius.circular(4),
                                ),
                                child: Text(
                                  'غير نشط',
                                  style: Theme.of(context).textTheme.bodySmall
                                      ?.copyWith(
                                        color: RevolutionaryColors.warningAmber,
                                        fontSize: 10,
                                      ),
                                ),
                              ),
                          ],
                        ),
                        const SizedBox(height: 4),
                        Row(
                          children: [
                            Text(
                              'كود: ${supplier.code}',
                              style: Theme.of(context).textTheme.bodySmall
                                  ?.copyWith(
                                    color: RevolutionaryColors.textSecondary,
                                  ),
                            ),
                            if (supplier.phone != null &&
                                supplier.phone!.isNotEmpty) ...[
                              const SizedBox(width: 16),
                              Icon(
                                Icons.phone,
                                size: 12,
                                color: RevolutionaryColors.textSecondary,
                              ),
                              const SizedBox(width: 4),
                              Text(
                                supplier.phone!,
                                style: Theme.of(context).textTheme.bodySmall
                                    ?.copyWith(
                                      color: RevolutionaryColors.textSecondary,
                                    ),
                              ),
                            ],
                          ],
                        ),
                      ],
                    ),
                  ),
                  // قائمة الخيارات
                  PopupMenuButton<String>(
                    onSelected: (value) {
                      switch (value) {
                        case 'edit':
                          onEdit();
                          break;
                        case 'delete':
                          onDelete();
                          break;
                      }
                    },
                    itemBuilder: (context) => [
                      const PopupMenuItem(
                        value: 'edit',
                        child: Row(
                          children: [
                            Icon(Icons.edit, size: 18),
                            SizedBox(width: 8),
                            Text('تعديل'),
                          ],
                        ),
                      ),
                      const PopupMenuItem(
                        value: 'delete',
                        child: Row(
                          children: [
                            Icon(Icons.delete, size: 18, color: Colors.red),
                            SizedBox(width: 8),
                            Text('حذف', style: TextStyle(color: Colors.red)),
                          ],
                        ),
                      ),
                    ],
                    child: const Icon(
                      Icons.more_vert,
                      color: RevolutionaryColors.textSecondary,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              // الرصيد وحالة المورد
              Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'الرصيد:',
                          style: Theme.of(context).textTheme.bodySmall
                              ?.copyWith(
                                color: RevolutionaryColors.textSecondary,
                              ),
                        ),
                        Text(
                          '${supplier.displayBalance} ل.س',
                          style: Theme.of(context).textTheme.titleMedium
                              ?.copyWith(
                                fontWeight: FontWeight.w600,
                                color: supplier.balance <= 0
                                    ? RevolutionaryColors.errorCoral
                                    : RevolutionaryColors.successGlow,
                              ),
                        ),
                      ],
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: _getBalanceStatusColor().withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(6),
                    ),
                    child: Text(
                      supplier.balanceStatus,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: _getBalanceStatusColor(),
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ],
              ),
              // البريد الإلكتروني (إذا كان موجوداً)
              if (supplier.email != null && supplier.email!.isNotEmpty) ...[
                const SizedBox(height: 8),
                Row(
                  children: [
                    Icon(
                      Icons.email,
                      size: 16,
                      color: RevolutionaryColors.textSecondary,
                    ),
                    const SizedBox(width: 4),
                    Expanded(
                      child: Text(
                        supplier.email!,
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: RevolutionaryColors.textSecondary,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),
              ],
              // العنوان (إذا كان موجوداً)
              if (supplier.address != null && supplier.address!.isNotEmpty) ...[
                const SizedBox(height: 8),
                Row(
                  children: [
                    Icon(
                      Icons.location_on,
                      size: 16,
                      color: RevolutionaryColors.textSecondary,
                    ),
                    const SizedBox(width: 4),
                    Expanded(
                      child: Text(
                        supplier.address!,
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: RevolutionaryColors.textSecondary,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Color _getBalanceStatusColor() {
    if (supplier.balance < 0) {
      return RevolutionaryColors.errorCoral; // دائن (نحن مدينون للمورد)
    } else if (supplier.balance > 0) {
      return RevolutionaryColors.successGlow; // مدين (المورد مدين لنا)
    } else {
      return RevolutionaryColors.textSecondary; // متوازن
    }
  }
}
