/// خدمة الإجازات المتقدمة
/// توفر ميزات متقدمة لإدارة الإجازات مثل الموافقات متعددة المستويات والتخطيط السنوي
library;

import '../database/database_helper.dart';
import '../services/logging_service.dart';
import '../services/leave_service.dart';
import '../services/employee_service.dart';
import '../exceptions/validation_exception.dart';
import '../models/hr_models.dart';

class AdvancedLeaveService {
  final DatabaseHelper _databaseHelper = DatabaseHelper();
  final LeaveService _leaveService = LeaveService();
  final EmployeeService _employeeService = EmployeeService();

  /// إنشاء جداول الإجازات المتقدمة
  Future<void> _createAdvancedLeaveTables() async {
    final db = await _databaseHelper.database;

    // جدول أنواع الإجازات المخصصة
    await db.execute('''
      CREATE TABLE IF NOT EXISTS leave_types (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        name_ar TEXT NOT NULL,
        max_days_per_year INTEGER,
        requires_approval BOOLEAN DEFAULT 1,
        approval_levels INTEGER DEFAULT 1,
        is_paid BOOLEAN DEFAULT 1,
        carry_forward BOOLEAN DEFAULT 0,
        requires_medical_certificate BOOLEAN DEFAULT 0,
        advance_notice_days INTEGER DEFAULT 0,
        is_active BOOLEAN DEFAULT 1,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL
      )
    ''');

    // جدول موافقات الإجازات متعددة المستويات
    await db.execute('''
      CREATE TABLE IF NOT EXISTS leave_approvals (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        leave_id INTEGER NOT NULL,
        approver_id INTEGER NOT NULL,
        approval_level INTEGER NOT NULL,
        status TEXT NOT NULL DEFAULT 'pending',
        approved_at TEXT,
        notes TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        FOREIGN KEY (leave_id) REFERENCES leaves (id),
        FOREIGN KEY (approver_id) REFERENCES users (id)
      )
    ''');

    // جدول رصيد الإجازات السنوي
    await db.execute('''
      CREATE TABLE IF NOT EXISTS leave_balances (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        employee_id INTEGER NOT NULL,
        leave_type TEXT NOT NULL,
        year INTEGER NOT NULL,
        allocated_days INTEGER NOT NULL,
        used_days INTEGER DEFAULT 0,
        carried_forward_days INTEGER DEFAULT 0,
        remaining_days INTEGER NOT NULL,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        FOREIGN KEY (employee_id) REFERENCES employees (id),
        UNIQUE(employee_id, leave_type, year)
      )
    ''');

    // جدول تخطيط الإجازات
    await db.execute('''
      CREATE TABLE IF NOT EXISTS leave_planning (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        employee_id INTEGER NOT NULL,
        planned_start_date TEXT NOT NULL,
        planned_end_date TEXT NOT NULL,
        leave_type TEXT NOT NULL,
        planned_days INTEGER NOT NULL,
        status TEXT NOT NULL DEFAULT 'planned',
        notes TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        FOREIGN KEY (employee_id) REFERENCES employees (id)
      )
    ''');

    // جدول تفويض الإجازات
    await db.execute('''
      CREATE TABLE IF NOT EXISTS leave_delegations (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        employee_id INTEGER NOT NULL,
        delegate_to_id INTEGER NOT NULL,
        start_date TEXT NOT NULL,
        end_date TEXT NOT NULL,
        responsibilities TEXT,
        status TEXT NOT NULL DEFAULT 'active',
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        FOREIGN KEY (employee_id) REFERENCES employees (id),
        FOREIGN KEY (delegate_to_id) REFERENCES employees (id)
      )
    ''');
  }

  /// إضافة أنواع إجازات افتراضية
  Future<void> initializeDefaultLeaveTypes() async {
    try {
      await _createAdvancedLeaveTables();

      final defaultTypes = [
        {
          'name': 'annual',
          'name_ar': 'إجازة سنوية',
          'max_days_per_year': 30,
          'requires_approval': 1,
          'approval_levels': 1,
          'is_paid': 1,
          'carry_forward': 1,
          'requires_medical_certificate': 0,
          'advance_notice_days': 7,
        },
        {
          'name': 'sick',
          'name_ar': 'إجازة مرضية',
          'max_days_per_year': 15,
          'requires_approval': 1,
          'approval_levels': 1,
          'is_paid': 1,
          'carry_forward': 0,
          'requires_medical_certificate': 1,
          'advance_notice_days': 0,
        },
        {
          'name': 'emergency',
          'name_ar': 'إجازة طارئة',
          'max_days_per_year': 5,
          'requires_approval': 1,
          'approval_levels': 2,
          'is_paid': 1,
          'carry_forward': 0,
          'requires_medical_certificate': 0,
          'advance_notice_days': 0,
        },
        {
          'name': 'maternity',
          'name_ar': 'إجازة أمومة',
          'max_days_per_year': 120,
          'requires_approval': 1,
          'approval_levels': 2,
          'is_paid': 1,
          'carry_forward': 0,
          'requires_medical_certificate': 1,
          'advance_notice_days': 30,
        },
        {
          'name': 'paternity',
          'name_ar': 'إجازة أبوة',
          'max_days_per_year': 7,
          'requires_approval': 1,
          'approval_levels': 1,
          'is_paid': 1,
          'carry_forward': 0,
          'requires_medical_certificate': 0,
          'advance_notice_days': 7,
        },
        {
          'name': 'unpaid',
          'name_ar': 'إجازة بدون راتب',
          'max_days_per_year': 90,
          'requires_approval': 1,
          'approval_levels': 3,
          'is_paid': 0,
          'carry_forward': 0,
          'requires_medical_certificate': 0,
          'advance_notice_days': 30,
        },
        {
          'name': 'study',
          'name_ar': 'إجازة دراسية',
          'max_days_per_year': 10,
          'requires_approval': 1,
          'approval_levels': 2,
          'is_paid': 1,
          'carry_forward': 0,
          'requires_medical_certificate': 0,
          'advance_notice_days': 14,
        },
        {
          'name': 'pilgrimage',
          'name_ar': 'إجازة حج',
          'max_days_per_year': 15,
          'requires_approval': 1,
          'approval_levels': 2,
          'is_paid': 1,
          'carry_forward': 0,
          'requires_medical_certificate': 0,
          'advance_notice_days': 60,
        },
        {
          'name': 'bereavement',
          'name_ar': 'إجازة وفاة',
          'max_days_per_year': 7,
          'requires_approval': 1,
          'approval_levels': 1,
          'is_paid': 1,
          'carry_forward': 0,
          'requires_medical_certificate': 0,
          'advance_notice_days': 0,
        },
        {
          'name': 'marriage',
          'name_ar': 'إجازة زواج',
          'max_days_per_year': 7,
          'requires_approval': 1,
          'approval_levels': 1,
          'is_paid': 1,
          'carry_forward': 0,
          'requires_medical_certificate': 0,
          'advance_notice_days': 14,
        },
      ];

      final db = await _databaseHelper.database;
      final now = DateTime.now().toIso8601String();

      for (final type in defaultTypes) {
        // التحقق من عدم وجود النوع مسبقاً
        final existing = await db.query(
          'leave_types',
          where: 'name = ?',
          whereArgs: [type['name']],
          limit: 1,
        );

        if (existing.isEmpty) {
          await db.insert('leave_types', {
            ...type,
            'is_active': 1,
            'created_at': now,
            'updated_at': now,
          });
        }
      }

      LoggingService.info(
        'تم تهيئة أنواع الإجازات الافتراضية',
        category: 'AdvancedLeaveService',
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في تهيئة أنواع الإجازات',
        category: 'AdvancedLeaveService',
        data: {'error': e.toString()},
      );
      rethrow;
    }
  }

  /// الحصول على جميع أنواع الإجازات
  Future<List<Map<String, dynamic>>> getAllLeaveTypes() async {
    try {
      await _createAdvancedLeaveTables();
      final db = await _databaseHelper.database;

      final result = await db.query(
        'leave_types',
        where: 'is_active = 1',
        orderBy: 'name_ar',
      );

      return result;
    } catch (e) {
      LoggingService.error(
        'خطأ في جلب أنواع الإجازات',
        category: 'AdvancedLeaveService',
        data: {'error': e.toString()},
      );
      return [];
    }
  }

  /// إنشاء طلب إجازة متقدم مع موافقات متعددة المستويات
  Future<Leave> createAdvancedLeaveRequest({
    required int employeeId,
    required String leaveType,
    required DateTime startDate,
    required DateTime endDate,
    String? reason,
    String? medicalCertificatePath,
    int? requestedBy,
  }) async {
    try {
      await _createAdvancedLeaveTables();

      // الحصول على معلومات نوع الإجازة
      final leaveTypeInfo = await _getLeaveTypeInfo(leaveType);
      if (leaveTypeInfo == null) {
        throw ValidationException('نوع الإجازة غير صحيح');
      }

      // التحقق من الشهادة الطبية إذا كانت مطلوبة
      if (leaveTypeInfo['requires_medical_certificate'] == 1 &&
          (medicalCertificatePath == null || medicalCertificatePath.isEmpty)) {
        throw ValidationException('هذا النوع من الإجازات يتطلب شهادة طبية');
      }

      // التحقق من فترة الإشعار المسبق
      final advanceNoticeDays = leaveTypeInfo['advance_notice_days'] as int;
      if (advanceNoticeDays > 0) {
        final requiredNoticeDate = startDate.subtract(
          Duration(days: advanceNoticeDays),
        );
        if (DateTime.now().isAfter(requiredNoticeDate)) {
          throw ValidationException(
            'يجب تقديم طلب الإجازة قبل $advanceNoticeDays أيام على الأقل',
          );
        }
      }

      // إنشاء طلب الإجازة الأساسي
      final leave = await _leaveService.createLeaveRequest(
        employeeId: employeeId,
        leaveType: leaveType,
        startDate: startDate,
        endDate: endDate,
        reason: reason,
        requestedBy: requestedBy,
      );

      // إنشاء موافقات متعددة المستويات
      await _createApprovalWorkflow(
        leave.id!,
        leaveTypeInfo['approval_levels'] as int,
      );

      LoggingService.info(
        'تم إنشاء طلب إجازة متقدم بنجاح',
        category: 'AdvancedLeaveService',
        data: {
          'leaveId': leave.id,
          'leaveType': leaveType,
          'approvalLevels': leaveTypeInfo['approval_levels'],
        },
      );

      return leave;
    } catch (e) {
      LoggingService.error(
        'خطأ في إنشاء طلب الإجازة المتقدم',
        category: 'AdvancedLeaveService',
        data: {'error': e.toString()},
      );
      rethrow;
    }
  }

  /// الحصول على معلومات نوع الإجازة
  Future<Map<String, dynamic>?> _getLeaveTypeInfo(String leaveType) async {
    final db = await _databaseHelper.database;
    final result = await db.query(
      'leave_types',
      where: 'name = ? AND is_active = 1',
      whereArgs: [leaveType],
      limit: 1,
    );

    return result.isNotEmpty ? result.first : null;
  }

  /// إنشاء سير عمل الموافقات
  Future<void> _createApprovalWorkflow(int leaveId, int approvalLevels) async {
    final db = await _databaseHelper.database;
    final now = DateTime.now().toIso8601String();

    // في التطبيق الحقيقي، يجب تحديد المعتمدين بناءً على الهيكل التنظيمي
    // هنا سنستخدم مثال بسيط
    for (int level = 1; level <= approvalLevels; level++) {
      await db.insert('leave_approvals', {
        'leave_id': leaveId,
        'approver_id': 1, // يجب تحديد المعتمد الفعلي
        'approval_level': level,
        'status': 'pending',
        'created_at': now,
        'updated_at': now,
      });
    }
  }

  /// تهيئة رصيد الإجازات السنوي للموظف
  Future<void> initializeEmployeeLeaveBalance(int employeeId, int year) async {
    try {
      await _createAdvancedLeaveTables();
      final db = await _databaseHelper.database;
      final now = DateTime.now().toIso8601String();

      final leaveTypes = await getAllLeaveTypes();

      for (final leaveType in leaveTypes) {
        // التحقق من عدم وجود رصيد مسبق
        final existing = await db.query(
          'leave_balances',
          where: 'employee_id = ? AND leave_type = ? AND year = ?',
          whereArgs: [employeeId, leaveType['name'], year],
          limit: 1,
        );

        if (existing.isEmpty) {
          final allocatedDays = leaveType['max_days_per_year'] as int;
          await db.insert('leave_balances', {
            'employee_id': employeeId,
            'leave_type': leaveType['name'],
            'year': year,
            'allocated_days': allocatedDays,
            'used_days': 0,
            'carried_forward_days': 0,
            'remaining_days': allocatedDays,
            'created_at': now,
            'updated_at': now,
          });
        }
      }

      LoggingService.info(
        'تم تهيئة رصيد الإجازات للموظف',
        category: 'AdvancedLeaveService',
        data: {'employeeId': employeeId, 'year': year},
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في تهيئة رصيد الإجازات',
        category: 'AdvancedLeaveService',
        data: {'employeeId': employeeId, 'year': year, 'error': e.toString()},
      );
      rethrow;
    }
  }

  /// الحصول على رصيد الإجازات للموظف
  Future<List<Map<String, dynamic>>> getEmployeeLeaveBalance(
    int employeeId,
    int year,
  ) async {
    try {
      await _createAdvancedLeaveTables();
      final db = await _databaseHelper.database;

      final result = await db.rawQuery(
        '''
        SELECT lb.*, lt.name_ar as leave_type_name
        FROM leave_balances lb
        LEFT JOIN leave_types lt ON lb.leave_type = lt.name
        WHERE lb.employee_id = ? AND lb.year = ?
        ORDER BY lt.name_ar
      ''',
        [employeeId, year],
      );

      return result;
    } catch (e) {
      LoggingService.error(
        'خطأ في جلب رصيد الإجازات',
        category: 'AdvancedLeaveService',
        data: {'employeeId': employeeId, 'year': year, 'error': e.toString()},
      );
      return [];
    }
  }

  /// إضافة خطة إجازة
  Future<int> addLeavePlan({
    required int employeeId,
    required DateTime plannedStartDate,
    required DateTime plannedEndDate,
    required String leaveType,
    String? notes,
  }) async {
    try {
      await _createAdvancedLeaveTables();
      final db = await _databaseHelper.database;
      final now = DateTime.now().toIso8601String();

      final plannedDays =
          plannedEndDate.difference(plannedStartDate).inDays + 1;

      final id = await db.insert('leave_planning', {
        'employee_id': employeeId,
        'planned_start_date': plannedStartDate.toIso8601String().split('T')[0],
        'planned_end_date': plannedEndDate.toIso8601String().split('T')[0],
        'leave_type': leaveType,
        'planned_days': plannedDays,
        'status': 'planned',
        'notes': notes,
        'created_at': now,
        'updated_at': now,
      });

      LoggingService.info(
        'تم إضافة خطة إجازة جديدة',
        category: 'AdvancedLeaveService',
        data: {
          'planId': id,
          'employeeId': employeeId,
          'leaveType': leaveType,
          'plannedDays': plannedDays,
        },
      );

      return id;
    } catch (e) {
      LoggingService.error(
        'خطأ في إضافة خطة الإجازة',
        category: 'AdvancedLeaveService',
        data: {'error': e.toString()},
      );
      rethrow;
    }
  }

  /// الحصول على خطط الإجازات
  Future<List<Map<String, dynamic>>> getLeavePlans({
    int? employeeId,
    String? status,
    DateTime? fromDate,
    DateTime? toDate,
  }) async {
    try {
      await _createAdvancedLeaveTables();
      final db = await _databaseHelper.database;

      String whereClause = '1=1';
      List<dynamic> whereArgs = [];

      if (employeeId != null) {
        whereClause += ' AND lp.employee_id = ?';
        whereArgs.add(employeeId);
      }

      if (status != null) {
        whereClause += ' AND lp.status = ?';
        whereArgs.add(status);
      }

      if (fromDate != null) {
        whereClause += ' AND lp.planned_start_date >= ?';
        whereArgs.add(fromDate.toIso8601String().split('T')[0]);
      }

      if (toDate != null) {
        whereClause += ' AND lp.planned_end_date <= ?';
        whereArgs.add(toDate.toIso8601String().split('T')[0]);
      }

      final result = await db.rawQuery('''
        SELECT lp.*, e.first_name, e.last_name, lt.name_ar as leave_type_name
        FROM leave_planning lp
        LEFT JOIN employees e ON lp.employee_id = e.id
        LEFT JOIN leave_types lt ON lp.leave_type = lt.name
        WHERE $whereClause
        ORDER BY lp.planned_start_date
      ''', whereArgs);

      return result;
    } catch (e) {
      LoggingService.error(
        'خطأ في جلب خطط الإجازات',
        category: 'AdvancedLeaveService',
        data: {'error': e.toString()},
      );
      return [];
    }
  }

  /// إضافة تفويض إجازة
  Future<int> addLeaveDelegation({
    required int employeeId,
    required int delegateToId,
    required DateTime startDate,
    required DateTime endDate,
    String? responsibilities,
  }) async {
    try {
      await _createAdvancedLeaveTables();
      final db = await _databaseHelper.database;
      final now = DateTime.now().toIso8601String();

      final id = await db.insert('leave_delegations', {
        'employee_id': employeeId,
        'delegate_to_id': delegateToId,
        'start_date': startDate.toIso8601String().split('T')[0],
        'end_date': endDate.toIso8601String().split('T')[0],
        'responsibilities': responsibilities,
        'status': 'active',
        'created_at': now,
        'updated_at': now,
      });

      LoggingService.info(
        'تم إضافة تفويض إجازة جديد',
        category: 'AdvancedLeaveService',
        data: {
          'delegationId': id,
          'employeeId': employeeId,
          'delegateToId': delegateToId,
        },
      );

      return id;
    } catch (e) {
      LoggingService.error(
        'خطأ في إضافة تفويض الإجازة',
        category: 'AdvancedLeaveService',
        data: {'error': e.toString()},
      );
      rethrow;
    }
  }

  /// الحصول على إحصائيات الإجازات المتقدمة
  Future<Map<String, dynamic>> getAdvancedLeaveStatistics() async {
    try {
      await _createAdvancedLeaveTables();
      final db = await _databaseHelper.database;

      final currentYear = DateTime.now().year;

      // إحصائيات عامة
      final totalPlanned = await db.rawQuery('''
        SELECT COUNT(*) as count FROM leave_planning
        WHERE status = 'planned'
      ''');

      final totalDelegations = await db.rawQuery('''
        SELECT COUNT(*) as count FROM leave_delegations
        WHERE status = 'active'
      ''');

      // إحصائيات الرصيد
      final balanceStats = await db.rawQuery(
        '''
        SELECT
          SUM(allocated_days) as total_allocated,
          SUM(used_days) as total_used,
          SUM(remaining_days) as total_remaining
        FROM leave_balances
        WHERE year = ?
      ''',
        [currentYear],
      );

      // أكثر أنواع الإجازات استخداماً
      final popularTypes = await db.rawQuery('''
        SELECT l.leave_type, COUNT(*) as count, lt.name_ar
        FROM leaves l
        LEFT JOIN leave_types lt ON l.leave_type = lt.name
        WHERE l.status = 'approved'
        GROUP BY l.leave_type
        ORDER BY count DESC
        LIMIT 5
      ''');

      return {
        'totalPlanned': totalPlanned.first['count'] ?? 0,
        'totalDelegations': totalDelegations.first['count'] ?? 0,
        'totalAllocated': balanceStats.first['total_allocated'] ?? 0,
        'totalUsed': balanceStats.first['total_used'] ?? 0,
        'totalRemaining': balanceStats.first['total_remaining'] ?? 0,
        'popularTypes': popularTypes,
      };
    } catch (e) {
      LoggingService.error(
        'خطأ في جلب إحصائيات الإجازات المتقدمة',
        category: 'AdvancedLeaveService',
        data: {'error': e.toString()},
      );
      return {};
    }
  }
}
