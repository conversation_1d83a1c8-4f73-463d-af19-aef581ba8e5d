/// شاشة إدارة قوالب التقارير
/// عرض وإدارة جميع قوالب التقارير المحفوظة
library;

import 'package:flutter/material.dart';
import '../models/visual_report_builder.dart';
import '../services/visual_report_builder_service.dart';
import '../screens/visual_report_builder_screen.dart';
import '../constants/revolutionary_design_colors.dart';
import '../widgets/loading_widget.dart';

class ReportTemplatesScreen extends StatefulWidget {
  const ReportTemplatesScreen({super.key});

  @override
  State<ReportTemplatesScreen> createState() => _ReportTemplatesScreenState();
}

class _ReportTemplatesScreenState extends State<ReportTemplatesScreen>
    with TickerProviderStateMixin {
  final VisualReportBuilderService _builderService =
      VisualReportBuilderService();

  late TabController _tabController;

  List<ReportTemplate> _allTemplates = [];
  List<ReportTemplate> _filteredTemplates = [];
  final Map<String, List<ReportTemplate>> _templatesByCategory = {};

  bool _isLoading = true;
  String _searchQuery = '';
  String _selectedCategory = 'الكل';

  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _loadTemplates();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  /// تحميل جميع القوالب
  Future<void> _loadTemplates() async {
    setState(() => _isLoading = true);

    try {
      final templates = await _builderService.getAllTemplates();

      setState(() {
        _allTemplates = templates;
        _filteredTemplates = templates;
        _groupTemplatesByCategory();
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('خطأ في تحميل القوالب: $e')));
      }
    }
  }

  /// تجميع القوالب حسب الفئة
  void _groupTemplatesByCategory() {
    _templatesByCategory.clear();

    for (final template in _allTemplates) {
      final category = template.category.isEmpty ? 'عام' : template.category;
      if (!_templatesByCategory.containsKey(category)) {
        _templatesByCategory[category] = [];
      }
      _templatesByCategory[category]!.add(template);
    }
  }

  /// فلترة القوالب
  void _filterTemplates() {
    setState(() {
      _filteredTemplates = _allTemplates.where((template) {
        final matchesSearch =
            _searchQuery.isEmpty ||
            template.name.toLowerCase().contains(_searchQuery.toLowerCase()) ||
            template.description.toLowerCase().contains(
              _searchQuery.toLowerCase(),
            );

        final matchesCategory =
            _selectedCategory == 'الكل' ||
            template.category == _selectedCategory;

        return matchesSearch && matchesCategory;
      }).toList();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'قوالب التقارير',
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        backgroundColor: RevolutionaryColors.damascusSky,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            onPressed: _createNewTemplate,
            icon: const Icon(Icons.add),
            tooltip: 'قالب جديد',
          ),
          IconButton(
            onPressed: _loadTemplates,
            icon: const Icon(Icons.refresh),
            tooltip: 'تحديث',
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          indicatorColor: Colors.white,
          tabs: const [
            Tab(text: 'جميع القوالب', icon: Icon(Icons.list)),
            Tab(text: 'حسب الفئة', icon: Icon(Icons.category)),
          ],
        ),
      ),
      body: _isLoading
          ? const LoadingWidget()
          : Column(
              children: [
                _buildSearchAndFilter(),
                Expanded(
                  child: TabBarView(
                    controller: _tabController,
                    children: [
                      _buildAllTemplatesTab(),
                      _buildCategorizedTemplatesTab(),
                    ],
                  ),
                ),
              ],
            ),
      floatingActionButton: FloatingActionButton(
        onPressed: _createNewTemplate,
        backgroundColor: RevolutionaryColors.damascusSky,
        child: const Icon(Icons.add, color: Colors.white),
      ),
    );
  }

  /// بناء شريط البحث والفلترة
  Widget _buildSearchAndFilter() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        border: Border(bottom: BorderSide(color: Colors.grey[300]!)),
      ),
      child: Column(
        children: [
          // شريط البحث
          TextField(
            controller: _searchController,
            decoration: InputDecoration(
              hintText: 'البحث في القوالب...',
              prefixIcon: const Icon(Icons.search),
              suffixIcon: _searchQuery.isNotEmpty
                  ? IconButton(
                      onPressed: () {
                        _searchController.clear();
                        setState(() {
                          _searchQuery = '';
                          _filterTemplates();
                        });
                      },
                      icon: const Icon(Icons.clear),
                    )
                  : null,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            onChanged: (value) {
              setState(() {
                _searchQuery = value;
                _filterTemplates();
              });
            },
          ),
          const SizedBox(height: 12),
          // فلتر الفئات
          Row(
            children: [
              const Text(
                'الفئة:',
                style: TextStyle(fontWeight: FontWeight.w500),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: DropdownButton<String>(
                  value: _selectedCategory,
                  isExpanded: true,
                  items: [
                    const DropdownMenuItem(
                      value: 'الكل',
                      child: Text('جميع الفئات'),
                    ),
                    ..._templatesByCategory.keys.map((category) {
                      return DropdownMenuItem(
                        value: category,
                        child: Text(category),
                      );
                    }),
                  ],
                  onChanged: (value) {
                    setState(() {
                      _selectedCategory = value!;
                      _filterTemplates();
                    });
                  },
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// تبويب جميع القوالب
  Widget _buildAllTemplatesTab() {
    if (_filteredTemplates.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.description_outlined, size: 64, color: Colors.grey[400]),
            const SizedBox(height: 16),
            Text(
              _searchQuery.isNotEmpty
                  ? 'لا توجد قوالب تطابق البحث'
                  : 'لا توجد قوالب محفوظة',
              style: TextStyle(fontSize: 18, color: Colors.grey[600]),
            ),
            const SizedBox(height: 16),
            ElevatedButton.icon(
              onPressed: _createNewTemplate,
              icon: const Icon(Icons.add),
              label: const Text('إنشاء قالب جديد'),
              style: ElevatedButton.styleFrom(
                backgroundColor: RevolutionaryColors.damascusSky,
                foregroundColor: Colors.white,
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _filteredTemplates.length,
      itemBuilder: (context, index) {
        final template = _filteredTemplates[index];
        return _buildTemplateCard(template);
      },
    );
  }

  /// تبويب القوالب حسب الفئة
  Widget _buildCategorizedTemplatesTab() {
    if (_templatesByCategory.isEmpty) {
      return const Center(
        child: Text('لا توجد قوالب محفوظة', style: TextStyle(fontSize: 18)),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _templatesByCategory.keys.length,
      itemBuilder: (context, index) {
        final category = _templatesByCategory.keys.elementAt(index);
        final templates = _templatesByCategory[category]!;

        return Card(
          margin: const EdgeInsets.only(bottom: 16),
          child: ExpansionTile(
            title: Text(
              category,
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
            subtitle: Text('${templates.length} قالب'),
            leading: Icon(
              _getCategoryIcon(category),
              color: RevolutionaryColors.damascusSky,
            ),
            children: templates.map((template) {
              return Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: _buildTemplateCard(template, isInExpansion: true),
              );
            }).toList(),
          ),
        );
      },
    );
  }

  /// بناء بطاقة القالب
  Widget _buildTemplateCard(
    ReportTemplate template, {
    bool isInExpansion = false,
  }) {
    return Card(
      margin: EdgeInsets.only(bottom: isInExpansion ? 8 : 16),
      elevation: isInExpansion ? 1 : 2,
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: RevolutionaryColors.damascusSky,
          child: Icon(
            _getCategoryIcon(template.category),
            color: Colors.white,
            size: 20,
          ),
        ),
        title: Text(
          template.name,
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (template.description.isNotEmpty) Text(template.description),
            const SizedBox(height: 4),
            Row(
              children: [
                Icon(Icons.widgets, size: 16, color: Colors.grey[600]),
                const SizedBox(width: 4),
                Text(
                  '${template.elements.length} عنصر',
                  style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                ),
                const SizedBox(width: 16),
                Icon(Icons.access_time, size: 16, color: Colors.grey[600]),
                const SizedBox(width: 4),
                Text(
                  _formatDate(template.createdAt),
                  style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                ),
              ],
            ),
          ],
        ),
        trailing: PopupMenuButton<String>(
          onSelected: (value) => _handleTemplateAction(value, template),
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'edit',
              child: Row(
                children: [Icon(Icons.edit), SizedBox(width: 8), Text('تحرير')],
              ),
            ),
            const PopupMenuItem(
              value: 'duplicate',
              child: Row(
                children: [Icon(Icons.copy), SizedBox(width: 8), Text('نسخ')],
              ),
            ),
            const PopupMenuItem(
              value: 'export',
              child: Row(
                children: [
                  Icon(Icons.download),
                  SizedBox(width: 8),
                  Text('تصدير'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'delete',
              child: Row(
                children: [
                  Icon(Icons.delete, color: Colors.red),
                  SizedBox(width: 8),
                  Text('حذف', style: TextStyle(color: Colors.red)),
                ],
              ),
            ),
          ],
        ),
        onTap: () => _editTemplate(template),
      ),
    );
  }

  /// الحصول على أيقونة الفئة
  IconData _getCategoryIcon(String category) {
    switch (category.toLowerCase()) {
      case 'المحاسبة':
        return Icons.account_balance;
      case 'المبيعات':
        return Icons.point_of_sale;
      case 'المخزون':
        return Icons.inventory;
      case 'العملاء':
        return Icons.people;
      case 'الموردين':
        return Icons.business;
      default:
        return Icons.description;
    }
  }

  /// تنسيق التاريخ
  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  /// إنشاء قالب جديد
  void _createNewTemplate() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const VisualReportBuilderScreen(),
      ),
    ).then((_) => _loadTemplates());
  }

  /// تحرير قالب
  void _editTemplate(ReportTemplate template) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => VisualReportBuilderScreen(template: template),
      ),
    ).then((_) => _loadTemplates());
  }

  /// معالجة إجراءات القالب
  void _handleTemplateAction(String action, ReportTemplate template) {
    switch (action) {
      case 'edit':
        _editTemplate(template);
        break;
      case 'duplicate':
        _duplicateTemplate(template);
        break;
      case 'export':
        _exportTemplate(template);
        break;
      case 'delete':
        _deleteTemplate(template);
        break;
    }
  }

  /// نسخ قالب
  void _duplicateTemplate(ReportTemplate template) {
    final duplicatedTemplate = template.copyWith(
      id: null,
      name: '${template.name} - نسخة',
      createdAt: DateTime.now(),
      updatedAt: null,
    );

    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) =>
            VisualReportBuilderScreen(template: duplicatedTemplate),
      ),
    ).then((_) => _loadTemplates());
  }

  /// تصدير قالب
  void _exportTemplate(ReportTemplate template) {
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('تصدير القالب - قيد التطوير')));
  }

  /// حذف قالب
  Future<void> _deleteTemplate(ReportTemplate template) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text('هل تريد حذف القالب "${template.name}"؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('حذف'),
          ),
        ],
      ),
    );

    if (confirmed == true && template.id != null) {
      try {
        await _builderService.deleteTemplate(template.id!);
        await _loadTemplates();

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم حذف القالب بنجاح'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(
            context,
          ).showSnackBar(SnackBar(content: Text('خطأ في حذف القالب: $e')));
        }
      }
    }
  }
}
