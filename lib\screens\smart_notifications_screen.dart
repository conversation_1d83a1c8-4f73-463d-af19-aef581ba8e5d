/// شاشة إدارة التنبيهات الذكية
/// عرض وإدارة جميع التنبيهات والإشعارات
library;

import 'package:flutter/material.dart';
import '../models/smart_notification_models.dart';
import '../services/smart_notification_service.dart';
import '../constants/revolutionary_design_colors.dart';
import '../widgets/loading_widget.dart';

class SmartNotificationsScreen extends StatefulWidget {
  const SmartNotificationsScreen({super.key});

  @override
  State<SmartNotificationsScreen> createState() =>
      _SmartNotificationsScreenState();
}

class _SmartNotificationsScreenState extends State<SmartNotificationsScreen>
    with TickerProviderStateMixin {
  final SmartNotificationService _notificationService =
      SmartNotificationService();

  late TabController _tabController;

  List<SmartNotification> _allNotifications = [];
  List<SmartNotification> _pendingNotifications = [];
  List<SmartNotification> _readNotifications = [];

  bool _isLoading = true;
  NotificationType? _selectedTypeFilter;
  NotificationPriority? _selectedPriorityFilter;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadNotifications();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  /// تحميل التنبيهات
  Future<void> _loadNotifications() async {
    setState(() => _isLoading = true);

    try {
      final allNotifications = await _notificationService.getAllNotifications();
      final pendingNotifications = await _notificationService
          .getPendingNotifications();

      setState(() {
        _allNotifications = allNotifications;
        _pendingNotifications = pendingNotifications;
        _readNotifications = allNotifications
            .where((n) => n.status == NotificationStatus.read)
            .toList();
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('خطأ في تحميل التنبيهات: $e')));
      }
    }
  }

  /// تحميل التنبيهات مع تطبيق الفلاتر
  Future<void> _loadNotificationsWithFilters() async {
    setState(() => _isLoading = true);

    try {
      // تحميل جميع التنبيهات مع تطبيق الفلاتر
      final allNotifications = await _notificationService.getAllNotifications(
        type: _selectedTypeFilter,
      );

      // فلترة التنبيهات المعلقة
      List<SmartNotification> pendingNotifications;
      if (_selectedTypeFilter != null || _selectedPriorityFilter != null) {
        // إذا كان هناك فلاتر، نحتاج لفلترة التنبيهات المعلقة يدوياً
        final allPending = await _notificationService.getPendingNotifications();
        pendingNotifications = allPending.where((notification) {
          bool matchesType =
              _selectedTypeFilter == null ||
              notification.type == _selectedTypeFilter;
          bool matchesPriority =
              _selectedPriorityFilter == null ||
              notification.priority == _selectedPriorityFilter;
          return matchesType && matchesPriority;
        }).toList();
      } else {
        // إذا لم تكن هناك فلاتر، استخدم الطريقة العادية
        pendingNotifications = await _notificationService
            .getPendingNotifications();
      }

      // فلترة التنبيهات المقروءة
      List<SmartNotification> readNotifications = allNotifications
          .where((n) => n.status == NotificationStatus.read)
          .toList();

      // تطبيق فلتر الأولوية على التنبيهات المقروءة إذا لزم الأمر
      if (_selectedPriorityFilter != null) {
        readNotifications = readNotifications
            .where((n) => n.priority == _selectedPriorityFilter)
            .toList();
      }

      setState(() {
        _allNotifications = allNotifications;
        _pendingNotifications = pendingNotifications;
        _readNotifications = readNotifications;
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('خطأ في تحميل التنبيهات: $e')));
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'التنبيهات الذكية',
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        backgroundColor: RevolutionaryColors.damascusSky,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            onPressed: _showFilterDialog,
            icon: Stack(
              children: [
                const Icon(Icons.filter_list),
                if (_hasActiveFilters())
                  Positioned(
                    right: 0,
                    top: 0,
                    child: Container(
                      padding: const EdgeInsets.all(2),
                      decoration: const BoxDecoration(
                        color: Colors.red,
                        shape: BoxShape.circle,
                      ),
                      constraints: const BoxConstraints(
                        minWidth: 8,
                        minHeight: 8,
                      ),
                    ),
                  ),
              ],
            ),
            tooltip: 'فلترة',
          ),
          IconButton(
            onPressed: _markAllAsRead,
            icon: const Icon(Icons.done_all),
            tooltip: 'تمييز الكل كمقروء',
          ),
          IconButton(
            onPressed: _loadNotifications,
            icon: const Icon(Icons.refresh),
            tooltip: 'تحديث',
          ),
          IconButton(
            onPressed: _showSettings,
            icon: const Icon(Icons.settings),
            tooltip: 'الإعدادات',
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          indicatorColor: Colors.white,
          tabs: [
            Tab(
              text: 'المعلقة',
              icon: Badge(
                label: Text(_pendingNotifications.length.toString()),
                child: const Icon(Icons.notifications_active),
              ),
            ),
            const Tab(text: 'المقروءة', icon: Icon(Icons.notifications)),
            const Tab(text: 'الكل', icon: Icon(Icons.list)),
          ],
        ),
      ),
      body: _isLoading
          ? const LoadingWidget()
          : TabBarView(
              controller: _tabController,
              children: [
                _buildPendingNotificationsTab(),
                _buildReadNotificationsTab(),
                _buildAllNotificationsTab(),
              ],
            ),
      floatingActionButton: FloatingActionButton(
        onPressed: _createManualNotification,
        backgroundColor: RevolutionaryColors.damascusSky,
        tooltip: 'تنبيه جديد',
        child: const Icon(Icons.add, color: Colors.white),
      ),
    );
  }

  /// تبويب التنبيهات المعلقة
  Widget _buildPendingNotificationsTab() {
    if (_pendingNotifications.isEmpty) {
      return _buildEmptyState(
        'لا توجد تنبيهات معلقة',
        'جميع التنبيهات محدثة',
        Icons.notifications_none,
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _pendingNotifications.length,
      itemBuilder: (context, index) {
        final notification = _pendingNotifications[index];
        return _buildNotificationCard(notification, isPending: true);
      },
    );
  }

  /// تبويب التنبيهات المقروءة
  Widget _buildReadNotificationsTab() {
    if (_readNotifications.isEmpty) {
      return _buildEmptyState(
        'لا توجد تنبيهات مقروءة',
        'ستظهر التنبيهات المقروءة هنا',
        Icons.mark_email_read,
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _readNotifications.length,
      itemBuilder: (context, index) {
        final notification = _readNotifications[index];
        return _buildNotificationCard(notification);
      },
    );
  }

  /// تبويب جميع التنبيهات
  Widget _buildAllNotificationsTab() {
    if (_allNotifications.isEmpty) {
      return _buildEmptyState(
        'لا توجد تنبيهات',
        'ستظهر جميع التنبيهات هنا',
        Icons.notifications_off,
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _allNotifications.length,
      itemBuilder: (context, index) {
        final notification = _allNotifications[index];
        return _buildNotificationCard(notification);
      },
    );
  }

  /// بناء بطاقة التنبيه
  Widget _buildNotificationCard(
    SmartNotification notification, {
    bool isPending = false,
  }) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: isPending ? 4 : 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: isPending
            ? BorderSide(
                color: _getPriorityColor(notification.priority),
                width: 2,
              )
            : BorderSide.none,
      ),
      child: InkWell(
        onTap: () => _viewNotification(notification),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  CircleAvatar(
                    backgroundColor: _getTypeColor(
                      notification.type,
                    ).withValues(alpha: 0.1),
                    child: Icon(
                      _getTypeIcon(notification.type),
                      color: _getTypeColor(notification.type),
                      size: 20,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          notification.title,
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight:
                                notification.status == NotificationStatus.read
                                ? FontWeight.normal
                                : FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          notification.message,
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.grey[600],
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ),
                  ),
                  Column(
                    children: [
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color: _getPriorityColor(
                            notification.priority,
                          ).withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          _getPriorityName(notification.priority),
                          style: TextStyle(
                            fontSize: 12,
                            color: _getPriorityColor(notification.priority),
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        _formatTime(notification.scheduledTime),
                        style: const TextStyle(
                          fontSize: 12,
                          color: Colors.grey,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
              if (isPending) ...[
                const SizedBox(height: 12),
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    TextButton.icon(
                      onPressed: () => _snoozeNotification(notification),
                      icon: const Icon(Icons.snooze, size: 16),
                      label: const Text('تأجيل'),
                      style: TextButton.styleFrom(
                        foregroundColor: Colors.orange,
                      ),
                    ),
                    const SizedBox(width: 8),
                    TextButton.icon(
                      onPressed: () => _markAsRead(notification),
                      icon: const Icon(Icons.done, size: 16),
                      label: const Text('تمييز كمقروء'),
                      style: TextButton.styleFrom(
                        foregroundColor: RevolutionaryColors.successGlow,
                      ),
                    ),
                    const SizedBox(width: 8),
                    TextButton.icon(
                      onPressed: () => _dismissNotification(notification),
                      icon: const Icon(Icons.close, size: 16),
                      label: const Text('رفض'),
                      style: TextButton.styleFrom(foregroundColor: Colors.red),
                    ),
                  ],
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  /// بناء حالة فارغة
  Widget _buildEmptyState(String title, String subtitle, IconData icon) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, size: 64, color: Colors.grey[400]),
          const SizedBox(height: 16),
          Text(
            title,
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey[600],
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            subtitle,
            style: TextStyle(fontSize: 14, color: Colors.grey[500]),
          ),
        ],
      ),
    );
  }

  /// الحصول على لون النوع
  Color _getTypeColor(NotificationType type) {
    switch (type) {
      case NotificationType.payment:
        return RevolutionaryColors.damascusSky;
      case NotificationType.invoice:
        return RevolutionaryColors.infoTurquoise;
      case NotificationType.inventory:
        return RevolutionaryColors.warningAmber;
      case NotificationType.tax:
        return Colors.purple;
      case NotificationType.warning:
        return RevolutionaryColors.warningAmber;
      case NotificationType.error:
        return RevolutionaryColors.errorCoral;
      case NotificationType.success:
        return RevolutionaryColors.successGlow;
      default:
        return Colors.grey;
    }
  }

  /// الحصول على أيقونة النوع
  IconData _getTypeIcon(NotificationType type) {
    switch (type) {
      case NotificationType.payment:
        return Icons.payment;
      case NotificationType.invoice:
        return Icons.receipt;
      case NotificationType.inventory:
        return Icons.inventory;
      case NotificationType.tax:
        return Icons.account_balance;
      case NotificationType.warning:
        return Icons.warning;
      case NotificationType.error:
        return Icons.error;
      case NotificationType.success:
        return Icons.check_circle;
      case NotificationType.reminder:
        return Icons.alarm;
      default:
        return Icons.notifications;
    }
  }

  /// الحصول على لون الأولوية
  Color _getPriorityColor(NotificationPriority priority) {
    switch (priority) {
      case NotificationPriority.low:
        return Colors.green;
      case NotificationPriority.medium:
        return Colors.orange;
      case NotificationPriority.high:
        return Colors.red;
      case NotificationPriority.urgent:
        return Colors.deepOrange;
      case NotificationPriority.critical:
        return Colors.red[900]!;
    }
  }

  /// الحصول على اسم الأولوية
  String _getPriorityName(NotificationPriority priority) {
    switch (priority) {
      case NotificationPriority.low:
        return 'منخفضة';
      case NotificationPriority.medium:
        return 'متوسطة';
      case NotificationPriority.high:
        return 'عالية';
      case NotificationPriority.urgent:
        return 'عاجلة';
      case NotificationPriority.critical:
        return 'حرجة';
    }
  }

  /// تنسيق الوقت
  String _formatTime(DateTime time) {
    final now = DateTime.now();
    final difference = now.difference(time);

    if (difference.inDays > 0) {
      return 'منذ ${difference.inDays} يوم';
    } else if (difference.inHours > 0) {
      return 'منذ ${difference.inHours} ساعة';
    } else if (difference.inMinutes > 0) {
      return 'منذ ${difference.inMinutes} دقيقة';
    } else {
      return 'الآن';
    }
  }

  // الدوال التفاعلية

  /// عرض حوار الفلترة
  void _showFilterDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          _hasActiveFilters()
              ? 'فلترة التنبيهات (${_getActiveFilterCount()} نشط)'
              : 'فلترة التنبيهات',
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            DropdownButtonFormField<NotificationType?>(
              value: _selectedTypeFilter,
              decoration: const InputDecoration(
                labelText: 'نوع التنبيه',
                border: OutlineInputBorder(),
              ),
              items: [
                const DropdownMenuItem(
                  value: null,
                  child: Text('جميع الأنواع'),
                ),
                ...NotificationType.values.map((type) {
                  return DropdownMenuItem(
                    value: type,
                    child: Text(_getTypeName(type)),
                  );
                }),
              ],
              onChanged: (value) {
                setState(() => _selectedTypeFilter = value);
              },
            ),
            const SizedBox(height: 16),
            DropdownButtonFormField<NotificationPriority?>(
              value: _selectedPriorityFilter,
              decoration: const InputDecoration(
                labelText: 'الأولوية',
                border: OutlineInputBorder(),
              ),
              items: [
                const DropdownMenuItem(
                  value: null,
                  child: Text('جميع الأولويات'),
                ),
                ...NotificationPriority.values.map((priority) {
                  return DropdownMenuItem(
                    value: priority,
                    child: Text(_getPriorityName(priority)),
                  );
                }),
              ],
              onChanged: (value) {
                setState(() => _selectedPriorityFilter = value);
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              _clearFilters();
              Navigator.pop(context);
            },
            child: const Text('مسح الفلاتر'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _applyFilters();
            },
            child: const Text('تطبيق'),
          ),
        ],
      ),
    );
  }

  /// تطبيق الفلاتر
  void _applyFilters() {
    _loadNotificationsWithFilters();
  }

  /// مسح الفلاتر
  void _clearFilters() {
    setState(() {
      _selectedTypeFilter = null;
      _selectedPriorityFilter = null;
    });
    _loadNotifications(); // تحميل جميع التنبيهات بدون فلاتر
  }

  /// فحص ما إذا كان هناك فلاتر نشطة
  bool _hasActiveFilters() {
    return _selectedTypeFilter != null || _selectedPriorityFilter != null;
  }

  /// الحصول على عدد الفلاتر النشطة
  int _getActiveFilterCount() {
    int count = 0;
    if (_selectedTypeFilter != null) count++;
    if (_selectedPriorityFilter != null) count++;
    return count;
  }

  /// تمييز جميع التنبيهات كمقروءة
  Future<void> _markAllAsRead() async {
    try {
      for (final notification in _pendingNotifications) {
        await _notificationService.updateNotificationStatus(
          notification.id!,
          NotificationStatus.read,
        );
      }

      await _loadNotifications();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم تمييز جميع التنبيهات كمقروءة'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('خطأ في تحديث التنبيهات: $e')));
      }
    }
  }

  /// عرض الإعدادات
  void _showSettings() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('إعدادات التنبيهات - قيد التطوير')),
    );
  }

  /// إنشاء تنبيه يدوي
  void _createManualNotification() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('إنشاء تنبيه جديد - قيد التطوير')),
    );
  }

  /// عرض تفاصيل التنبيه
  void _viewNotification(SmartNotification notification) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(notification.title),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(notification.message),
            const SizedBox(height: 16),
            Row(
              children: [
                const Text(
                  'النوع: ',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                Text(_getTypeName(notification.type)),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                const Text(
                  'الأولوية: ',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                Text(_getPriorityName(notification.priority)),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                const Text(
                  'الوقت: ',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                Text(_formatDateTime(notification.scheduledTime)),
              ],
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
          if (notification.status == NotificationStatus.pending)
            TextButton(
              onPressed: () {
                Navigator.pop(context);
                _markAsRead(notification);
              },
              child: const Text('تمييز كمقروء'),
            ),
        ],
      ),
    );
  }

  /// تمييز التنبيه كمقروء
  Future<void> _markAsRead(SmartNotification notification) async {
    try {
      await _notificationService.updateNotificationStatus(
        notification.id!,
        NotificationStatus.read,
      );

      await _loadNotifications();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم تمييز التنبيه كمقروء'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('خطأ في تحديث التنبيه: $e')));
      }
    }
  }

  /// تأجيل التنبيه
  Future<void> _snoozeNotification(SmartNotification notification) async {
    final duration = await showDialog<Duration>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأجيل التنبيه'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              title: const Text('15 دقيقة'),
              onTap: () => Navigator.pop(context, const Duration(minutes: 15)),
            ),
            ListTile(
              title: const Text('30 دقيقة'),
              onTap: () => Navigator.pop(context, const Duration(minutes: 30)),
            ),
            ListTile(
              title: const Text('ساعة واحدة'),
              onTap: () => Navigator.pop(context, const Duration(hours: 1)),
            ),
            ListTile(
              title: const Text('يوم واحد'),
              onTap: () => Navigator.pop(context, const Duration(days: 1)),
            ),
          ],
        ),
      ),
    );

    if (duration != null) {
      try {
        await _notificationService.snoozeNotification(
          notification.id!,
          duration,
        );
        await _loadNotifications();

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'تم تأجيل التنبيه لمدة ${_formatDuration(duration)}',
              ),
              backgroundColor: Colors.orange,
            ),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(
            context,
          ).showSnackBar(SnackBar(content: Text('خطأ في تأجيل التنبيه: $e')));
        }
      }
    }
  }

  /// رفض التنبيه
  Future<void> _dismissNotification(SmartNotification notification) async {
    try {
      await _notificationService.updateNotificationStatus(
        notification.id!,
        NotificationStatus.dismissed,
      );

      await _loadNotifications();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم رفض التنبيه'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('خطأ في رفض التنبيه: $e')));
      }
    }
  }

  /// الحصول على اسم النوع
  String _getTypeName(NotificationType type) {
    switch (type) {
      case NotificationType.payment:
        return 'دفعة';
      case NotificationType.invoice:
        return 'فاتورة';
      case NotificationType.inventory:
        return 'مخزون';
      case NotificationType.tax:
        return 'ضريبة';
      case NotificationType.warning:
        return 'تحذير';
      case NotificationType.error:
        return 'خطأ';
      case NotificationType.success:
        return 'نجاح';
      case NotificationType.reminder:
        return 'تذكير';
      default:
        return 'عام';
    }
  }

  /// تنسيق التاريخ والوقت
  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.day}/${dateTime.month}/${dateTime.year} ${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  /// تنسيق المدة
  String _formatDuration(Duration duration) {
    if (duration.inDays > 0) {
      return '${duration.inDays} يوم';
    } else if (duration.inHours > 0) {
      return '${duration.inHours} ساعة';
    } else {
      return '${duration.inMinutes} دقيقة';
    }
  }
}
