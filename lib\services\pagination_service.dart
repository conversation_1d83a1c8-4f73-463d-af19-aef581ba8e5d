/// خدمة التحميل التدريجي (Pagination) - <PERSON> Ledger
/// تدير تحميل البيانات بشكل تدريجي لتحسين الأداء
library;

/// خدمة التحميل التدريجي المتقدمة
class PaginationService {
  static const int defaultPageSize = 20;
  static const int maxPageSize = 100;

  /// نتيجة التقسيم
  static PaginationResult<T> paginate<T>(
    List<T> items,
    int page, {
    int pageSize = defaultPageSize,
  }) {
    // التحقق من صحة المعاملات
    if (page < 1) page = 1;
    if (pageSize < 1) pageSize = defaultPageSize;
    if (pageSize > maxPageSize) pageSize = maxPageSize;

    final totalItems = items.length;
    final totalPages = (totalItems / pageSize).ceil();
    final startIndex = (page - 1) * pageSize;
    final endIndex = (startIndex + pageSize).clamp(0, totalItems);

    final pageItems = startIndex < totalItems
        ? items.sublist(startIndex, endIndex)
        : <T>[];

    return PaginationResult<T>(
      items: pageItems,
      currentPage: page,
      pageSize: pageSize,
      totalItems: totalItems,
      totalPages: totalPages,
      hasNextPage: page < totalPages,
      hasPreviousPage: page > 1,
    );
  }

  /// تقسيم البيانات مع البحث والفلترة
  static PaginationResult<T> paginateWithFilter<T>(
    List<T> items,
    int page, {
    int pageSize = defaultPageSize,
    String? searchQuery,
    bool Function(T item, String query)? searchFunction,
    bool Function(T item)? filterFunction,
  }) {
    var filteredItems = items;

    // تطبيق الفلترة
    if (filterFunction != null) {
      filteredItems = filteredItems.where(filterFunction).toList();
    }

    // تطبيق البحث
    if (searchQuery != null &&
        searchQuery.isNotEmpty &&
        searchFunction != null) {
      filteredItems = filteredItems
          .where((item) => searchFunction(item, searchQuery))
          .toList();
    }

    return paginate(filteredItems, page, pageSize: pageSize);
  }

  /// حساب معلومات الصفحة
  static PageInfo calculatePageInfo(
    int totalItems,
    int currentPage,
    int pageSize,
  ) {
    final totalPages = (totalItems / pageSize).ceil();
    final startItem = totalItems > 0 ? (currentPage - 1) * pageSize + 1 : 0;
    final endItem = (currentPage * pageSize).clamp(0, totalItems);

    return PageInfo(
      currentPage: currentPage,
      totalPages: totalPages,
      pageSize: pageSize,
      totalItems: totalItems,
      startItem: startItem,
      endItem: endItem,
      hasNextPage: currentPage < totalPages,
      hasPreviousPage: currentPage > 1,
    );
  }

  /// إنشاء أرقام الصفحات للعرض
  static List<int> generatePageNumbers(
    int currentPage,
    int totalPages, {
    int maxVisible = 5,
  }) {
    if (totalPages <= maxVisible) {
      return List.generate(totalPages, (index) => index + 1);
    }

    final List<int> pages = [];
    final int half = maxVisible ~/ 2;

    int start = (currentPage - half).clamp(1, totalPages - maxVisible + 1);
    int end = (start + maxVisible - 1).clamp(maxVisible, totalPages);

    // تعديل البداية إذا كانت النهاية في الحد الأقصى
    if (end == totalPages) {
      start = (totalPages - maxVisible + 1).clamp(1, totalPages);
    }

    for (int i = start; i <= end; i++) {
      pages.add(i);
    }

    return pages;
  }
}

/// نتيجة التقسيم
class PaginationResult<T> {
  final List<T> items;
  final int currentPage;
  final int pageSize;
  final int totalItems;
  final int totalPages;
  final bool hasNextPage;
  final bool hasPreviousPage;

  const PaginationResult({
    required this.items,
    required this.currentPage,
    required this.pageSize,
    required this.totalItems,
    required this.totalPages,
    required this.hasNextPage,
    required this.hasPreviousPage,
  });

  /// معلومات الصفحة كنص
  String get pageInfo {
    if (totalItems == 0) return 'لا توجد عناصر';

    final start = (currentPage - 1) * pageSize + 1;
    final end = (currentPage * pageSize).clamp(0, totalItems);

    return 'عرض $start-$end من أصل $totalItems';
  }

  /// نسخة محدثة من النتيجة
  PaginationResult<T> copyWith({
    List<T>? items,
    int? currentPage,
    int? pageSize,
    int? totalItems,
    int? totalPages,
    bool? hasNextPage,
    bool? hasPreviousPage,
  }) {
    return PaginationResult<T>(
      items: items ?? this.items,
      currentPage: currentPage ?? this.currentPage,
      pageSize: pageSize ?? this.pageSize,
      totalItems: totalItems ?? this.totalItems,
      totalPages: totalPages ?? this.totalPages,
      hasNextPage: hasNextPage ?? this.hasNextPage,
      hasPreviousPage: hasPreviousPage ?? this.hasPreviousPage,
    );
  }

  @override
  String toString() {
    return 'PaginationResult(page: $currentPage/$totalPages, items: ${items.length}, total: $totalItems)';
  }
}

/// معلومات الصفحة
class PageInfo {
  final int currentPage;
  final int totalPages;
  final int pageSize;
  final int totalItems;
  final int startItem;
  final int endItem;
  final bool hasNextPage;
  final bool hasPreviousPage;

  const PageInfo({
    required this.currentPage,
    required this.totalPages,
    required this.pageSize,
    required this.totalItems,
    required this.startItem,
    required this.endItem,
    required this.hasNextPage,
    required this.hasPreviousPage,
  });

  @override
  String toString() {
    return 'عرض $startItem-$endItem من أصل $totalItems (صفحة $currentPage من $totalPages)';
  }
}

/// معاملات التقسيم
class PaginationParams {
  final int page;
  final int pageSize;
  final String? searchQuery;
  final Map<String, dynamic>? filters;
  final String? sortBy;
  final bool sortAscending;

  const PaginationParams({
    this.page = 1,
    this.pageSize = PaginationService.defaultPageSize,
    this.searchQuery,
    this.filters,
    this.sortBy,
    this.sortAscending = true,
  });

  /// نسخة محدثة من المعاملات
  PaginationParams copyWith({
    int? page,
    int? pageSize,
    String? searchQuery,
    Map<String, dynamic>? filters,
    String? sortBy,
    bool? sortAscending,
  }) {
    return PaginationParams(
      page: page ?? this.page,
      pageSize: pageSize ?? this.pageSize,
      searchQuery: searchQuery ?? this.searchQuery,
      filters: filters ?? this.filters,
      sortBy: sortBy ?? this.sortBy,
      sortAscending: sortAscending ?? this.sortAscending,
    );
  }

  /// تحويل إلى Map للاستعلامات
  Map<String, dynamic> toMap() {
    return {
      'page': page,
      'pageSize': pageSize,
      'searchQuery': searchQuery,
      'filters': filters,
      'sortBy': sortBy,
      'sortAscending': sortAscending,
    };
  }

  @override
  String toString() {
    return 'PaginationParams(page: $page, pageSize: $pageSize, search: $searchQuery)';
  }
}

/// مساعد للتقسيم في قاعدة البيانات
class DatabasePagination {
  /// إنشاء استعلام SQL مع LIMIT و OFFSET
  static String addPaginationToQuery(
    String baseQuery,
    PaginationParams params,
  ) {
    final offset = (params.page - 1) * params.pageSize;
    return '$baseQuery LIMIT ${params.pageSize} OFFSET $offset';
  }

  /// إنشاء استعلام العد
  static String createCountQuery(String baseQuery) {
    // إزالة ORDER BY من استعلام العد لتحسين الأداء
    final queryWithoutOrderBy = baseQuery.replaceAll(
      RegExp(r'ORDER BY.*$', caseSensitive: false),
      '',
    );
    return 'SELECT COUNT(*) as count FROM ($queryWithoutOrderBy)';
  }

  /// إضافة شروط البحث
  static String addSearchCondition(
    String query,
    String searchQuery,
    List<String> searchColumns,
  ) {
    if (searchQuery.isEmpty || searchColumns.isEmpty) return query;

    final searchConditions = searchColumns
        .map((column) => '$column LIKE ?')
        .join(' OR ');

    final whereClause = query.toUpperCase().contains('WHERE')
        ? ' AND ($searchConditions)'
        : ' WHERE ($searchConditions)';

    return query + whereClause;
  }

  /// إضافة شروط الفلترة
  static String addFilterConditions(
    String query,
    Map<String, dynamic> filters,
  ) {
    if (filters.isEmpty) return query;

    final filterConditions = <String>[];
    for (final entry in filters.entries) {
      if (entry.value != null) {
        filterConditions.add('${entry.key} = ?');
      }
    }

    if (filterConditions.isEmpty) return query;

    final whereClause = query.toUpperCase().contains('WHERE')
        ? ' AND ${filterConditions.join(' AND ')}'
        : ' WHERE ${filterConditions.join(' AND ')}';

    return query + whereClause;
  }

  /// إضافة ترتيب
  static String addSorting(String query, String? sortBy, bool sortAscending) {
    if (sortBy == null || sortBy.isEmpty) return query;

    final direction = sortAscending ? 'ASC' : 'DESC';
    return '$query ORDER BY $sortBy $direction';
  }

  /// بناء استعلام كامل مع جميع الخيارات
  static QueryBuilder buildQuery(
    String baseQuery,
    PaginationParams params,
    List<String> searchColumns,
  ) {
    var query = baseQuery;
    final queryParams = <dynamic>[];

    // إضافة البحث
    if (params.searchQuery != null && params.searchQuery!.isNotEmpty) {
      query = addSearchCondition(query, params.searchQuery!, searchColumns);
      // إضافة معاملات البحث
      for (int i = 0; i < searchColumns.length; i++) {
        queryParams.add('%${params.searchQuery}%');
      }
    }

    // إضافة الفلترة
    if (params.filters != null && params.filters!.isNotEmpty) {
      query = addFilterConditions(query, params.filters!);
      // إضافة معاملات الفلترة
      for (final value in params.filters!.values) {
        if (value != null) {
          queryParams.add(value);
        }
      }
    }

    // إضافة الترتيب
    query = addSorting(query, params.sortBy, params.sortAscending);

    // إضافة التقسيم
    final paginatedQuery = addPaginationToQuery(query, params);

    // إنشاء استعلام العد
    final countQuery = createCountQuery(query);

    return QueryBuilder(
      query: paginatedQuery,
      countQuery: countQuery,
      params: queryParams,
    );
  }
}

/// بناء الاستعلامات
class QueryBuilder {
  final String query;
  final String countQuery;
  final List<dynamic> params;

  const QueryBuilder({
    required this.query,
    required this.countQuery,
    required this.params,
  });

  @override
  String toString() {
    return 'QueryBuilder(query: $query, params: $params)';
  }
}
