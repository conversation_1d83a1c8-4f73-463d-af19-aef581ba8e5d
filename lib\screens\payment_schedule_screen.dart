/// شاشة جدولة الدفعات المتقدمة
/// تعرض وتدير جميع جداول الدفعات مع واجهة جذابة وسهلة الاستخدام
library;

import 'package:flutter/material.dart';
import '../models/payment_schedule.dart';
import '../services/payment_schedule_service.dart';
import '../constants/revolutionary_design_colors.dart';
import '../widgets/loading_widget.dart';
import 'payment_schedule_screens/create_payment_schedule_screen.dart';
import 'payment_schedule_screens/payment_schedule_details_screen.dart';
import 'payment_schedule_screens/edit_payment_schedule_screen.dart';

class PaymentScheduleScreen extends StatefulWidget {
  const PaymentScheduleScreen({super.key});

  @override
  State<PaymentScheduleScreen> createState() => _PaymentScheduleScreenState();
}

class _PaymentScheduleScreenState extends State<PaymentScheduleScreen>
    with TickerProviderStateMixin {
  final PaymentScheduleService _scheduleService = PaymentScheduleService();

  List<PaymentSchedule> _schedules = [];
  List<PaymentSchedule> _filteredSchedules = [];
  Map<String, dynamic> _statistics = {};

  bool _isLoading = true;
  String _searchQuery = '';
  ScheduleStatus? _selectedStatus;
  PaymentFrequency? _selectedFrequency;

  late TabController _tabController;
  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _loadData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    setState(() => _isLoading = true);

    try {
      final schedules = await _scheduleService.getAllPaymentSchedules();
      final statistics = await _scheduleService.getScheduleStatistics();

      setState(() {
        _schedules = schedules;
        _filteredSchedules = schedules;
        _statistics = statistics;
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      _showError('خطأ في تحميل البيانات: $e');
    }
  }

  void _filterSchedules() {
    setState(() {
      _filteredSchedules = _schedules.where((schedule) {
        // فلترة النص
        bool matchesSearch =
            _searchQuery.isEmpty ||
            schedule.scheduleName.toLowerCase().contains(
              _searchQuery.toLowerCase(),
            );

        // فلترة الحالة
        bool matchesStatus =
            _selectedStatus == null || schedule.status == _selectedStatus;

        // فلترة التكرار
        bool matchesFrequency =
            _selectedFrequency == null ||
            schedule.frequency == _selectedFrequency;

        return matchesSearch && matchesStatus && matchesFrequency;
      }).toList();
    });
  }

  void _showError(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: RevolutionaryColors.errorCoral,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _showSuccess(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: RevolutionaryColors.successGlow,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: RevolutionaryColors.backgroundPrimary,
      appBar: _buildAppBar(),
      body: _isLoading
          ? const LoadingWidget()
          : Column(
              children: [
                _buildStatisticsCards(),
                _buildFilters(),
                Expanded(
                  child: TabBarView(
                    controller: _tabController,
                    children: [
                      _buildSchedulesList(null), // جميع الجداول
                      _buildSchedulesList(ScheduleStatus.active), // النشطة
                      _buildSchedulesList(ScheduleStatus.completed), // المكتملة
                      _buildDueSchedules(), // المستحقة
                    ],
                  ),
                ),
              ],
            ),
      floatingActionButton: _buildFloatingActionButton(),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: const Text(
        'جدولة الدفعات',
        style: TextStyle(
          fontWeight: FontWeight.bold,
          color: RevolutionaryColors.textOnDark,
        ),
      ),
      backgroundColor: RevolutionaryColors.damascusSky,
      elevation: 0,
      bottom: TabBar(
        controller: _tabController,
        indicatorColor: RevolutionaryColors.syrianGold,
        labelColor: RevolutionaryColors.textOnDark,
        unselectedLabelColor: RevolutionaryColors.textOnDark.withValues(
          alpha: 0.7,
        ),
        tabs: const [
          Tab(text: 'الكل'),
          Tab(text: 'النشطة'),
          Tab(text: 'المكتملة'),
          Tab(text: 'المستحقة'),
        ],
      ),
      actions: [
        IconButton(
          onPressed: _loadData,
          icon: const Icon(
            Icons.refresh,
            color: RevolutionaryColors.textOnDark,
          ),
        ),
      ],
    );
  }

  Widget _buildStatisticsCards() {
    if (_statistics.isEmpty) return const SizedBox.shrink();

    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          Expanded(
            child: _buildStatCard(
              'إجمالي الجداول',
              _statistics['total_schedules']?.toString() ?? '0',
              Icons.schedule,
              RevolutionaryColors.damascusSky,
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: _buildStatCard(
              'النشطة',
              _statistics['active_schedules']?.toString() ?? '0',
              Icons.play_circle,
              RevolutionaryColors.successGlow,
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: _buildStatCard(
              'المستحقة',
              _statistics['due_schedules']?.toString() ?? '0',
              Icons.warning,
              RevolutionaryColors.warningAmber,
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: _buildStatCard(
              'نسبة الإكمال',
              '${(_statistics['completion_percentage'] ?? 0.0).toStringAsFixed(1)}%',
              Icons.pie_chart,
              RevolutionaryColors.infoTurquoise,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Card(
      elevation: 2,
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              color.withValues(alpha: 0.1),
              color.withValues(alpha: 0.05),
            ],
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(icon, color: color, size: 24),
            const SizedBox(height: 4),
            Text(
              value,
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            Text(
              title,
              style: const TextStyle(
                fontSize: 12,
                color: RevolutionaryColors.textSecondary,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFilters() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Column(
        children: [
          // شريط البحث
          TextField(
            controller: _searchController,
            decoration: InputDecoration(
              hintText: 'البحث في الجداول...',
              prefixIcon: const Icon(Icons.search),
              suffixIcon: _searchQuery.isNotEmpty
                  ? IconButton(
                      onPressed: () {
                        _searchController.clear();
                        setState(() => _searchQuery = '');
                        _filterSchedules();
                      },
                      icon: const Icon(Icons.clear),
                    )
                  : null,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              filled: true,
              fillColor: RevolutionaryColors.backgroundCard,
            ),
            onChanged: (value) {
              setState(() => _searchQuery = value);
              _filterSchedules();
            },
          ),
          const SizedBox(height: 8),
          // فلاتر إضافية
          Row(
            children: [
              Expanded(
                child: DropdownButtonFormField<ScheduleStatus>(
                  value: _selectedStatus,
                  decoration: InputDecoration(
                    labelText: 'الحالة',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    filled: true,
                    fillColor: RevolutionaryColors.backgroundCard,
                  ),
                  items: ScheduleStatus.values.map((status) {
                    return DropdownMenuItem(
                      value: status,
                      child: Text(status.displayName),
                    );
                  }).toList(),
                  onChanged: (value) {
                    setState(() => _selectedStatus = value);
                    _filterSchedules();
                  },
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: DropdownButtonFormField<PaymentFrequency>(
                  value: _selectedFrequency,
                  decoration: InputDecoration(
                    labelText: 'التكرار',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    filled: true,
                    fillColor: RevolutionaryColors.backgroundCard,
                  ),
                  items: PaymentFrequency.values.map((frequency) {
                    return DropdownMenuItem(
                      value: frequency,
                      child: Text(frequency.displayName),
                    );
                  }).toList(),
                  onChanged: (value) {
                    setState(() => _selectedFrequency = value);
                    _filterSchedules();
                  },
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSchedulesList(ScheduleStatus? filterStatus) {
    List<PaymentSchedule> schedules = _filteredSchedules;

    if (filterStatus != null) {
      schedules = schedules.where((s) => s.status == filterStatus).toList();
    }

    if (schedules.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.schedule_outlined,
              size: 64,
              color: RevolutionaryColors.textSecondary,
            ),
            const SizedBox(height: 16),
            Text(
              'لا توجد جداول دفعات',
              style: TextStyle(
                fontSize: 18,
                color: RevolutionaryColors.textSecondary,
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: schedules.length,
      itemBuilder: (context, index) {
        final schedule = schedules[index];
        return _buildScheduleCard(schedule);
      },
    );
  }

  Widget _buildDueSchedules() {
    return FutureBuilder<List<PaymentSchedule>>(
      future: _scheduleService.getDueSchedules(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const LoadingWidget();
        }

        if (snapshot.hasError) {
          return Center(child: Text('خطأ: ${snapshot.error}'));
        }

        final dueSchedules = snapshot.data ?? [];

        if (dueSchedules.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.check_circle_outline,
                  size: 64,
                  color: RevolutionaryColors.successGlow,
                ),
                const SizedBox(height: 16),
                Text(
                  'لا توجد دفعات مستحقة',
                  style: TextStyle(
                    fontSize: 18,
                    color: RevolutionaryColors.successGlow,
                  ),
                ),
              ],
            ),
          );
        }

        return ListView.builder(
          padding: const EdgeInsets.all(16),
          itemCount: dueSchedules.length,
          itemBuilder: (context, index) {
            final schedule = dueSchedules[index];
            return _buildScheduleCard(schedule, isDue: true);
          },
        );
      },
    );
  }

  Widget _buildScheduleCard(PaymentSchedule schedule, {bool isDue = false}) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          border: isDue
              ? Border.all(color: RevolutionaryColors.warningAmber, width: 2)
              : null,
        ),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // العنوان والحالة
              Row(
                children: [
                  Expanded(
                    child: Text(
                      schedule.scheduleName,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  _buildStatusChip(schedule.status),
                ],
              ),
              const SizedBox(height: 8),

              // معلومات أساسية
              Row(
                children: [
                  Expanded(
                    child: _buildInfoItem(
                      'التكرار',
                      schedule.frequency.displayName,
                      Icons.repeat,
                    ),
                  ),
                  Expanded(
                    child: _buildInfoItem(
                      'المبلغ الإجمالي',
                      '${schedule.totalAmount.toStringAsFixed(2)} ل.س',
                      Icons.attach_money,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),

              Row(
                children: [
                  Expanded(
                    child: _buildInfoItem(
                      'المبلغ المدفوع',
                      '${schedule.paidAmount.toStringAsFixed(2)} ل.س',
                      Icons.payment,
                    ),
                  ),
                  Expanded(
                    child: _buildInfoItem(
                      'الدفعة التالية',
                      _formatDate(schedule.nextPaymentDate),
                      Icons.schedule,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),

              // شريط التقدم
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'التقدم: ${schedule.completedInstallments}/${schedule.totalInstallments}',
                        style: const TextStyle(
                          fontSize: 12,
                          color: RevolutionaryColors.textSecondary,
                        ),
                      ),
                      Text(
                        '${schedule.completionPercentage.toStringAsFixed(1)}%',
                        style: const TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                          color: RevolutionaryColors.damascusSky,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 4),
                  LinearProgressIndicator(
                    value: schedule.completionPercentage / 100,
                    backgroundColor: RevolutionaryColors.borderLight,
                    valueColor: AlwaysStoppedAnimation<Color>(
                      schedule.isCompleted
                          ? RevolutionaryColors.successGlow
                          : RevolutionaryColors.damascusSky,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),

              // أزرار الإجراءات
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  if (schedule.isActive && schedule.isNextPaymentDue)
                    TextButton.icon(
                      onPressed: () => _recordPayment(schedule),
                      icon: const Icon(Icons.payment),
                      label: const Text('تسجيل دفعة'),
                      style: TextButton.styleFrom(
                        foregroundColor: RevolutionaryColors.successGlow,
                      ),
                    ),
                  TextButton.icon(
                    onPressed: () => _viewScheduleDetails(schedule),
                    icon: const Icon(Icons.visibility),
                    label: const Text('التفاصيل'),
                    style: TextButton.styleFrom(
                      foregroundColor: RevolutionaryColors.damascusSky,
                    ),
                  ),
                  PopupMenuButton<String>(
                    onSelected: (value) =>
                        _handleScheduleAction(schedule, value),
                    itemBuilder: (context) => [
                      const PopupMenuItem(
                        value: 'edit',
                        child: ListTile(
                          leading: Icon(Icons.edit),
                          title: Text('تعديل'),
                        ),
                      ),
                      if (schedule.isActive)
                        const PopupMenuItem(
                          value: 'pause',
                          child: ListTile(
                            leading: Icon(Icons.pause),
                            title: Text('إيقاف مؤقت'),
                          ),
                        ),
                      if (schedule.status == ScheduleStatus.paused)
                        const PopupMenuItem(
                          value: 'resume',
                          child: ListTile(
                            leading: Icon(Icons.play_arrow),
                            title: Text('استئناف'),
                          ),
                        ),
                      const PopupMenuItem(
                        value: 'cancel',
                        child: ListTile(
                          leading: Icon(Icons.cancel),
                          title: Text('إلغاء'),
                        ),
                      ),
                      const PopupMenuItem(
                        value: 'delete',
                        child: ListTile(
                          leading: Icon(Icons.delete),
                          title: Text('حذف'),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatusChip(ScheduleStatus status) {
    Color color;
    switch (status) {
      case ScheduleStatus.active:
        color = RevolutionaryColors.successGlow;
        break;
      case ScheduleStatus.paused:
        color = RevolutionaryColors.warningAmber;
        break;
      case ScheduleStatus.completed:
        color = RevolutionaryColors.damascusSky;
        break;
      case ScheduleStatus.cancelled:
        color = RevolutionaryColors.errorCoral;
        break;
      case ScheduleStatus.expired:
        color = RevolutionaryColors.textSecondary;
        break;
    }

    return Chip(
      label: Text(
        status.displayName,
        style: const TextStyle(
          fontSize: 12,
          color: RevolutionaryColors.textOnDark,
        ),
      ),
      backgroundColor: color,
      materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
    );
  }

  Widget _buildInfoItem(String label, String value, IconData icon) {
    return Row(
      children: [
        Icon(icon, size: 16, color: RevolutionaryColors.textSecondary),
        const SizedBox(width: 4),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: const TextStyle(
                  fontSize: 12,
                  color: RevolutionaryColors.textSecondary,
                ),
              ),
              Text(
                value,
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildFloatingActionButton() {
    return FloatingActionButton.extended(
      onPressed: _createNewSchedule,
      backgroundColor: RevolutionaryColors.damascusSky,
      foregroundColor: RevolutionaryColors.textOnDark,
      icon: const Icon(Icons.add),
      label: const Text('جدولة جديدة'),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day.toString().padLeft(2, '0')}/'
        '${date.month.toString().padLeft(2, '0')}/'
        '${date.year}';
  }

  // دوال الإجراءات
  void _createNewSchedule() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => CreatePaymentScheduleScreen(
          onScheduleCreated: () {
            _loadData();
            _showSuccess('تم إنشاء الجدولة بنجاح');
          },
        ),
      ),
    );
  }

  void _viewScheduleDetails(PaymentSchedule schedule) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => PaymentScheduleDetailsScreen(
          schedule: schedule,
          onScheduleUpdated: () {
            _loadData();
          },
        ),
      ),
    );
  }

  void _recordPayment(PaymentSchedule schedule) async {
    try {
      await _scheduleService.recordScheduledPayment(
        schedule.id!,
        schedule.installmentAmount,
      );
      _showSuccess('تم تسجيل الدفعة بنجاح');
      _loadData();
    } catch (e) {
      _showError('خطأ في تسجيل الدفعة: $e');
    }
  }

  void _handleScheduleAction(PaymentSchedule schedule, String action) async {
    try {
      switch (action) {
        case 'edit':
          Navigator.of(context).push(
            MaterialPageRoute(
              builder: (context) => EditPaymentScheduleScreen(
                schedule: schedule,
                onScheduleUpdated: () {
                  _loadData();
                  _showSuccess('تم تحديث الجدولة بنجاح');
                },
              ),
            ),
          );
          break;
        case 'pause':
          await _scheduleService.pauseSchedule(schedule.id!);
          _showSuccess('تم إيقاف الجدولة مؤقتاً');
          _loadData();
          break;
        case 'resume':
          await _scheduleService.resumeSchedule(schedule.id!);
          _showSuccess('تم استئناف الجدولة');
          _loadData();
          break;
        case 'cancel':
          await _scheduleService.cancelSchedule(schedule.id!);
          _showSuccess('تم إلغاء الجدولة');
          _loadData();
          break;
        case 'delete':
          final confirmed = await _showDeleteConfirmation();
          if (confirmed) {
            await _scheduleService.deleteSchedule(schedule.id!);
            _showSuccess('تم حذف الجدولة');
            _loadData();
          }
          break;
      }
    } catch (e) {
      _showError('خطأ في تنفيذ العملية: $e');
    }
  }

  Future<bool> _showDeleteConfirmation() async {
    return await showDialog<bool>(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('تأكيد الحذف'),
            content: const Text('هل أنت متأكد من حذف هذه الجدولة؟'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: const Text('إلغاء'),
              ),
              TextButton(
                onPressed: () => Navigator.of(context).pop(true),
                style: TextButton.styleFrom(
                  foregroundColor: RevolutionaryColors.errorCoral,
                ),
                child: const Text('حذف'),
              ),
            ],
          ),
        ) ??
        false;
  }
}
