/// شاشة تفاصيل الفاتورة المتكررة
/// تعرض تفاصيل الفاتورة المتكررة مع إمكانية التعديل والإدارة
library;

import 'package:flutter/material.dart';
import '../models/recurring_invoice.dart';
import '../services/recurring_invoice_service.dart';

import '../constants/revolutionary_design_colors.dart';
import '../widgets/loading_widget.dart';
import 'add_recurring_invoice_screen.dart';

class RecurringInvoiceDetailsScreen extends StatefulWidget {
  final RecurringInvoice recurringInvoice;

  const RecurringInvoiceDetailsScreen({
    super.key,
    required this.recurringInvoice,
  });

  @override
  State<RecurringInvoiceDetailsScreen> createState() =>
      _RecurringInvoiceDetailsScreenState();
}

class _RecurringInvoiceDetailsScreenState
    extends State<RecurringInvoiceDetailsScreen> {
  final RecurringInvoiceService _recurringInvoiceService =
      RecurringInvoiceService();
  late RecurringInvoice _recurringInvoice;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _recurringInvoice = widget.recurringInvoice;
  }

  Future<void> _refreshRecurringInvoice() async {
    setState(() => _isLoading = true);

    try {
      final updatedRecurringInvoice = await _recurringInvoiceService
          .getRecurringInvoiceById(_recurringInvoice.id!);
      if (updatedRecurringInvoice != null) {
        setState(() => _recurringInvoice = updatedRecurringInvoice);
      }
    } catch (e) {
      _showErrorSnackBar('خطأ في تحديث البيانات: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _toggleStatus() async {
    setState(() => _isLoading = true);

    try {
      await _recurringInvoiceService.toggleRecurringInvoiceStatus(
        _recurringInvoice.id!,
        !_recurringInvoice.isActive,
      );

      _showSuccessSnackBar(
        _recurringInvoice.isActive
            ? 'تم إلغاء تفعيل الفاتورة المتكررة'
            : 'تم تفعيل الفاتورة المتكررة',
      );

      await _refreshRecurringInvoice();
    } catch (e) {
      _showErrorSnackBar('خطأ في تغيير حالة الفاتورة المتكررة: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _generateInvoice() async {
    final confirmed = await _showConfirmDialog(
      'إنشاء فاتورة',
      'هل تريد إنشاء فاتورة جديدة من هذا النموذج؟',
    );

    if (!confirmed) return;

    setState(() => _isLoading = true);

    try {
      final invoiceId = await _recurringInvoiceService.generateInvoiceManually(
        _recurringInvoice.id!,
      );

      _showSuccessSnackBar('تم إنشاء الفاتورة بنجاح (رقم: $invoiceId)');
      await _refreshRecurringInvoice();
    } catch (e) {
      _showErrorSnackBar('خطأ في إنشاء الفاتورة: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<bool> _showConfirmDialog(String title, String content) async {
    return await showDialog<bool>(
          context: context,
          builder: (context) => AlertDialog(
            title: Text(title),
            content: Text(content),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: const Text('إلغاء'),
              ),
              ElevatedButton(
                onPressed: () => Navigator.of(context).pop(true),
                style: ElevatedButton.styleFrom(
                  backgroundColor: RevolutionaryColors.damascusSky,
                ),
                child: const Text('تأكيد'),
              ),
            ],
          ),
        ) ??
        false;
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: RevolutionaryColors.successGlow,
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: RevolutionaryColors.errorCoral,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('الفاتورة المتكررة: ${_recurringInvoice.templateName}'),
        backgroundColor: RevolutionaryColors.damascusSky,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.edit),
            onPressed: () async {
              final navigator = Navigator.of(context);
              final result = await Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => AddRecurringInvoiceScreen(
                    recurringInvoice: _recurringInvoice,
                  ),
                ),
              );
              if (result == true) {
                await _refreshRecurringInvoice();
                if (mounted) {
                  navigator.pop(true);
                }
              }
            },
            tooltip: 'تعديل',
          ),
          PopupMenuButton<String>(
            onSelected: (value) {
              switch (value) {
                case 'toggle_status':
                  _toggleStatus();
                  break;
                case 'generate':
                  _generateInvoice();
                  break;
              }
            },
            itemBuilder: (context) => [
              PopupMenuItem(
                value: 'toggle_status',
                child: Row(
                  children: [
                    Icon(
                      _recurringInvoice.isActive
                          ? Icons.pause
                          : Icons.play_arrow,
                      size: 16,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      _recurringInvoice.isActive ? 'إلغاء التفعيل' : 'تفعيل',
                    ),
                  ],
                ),
              ),
              if (_recurringInvoice.isActive)
                const PopupMenuItem(
                  value: 'generate',
                  child: Row(
                    children: [
                      Icon(Icons.add_circle, size: 16),
                      SizedBox(width: 8),
                      Text('إنشاء فاتورة'),
                    ],
                  ),
                ),
            ],
          ),
        ],
      ),
      body: _isLoading
          ? const LoadingWidget()
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildHeader(),
                  const SizedBox(height: 24),
                  _buildRecurrenceInfo(),
                  const SizedBox(height: 24),
                  _buildInvoiceTemplate(),
                  const SizedBox(height: 24),
                  _buildNotesSection(),
                  if (_recurringInvoice.isActive &&
                      _recurringInvoice.shouldGenerate) ...[
                    const SizedBox(height: 24),
                    _buildGenerateButton(),
                  ],
                ],
              ),
            ),
    );
  }

  Widget _buildHeader() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        _recurringInvoice.templateName,
                        style: const TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                          color: RevolutionaryColors.damascusSky,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'العميل/المورد: ${_recurringInvoice.clientName}',
                        style: const TextStyle(fontSize: 16),
                      ),
                    ],
                  ),
                ),
                _buildStatusChip(),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusChip() {
    Color backgroundColor;
    Color textColor;
    String displayText;
    IconData icon;

    if (!_recurringInvoice.isActive) {
      backgroundColor = Colors.grey[100]!;
      textColor = Colors.grey[700]!;
      displayText = 'معطلة';
      icon = Icons.pause_circle;
    } else if (_recurringInvoice.isDue) {
      backgroundColor = Colors.orange[100]!;
      textColor = Colors.orange[700]!;
      displayText = 'مستحقة';
      icon = Icons.schedule;
    } else if (_recurringInvoice.isExpired) {
      backgroundColor = Colors.red[100]!;
      textColor = Colors.red[700]!;
      displayText = 'منتهية';
      icon = Icons.cancel;
    } else {
      backgroundColor = Colors.green[100]!;
      textColor = Colors.green[700]!;
      displayText = 'مفعلة';
      icon = Icons.check_circle;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(20),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 20, color: textColor),
          const SizedBox(width: 8),
          Text(
            displayText,
            style: TextStyle(
              color: textColor,
              fontSize: 14,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRecurrenceInfo() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'معلومات التكرار',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            _buildInfoRow('التكرار:', _recurringInvoice.displayFrequency),
            const SizedBox(height: 8),
            _buildInfoRow(
              'تاريخ البداية:',
              _formatDate(_recurringInvoice.startDate),
            ),
            const SizedBox(height: 8),
            _buildInfoRow(
              'تاريخ الانتهاء:',
              _recurringInvoice.endDate != null
                  ? _formatDate(_recurringInvoice.endDate!)
                  : 'بدون انتهاء',
            ),
            const SizedBox(height: 8),
            _buildInfoRow(
              'التاريخ التالي للإنشاء:',
              _formatDate(_recurringInvoice.nextGenerationDate),
              isHighlighted: _recurringInvoice.isDue,
            ),
            if (_recurringInvoice.lastGeneratedDate != null) ...[
              const SizedBox(height: 8),
              _buildInfoRow(
                'آخر إنشاء:',
                _formatDate(_recurringInvoice.lastGeneratedDate!),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildInvoiceTemplate() {
    final template = _recurringInvoice.getParsedInvoiceTemplate();

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'الفاتورة النموذجية',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            if (template != null) ...[
              _buildInfoRow('رقم الفاتورة النموذجية:', template.invoiceNumber),
              const SizedBox(height: 8),
              _buildInfoRow(
                'نوع الفاتورة:',
                template.type == 'sale' ? 'مبيعات' : 'مشتريات',
              ),
              const SizedBox(height: 8),
              _buildInfoRow(
                'المبلغ الإجمالي:',
                '${template.totalAmount.toStringAsFixed(2)} ل.س',
              ),
              const SizedBox(height: 8),
              _buildInfoRow('عدد الأصناف:', '${template.items.length}'),
              const SizedBox(height: 16),
              const Text(
                'الأصناف:',
                style: TextStyle(fontWeight: FontWeight.w500),
              ),
              const SizedBox(height: 8),
              ...template.items.take(3).map((item) {
                return Padding(
                  padding: const EdgeInsets.only(bottom: 4),
                  child: Text(
                    '• صنف ${item.itemId} (${item.quantity} × ${item.unitPrice.toStringAsFixed(2)})',
                    style: const TextStyle(fontSize: 14),
                  ),
                );
              }),
              if (template.items.length > 3)
                Text(
                  '... و ${template.items.length - 3} أصناف أخرى',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[600],
                    fontStyle: FontStyle.italic,
                  ),
                ),
            ] else ...[
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.red[50],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.red[200]!),
                ),
                child: Row(
                  children: [
                    Icon(Icons.error, color: Colors.red[700]),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        'خطأ في تحليل الفاتورة النموذجية',
                        style: TextStyle(color: Colors.red[700]),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildNotesSection() {
    if (_recurringInvoice.notes == null || _recurringInvoice.notes!.isEmpty) {
      return const SizedBox.shrink();
    }

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'ملاحظات',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Text(
              _recurringInvoice.notes!,
              style: const TextStyle(fontSize: 16),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildGenerateButton() {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton.icon(
        onPressed: _generateInvoice,
        icon: const Icon(Icons.add_circle),
        label: const Text('إنشاء فاتورة جديدة'),
        style: ElevatedButton.styleFrom(
          backgroundColor: RevolutionaryColors.successGlow,
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(vertical: 16),
        ),
      ),
    );
  }

  Widget _buildInfoRow(
    String label,
    String value, {
    bool isHighlighted = false,
  }) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: 120,
          child: Text(
            label,
            style: const TextStyle(fontWeight: FontWeight.w500),
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: TextStyle(
              color: isHighlighted ? Colors.orange : null,
              fontWeight: isHighlighted ? FontWeight.w500 : null,
            ),
          ),
        ),
      ],
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}
