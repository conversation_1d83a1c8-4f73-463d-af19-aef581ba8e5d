/// شاشة إدارة الضرائب السورية المتقدمة
/// تدير جميع أنواع الضرائب السورية مع التقارير المتخصصة
library;

import 'package:flutter/material.dart';
import '../models/syrian_tax_models.dart';
import '../services/syrian_tax_service.dart';
import '../constants/revolutionary_design_colors.dart';
import '../widgets/loading_widget.dart';
import '../responsive/responsive.dart';

class SyrianTaxManagementScreen extends StatefulWidget {
  const SyrianTaxManagementScreen({super.key});

  @override
  State<SyrianTaxManagementScreen> createState() =>
      _SyrianTaxManagementScreenState();
}

class _SyrianTaxManagementScreenState extends State<SyrianTaxManagementScreen>
    with TickerProviderStateMixin {
  final SyrianTaxService _taxService = SyrianTaxService();

  late TabController _tabController;
  bool _isLoading = true;

  // بيانات الضرائب
  Map<String, dynamic> _taxSummary = {};
  List<TaxCalculation> _recentCalculations = [];
  Map<String, dynamic> _taxSettings = {};

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _loadTaxData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadTaxData() async {
    setState(() => _isLoading = true);
    try {
      // تحميل بيانات الضرائب من الخدمة
      final declarations = await _taxService.getAllTaxDeclarations();

      // حساب ملخص الضرائب
      double totalTaxAmount = 0.0;
      double incomeTax = 0.0;
      double salesTax = 0.0;
      double serviceFee = 0.0;
      int pendingDeclarations = 0;
      int completedDeclarations = 0;

      for (final declaration in declarations) {
        totalTaxAmount += declaration.totalTaxAmount;

        switch (declaration.taxType) {
          case SyrianTaxType.incomeTax:
            incomeTax += declaration.totalTaxAmount;
            break;
          case SyrianTaxType.salesTax:
            salesTax += declaration.totalTaxAmount;
            break;
          case SyrianTaxType.servicesFeeTax:
            serviceFee += declaration.totalTaxAmount;
            break;
          default:
            break;
        }

        switch (declaration.status) {
          case TaxDeclarationStatus.draft:
          case TaxDeclarationStatus.submitted:
          case TaxDeclarationStatus.underReview:
            pendingDeclarations++;
            break;
          case TaxDeclarationStatus.approved:
          case TaxDeclarationStatus.paid:
            completedDeclarations++;
            break;
          default:
            break;
        }
      }

      final summary = {
        'totalTaxAmount': totalTaxAmount,
        'incomeTax': incomeTax,
        'salesTax': salesTax,
        'serviceFee': serviceFee,
        'pendingDeclarations': pendingDeclarations,
        'completedDeclarations': completedDeclarations,
      };

      // إنشاء قائمة فارغة للحسابات الأخيرة (سيتم تطويرها لاحقاً)
      final calculations = <TaxCalculation>[];

      setState(() {
        _taxSummary = summary;
        _recentCalculations = calculations;
        _taxSettings = {
          'enableAutomaticCalculation': true,
          'enableTaxReminders': true,
          'reminderDaysBefore': 7,
          'enabledTaxTypes': ['income_tax', 'sales_tax', 'service_fee'],
        };
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      _showErrorSnackBar('خطأ في تحميل بيانات الضرائب: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const ResponsiveText.h2('إدارة الضرائب السورية'),
        backgroundColor: RevolutionaryColors.damascusSky,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            onPressed: _generateTaxReport,
            icon: const Icon(Icons.assessment),
            tooltip: 'تقرير ضريبي',
          ),
          IconButton(
            onPressed: _exportTaxData,
            icon: const Icon(Icons.file_download),
            tooltip: 'تصدير البيانات',
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          isScrollable: true,
          tabs: const [
            Tab(icon: Icon(Icons.dashboard), text: 'لوحة التحكم'),
            Tab(icon: Icon(Icons.calculate), text: 'حاسبة الضرائب'),
            Tab(icon: Icon(Icons.receipt), text: 'التقارير'),
            Tab(icon: Icon(Icons.settings), text: 'الإعدادات'),
          ],
        ),
      ),
      body: _isLoading
          ? const LoadingWidget()
          : TabBarView(
              controller: _tabController,
              children: [
                _buildDashboardTab(),
                _buildCalculatorTab(),
                _buildReportsTab(),
                _buildSettingsTab(),
              ],
            ),
    );
  }

  Widget _buildDashboardTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // ملخص الضرائب
          const Text(
            'ملخص الضرائب',
            style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),

          Row(
            children: [
              Expanded(
                child: _buildTaxSummaryCard(
                  'ضريبة الدخل',
                  _taxSummary['income_tax']?.toString() ?? '0',
                  'ل.س',
                  Icons.account_balance_wallet,
                  RevolutionaryColors.damascusSky,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildTaxSummaryCard(
                  'ضريبة القيمة المضافة',
                  _taxSummary['vat']?.toString() ?? '0',
                  'ل.س',
                  Icons.receipt,
                  RevolutionaryColors.successGlow,
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          Row(
            children: [
              Expanded(
                child: _buildTaxSummaryCard(
                  'الضرائب المستحقة',
                  _taxSummary['due_taxes']?.toString() ?? '0',
                  'ل.س',
                  Icons.warning,
                  RevolutionaryColors.warningAmber,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildTaxSummaryCard(
                  'الضرائب المدفوعة',
                  _taxSummary['paid_taxes']?.toString() ?? '0',
                  'ل.س',
                  Icons.check_circle,
                  RevolutionaryColors.successGlow,
                ),
              ),
            ],
          ),

          const SizedBox(height: 24),

          // الحسابات الأخيرة
          const Text(
            'الحسابات الضريبية الأخيرة',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),

          if (_recentCalculations.isEmpty)
            const Center(
              child: Text(
                'لا توجد حسابات ضريبية',
                style: TextStyle(color: Colors.grey),
              ),
            )
          else
            ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: _recentCalculations.length,
              itemBuilder: (context, index) {
                final calculation = _recentCalculations[index];
                return _buildCalculationCard(calculation);
              },
            ),
        ],
      ),
    );
  }

  Widget _buildCalculatorTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // حاسبة ضريبة الدخل
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.calculate,
                        color: RevolutionaryColors.damascusSky,
                      ),
                      const SizedBox(width: 8),
                      const Text(
                        'حاسبة ضريبة الدخل',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),

                  TextFormField(
                    decoration: const InputDecoration(
                      labelText: 'الدخل السنوي (ل.س)',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.money),
                    ),
                    keyboardType: TextInputType.number,
                  ),
                  const SizedBox(height: 16),

                  DropdownButtonFormField<String>(
                    decoration: const InputDecoration(
                      labelText: 'فئة الضريبة',
                      border: OutlineInputBorder(),
                    ),
                    items: const [
                      DropdownMenuItem(value: 'individual', child: Text('فرد')),
                      DropdownMenuItem(value: 'company', child: Text('شركة')),
                      DropdownMenuItem(
                        value: 'freelancer',
                        child: Text('مهنة حرة'),
                      ),
                    ],
                    onChanged: (value) {},
                  ),
                  const SizedBox(height: 16),

                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton.icon(
                      onPressed: _calculateIncomeTax,
                      icon: const Icon(Icons.calculate),
                      label: const Text('حساب الضريبة'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: RevolutionaryColors.damascusSky,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.all(16),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: 16),

          // حاسبة ضريبة القيمة المضافة
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.receipt,
                        color: RevolutionaryColors.successGlow,
                      ),
                      const SizedBox(width: 8),
                      const Text(
                        'حاسبة ضريبة القيمة المضافة',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),

                  TextFormField(
                    decoration: const InputDecoration(
                      labelText: 'المبلغ قبل الضريبة (ل.س)',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.money),
                    ),
                    keyboardType: TextInputType.number,
                  ),
                  const SizedBox(height: 16),

                  DropdownButtonFormField<double>(
                    decoration: const InputDecoration(
                      labelText: 'نسبة الضريبة',
                      border: OutlineInputBorder(),
                    ),
                    items: const [
                      DropdownMenuItem(
                        value: 0.0,
                        child: Text('معفى من الضريبة'),
                      ),
                      DropdownMenuItem(value: 5.0, child: Text('5%')),
                      DropdownMenuItem(value: 10.0, child: Text('10%')),
                      DropdownMenuItem(value: 15.0, child: Text('15%')),
                    ],
                    onChanged: (value) {},
                  ),
                  const SizedBox(height: 16),

                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton.icon(
                      onPressed: _calculateVAT,
                      icon: const Icon(Icons.calculate),
                      label: const Text('حساب ضريبة القيمة المضافة'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: RevolutionaryColors.successGlow,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.all(16),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildReportsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'التقارير الضريبية',
            style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),

          // تقارير ضريبة الدخل
          _buildReportCard(
            'تقرير ضريبة الدخل السنوي',
            'تقرير شامل لضريبة الدخل للسنة المالية',
            Icons.account_balance_wallet,
            RevolutionaryColors.damascusSky,
            () => _generateIncomeReport(),
          ),

          // تقارير ضريبة القيمة المضافة
          _buildReportCard(
            'تقرير ضريبة القيمة المضافة',
            'تقرير شهري/ربع سنوي لضريبة القيمة المضافة',
            Icons.receipt,
            RevolutionaryColors.successGlow,
            () => _generateVATReport(),
          ),

          // تقرير الضرائب المستحقة
          _buildReportCard(
            'تقرير الضرائب المستحقة',
            'قائمة بجميع الضرائب المستحقة والمواعيد',
            Icons.warning,
            RevolutionaryColors.warningAmber,
            () => _generateDueTaxesReport(),
          ),

          // تقرير الضرائب المدفوعة
          _buildReportCard(
            'تقرير الضرائب المدفوعة',
            'سجل بجميع الضرائب المدفوعة مع الإيصالات',
            Icons.check_circle,
            RevolutionaryColors.successGlow,
            () => _generatePaidTaxesReport(),
          ),
        ],
      ),
    );
  }

  Widget _buildSettingsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'إعدادات الضرائب',
            style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),

          // تفعيل/إلغاء أنواع الضرائب
          Card(
            child: Column(
              children: [
                SwitchListTile(
                  title: const Text('ضريبة الدخل'),
                  subtitle: const Text('تفعيل حساب ضريبة الدخل'),
                  value: _taxSettings['income_tax'] ?? true,
                  onChanged: (value) => _updateTaxSetting('income_tax', value),
                  activeColor: RevolutionaryColors.damascusSky,
                ),
                const Divider(height: 1),
                SwitchListTile(
                  title: const Text('ضريبة القيمة المضافة'),
                  subtitle: const Text('تفعيل حساب ضريبة القيمة المضافة'),
                  value: _taxSettings['vat'] ?? true,
                  onChanged: (value) => _updateTaxSetting('vat', value),
                  activeColor: RevolutionaryColors.damascusSky,
                ),
                const Divider(height: 1),
                SwitchListTile(
                  title: const Text('ضريبة الأرباح التجارية'),
                  subtitle: const Text('تفعيل حساب ضريبة الأرباح التجارية'),
                  value: _taxSettings['business_profit'] ?? false,
                  onChanged: (value) =>
                      _updateTaxSetting('business_profit', value),
                  activeColor: RevolutionaryColors.damascusSky,
                ),
              ],
            ),
          ),

          const SizedBox(height: 16),

          // إعدادات النسب الضريبية
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'النسب الضريبية',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 16),

                  TextFormField(
                    decoration: const InputDecoration(
                      labelText: 'نسبة ضريبة القيمة المضافة الافتراضية (%)',
                      border: OutlineInputBorder(),
                    ),
                    keyboardType: TextInputType.number,
                    initialValue: '10',
                  ),
                  const SizedBox(height: 16),

                  TextFormField(
                    decoration: const InputDecoration(
                      labelText: 'الحد الأدنى المعفى من ضريبة الدخل (ل.س)',
                      border: OutlineInputBorder(),
                    ),
                    keyboardType: TextInputType.number,
                    initialValue: '500000',
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: 16),

          // إعدادات التقارير
          Card(
            child: Column(
              children: [
                ListTile(
                  leading: const Icon(Icons.schedule),
                  title: const Text('تذكيرات الضرائب'),
                  subtitle: const Text('تفعيل تذكيرات مواعيد الضرائب'),
                  trailing: Switch(
                    value: true,
                    onChanged: (value) {},
                    activeColor: RevolutionaryColors.damascusSky,
                  ),
                ),
                const Divider(height: 1),
                ListTile(
                  leading: const Icon(Icons.email),
                  title: const Text('إرسال التقارير بالبريد'),
                  subtitle: const Text('إرسال التقارير الضريبية تلقائياً'),
                  trailing: Switch(
                    value: false,
                    onChanged: (value) {},
                    activeColor: RevolutionaryColors.damascusSky,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTaxSummaryCard(
    String title,
    String value,
    String unit,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 32),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            unit,
            style: TextStyle(fontSize: 12, color: color.withValues(alpha: 0.8)),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: TextStyle(fontSize: 12, color: color.withValues(alpha: 0.8)),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildCalculationCard(TaxCalculation calculation) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: RevolutionaryColors.damascusSky.withValues(
            alpha: 0.1,
          ),
          child: Icon(Icons.calculate, color: RevolutionaryColors.damascusSky),
        ),
        title: Text(_getTaxTypeName(calculation.taxType)),
        subtitle: Text('${calculation.taxAmount.toStringAsFixed(2)} ل.س'),
        trailing: Text(
          _formatDate(calculation.calculationDate),
          style: const TextStyle(fontSize: 12, color: Colors.grey),
        ),
      ),
    );
  }

  Widget _buildReportCard(
    String title,
    String description,
    IconData icon,
    Color color,
    VoidCallback onTap,
  ) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: color.withValues(alpha: 0.1),
          child: Icon(icon, color: color),
        ),
        title: Text(title),
        subtitle: Text(description),
        trailing: const Icon(Icons.arrow_forward_ios),
        onTap: onTap,
      ),
    );
  }

  Future<void> _calculateIncomeTax() async {
    try {
      // حساب ضريبة الدخل باستخدام الخدمة
      final calculation = await _taxService.calculateIncomeTax(
        grossIncome: 1000000.0, // مثال
        deductions: 100000.0,
        period: DateTime.now().year.toString(),
      );

      _showSuccessSnackBar(
        'تم حساب ضريبة الدخل: ${calculation.taxAmount.toStringAsFixed(2)} ل.س',
      );
      _loadTaxData(); // إعادة تحميل البيانات
    } catch (e) {
      _showErrorSnackBar('خطأ في حساب ضريبة الدخل: $e');
    }
  }

  Future<void> _calculateVAT() async {
    try {
      // حساب ضريبة القيمة المضافة باستخدام الخدمة
      final calculation = await _taxService.calculateSalesTax(
        salesAmount: 500000.0, // مثال
        itemCategory: 'standard',
        period:
            '${DateTime.now().year}-${DateTime.now().month.toString().padLeft(2, '0')}',
      );

      _showSuccessSnackBar(
        'تم حساب ضريبة القيمة المضافة: ${calculation.taxAmount.toStringAsFixed(2)} ل.س',
      );
      _loadTaxData(); // إعادة تحميل البيانات
    } catch (e) {
      _showErrorSnackBar('خطأ في حساب ضريبة القيمة المضافة: $e');
    }
  }

  Future<void> _generateTaxReport() async {
    try {
      // إنشاء تقرير ضريبة الدخل السنوي
      final report = await _taxService.generateAnnualIncomeTaxReport(
        taxpayerId: 'default_taxpayer',
        year: DateTime.now().year,
      );

      _showSuccessSnackBar('تم إنشاء التقرير الضريبي: ${report.title}');
    } catch (e) {
      _showErrorSnackBar('خطأ في إنشاء التقرير الضريبي: $e');
    }
  }

  void _exportTaxData() {
    // تصدير البيانات الضريبية
    _showInfoSnackBar('تصدير البيانات الضريبية قيد التطوير');
  }

  Future<void> _generateIncomeReport() async {
    try {
      final report = await _taxService.generateAnnualIncomeTaxReport(
        taxpayerId: 'default_taxpayer',
        year: DateTime.now().year,
      );
      _showSuccessSnackBar('تم إنشاء تقرير ضريبة الدخل: ${report.title}');
    } catch (e) {
      _showErrorSnackBar('خطأ في إنشاء تقرير ضريبة الدخل: $e');
    }
  }

  Future<void> _generateVATReport() async {
    try {
      final report = await _taxService.generateMonthlySalesTaxReport(
        year: DateTime.now().year,
        month: DateTime.now().month,
      );
      _showSuccessSnackBar(
        'تم إنشاء تقرير ضريبة القيمة المضافة: ${report.title}',
      );
    } catch (e) {
      _showErrorSnackBar('خطأ في إنشاء تقرير ضريبة القيمة المضافة: $e');
    }
  }

  void _generateDueTaxesReport() {
    _showInfoSnackBar('تقرير الضرائب المستحقة قيد التطوير');
  }

  void _generatePaidTaxesReport() {
    _showInfoSnackBar('تقرير الضرائب المدفوعة قيد التطوير');
  }

  void _updateTaxSetting(String key, bool value) {
    setState(() {
      _taxSettings[key] = value;
    });
    // حفظ الإعدادات في قاعدة البيانات
    _showInfoSnackBar('تم حفظ الإعدادات');
  }

  String _getTaxTypeName(SyrianTaxType taxType) {
    switch (taxType) {
      case SyrianTaxType.incomeTax:
        return 'ضريبة الدخل';
      case SyrianTaxType.salesTax:
        return 'ضريبة المبيعات';
      case SyrianTaxType.servicesFeeTax:
        return 'رسم الخدمات';
      case SyrianTaxType.realEstateTax:
        return 'ضريبة العقارات';
      case SyrianTaxType.vehicleTax:
        return 'ضريبة المركبات';
      case SyrianTaxType.professionalTax:
        return 'ضريبة المهن الحرة';
      case SyrianTaxType.stampDuty:
        return 'رسم الطابع';
      case SyrianTaxType.customsDuty:
        return 'الرسوم الجمركية';
      default:
        return 'ضريبة غير محددة';
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message), backgroundColor: Colors.red),
    );
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message), backgroundColor: Colors.green),
    );
  }

  void _showInfoSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: RevolutionaryColors.damascusSky,
      ),
    );
  }
}
