/// شاشة التكامل المحاسبي لنظام الموارد البشرية
/// توفر واجهة لإدارة التكامل بين HR والنظام المحاسبي
library;

import 'package:flutter/material.dart';
import '../models/hr_models.dart';
import '../services/hr_accounting_integration_service.dart';
import '../services/payroll_service.dart';
import '../services/employee_service.dart';
import '../services/logging_service.dart';
import '../widgets/loading_widget.dart';
import '../widgets/custom_error_widget.dart' as custom_widgets;
import '../constants/revolutionary_design_colors.dart';

class HRAccountingIntegrationScreen extends StatefulWidget {
  const HRAccountingIntegrationScreen({super.key});

  @override
  State<HRAccountingIntegrationScreen> createState() =>
      _HRAccountingIntegrationScreenState();
}

class _HRAccountingIntegrationScreenState
    extends State<HRAccountingIntegrationScreen>
    with TickerProviderStateMixin {
  final HRAccountingIntegrationService _integrationService =
      HRAccountingIntegrationService();
  final PayrollService _payrollService = PayrollService();
  final EmployeeService _employeeService = EmployeeService();

  late TabController _tabController;
  List<PayrollRecord> _payrollRecords = [];
  List<Employee> _employees = [];
  Map<String, dynamic> _accountingReport = {};

  bool _isLoading = true;
  String? _error;
  DateTime _selectedFromDate = DateTime(
    DateTime.now().year,
    DateTime.now().month,
    1,
  );
  DateTime _selectedToDate = DateTime.now();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _loadData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      // تحميل الموظفين أولاً
      final employees = await _employeeService.getAllEmployees();

      // تحميل كشوف الرواتب لكل موظف
      final payrollRecords = <PayrollRecord>[];
      for (final employee in employees) {
        final record = await _payrollService.getPayrollRecord(
          employee.id!,
          _selectedFromDate.month,
          _selectedFromDate.year,
        );
        if (record != null) {
          payrollRecords.add(record);
        }
      }

      // تحميل التقرير المحاسبي
      final accountingReport = await _integrationService.getHRAccountingReport(
        fromDate: _selectedFromDate,
        toDate: _selectedToDate,
      );

      setState(() {
        _payrollRecords = payrollRecords;
        _employees = employees;
        _accountingReport = accountingReport;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });

      LoggingService.error(
        'خطأ في تحميل بيانات التكامل المحاسبي',
        category: 'HRAccountingIntegrationScreen',
        data: {'error': e.toString()},
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('التكامل المحاسبي - الموارد البشرية'),
        backgroundColor: RevolutionaryColors.damascusSky,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadData,
            tooltip: 'تحديث',
          ),
          PopupMenuButton<String>(
            icon: const Icon(Icons.more_vert),
            onSelected: (value) {
              switch (value) {
                case 'create_accounts':
                  _createHRAccounts();
                  break;
                case 'export_report':
                  _exportAccountingReport();
                  break;
                case 'settings':
                  _showIntegrationSettings();
                  break;
              }
            },
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'create_accounts',
                child: Text('إنشاء الحسابات المحاسبية'),
              ),
              const PopupMenuItem(
                value: 'export_report',
                child: Text('تصدير التقرير'),
              ),
              const PopupMenuItem(
                value: 'settings',
                child: Text('إعدادات التكامل'),
              ),
            ],
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          indicatorColor: Colors.white,
          tabs: const [
            Tab(icon: Icon(Icons.dashboard), text: 'نظرة عامة'),
            Tab(icon: Icon(Icons.account_balance), text: 'القيود المحاسبية'),
            Tab(icon: Icon(Icons.assessment), text: 'التقارير'),
            Tab(icon: Icon(Icons.settings), text: 'الإعدادات'),
          ],
        ),
      ),
      body: _buildBody(),
      floatingActionButton: _buildFloatingActionButton(),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const LoadingWidget();
    }

    if (_error != null) {
      return custom_widgets.ErrorWidget(message: _error!, onRetry: _loadData);
    }

    return TabBarView(
      controller: _tabController,
      children: [
        _buildOverviewTab(),
        _buildJournalEntriesTab(),
        _buildReportsTab(),
        _buildSettingsTab(),
      ],
    );
  }

  Widget _buildOverviewTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildDateRangeSelector(),
          const SizedBox(height: 24),
          _buildSummaryCards(),
          const SizedBox(height: 24),
          _buildQuickActions(),
          const SizedBox(height: 24),
          _buildRecentPayrollRecords(),
        ],
      ),
    );
  }

  Widget _buildDateRangeSelector() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'فترة التقرير',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: InkWell(
                    onTap: () => _selectDate(true),
                    child: Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.grey),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'من تاريخ',
                            style: TextStyle(fontSize: 12),
                          ),
                          Text(
                            '${_selectedFromDate.day}/${_selectedFromDate.month}/${_selectedFromDate.year}',
                            style: const TextStyle(fontWeight: FontWeight.bold),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: InkWell(
                    onTap: () => _selectDate(false),
                    child: Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.grey),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'إلى تاريخ',
                            style: TextStyle(fontSize: 12),
                          ),
                          Text(
                            '${_selectedToDate.day}/${_selectedToDate.month}/${_selectedToDate.year}',
                            style: const TextStyle(fontWeight: FontWeight.bold),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                ElevatedButton(
                  onPressed: _loadData,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: RevolutionaryColors.successGlow,
                    foregroundColor: Colors.white,
                  ),
                  child: const Text('تحديث'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryCards() {
    final salaryExpenses =
        _accountingReport['salaryExpenses'] as List<dynamic>? ?? [];
    final liabilities =
        _accountingReport['liabilities'] as List<dynamic>? ?? [];
    final totalLoans = _accountingReport['totalLoans'] as double? ?? 0.0;

    double totalSalaryExpenses = 0;
    for (final expense in salaryExpenses) {
      totalSalaryExpenses += (expense['total_debit'] as double? ?? 0);
    }

    double totalLiabilities = 0;
    for (final liability in liabilities) {
      totalLiabilities += (liability['balance'] as double? ?? 0);
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'ملخص التكامل المحاسبي',
          style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 12),
        GridView.count(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisCount: 2,
          crossAxisSpacing: 12,
          mainAxisSpacing: 12,
          childAspectRatio: 1.5,
          children: [
            _buildSummaryCard(
              'مصاريف الرواتب',
              '${totalSalaryExpenses.toStringAsFixed(0)} ل.س',
              Icons.money_off,
              RevolutionaryColors.errorCoral,
            ),
            _buildSummaryCard(
              'الالتزامات المستحقة',
              '${totalLiabilities.toStringAsFixed(0)} ل.س',
              Icons.account_balance_wallet,
              RevolutionaryColors.warningAmber,
            ),
            _buildSummaryCard(
              'قروض الموظفين',
              '${totalLoans.toStringAsFixed(0)} ل.س',
              Icons.credit_card,
              RevolutionaryColors.infoTurquoise,
            ),
            _buildSummaryCard(
              'عدد كشوف الرواتب',
              '${_payrollRecords.length}',
              Icons.receipt,
              RevolutionaryColors.successGlow,
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildSummaryCard(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, size: 32, color: color),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: color,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: const TextStyle(fontSize: 12),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildQuickActions() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'الإجراءات السريعة',
          style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 12),
        Wrap(
          spacing: 12,
          runSpacing: 12,
          children: [
            _buildActionCard(
              'إنشاء قيد رواتب',
              Icons.add_circle,
              RevolutionaryColors.successGlow,
              _createPayrollJournalEntry,
            ),
            _buildActionCard(
              'إنشاء قيد قرض',
              Icons.credit_card,
              RevolutionaryColors.infoTurquoise,
              _showCreateLoanDialog,
            ),
            _buildActionCard(
              'إنشاء قيد مكافأة',
              Icons.star,
              RevolutionaryColors.warningAmber,
              _showCreateBonusDialog,
            ),
            _buildActionCard(
              'تقرير محاسبي',
              Icons.assessment,
              RevolutionaryColors.damascusSky,
              _generateAccountingReport,
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildActionCard(
    String title,
    IconData icon,
    Color color,
    VoidCallback onTap,
  ) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        width: (MediaQuery.of(context).size.width - 56) / 2,
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: color.withValues(alpha: 0.3)),
        ),
        child: Row(
          children: [
            Icon(icon, color: color, size: 24),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                title,
                style: TextStyle(fontWeight: FontWeight.w600, color: color),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRecentPayrollRecords() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'كشوف الرواتب الحديثة',
          style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 12),
        if (_payrollRecords.isEmpty)
          const Center(
            child: Padding(
              padding: EdgeInsets.all(32),
              child: Text(
                'لا توجد كشوف رواتب للفترة المحددة',
                style: TextStyle(color: Colors.grey),
              ),
            ),
          )
        else
          ...(_payrollRecords.take(5).map((record) {
            final employee = _employees.firstWhere(
              (emp) => emp.id == record.employeeId,
              orElse: () => Employee(
                employeeNumber: 'غير معروف',
                nationalId: '',
                firstName: 'غير معروف',
                lastName: '',
                fullName: 'غير معروف',
                email: '',
                phone: '',
                hireDate: DateTime.now(),
                basicSalary: 0,
                createdAt: DateTime.now(),
                updatedAt: DateTime.now(),
              ),
            );

            return Card(
              margin: const EdgeInsets.only(bottom: 8),
              child: ListTile(
                leading: CircleAvatar(
                  backgroundColor: RevolutionaryColors.damascusSky.withValues(
                    alpha: 0.1,
                  ),
                  child: Text(
                    employee.firstName.isNotEmpty ? employee.firstName[0] : '؟',
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      color: RevolutionaryColors.damascusSky,
                    ),
                  ),
                ),
                title: Text('${employee.firstName} ${employee.lastName}'),
                subtitle: Text('${record.month}/${record.year}'),
                trailing: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      '${record.netSalary.toStringAsFixed(0)} ل.س',
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        color: RevolutionaryColors.successGlow,
                      ),
                    ),
                    Text(
                      record.status,
                      style: TextStyle(
                        fontSize: 12,
                        color: _getStatusColor(record.status),
                      ),
                    ),
                  ],
                ),
                onTap: () => _showPayrollDetails(record, employee),
              ),
            );
          })),
      ],
    );
  }

  Widget _buildJournalEntriesTab() {
    return const Center(child: Text('تبويب القيود المحاسبية - قيد التطوير'));
  }

  Widget _buildReportsTab() {
    return const Center(child: Text('تبويب التقارير - قيد التطوير'));
  }

  Widget _buildSettingsTab() {
    return const Center(child: Text('تبويب الإعدادات - قيد التطوير'));
  }

  Widget? _buildFloatingActionButton() {
    switch (_tabController.index) {
      case 1: // القيود المحاسبية
        return FloatingActionButton(
          onPressed: _createPayrollJournalEntry,
          backgroundColor: RevolutionaryColors.successGlow,
          child: const Icon(Icons.add, color: Colors.white),
        );
      default:
        return null;
    }
  }

  Color _getStatusColor(String status) {
    switch (status) {
      case 'paid':
        return RevolutionaryColors.successGlow;
      case 'calculated':
        return RevolutionaryColors.warningAmber;
      case 'draft':
        return RevolutionaryColors.infoTurquoise;
      default:
        return Colors.grey;
    }
  }

  // دوال الأحداث
  Future<void> _selectDate(bool isFromDate) async {
    final date = await showDatePicker(
      context: context,
      initialDate: isFromDate ? _selectedFromDate : _selectedToDate,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
    );

    if (date != null) {
      setState(() {
        if (isFromDate) {
          _selectedFromDate = date;
        } else {
          _selectedToDate = date;
        }
      });
    }
  }

  void _createHRAccounts() async {
    try {
      await _integrationService.createHRAccounts();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم إنشاء الحسابات المحاسبية بنجاح'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في إنشاء الحسابات: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _createPayrollJournalEntry() {
    if (_payrollRecords.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('لا توجد كشوف رواتب لإنشاء قيد محاسبي'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إنشاء قيد محاسبي للرواتب'),
        content: Text(
          'هل تريد إنشاء قيد محاسبي لـ ${_payrollRecords.length} كشف راتب؟',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.of(context).pop();
              await _processPayrollJournalEntry();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: RevolutionaryColors.successGlow,
              foregroundColor: Colors.white,
            ),
            child: const Text('إنشاء القيد'),
          ),
        ],
      ),
    );
  }

  Future<void> _processPayrollJournalEntry() async {
    try {
      final entryId = await _integrationService.createPayrollJournalEntry(
        payrollRecords: _payrollRecords,
        payDate: _selectedToDate,
        description:
            'قيد الرواتب للفترة ${_selectedFromDate.month}/${_selectedFromDate.year}',
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('تم إنشاء القيد المحاسبي رقم: $entryId'),
            backgroundColor: Colors.green,
          ),
        );
        _loadData();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في إنشاء القيد: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _showCreateLoanDialog() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('حوار إنشاء قيد قرض - قيد التطوير'),
        backgroundColor: Colors.orange,
      ),
    );
  }

  void _showCreateBonusDialog() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('حوار إنشاء قيد مكافأة - قيد التطوير'),
        backgroundColor: Colors.orange,
      ),
    );
  }

  void _generateAccountingReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تم تحديث التقرير المحاسبي'),
        backgroundColor: Colors.blue,
      ),
    );
    _loadData();
  }

  void _exportAccountingReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تصدير التقرير المحاسبي - قيد التطوير'),
        backgroundColor: Colors.orange,
      ),
    );
  }

  void _showIntegrationSettings() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('إعدادات التكامل - قيد التطوير'),
        backgroundColor: Colors.orange,
      ),
    );
  }

  void _showPayrollDetails(PayrollRecord record, Employee employee) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('كشف راتب ${employee.firstName} ${employee.lastName}'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildDetailRow(
                'الراتب الأساسي',
                '${record.basicSalary.toStringAsFixed(0)} ل.س',
              ),
              _buildDetailRow(
                'البدلات',
                '${record.allowances.toStringAsFixed(0)} ل.س',
              ),
              _buildDetailRow(
                'المكافآت',
                '${record.bonuses.toStringAsFixed(0)} ل.س',
              ),
              _buildDetailRow(
                'الساعات الإضافية',
                '${record.overtimeAmount.toStringAsFixed(0)} ل.س',
              ),
              const Divider(),
              _buildDetailRow(
                'الراتب الإجمالي',
                '${record.grossSalary.toStringAsFixed(0)} ل.س',
              ),
              const Divider(),
              _buildDetailRow(
                'ضريبة الدخل',
                '${record.incomeTax.toStringAsFixed(0)} ل.س',
              ),
              _buildDetailRow(
                'الضمان الاجتماعي',
                '${record.socialInsurance.toStringAsFixed(0)} ل.س',
              ),
              _buildDetailRow(
                'خصم القروض',
                '${record.loanDeductions.toStringAsFixed(0)} ل.س',
              ),
              _buildDetailRow(
                'خصومات أخرى',
                '${record.otherDeductions.toStringAsFixed(0)} ل.س',
              ),
              const Divider(),
              _buildDetailRow(
                'إجمالي الخصومات',
                '${record.totalDeductions.toStringAsFixed(0)} ل.س',
              ),
              const Divider(),
              _buildDetailRow(
                'صافي الراتب',
                '${record.netSalary.toStringAsFixed(0)} ل.س',
                isHighlight: true,
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(
    String label,
    String value, {
    bool isHighlight = false,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontWeight: isHighlight ? FontWeight.bold : FontWeight.normal,
              color: isHighlight ? RevolutionaryColors.successGlow : null,
            ),
          ),
          Text(
            value,
            style: TextStyle(
              fontWeight: FontWeight.bold,
              color: isHighlight ? RevolutionaryColors.successGlow : null,
            ),
          ),
        ],
      ),
    );
  }
}
