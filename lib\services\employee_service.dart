/// خدمة إدارة الموظفين
/// توفر عمليات CRUD للموظفين مع التكامل مع المحاسبة
library;

import '../database/database_helper.dart';
import '../models/hr_models.dart';
import '../constants/app_constants.dart';
import '../services/logging_service.dart';
import '../services/audit_service.dart';
import '../exceptions/validation_exception.dart';

class EmployeeService {
  final DatabaseHelper _databaseHelper = DatabaseHelper();

  /// الحصول على جميع الموظفين
  Future<List<Employee>> getAllEmployees({
    bool activeOnly = false,
    int? departmentId,
    String? searchQuery,
  }) async {
    try {
      final db = await _databaseHelper.database;

      String whereClause = '1=1';
      List<dynamic> whereArgs = [];

      if (activeOnly) {
        whereClause += ' AND status = ?';
        whereArgs.add(AppConstants.employeeStatusActive);
      }

      if (departmentId != null) {
        whereClause += ' AND department_id = ?';
        whereArgs.add(departmentId);
      }

      if (searchQuery != null && searchQuery.isNotEmpty) {
        whereClause +=
            ' AND (full_name LIKE ? OR employee_number LIKE ? OR national_id LIKE ?)';
        final searchPattern = '%$searchQuery%';
        whereArgs.addAll([searchPattern, searchPattern, searchPattern]);
      }

      final result = await db.query(
        AppConstants.employeesTable,
        where: whereClause,
        whereArgs: whereArgs.isNotEmpty ? whereArgs : null,
        orderBy: 'full_name ASC',
      );

      return result.map((map) => Employee.fromMap(map)).toList();
    } catch (e) {
      LoggingService.error(
        'خطأ في الحصول على قائمة الموظفين',
        category: 'EmployeeService',
        data: {'error': e.toString()},
      );
      rethrow;
    }
  }

  /// الحصول على موظف بالمعرف
  Future<Employee?> getEmployeeById(int id) async {
    try {
      final db = await _databaseHelper.database;

      final result = await db.query(
        AppConstants.employeesTable,
        where: 'id = ?',
        whereArgs: [id],
        limit: 1,
      );

      if (result.isEmpty) return null;
      return Employee.fromMap(result.first);
    } catch (e) {
      LoggingService.error(
        'خطأ في الحصول على الموظف',
        category: 'EmployeeService',
        data: {'id': id, 'error': e.toString()},
      );
      rethrow;
    }
  }

  /// الحصول على موظف برقم الموظف
  Future<Employee?> getEmployeeByNumber(String employeeNumber) async {
    try {
      final db = await _databaseHelper.database;

      final result = await db.query(
        AppConstants.employeesTable,
        where: 'employee_number = ?',
        whereArgs: [employeeNumber],
        limit: 1,
      );

      if (result.isEmpty) return null;
      return Employee.fromMap(result.first);
    } catch (e) {
      LoggingService.error(
        'خطأ في الحصول على الموظف برقم الموظف',
        category: 'EmployeeService',
        data: {'employeeNumber': employeeNumber, 'error': e.toString()},
      );
      rethrow;
    }
  }

  /// إضافة موظف جديد
  Future<Employee> addEmployee(Employee employee) async {
    try {
      // التحقق من صحة البيانات
      await _validateEmployee(employee);

      final db = await _databaseHelper.database;

      // التحقق من عدم تكرار رقم الموظف
      final existingByNumber = await getEmployeeByNumber(
        employee.employeeNumber,
      );
      if (existingByNumber != null) {
        throw ValidationException('رقم الموظف موجود مسبقاً');
      }

      // التحقق من عدم تكرار الرقم الوطني
      final existingByNationalId = await db.query(
        AppConstants.employeesTable,
        where: 'national_id = ?',
        whereArgs: [employee.nationalId],
        limit: 1,
      );

      if (existingByNationalId.isNotEmpty) {
        throw ValidationException('الرقم الوطني موجود مسبقاً');
      }

      final now = DateTime.now();
      final employeeData = employee.copyWith(createdAt: now, updatedAt: now);

      final id = await db.insert(
        AppConstants.employeesTable,
        employeeData.toMap(),
      );

      final newEmployee = employeeData.copyWith(id: id);

      // تسجيل في سجل المراجعة
      await AuditService.log(
        action: 'CREATE',
        entityType: 'Employee',
        entityId: id,
        description: 'إضافة موظف جديد: ${employee.fullName}',
        newValues: newEmployee.toMap(),
      );

      LoggingService.info(
        'تم إضافة موظف جديد بنجاح',
        category: 'EmployeeService',
        data: {'id': id, 'name': employee.fullName},
      );

      return newEmployee;
    } catch (e) {
      LoggingService.error(
        'خطأ في إضافة الموظف',
        category: 'EmployeeService',
        data: {'employee': employee.toMap(), 'error': e.toString()},
      );
      rethrow;
    }
  }

  /// تحديث بيانات موظف
  Future<Employee> updateEmployee(Employee employee) async {
    try {
      if (employee.id == null) {
        throw ValidationException('معرف الموظف مطلوب للتحديث');
      }

      // التحقق من صحة البيانات
      await _validateEmployee(employee);

      final db = await _databaseHelper.database;

      // الحصول على البيانات القديمة
      final oldEmployee = await getEmployeeById(employee.id!);
      if (oldEmployee == null) {
        throw ValidationException('الموظف غير موجود');
      }

      // التحقق من عدم تكرار رقم الموظف (إذا تم تغييره)
      if (employee.employeeNumber != oldEmployee.employeeNumber) {
        final existingByNumber = await getEmployeeByNumber(
          employee.employeeNumber,
        );
        if (existingByNumber != null && existingByNumber.id != employee.id) {
          throw ValidationException('رقم الموظف موجود مسبقاً');
        }
      }

      // التحقق من عدم تكرار الرقم الوطني (إذا تم تغييره)
      if (employee.nationalId != oldEmployee.nationalId) {
        final existingByNationalId = await db.query(
          AppConstants.employeesTable,
          where: 'national_id = ? AND id != ?',
          whereArgs: [employee.nationalId, employee.id],
          limit: 1,
        );

        if (existingByNationalId.isNotEmpty) {
          throw ValidationException('الرقم الوطني موجود مسبقاً');
        }
      }

      final updatedEmployee = employee.copyWith(updatedAt: DateTime.now());

      await db.update(
        AppConstants.employeesTable,
        updatedEmployee.toMap(),
        where: 'id = ?',
        whereArgs: [employee.id],
      );

      // تسجيل في سجل المراجعة
      await AuditService.log(
        action: 'UPDATE',
        entityType: 'Employee',
        entityId: employee.id,
        description: 'تحديث بيانات الموظف: ${employee.fullName}',
        oldValues: oldEmployee.toMap(),
        newValues: updatedEmployee.toMap(),
      );

      LoggingService.info(
        'تم تحديث بيانات الموظف بنجاح',
        category: 'EmployeeService',
        data: {'id': employee.id, 'name': employee.fullName},
      );

      return updatedEmployee;
    } catch (e) {
      LoggingService.error(
        'خطأ في تحديث الموظف',
        category: 'EmployeeService',
        data: {'employee': employee.toMap(), 'error': e.toString()},
      );
      rethrow;
    }
  }

  /// حذف موظف
  Future<void> deleteEmployee(int id) async {
    try {
      final db = await _databaseHelper.database;

      // الحصول على بيانات الموظف قبل الحذف
      final employee = await getEmployeeById(id);
      if (employee == null) {
        throw ValidationException('الموظف غير موجود');
      }

      // التحقق من عدم وجود بيانات مرتبطة
      await _checkEmployeeDependencies(id);

      await db.delete(
        AppConstants.employeesTable,
        where: 'id = ?',
        whereArgs: [id],
      );

      // تسجيل في سجل المراجعة
      await AuditService.log(
        action: 'DELETE',
        entityType: 'Employee',
        entityId: id,
        description: 'حذف الموظف: ${employee.fullName}',
        oldValues: employee.toMap(),
      );

      LoggingService.info(
        'تم حذف الموظف بنجاح',
        category: 'EmployeeService',
        data: {'id': id, 'name': employee.fullName},
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في حذف الموظف',
        category: 'EmployeeService',
        data: {'id': id, 'error': e.toString()},
      );
      rethrow;
    }
  }

  /// التحقق من صحة بيانات الموظف
  Future<void> _validateEmployee(Employee employee) async {
    if (employee.employeeNumber.trim().isEmpty) {
      throw ValidationException('رقم الموظف مطلوب');
    }

    if (employee.nationalId.trim().isEmpty) {
      throw ValidationException('الرقم الوطني مطلوب');
    }

    if (employee.firstName.trim().isEmpty) {
      throw ValidationException('الاسم الأول مطلوب');
    }

    if (employee.lastName.trim().isEmpty) {
      throw ValidationException('الاسم الأخير مطلوب');
    }

    if (employee.basicSalary < 0) {
      throw ValidationException('الراتب الأساسي لا يمكن أن يكون سالباً');
    }

    // التحقق من صحة الرقم الوطني السوري (11 رقم)
    if (employee.nationalId.length != 11 ||
        !RegExp(r'^\d{11}$').hasMatch(employee.nationalId)) {
      throw ValidationException('الرقم الوطني يجب أن يكون 11 رقماً');
    }

    // التحقق من صحة رقم الهاتف إذا تم إدخاله
    if (employee.phone != null && employee.phone!.isNotEmpty) {
      if (!RegExp(r'^[+]?[0-9\s\-\(\)]+$').hasMatch(employee.phone!)) {
        throw ValidationException('رقم الهاتف غير صحيح');
      }
    }

    // التحقق من صحة البريد الإلكتروني إذا تم إدخاله
    if (employee.email != null && employee.email!.isNotEmpty) {
      if (!RegExp(
        r'^[\w\-\.]+@([\w\-]+\.)+[\w\-]{2,4}$',
      ).hasMatch(employee.email!)) {
        throw ValidationException('البريد الإلكتروني غير صحيح');
      }
    }
  }

  /// التحقق من وجود بيانات مرتبطة بالموظف
  Future<void> _checkEmployeeDependencies(int employeeId) async {
    final db = await _databaseHelper.database;

    // التحقق من وجود سجلات حضور
    final attendanceCount = await db.rawQuery(
      'SELECT COUNT(*) as count FROM ${AppConstants.attendanceTable} WHERE employee_id = ?',
      [employeeId],
    );

    if ((attendanceCount.first['count'] as int) > 0) {
      throw ValidationException(
        'لا يمكن حذف الموظف لوجود سجلات حضور مرتبطة به',
      );
    }

    // التحقق من وجود قروض
    final loansCount = await db.rawQuery(
      'SELECT COUNT(*) as count FROM ${AppConstants.loansTable} WHERE employee_id = ?',
      [employeeId],
    );

    if ((loansCount.first['count'] as int) > 0) {
      throw ValidationException('لا يمكن حذف الموظف لوجود قروض مرتبطة به');
    }

    // التحقق من وجود كشوف رواتب
    final payrollCount = await db.rawQuery(
      'SELECT COUNT(*) as count FROM ${AppConstants.payrollDetailsTable} WHERE employee_id = ?',
      [employeeId],
    );

    if ((payrollCount.first['count'] as int) > 0) {
      throw ValidationException(
        'لا يمكن حذف الموظف لوجود كشوف رواتب مرتبطة به',
      );
    }
  }

  /// توليد رقم موظف تلقائي
  Future<String> generateEmployeeNumber() async {
    try {
      final db = await _databaseHelper.database;

      final result = await db.rawQuery(
        'SELECT MAX(CAST(SUBSTR(employee_number, 4) AS INTEGER)) as max_num FROM ${AppConstants.employeesTable} WHERE employee_number LIKE "EMP%"',
      );

      int nextNumber = 1;
      if (result.isNotEmpty && result.first['max_num'] != null) {
        nextNumber = (result.first['max_num'] as int) + 1;
      }

      return 'EMP${nextNumber.toString().padLeft(4, '0')}';
    } catch (e) {
      LoggingService.error(
        'خطأ في توليد رقم الموظف',
        category: 'EmployeeService',
        data: {'error': e.toString()},
      );
      // في حالة الخطأ، إرجاع رقم افتراضي
      return 'EMP${DateTime.now().millisecondsSinceEpoch.toString().substring(8)}';
    }
  }
}
