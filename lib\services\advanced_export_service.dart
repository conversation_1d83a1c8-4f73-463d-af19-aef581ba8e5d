import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:path_provider/path_provider.dart';
import 'package:excel/excel.dart';
import 'package:intl/intl.dart';
import '../constants/app_constants.dart';
import '../constants/revolutionary_design_colors.dart';
import '../services/logging_service.dart';
import '../services/audit_service.dart';

/// خدمة التصدير المتقدمة للتقارير والرسوم البيانية
class AdvancedExportService {
  static const String _companyName = 'دفتر الأستاذ الذكي';

  /// خيارات تخصيص التصدير
  static const Map<String, ExportCustomization> _defaultCustomizations = {
    'trial_balance': ExportCustomization(
      title: 'ميزان المراجعة',
      headerColor: RevolutionaryColors.damascusSky,
      includeCharts: true,
      includeStatistics: true,
    ),
    'profit_loss': ExportCustomization(
      title: 'قائمة الدخل',
      headerColor: RevolutionaryColors.successGlow,
      includeCharts: true,
      includeStatistics: true,
    ),
    'balance_sheet': ExportCustomization(
      title: 'الميزانية العمومية',
      headerColor: RevolutionaryColors.infoTurquoise,
      includeCharts: true,
      includeStatistics: true,
    ),
    'inventory_report': ExportCustomization(
      title: 'تقرير المخزون',
      headerColor: RevolutionaryColors.warningAmber,
      includeCharts: true,
      includeStatistics: true,
    ),
  };

  /// تصدير تقرير متقدم إلى PDF مع تنسيق احترافي
  static Future<String?> exportAdvancedPDF({
    required String reportType,
    required dynamic reportData,
    required Map<String, dynamic> filters,
    Uint8List? chartImage,
    ExportCustomization? customization,
    bool includeWatermark = false,
  }) async {
    try {
      LoggingService.info(
        'بدء تصدير PDF متقدم',
        category: 'AdvancedExport',
        data: {'reportType': reportType},
      );

      final custom =
          customization ??
          _defaultCustomizations[reportType] ??
          const ExportCustomization(title: 'تقرير');

      final pdf = pw.Document();

      // إضافة الصفحات
      await _addPDFPages(
        pdf,
        reportType,
        reportData,
        filters,
        chartImage,
        custom,
        includeWatermark,
      );

      // حفظ الملف
      final filePath = await _savePDFFile(pdf, reportType, custom.title);

      if (filePath != null) {
        await _logExportOperation('PDF', reportType, filePath);
      }

      return filePath;
    } catch (e) {
      LoggingService.error(
        'خطأ في تصدير PDF متقدم',
        category: 'AdvancedExport',
        data: {'error': e.toString()},
      );
      return null;
    }
  }

  /// تصدير تقرير متقدم إلى Excel مع جداول منسقة
  static Future<String?> exportAdvancedExcel({
    required String reportType,
    required dynamic reportData,
    required Map<String, dynamic> filters,
    ExportCustomization? customization,
    bool includeCharts = true,
  }) async {
    try {
      LoggingService.info(
        'بدء تصدير Excel متقدم',
        category: 'AdvancedExport',
        data: {'reportType': reportType},
      );

      final custom =
          customization ??
          _defaultCustomizations[reportType] ??
          const ExportCustomization(title: 'تقرير');

      final excel = Excel.createExcel();

      // إنشاء الأوراق المطلوبة
      await _createExcelSheets(excel, reportType, reportData, filters, custom);

      // حفظ الملف
      final filePath = await _saveExcelFile(excel, reportType, custom.title);

      if (filePath != null) {
        await _logExportOperation('Excel', reportType, filePath);
      }

      return filePath;
    } catch (e) {
      LoggingService.error(
        'خطأ في تصدير Excel متقدم',
        category: 'AdvancedExport',
        data: {'error': e.toString()},
      );
      return null;
    }
  }

  /// إضافة صفحات PDF
  static Future<void> _addPDFPages(
    pw.Document pdf,
    String reportType,
    dynamic reportData,
    Map<String, dynamic> filters,
    Uint8List? chartImage,
    ExportCustomization customization,
    bool includeWatermark,
  ) async {
    pdf.addPage(
      pw.MultiPage(
        pageFormat: PdfPageFormat.a4,
        margin: const pw.EdgeInsets.all(32),
        header: (context) => _buildPDFHeader(customization),
        footer: (context) => _buildPDFFooter(context),
        build: (context) => [
          // معلومات التقرير
          _buildReportInfo(reportType, filters, customization),
          pw.SizedBox(height: 20),

          // الرسم البياني إذا كان متوفراً
          if (chartImage != null && customization.includeCharts) ...[
            _buildChartSection(chartImage),
            pw.SizedBox(height: 20),
          ],

          // بيانات التقرير
          _buildReportData(reportType, reportData),

          // الإحصائيات إذا كانت مطلوبة
          if (customization.includeStatistics) ...[
            pw.SizedBox(height: 20),
            _buildStatisticsSection(reportType, reportData),
          ],
        ],
      ),
    );
  }

  /// بناء رأس PDF
  static pw.Widget _buildPDFHeader(ExportCustomization customization) {
    return pw.Container(
      padding: const pw.EdgeInsets.only(bottom: 20),
      decoration: pw.BoxDecoration(
        border: pw.Border(
          bottom: pw.BorderSide(
            color: PdfColor.fromHex(
              '#${customization.headerColor.toARGB32().toRadixString(16).substring(2)}',
            ),
            width: 2,
          ),
        ),
      ),
      child: pw.Row(
        mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
        children: [
          pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              pw.Text(
                _companyName,
                style: pw.TextStyle(
                  fontSize: 20,
                  fontWeight: pw.FontWeight.bold,
                  color: PdfColor.fromHex(
                    '#${customization.headerColor.toARGB32().toRadixString(16).substring(2)}',
                  ),
                ),
              ),
              pw.SizedBox(height: 5),
              pw.Text(customization.title, style: pw.TextStyle(fontSize: 16)),
            ],
          ),
          pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.end,
            children: [
              pw.Text(
                'تاريخ التقرير: ${DateFormat('yyyy/MM/dd').format(DateTime.now())}',
                style: pw.TextStyle(fontSize: 12),
              ),
              pw.Text(
                'وقت الإنشاء: ${DateFormat('HH:mm').format(DateTime.now())}',
                style: pw.TextStyle(fontSize: 12),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// بناء تذييل PDF
  static pw.Widget _buildPDFFooter(pw.Context context) {
    return pw.Container(
      padding: const pw.EdgeInsets.only(top: 20),
      decoration: const pw.BoxDecoration(
        border: pw.Border(
          top: pw.BorderSide(color: PdfColors.grey300, width: 1),
        ),
      ),
      child: pw.Row(
        mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
        children: [
          pw.Text(
            'تم إنشاؤه بواسطة $_companyName',
            style: pw.TextStyle(fontSize: 10, color: PdfColors.grey600),
          ),
          pw.Text(
            'صفحة ${context.pageNumber} من ${context.pagesCount}',
            style: pw.TextStyle(fontSize: 10, color: PdfColors.grey600),
          ),
        ],
      ),
    );
  }

  /// بناء معلومات التقرير
  static pw.Widget _buildReportInfo(
    String reportType,
    Map<String, dynamic> filters,
    ExportCustomization customization,
  ) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(16),
      decoration: pw.BoxDecoration(
        color: PdfColor.fromHex(
          '#${customization.headerColor.toARGB32().toRadixString(16).substring(2)}',
        ).shade(0.1),
        borderRadius: pw.BorderRadius.circular(8),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Text(
            'معلومات التقرير',
            style: pw.TextStyle(fontSize: 14, fontWeight: pw.FontWeight.bold),
          ),
          pw.SizedBox(height: 10),
          ...filters.entries.map(
            (entry) => pw.Padding(
              padding: const pw.EdgeInsets.only(bottom: 4),
              child: pw.Text(
                '${entry.key}: ${entry.value}',
                style: pw.TextStyle(fontSize: 12),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// بناء قسم الرسم البياني
  static pw.Widget _buildChartSection(Uint8List chartImage) {
    return pw.Container(
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Text(
            'الرسم البياني',
            style: pw.TextStyle(fontSize: 14, fontWeight: pw.FontWeight.bold),
          ),
          pw.SizedBox(height: 10),
          pw.Center(
            child: pw.Image(
              pw.MemoryImage(chartImage),
              height: 300,
              fit: pw.BoxFit.contain,
            ),
          ),
        ],
      ),
    );
  }

  /// بناء بيانات التقرير
  static pw.Widget _buildReportData(String reportType, dynamic reportData) {
    // سيتم تنفيذ هذا حسب نوع التقرير
    return pw.Container(
      child: pw.Text('بيانات التقرير - سيتم تنفيذها حسب النوع'),
    );
  }

  /// بناء قسم الإحصائيات
  static pw.Widget _buildStatisticsSection(
    String reportType,
    dynamic reportData,
  ) {
    return pw.Container(child: pw.Text('الإحصائيات - سيتم تنفيذها حسب النوع'));
  }

  /// إنشاء أوراق Excel
  static Future<void> _createExcelSheets(
    Excel excel,
    String reportType,
    dynamic reportData,
    Map<String, dynamic> filters,
    ExportCustomization customization,
  ) async {
    // إزالة الورقة الافتراضية
    excel.delete('Sheet1');

    // إنشاء ورقة البيانات الرئيسية
    final mainSheet = excel[customization.title];
    await _setupMainExcelSheet(
      mainSheet,
      reportType,
      reportData,
      filters,
      customization,
    );

    // إنشاء ورقة الإحصائيات إذا كانت مطلوبة
    if (customization.includeStatistics) {
      final statsSheet = excel['الإحصائيات'];
      await _setupStatisticsExcelSheet(statsSheet, reportType, reportData);
    }
  }

  /// إعداد الورقة الرئيسية في Excel
  static Future<void> _setupMainExcelSheet(
    Sheet sheet,
    String reportType,
    dynamic reportData,
    Map<String, dynamic> filters,
    ExportCustomization customization,
  ) async {
    int currentRow = 0;

    // إضافة رأس الشركة
    currentRow = await _addCompanyHeader(sheet, customization, currentRow);
    currentRow += 2;

    // إضافة معلومات التقرير
    currentRow = await _addReportInfo(sheet, reportType, filters, currentRow);
    currentRow += 2;

    // إضافة البيانات حسب نوع التقرير
    switch (reportType) {
      case 'trial_balance':
        await _addTrialBalanceData(sheet, reportData, currentRow);
        break;
      case 'profit_loss':
        await _addProfitLossData(sheet, reportData, currentRow);
        break;
      case 'balance_sheet':
        await _addBalanceSheetData(sheet, reportData, currentRow);
        break;
      case 'inventory_report':
        await _addInventoryData(sheet, reportData, currentRow);
        break;
      default:
        await _addGenericData(sheet, reportData, currentRow);
    }
  }

  /// إعداد ورقة الإحصائيات في Excel
  static Future<void> _setupStatisticsExcelSheet(
    Sheet sheet,
    String reportType,
    dynamic reportData,
  ) async {
    int currentRow = 0;

    // عنوان الإحصائيات
    sheet
        .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: currentRow))
        .value = TextCellValue(
      'الإحصائيات والملخصات',
    );
    sheet
        .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: currentRow))
        .cellStyle = CellStyle(
      bold: true,
      fontSize: 16,
      horizontalAlign: HorizontalAlign.Center,
      // backgroundColor: سيتم إضافتها لاحقاً
    );
    currentRow += 2;

    // إضافة الإحصائيات حسب نوع التقرير
    switch (reportType) {
      case 'trial_balance':
        await _addTrialBalanceStatistics(sheet, reportData, currentRow);
        break;
      case 'profit_loss':
        await _addProfitLossStatistics(sheet, reportData, currentRow);
        break;
      case 'balance_sheet':
        await _addBalanceSheetStatistics(sheet, reportData, currentRow);
        break;
      case 'inventory_report':
        await _addInventoryStatistics(sheet, reportData, currentRow);
        break;
    }
  }

  /// إضافة رأس الشركة
  static Future<int> _addCompanyHeader(
    Sheet sheet,
    ExportCustomization customization,
    int startRow,
  ) async {
    // اسم الشركة
    sheet
        .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: startRow))
        .value = TextCellValue(
      _companyName,
    );
    sheet
        .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: startRow))
        .cellStyle = CellStyle(
      bold: true,
      fontSize: 18,
      horizontalAlign: HorizontalAlign.Center,
      // backgroundColor: سيتم إضافتها لاحقاً
      // fontColor: سيتم إضافتها لاحقاً
    );

    // عنوان التقرير
    sheet
        .cell(
          CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: startRow + 1),
        )
        .value = TextCellValue(
      customization.title,
    );
    sheet
        .cell(
          CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: startRow + 1),
        )
        .cellStyle = CellStyle(
      bold: true,
      fontSize: 14,
      horizontalAlign: HorizontalAlign.Center,
    );

    return startRow + 2;
  }

  /// إضافة معلومات التقرير
  static Future<int> _addReportInfo(
    Sheet sheet,
    String reportType,
    Map<String, dynamic> filters,
    int startRow,
  ) async {
    int currentRow = startRow;

    // تاريخ التقرير
    sheet
        .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: currentRow))
        .value = TextCellValue(
      'تاريخ التقرير:',
    );
    sheet
        .cell(CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: currentRow))
        .value = TextCellValue(
      DateFormat('yyyy/MM/dd HH:mm').format(DateTime.now()),
    );
    currentRow++;

    // معلومات الفلاتر
    for (final filter in filters.entries) {
      sheet
          .cell(
            CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: currentRow),
          )
          .value = TextCellValue(
        '${filter.key}:',
      );
      sheet
          .cell(
            CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: currentRow),
          )
          .value = TextCellValue(
        '${filter.value}',
      );
      currentRow++;
    }

    return currentRow;
  }

  /// إضافة بيانات ميزان المراجعة
  static Future<void> _addTrialBalanceData(
    Sheet sheet,
    dynamic reportData,
    int startRow,
  ) async {
    // سيتم تنفيذ هذا لاحقاً
  }

  /// إضافة بيانات قائمة الدخل
  static Future<void> _addProfitLossData(
    Sheet sheet,
    dynamic reportData,
    int startRow,
  ) async {
    // سيتم تنفيذ هذا لاحقاً
  }

  /// إضافة بيانات الميزانية العمومية
  static Future<void> _addBalanceSheetData(
    Sheet sheet,
    dynamic reportData,
    int startRow,
  ) async {
    // سيتم تنفيذ هذا لاحقاً
  }

  /// إضافة بيانات المخزون
  static Future<void> _addInventoryData(
    Sheet sheet,
    dynamic reportData,
    int startRow,
  ) async {
    // سيتم تنفيذ هذا لاحقاً
  }

  /// إضافة بيانات عامة
  static Future<void> _addGenericData(
    Sheet sheet,
    dynamic reportData,
    int startRow,
  ) async {
    // سيتم تنفيذ هذا لاحقاً
  }

  /// إضافة إحصائيات ميزان المراجعة
  static Future<void> _addTrialBalanceStatistics(
    Sheet sheet,
    dynamic reportData,
    int startRow,
  ) async {
    // سيتم تنفيذ هذا لاحقاً
  }

  /// إضافة إحصائيات قائمة الدخل
  static Future<void> _addProfitLossStatistics(
    Sheet sheet,
    dynamic reportData,
    int startRow,
  ) async {
    // سيتم تنفيذ هذا لاحقاً
  }

  /// إضافة إحصائيات الميزانية العمومية
  static Future<void> _addBalanceSheetStatistics(
    Sheet sheet,
    dynamic reportData,
    int startRow,
  ) async {
    // سيتم تنفيذ هذا لاحقاً
  }

  /// إضافة إحصائيات المخزون
  static Future<void> _addInventoryStatistics(
    Sheet sheet,
    dynamic reportData,
    int startRow,
  ) async {
    // سيتم تنفيذ هذا لاحقاً
  }

  /// حفظ ملف PDF
  static Future<String?> _savePDFFile(
    pw.Document pdf,
    String reportType,
    String title,
  ) async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final fileName =
          '${title}_${DateFormat('yyyyMMdd_HHmmss').format(DateTime.now())}.pdf';
      final filePath = '${directory.path}/$fileName';

      final file = File(filePath);
      await file.writeAsBytes(await pdf.save());

      return filePath;
    } catch (e) {
      LoggingService.error('خطأ في حفظ ملف PDF', category: 'AdvancedExport');
      return null;
    }
  }

  /// حفظ ملف Excel
  static Future<String?> _saveExcelFile(
    Excel excel,
    String reportType,
    String title,
  ) async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final fileName =
          '${title}_${DateFormat('yyyyMMdd_HHmmss').format(DateTime.now())}.xlsx';
      final filePath = '${directory.path}/$fileName';

      final file = File(filePath);
      await file.writeAsBytes(excel.encode()!);

      return filePath;
    } catch (e) {
      LoggingService.error('خطأ في حفظ ملف Excel', category: 'AdvancedExport');
      return null;
    }
  }

  /// تسجيل عملية التصدير
  static Future<void> _logExportOperation(
    String format,
    String reportType,
    String filePath,
  ) async {
    await AuditService.logCreate(
      entityType: AppConstants.auditEntitySystem,
      entityId: 0,
      entityName: 'تصدير تقرير',
      newValues: {
        'format': format,
        'reportType': reportType,
        'filePath': filePath,
        'timestamp': DateTime.now().toIso8601String(),
      },
      description: 'تم تصدير تقرير $reportType بصيغة $format',
      category: 'AdvancedExport',
    );
  }
}

/// فئة تخصيص التصدير
class ExportCustomization {
  final String title;
  final Color headerColor;
  final bool includeCharts;
  final bool includeStatistics;
  final bool includeWatermark;
  final String? customFooter;

  const ExportCustomization({
    required this.title,
    this.headerColor = RevolutionaryColors.damascusSky,
    this.includeCharts = true,
    this.includeStatistics = true,
    this.includeWatermark = false,
    this.customFooter,
  });
}
