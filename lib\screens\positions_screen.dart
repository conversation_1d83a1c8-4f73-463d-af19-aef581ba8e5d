/// شاشة إدارة المناصب
/// توفر واجهة لإدارة المناصب الوظيفية في نظام الموارد البشرية
library;

import 'package:flutter/material.dart';
import '../models/hr_models.dart';
import '../services/position_service.dart';
import '../services/department_service.dart';
import '../services/logging_service.dart';
import '../widgets/loading_widget.dart';
import '../widgets/custom_error_widget.dart' as custom_widgets;

class PositionsScreen extends StatefulWidget {
  const PositionsScreen({super.key});

  @override
  State<PositionsScreen> createState() => _PositionsScreenState();
}

class _PositionsScreenState extends State<PositionsScreen> {
  final PositionService _positionService = PositionService();
  final DepartmentService _departmentService = DepartmentService();

  List<Position> _positions = [];
  List<Department> _departments = [];
  bool _isLoading = true;
  String? _error;
  bool _showActiveOnly = true;
  int? _selectedDepartmentId;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final departments = await _departmentService.getAllDepartments(
        activeOnly: true,
      );
      final positions = await _positionService.getAllPositions(
        activeOnly: _showActiveOnly,
        departmentId: _selectedDepartmentId,
      );

      setState(() {
        _departments = departments;
        _positions = positions;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });

      LoggingService.error(
        'خطأ في تحميل المناصب',
        category: 'PositionsScreen',
        data: {'error': e.toString()},
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إدارة المناصب'),
        backgroundColor: Colors.green[700],
        foregroundColor: Colors.white,
        actions: [
          PopupMenuButton<int?>(
            icon: const Icon(Icons.filter_list),
            tooltip: 'تصفية حسب القسم',
            onSelected: (departmentId) {
              setState(() {
                _selectedDepartmentId = departmentId;
              });
              _loadData();
            },
            itemBuilder: (context) => [
              const PopupMenuItem<int?>(
                value: null,
                child: Text('جميع الأقسام'),
              ),
              ...(_departments.map(
                (dept) =>
                    PopupMenuItem<int?>(value: dept.id, child: Text(dept.name)),
              )),
            ],
          ),
          IconButton(
            icon: Icon(
              _showActiveOnly ? Icons.visibility : Icons.visibility_off,
            ),
            onPressed: () {
              setState(() {
                _showActiveOnly = !_showActiveOnly;
              });
              _loadData();
            },
            tooltip: _showActiveOnly
                ? 'إظهار جميع المناصب'
                : 'إظهار المناصب النشطة فقط',
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadData,
            tooltip: 'تحديث',
          ),
        ],
      ),
      body: _buildBody(),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _showAddPositionDialog(),
        backgroundColor: Colors.green[700],
        tooltip: 'إضافة منصب جديد',
        child: const Icon(Icons.add, color: Colors.white),
      ),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const LoadingWidget();
    }

    if (_error != null) {
      return custom_widgets.ErrorWidget(message: _error!, onRetry: _loadData);
    }

    if (_positions.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.work, size: 64, color: Colors.grey[400]),
            const SizedBox(height: 16),
            Text(
              _selectedDepartmentId != null
                  ? 'لا توجد مناصب في القسم المحدد'
                  : (_showActiveOnly ? 'لا توجد مناصب نشطة' : 'لا توجد مناصب'),
              style: TextStyle(fontSize: 18, color: Colors.grey[600]),
            ),
            const SizedBox(height: 16),
            ElevatedButton.icon(
              onPressed: () => _showAddPositionDialog(),
              icon: const Icon(Icons.add),
              label: const Text('إضافة منصب جديد'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green[700],
                foregroundColor: Colors.white,
              ),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _loadData,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: _positions.length,
        itemBuilder: (context, index) {
          final position = _positions[index];
          return _buildPositionCard(position);
        },
      ),
    );
  }

  Widget _buildPositionCard(Position position) {
    final department = _departments.firstWhere(
      (dept) => dept.id == position.departmentId,
      orElse: () => Department(
        id: 0,
        name: 'قسم غير معروف',
        code: 'UNK',
        isActive: false,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
    );

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: position.isActive ? Colors.green[700] : Colors.grey,
          child: Text(
            position.code.substring(0, 2).toUpperCase(),
            style: const TextStyle(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        title: Text(
          position.title,
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: position.isActive ? Colors.black87 : Colors.grey[600],
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('الرمز: ${position.code}'),
            Text('القسم: ${department.name}'),
            if (position.minSalary > 0 || position.maxSalary > 0)
              Text(
                'نطاق الراتب: ${position.minSalary.toStringAsFixed(0)} - ${position.maxSalary > 0 ? position.maxSalary.toStringAsFixed(0) : '∞'} ل.س',
                style: TextStyle(color: Colors.grey[600]),
              ),
            Row(
              children: [
                Icon(
                  position.isActive ? Icons.check_circle : Icons.cancel,
                  size: 16,
                  color: position.isActive ? Colors.green : Colors.red,
                ),
                const SizedBox(width: 4),
                Text(
                  position.isActive ? 'نشط' : 'غير نشط',
                  style: TextStyle(
                    color: position.isActive ? Colors.green : Colors.red,
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ],
        ),
        trailing: PopupMenuButton<String>(
          onSelected: (value) => _handleMenuAction(value, position),
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'edit',
              child: ListTile(
                leading: Icon(Icons.edit),
                title: Text('تعديل'),
                contentPadding: EdgeInsets.zero,
              ),
            ),
            const PopupMenuItem(
              value: 'delete',
              child: ListTile(
                leading: Icon(Icons.delete, color: Colors.red),
                title: Text('حذف', style: TextStyle(color: Colors.red)),
                contentPadding: EdgeInsets.zero,
              ),
            ),
          ],
        ),
        onTap: () => _showPositionDetails(position),
      ),
    );
  }

  void _handleMenuAction(String action, Position position) {
    switch (action) {
      case 'edit':
        _showEditPositionDialog(position);
        break;
      case 'delete':
        _deletePosition(position);
        break;
    }
  }

  void _showAddPositionDialog() {
    if (_departments.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يجب إضافة قسم واحد على الأقل أولاً'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    showDialog(
      context: context,
      builder: (context) => _PositionFormDialog(
        departments: _departments,
        onSave:
            (
              title,
              code,
              departmentId,
              description,
              minSalary,
              maxSalary,
              requirements,
            ) async {
              final scaffoldMessenger = ScaffoldMessenger.of(context);
              try {
                await _positionService.createPosition(
                  title: title,
                  code: code,
                  departmentId: departmentId,
                  description: description.isEmpty ? null : description,
                  minSalary: minSalary,
                  maxSalary: maxSalary,
                  requirements: requirements.isEmpty ? null : requirements,
                );

                if (mounted) {
                  scaffoldMessenger.showSnackBar(
                    const SnackBar(
                      content: Text('تم إضافة المنصب بنجاح'),
                      backgroundColor: Colors.green,
                    ),
                  );
                  _loadData();
                }
              } catch (e) {
                if (mounted) {
                  scaffoldMessenger.showSnackBar(
                    SnackBar(
                      content: Text('خطأ في إضافة المنصب: ${e.toString()}'),
                      backgroundColor: Colors.red,
                    ),
                  );
                }
              }
            },
      ),
    );
  }

  void _showEditPositionDialog(Position position) {
    showDialog(
      context: context,
      builder: (context) => _PositionFormDialog(
        position: position,
        departments: _departments,
        onSave:
            (
              title,
              code,
              departmentId,
              description,
              minSalary,
              maxSalary,
              requirements,
            ) async {
              final scaffoldMessenger = ScaffoldMessenger.of(context);
              try {
                final updatedPosition = position.copyWith(
                  title: title,
                  code: code,
                  departmentId: departmentId,
                  description: description.isEmpty ? null : description,
                  minSalary: minSalary,
                  maxSalary: maxSalary,
                  requirements: requirements.isEmpty ? null : requirements,
                );

                await _positionService.updatePosition(updatedPosition);

                if (mounted) {
                  scaffoldMessenger.showSnackBar(
                    const SnackBar(
                      content: Text('تم تحديث المنصب بنجاح'),
                      backgroundColor: Colors.green,
                    ),
                  );
                  _loadData();
                }
              } catch (e) {
                if (mounted) {
                  scaffoldMessenger.showSnackBar(
                    SnackBar(
                      content: Text('خطأ في تحديث المنصب: ${e.toString()}'),
                      backgroundColor: Colors.red,
                    ),
                  );
                }
              }
            },
      ),
    );
  }

  void _deletePosition(Position position) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text('هل أنت متأكد من حذف المنصب "${position.title}"؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              final navigator = Navigator.of(context);
              final scaffoldMessenger = ScaffoldMessenger.of(context);
              navigator.pop();

              try {
                await _positionService.deletePosition(position.id!);

                if (mounted) {
                  scaffoldMessenger.showSnackBar(
                    const SnackBar(
                      content: Text('تم حذف المنصب بنجاح'),
                      backgroundColor: Colors.green,
                    ),
                  );
                  _loadData();
                }
              } catch (e) {
                if (mounted) {
                  scaffoldMessenger.showSnackBar(
                    SnackBar(
                      content: Text('خطأ في حذف المنصب: ${e.toString()}'),
                      backgroundColor: Colors.red,
                    ),
                  );
                }
              }
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('حذف', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );
  }

  void _showPositionDetails(Position position) {
    // يمكن إضافة شاشة تفاصيل المنصب هنا
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(SnackBar(content: Text('تفاصيل المنصب: ${position.title}')));
  }
}

class _PositionFormDialog extends StatefulWidget {
  final Position? position;
  final List<Department> departments;
  final Function(
    String title,
    String code,
    int departmentId,
    String description,
    double? minSalary,
    double? maxSalary,
    String requirements,
  )
  onSave;

  const _PositionFormDialog({
    this.position,
    required this.departments,
    required this.onSave,
  });

  @override
  State<_PositionFormDialog> createState() => _PositionFormDialogState();
}

class _PositionFormDialogState extends State<_PositionFormDialog> {
  final _formKey = GlobalKey<FormState>();
  late final TextEditingController _titleController;
  late final TextEditingController _codeController;
  late final TextEditingController _descriptionController;
  late final TextEditingController _minSalaryController;
  late final TextEditingController _maxSalaryController;
  late final TextEditingController _requirementsController;

  int? _selectedDepartmentId;

  @override
  void initState() {
    super.initState();
    _titleController = TextEditingController(
      text: widget.position?.title ?? '',
    );
    _codeController = TextEditingController(text: widget.position?.code ?? '');
    _descriptionController = TextEditingController(
      text: widget.position?.description ?? '',
    );
    _minSalaryController = TextEditingController(
      text: widget.position?.minSalary.toString() ?? '',
    );
    _maxSalaryController = TextEditingController(
      text: widget.position?.maxSalary.toString() ?? '',
    );
    _requirementsController = TextEditingController(
      text: widget.position?.requirements ?? '',
    );
    _selectedDepartmentId = widget.position?.departmentId;
  }

  @override
  void dispose() {
    _titleController.dispose();
    _codeController.dispose();
    _descriptionController.dispose();
    _minSalaryController.dispose();
    _maxSalaryController.dispose();
    _requirementsController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(widget.position == null ? 'إضافة منصب جديد' : 'تعديل المنصب'),
      content: SingleChildScrollView(
        child: Form(
          key: _formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextFormField(
                controller: _titleController,
                decoration: const InputDecoration(
                  labelText: 'عنوان المنصب *',
                  border: OutlineInputBorder(),
                ),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'عنوان المنصب مطلوب';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _codeController,
                decoration: const InputDecoration(
                  labelText: 'رمز المنصب *',
                  border: OutlineInputBorder(),
                ),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'رمز المنصب مطلوب';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              DropdownButtonFormField<int>(
                value: _selectedDepartmentId,
                decoration: const InputDecoration(
                  labelText: 'القسم *',
                  border: OutlineInputBorder(),
                ),
                items: widget.departments.map((dept) {
                  return DropdownMenuItem<int>(
                    value: dept.id,
                    child: Text(dept.name),
                  );
                }).toList(),
                onChanged: (value) {
                  setState(() {
                    _selectedDepartmentId = value;
                  });
                },
                validator: (value) {
                  if (value == null) {
                    return 'القسم مطلوب';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              Row(
                children: [
                  Expanded(
                    child: TextFormField(
                      controller: _minSalaryController,
                      decoration: const InputDecoration(
                        labelText: 'الحد الأدنى للراتب',
                        border: OutlineInputBorder(),
                      ),
                      keyboardType: TextInputType.number,
                      validator: (value) {
                        if (value != null && value.isNotEmpty) {
                          final minSalary = double.tryParse(value);
                          if (minSalary == null || minSalary < 0) {
                            return 'قيمة غير صحيحة';
                          }
                        }
                        return null;
                      },
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: TextFormField(
                      controller: _maxSalaryController,
                      decoration: const InputDecoration(
                        labelText: 'الحد الأقصى للراتب',
                        border: OutlineInputBorder(),
                      ),
                      keyboardType: TextInputType.number,
                      validator: (value) {
                        if (value != null && value.isNotEmpty) {
                          final maxSalary = double.tryParse(value);
                          if (maxSalary == null || maxSalary < 0) {
                            return 'قيمة غير صحيحة';
                          }

                          final minSalaryText = _minSalaryController.text;
                          if (minSalaryText.isNotEmpty) {
                            final minSalary = double.tryParse(minSalaryText);
                            if (minSalary != null && maxSalary < minSalary) {
                              return 'يجب أن يكون أكبر من الحد الأدنى';
                            }
                          }
                        }
                        return null;
                      },
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _descriptionController,
                decoration: const InputDecoration(
                  labelText: 'الوصف',
                  border: OutlineInputBorder(),
                ),
                maxLines: 2,
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _requirementsController,
                decoration: const InputDecoration(
                  labelText: 'المتطلبات',
                  border: OutlineInputBorder(),
                ),
                maxLines: 3,
              ),
            ],
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('إلغاء'),
        ),
        ElevatedButton(
          onPressed: () {
            if (_formKey.currentState!.validate()) {
              final minSalary = _minSalaryController.text.isEmpty
                  ? null
                  : double.tryParse(_minSalaryController.text);
              final maxSalary = _maxSalaryController.text.isEmpty
                  ? null
                  : double.tryParse(_maxSalaryController.text);

              widget.onSave(
                _titleController.text.trim(),
                _codeController.text.trim(),
                _selectedDepartmentId!,
                _descriptionController.text.trim(),
                minSalary,
                maxSalary,
                _requirementsController.text.trim(),
              );
              Navigator.of(context).pop();
            }
          },
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.green[700],
            foregroundColor: Colors.white,
          ),
          child: Text(widget.position == null ? 'إضافة' : 'تحديث'),
        ),
      ],
    );
  }
}
