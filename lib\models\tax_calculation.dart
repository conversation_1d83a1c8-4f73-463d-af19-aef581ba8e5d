/// نموذج حساب الضريبة
/// يحتوي على معلومات حساب الضريبة والنتائج
class TaxCalculation {
  final int? id;
  final String type; // income_tax, vat, business_profit
  final double baseAmount;
  final double taxRate;
  final double taxAmount;
  final double totalAmount;
  final DateTime date;
  final String? description;
  final Map<String, dynamic>? details;
  final String status; // calculated, applied, cancelled
  final DateTime createdAt;
  final DateTime updatedAt;

  const TaxCalculation({
    this.id,
    required this.type,
    required this.baseAmount,
    required this.taxRate,
    required this.taxAmount,
    required this.totalAmount,
    required this.date,
    this.description,
    this.details,
    required this.status,
    required this.createdAt,
    required this.updatedAt,
  });

  /// إنشاء TaxCalculation من Map
  factory TaxCalculation.fromMap(Map<String, dynamic> map) {
    return TaxCalculation(
      id: map['id']?.toInt(),
      type: map['type'] ?? '',
      baseAmount: (map['base_amount'] ?? 0).toDouble(),
      taxRate: (map['tax_rate'] ?? 0).toDouble(),
      taxAmount: (map['tax_amount'] ?? 0).toDouble(),
      totalAmount: (map['total_amount'] ?? 0).toDouble(),
      date: DateTime.parse(map['date']),
      description: map['description'],
      details: map['details'] != null 
          ? Map<String, dynamic>.from(map['details'])
          : null,
      status: map['status'] ?? 'calculated',
      createdAt: DateTime.parse(map['created_at']),
      updatedAt: DateTime.parse(map['updated_at']),
    );
  }

  /// تحويل TaxCalculation إلى Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'type': type,
      'base_amount': baseAmount,
      'tax_rate': taxRate,
      'tax_amount': taxAmount,
      'total_amount': totalAmount,
      'date': date.toIso8601String(),
      'description': description,
      'details': details,
      'status': status,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  /// إنشاء نسخة معدلة من TaxCalculation
  TaxCalculation copyWith({
    int? id,
    String? type,
    double? baseAmount,
    double? taxRate,
    double? taxAmount,
    double? totalAmount,
    DateTime? date,
    String? description,
    Map<String, dynamic>? details,
    String? status,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return TaxCalculation(
      id: id ?? this.id,
      type: type ?? this.type,
      baseAmount: baseAmount ?? this.baseAmount,
      taxRate: taxRate ?? this.taxRate,
      taxAmount: taxAmount ?? this.taxAmount,
      totalAmount: totalAmount ?? this.totalAmount,
      date: date ?? this.date,
      description: description ?? this.description,
      details: details ?? this.details,
      status: status ?? this.status,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// الحصول على اسم نوع الضريبة
  String get typeDisplayName {
    switch (type) {
      case 'income_tax':
        return 'ضريبة الدخل';
      case 'vat':
        return 'ضريبة القيمة المضافة';
      case 'business_profit':
        return 'ضريبة الأرباح التجارية';
      default:
        return type;
    }
  }

  /// الحصول على اسم الحالة
  String get statusDisplayName {
    switch (status) {
      case 'calculated':
        return 'محسوبة';
      case 'applied':
        return 'مطبقة';
      case 'cancelled':
        return 'ملغية';
      default:
        return status;
    }
  }

  /// حساب النسبة المئوية للضريبة
  double get taxPercentage => taxRate * 100;

  @override
  String toString() {
    return 'TaxCalculation(id: $id, type: $type, baseAmount: $baseAmount, taxAmount: $taxAmount)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is TaxCalculation &&
        other.id == id &&
        other.type == type &&
        other.baseAmount == baseAmount &&
        other.taxAmount == taxAmount;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        type.hashCode ^
        baseAmount.hashCode ^
        taxAmount.hashCode;
  }
}

/// أنواع الضرائب السورية
class SyrianTaxTypes {
  static const String incomeTax = 'income_tax';
  static const String vat = 'vat';
  static const String businessProfit = 'business_profit';
  static const String propertyTax = 'property_tax';
  static const String stampDuty = 'stamp_duty';

  /// الحصول على جميع أنواع الضرائب
  static List<String> getAllTypes() {
    return [
      incomeTax,
      vat,
      businessProfit,
      propertyTax,
      stampDuty,
    ];
  }

  /// الحصول على اسم نوع الضريبة
  static String getDisplayName(String type) {
    switch (type) {
      case incomeTax:
        return 'ضريبة الدخل';
      case vat:
        return 'ضريبة القيمة المضافة';
      case businessProfit:
        return 'ضريبة الأرباح التجارية';
      case propertyTax:
        return 'ضريبة العقارات';
      case stampDuty:
        return 'رسم الطابع';
      default:
        return type;
    }
  }
}

/// شرائح ضريبة الدخل السورية
class SyrianIncomeTaxBrackets {
  static const List<Map<String, dynamic>> brackets = [
    {
      'min': 0,
      'max': 500000,
      'rate': 0.0,
      'description': 'معفى من الضريبة'
    },
    {
      'min': 500001,
      'max': 1000000,
      'rate': 0.05,
      'description': '5% للشريحة الأولى'
    },
    {
      'min': 1000001,
      'max': 2000000,
      'rate': 0.10,
      'description': '10% للشريحة الثانية'
    },
    {
      'min': 2000001,
      'max': 5000000,
      'rate': 0.15,
      'description': '15% للشريحة الثالثة'
    },
    {
      'min': 5000001,
      'max': double.infinity,
      'rate': 0.20,
      'description': '20% للشريحة العليا'
    },
  ];

  /// حساب ضريبة الدخل حسب الشرائح
  static TaxCalculation calculateIncomeTax(double income) {
    double totalTax = 0;
    final details = <String, dynamic>{};
    final bracketDetails = <Map<String, dynamic>>[];

    for (final bracket in brackets) {
      final min = bracket['min'] as double;
      final max = bracket['max'] as double;
      final rate = bracket['rate'] as double;

      if (income > min) {
        final taxableInThisBracket = income > max ? max - min : income - min;
        final taxInThisBracket = taxableInThisBracket * rate;
        totalTax += taxInThisBracket;

        bracketDetails.add({
          'bracket': bracket['description'],
          'taxable_amount': taxableInThisBracket,
          'rate': rate,
          'tax_amount': taxInThisBracket,
        });

        if (income <= max) break;
      }
    }

    details['brackets'] = bracketDetails;
    details['total_taxable_income'] = income;

    return TaxCalculation(
      type: SyrianTaxTypes.incomeTax,
      baseAmount: income,
      taxRate: totalTax / income,
      taxAmount: totalTax,
      totalAmount: income + totalTax,
      date: DateTime.now(),
      description: 'حساب ضريبة الدخل حسب الشرائح السورية',
      details: details,
      status: 'calculated',
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );
  }
}

/// نسب ضريبة القيمة المضافة السورية
class SyrianVATRates {
  static const double standard = 0.10; // 10%
  static const double reduced = 0.05; // 5%
  static const double zero = 0.0; // 0%

  /// الحصول على جميع النسب
  static List<Map<String, dynamic>> getAllRates() {
    return [
      {
        'rate': zero,
        'description': 'معفى من الضريبة',
        'items': ['الأدوية الأساسية', 'المواد الغذائية الأساسية']
      },
      {
        'rate': reduced,
        'description': 'نسبة مخفضة 5%',
        'items': ['الكتب', 'المواد التعليمية']
      },
      {
        'rate': standard,
        'description': 'النسبة العادية 10%',
        'items': ['معظم السلع والخدمات']
      },
    ];
  }

  /// حساب ضريبة القيمة المضافة
  static TaxCalculation calculateVAT(double amount, double rate) {
    final taxAmount = amount * rate;
    final totalAmount = amount + taxAmount;

    return TaxCalculation(
      type: SyrianTaxTypes.vat,
      baseAmount: amount,
      taxRate: rate,
      taxAmount: taxAmount,
      totalAmount: totalAmount,
      date: DateTime.now(),
      description: 'حساب ضريبة القيمة المضافة ${(rate * 100).toStringAsFixed(0)}%',
      details: {
        'rate_percentage': rate * 100,
        'calculation_method': 'amount * rate',
      },
      status: 'calculated',
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );
  }
}
