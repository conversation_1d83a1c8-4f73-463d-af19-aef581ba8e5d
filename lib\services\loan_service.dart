/// خدمة إدارة القروض والسلف
/// توفر عمليات إدارة القروض والأقساط للموظفين
library;

import '../database/database_helper.dart';
import '../constants/app_constants.dart';
import '../services/logging_service.dart';
import '../services/audit_service.dart';
import '../exceptions/validation_exception.dart';
import '../models/hr_models.dart';

class LoanService {
  final DatabaseHelper _databaseHelper = DatabaseHelper();

  /// إنشاء قرض جديد
  Future<Loan> createLoan({
    required int employeeId,
    required double amount,
    double interestRate = 0,
    required int installments,
    DateTime? loanDate,
    DateTime? firstInstallmentDate,
    String? purpose,
    String? notes,
  }) async {
    try {
      // التحقق من صحة البيانات
      if (amount <= 0) {
        throw ValidationException('مبلغ القرض يجب أن يكون أكبر من صفر');
      }
      if (installments <= 0) {
        throw ValidationException('عدد الأقساط يجب أن يكون أكبر من صفر');
      }

      final actualLoanDate = loanDate ?? DateTime.now();
      final actualFirstInstallmentDate =
          firstInstallmentDate ??
          DateTime(
            actualLoanDate.year,
            actualLoanDate.month + 1,
            actualLoanDate.day,
          );

      // حساب المبلغ الإجمالي مع الفوائد
      final totalAmount = amount + (amount * interestRate / 100);

      // حساب القسط الشهري
      final monthlyInstallment = totalAmount / installments;

      final loan = Loan(
        employeeId: employeeId,
        amount: amount,
        interestRate: interestRate,
        installments: installments,
        monthlyInstallment: monthlyInstallment,
        totalAmount: totalAmount,
        remainingAmount: totalAmount,
        loanDate: actualLoanDate,
        firstInstallmentDate: actualFirstInstallmentDate,
        purpose: purpose,
        notes: notes,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      // حفظ القرض في قاعدة البيانات
      final db = await _databaseHelper.database;
      final id = await db.insert(AppConstants.loansTable, loan.toMap());

      final savedLoan = loan.copyWith(id: id);

      // إنشاء جدول الأقساط
      await _generateInstallments(savedLoan);

      // تسجيل في سجل المراجعة
      await AuditService.log(
        action: 'CREATE',
        entityType: 'Loan',
        entityId: id,
        description: 'إنشاء قرض جديد للموظف',
        newValues: savedLoan.toMap(),
      );

      LoggingService.info(
        'تم إنشاء قرض جديد بنجاح',
        category: 'LoanService',
        data: {
          'loanId': id,
          'employeeId': employeeId,
          'amount': amount,
          'installments': installments,
        },
      );

      return savedLoan;
    } catch (e) {
      LoggingService.error(
        'خطأ في إنشاء القرض',
        category: 'LoanService',
        data: {
          'employeeId': employeeId,
          'amount': amount,
          'error': e.toString(),
        },
      );
      rethrow;
    }
  }

  /// الحصول على جميع قروض موظف
  Future<List<Loan>> getEmployeeLoans(int employeeId) async {
    try {
      final db = await _databaseHelper.database;
      final result = await db.query(
        AppConstants.loansTable,
        where: 'employee_id = ?',
        whereArgs: [employeeId],
        orderBy: 'created_at DESC',
      );

      return result.map((map) => Loan.fromMap(map)).toList();
    } catch (e) {
      LoggingService.error(
        'خطأ في جلب قروض الموظف',
        category: 'LoanService',
        data: {'employeeId': employeeId, 'error': e.toString()},
      );
      return [];
    }
  }

  /// الحصول على قرض بالمعرف
  Future<Loan?> getLoanById(int id) async {
    try {
      final db = await _databaseHelper.database;
      final result = await db.query(
        AppConstants.loansTable,
        where: 'id = ?',
        whereArgs: [id],
        limit: 1,
      );

      if (result.isNotEmpty) {
        return Loan.fromMap(result.first);
      }
      return null;
    } catch (e) {
      LoggingService.error(
        'خطأ في جلب القرض',
        category: 'LoanService',
        data: {'loanId': id, 'error': e.toString()},
      );
      return null;
    }
  }

  /// إنشاء جدول الأقساط للقرض
  Future<void> _generateInstallments(Loan loan) async {
    try {
      final db = await _databaseHelper.database;
      final installments = <LoanInstallment>[];

      for (int i = 1; i <= loan.installments; i++) {
        final dueDate = DateTime(
          loan.firstInstallmentDate!.year,
          loan.firstInstallmentDate!.month + (i - 1),
          loan.firstInstallmentDate!.day,
        );

        final installment = LoanInstallment(
          loanId: loan.id!,
          installmentNumber: i,
          amount: loan.monthlyInstallment,
          dueDate: dueDate,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        installments.add(installment);
      }

      // حفظ جميع الأقساط
      for (final installment in installments) {
        await db.insert(
          AppConstants.loanInstallmentsTable,
          installment.toMap(),
        );
      }

      LoggingService.info(
        'تم إنشاء جدول الأقساط بنجاح',
        category: 'LoanService',
        data: {'loanId': loan.id, 'installmentsCount': installments.length},
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في إنشاء جدول الأقساط',
        category: 'LoanService',
        data: {'loanId': loan.id, 'error': e.toString()},
      );
      rethrow;
    }
  }

  /// الحصول على أقساط القرض
  Future<List<LoanInstallment>> getLoanInstallments(int loanId) async {
    try {
      final db = await _databaseHelper.database;
      final result = await db.query(
        AppConstants.loanInstallmentsTable,
        where: 'loan_id = ?',
        whereArgs: [loanId],
        orderBy: 'installment_number ASC',
      );

      return result.map((map) => LoanInstallment.fromMap(map)).toList();
    } catch (e) {
      LoggingService.error(
        'خطأ في جلب أقساط القرض',
        category: 'LoanService',
        data: {'loanId': loanId, 'error': e.toString()},
      );
      return [];
    }
  }

  /// الحصول على الأقساط المتأخرة
  Future<List<LoanInstallment>> getOverdueInstallments() async {
    try {
      final db = await _databaseHelper.database;
      final today = DateTime.now().toIso8601String().split('T')[0];

      final result = await db.query(
        AppConstants.loanInstallmentsTable,
        where: 'status = ? AND due_date < ?',
        whereArgs: ['pending', today],
        orderBy: 'due_date ASC',
      );

      return result.map((map) => LoanInstallment.fromMap(map)).toList();
    } catch (e) {
      LoggingService.error(
        'خطأ في جلب الأقساط المتأخرة',
        category: 'LoanService',
        data: {'error': e.toString()},
      );
      return [];
    }
  }

  /// دفع قسط
  Future<void> payInstallment({
    required int installmentId,
    DateTime? paidDate,
    double? paidAmount,
    String? notes,
  }) async {
    try {
      final db = await _databaseHelper.database;

      // الحصول على القسط
      final installmentResult = await db.query(
        AppConstants.loanInstallmentsTable,
        where: 'id = ?',
        whereArgs: [installmentId],
        limit: 1,
      );

      if (installmentResult.isEmpty) {
        throw ValidationException('القسط غير موجود');
      }

      final installment = LoanInstallment.fromMap(installmentResult.first);
      final actualPaidDate = paidDate ?? DateTime.now();
      final actualPaidAmount = paidAmount ?? installment.amount;

      // تحديث القسط
      final updatedInstallment = installment.copyWith(
        paidDate: actualPaidDate,
        paidAmount: actualPaidAmount,
        status: 'paid',
        notes: notes,
        updatedAt: DateTime.now(),
      );

      await db.update(
        AppConstants.loanInstallmentsTable,
        updatedInstallment.toMap(),
        where: 'id = ?',
        whereArgs: [installmentId],
      );

      // تحديث القرض
      await _updateLoanAfterPayment(installment.loanId, actualPaidAmount);

      // تسجيل في سجل المراجعة
      await AuditService.log(
        action: 'UPDATE',
        entityType: 'LoanInstallment',
        entityId: installmentId,
        description: 'دفع قسط القرض',
        newValues: updatedInstallment.toMap(),
      );

      LoggingService.info(
        'تم دفع القسط بنجاح',
        category: 'LoanService',
        data: {
          'installmentId': installmentId,
          'loanId': installment.loanId,
          'paidAmount': actualPaidAmount,
        },
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في دفع القسط',
        category: 'LoanService',
        data: {'installmentId': installmentId, 'error': e.toString()},
      );
      rethrow;
    }
  }

  /// تحديث القرض بعد دفع قسط
  Future<void> _updateLoanAfterPayment(int loanId, double paidAmount) async {
    try {
      final db = await _databaseHelper.database;

      // الحصول على القرض
      final loanResult = await db.query(
        AppConstants.loansTable,
        where: 'id = ?',
        whereArgs: [loanId],
        limit: 1,
      );

      if (loanResult.isNotEmpty) {
        final loan = Loan.fromMap(loanResult.first);
        final newPaidAmount = loan.paidAmount + paidAmount;
        final newRemainingAmount = loan.totalAmount - newPaidAmount;

        String newStatus = loan.status;
        if (newRemainingAmount <= 0) {
          newStatus = 'completed';
        }

        final updatedLoan = loan.copyWith(
          paidAmount: newPaidAmount,
          remainingAmount: newRemainingAmount,
          status: newStatus,
          updatedAt: DateTime.now(),
        );

        await db.update(
          AppConstants.loansTable,
          updatedLoan.toMap(),
          where: 'id = ?',
          whereArgs: [loanId],
        );
      }
    } catch (e) {
      LoggingService.error(
        'خطأ في تحديث القرض بعد الدفع',
        category: 'LoanService',
        data: {'loanId': loanId, 'error': e.toString()},
      );
    }
  }
}
