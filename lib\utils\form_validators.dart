import '../services/validation_service.dart';
import '../services/error_message_service.dart';

/// مجموعة من المدققات للاستخدام في النماذج
class FormValidators {
  // ===============================
  // مدققات الحقول الأساسية
  // ===============================

  /// مدقق الحقل المطلوب
  static String? required(String? value, [String? fieldName]) {
    if (fieldName != null) {
      final result = ValidationService.validateRequired(value, fieldName);
      return result.isValid ? null : result.errorMessage;
    }

    // نسخة مبسطة للاستخدام العام
    if (value == null || value.trim().isEmpty) {
      return 'هذا الحقل مطلوب';
    }
    return null;
  }

  /// مدقق طول النص
  static String? length(
    String? value,
    String fieldName, {
    int? minLength,
    int? maxLength,
  }) {
    final result = ValidationService.validateLength(
      value,
      fieldName,
      minLength: minLength,
      maxLength: maxLength,
    );
    return result.isValid ? null : result.errorMessage;
  }

  /// مدقق مركب للحقل المطلوب مع طول محدد
  static String? requiredWithLength(
    String? value,
    String fieldName, {
    int? maxLength,
  }) {
    // التحقق من الحقل المطلوب أولاً
    final requiredResult = required(value, fieldName);
    if (requiredResult != null) return requiredResult;

    // ثم التحقق من الطول
    return length(value, fieldName, maxLength: maxLength);
  }

  // ===============================
  // مدققات الأكواد
  // ===============================

  /// مدقق كود الحساب
  static String? accountCode(String? value) {
    final result = ValidationService.validateAccountCode(value);
    return result.isValid ? null : result.errorMessage;
  }

  /// مدقق كود العميل
  static String? customerCode(String? value) {
    final result = ValidationService.validateCustomerCode(value);
    return result.isValid ? null : result.errorMessage;
  }

  /// مدقق كود المورد
  static String? supplierCode(String? value) {
    final result = ValidationService.validateSupplierCode(value);
    return result.isValid ? null : result.errorMessage;
  }

  /// مدقق كود الصنف
  static String? itemCode(String? value) {
    final result = ValidationService.validateItemCode(value);
    return result.isValid ? null : result.errorMessage;
  }

  /// مدقق رقم القيد
  static String? journalEntryNumber(String? value) {
    final result = ValidationService.validateJournalEntryNumber(value);
    return result.isValid ? null : result.errorMessage;
  }

  // ===============================
  // مدققات بيانات الاتصال
  // ===============================

  /// مدقق رقم الهاتف (اختياري)
  static String? phoneNumber(String? value) {
    if (value == null || value.trim().isEmpty) return null; // اختياري
    final result = ValidationService.validatePhoneNumber(value);
    return result.isValid ? null : result.errorMessage;
  }

  /// مدقق البريد الإلكتروني (اختياري)
  static String? email(String? value) {
    if (value == null || value.trim().isEmpty) return null; // اختياري
    final result = ValidationService.validateEmail(value);
    return result.isValid ? null : result.errorMessage;
  }

  // ===============================
  // مدققات المبالغ والكميات
  // ===============================

  /// مدقق المبلغ المالي
  static String? amount(String? value, {bool allowZero = true}) {
    if (value == null || value.trim().isEmpty) {
      return ErrorMessageService.getRequiredFieldMessage('المبلغ');
    }

    final doubleValue = double.tryParse(value);
    if (doubleValue == null) {
      return 'المبلغ يجب أن يكون رقماً صحيحاً';
    }

    final result = ValidationService.validateAmount(
      doubleValue,
      allowZero: allowZero,
    );
    return result.isValid ? null : result.errorMessage;
  }

  /// مدقق الكمية
  static String? quantity(String? value, {bool allowZero = false}) {
    if (value == null || value.trim().isEmpty) {
      return ErrorMessageService.getRequiredFieldMessage('الكمية');
    }

    final doubleValue = double.tryParse(value);
    if (doubleValue == null) {
      return 'الكمية يجب أن تكون رقماً صحيحاً';
    }

    final result = ValidationService.validateQuantity(
      doubleValue,
      allowZero: allowZero,
    );
    return result.isValid ? null : result.errorMessage;
  }

  // ===============================
  // مدققات مخصصة للنماذج
  // ===============================

  /// مدقق اسم الحساب
  static String? accountName(String? value) {
    return requiredWithLength(value, 'اسم الحساب', maxLength: 100);
  }

  /// مدقق اسم العميل
  static String? customerName(String? value) {
    return requiredWithLength(value, 'اسم العميل', maxLength: 100);
  }

  /// مدقق اسم المورد
  static String? supplierName(String? value) {
    return requiredWithLength(value, 'اسم المورد', maxLength: 100);
  }

  /// مدقق اسم الصنف
  static String? itemName(String? value) {
    return requiredWithLength(value, 'اسم الصنف', maxLength: 100);
  }

  /// مدقق وحدة القياس
  static String? itemUnit(String? value) {
    return required(value, 'وحدة القياس');
  }

  /// مدقق الوصف (اختياري)
  static String? description(String? value) {
    if (value == null || value.trim().isEmpty) return null; // اختياري
    return length(value, 'الوصف', maxLength: 500);
  }

  /// مدقق العنوان (اختياري)
  static String? address(String? value) {
    if (value == null || value.trim().isEmpty) return null; // اختياري
    return length(value, 'العنوان', maxLength: 200);
  }

  // ===============================
  // مدققات مقارنة الأسعار
  // ===============================

  /// مدقق سعر البيع مقارنة بسعر التكلفة
  static String? sellingPriceVsCost(
    String? sellingPriceValue,
    String? costPriceValue,
  ) {
    if (sellingPriceValue == null || sellingPriceValue.trim().isEmpty) {
      return null;
    }
    if (costPriceValue == null || costPriceValue.trim().isEmpty) return null;

    final sellingPrice = double.tryParse(sellingPriceValue);
    final costPrice = double.tryParse(costPriceValue);

    if (sellingPrice == null || costPrice == null) return null;

    if (costPrice > 0 && sellingPrice > 0 && sellingPrice < costPrice) {
      return ErrorMessageService.sellingPriceLowerThanCostMessage;
    }

    return null;
  }

  // ===============================
  // مدققات التواريخ
  // ===============================

  /// مدقق التاريخ المطلوب
  static String? requiredDate(DateTime? value) {
    if (value == null) {
      return ErrorMessageService.getRequiredFieldMessage('التاريخ');
    }

    final result = ValidationService.validateDate(value);
    return result.isValid ? null : result.errorMessage;
  }

  /// مدقق التاريخ الاختياري
  static String? optionalDate(DateTime? value) {
    if (value == null) return null; // اختياري

    final result = ValidationService.validateDate(value);
    return result.isValid ? null : result.errorMessage;
  }

  // ===============================
  // مدققات مركبة للكيانات
  // ===============================

  /// التحقق من صحة بيانات الحساب كاملة
  static Map<String, String?> validateAccountData({
    required String? code,
    required String? name,
    required String? type,
    required String? balanceText,
  }) {
    return {
      'code': accountCode(code),
      'name': accountName(name),
      'type': type == null || type.isEmpty ? 'نوع الحساب مطلوب' : null,
      'balance': amount(balanceText, allowZero: true),
    };
  }

  /// التحقق من صحة بيانات العميل كاملة
  static Map<String, String?> validateCustomerData({
    required String? code,
    required String? name,
    String? phone,
    String? email,
    String? address,
  }) {
    return {
      'code': customerCode(code),
      'name': customerName(name),
      'phone': phoneNumber(phone),
      'email': FormValidators.email(email),
      'address': FormValidators.address(address),
    };
  }

  /// التحقق من صحة بيانات الصنف كاملة
  static Map<String, String?> validateItemData({
    required String? code,
    required String? name,
    required String? unit,
    required String? costPriceText,
    required String? sellingPriceText,
    required String? quantityText,
    required String? minQuantityText,
  }) {
    return {
      'code': itemCode(code),
      'name': itemName(name),
      'unit': itemUnit(unit),
      'costPrice': amount(costPriceText, allowZero: true),
      'sellingPrice': amount(sellingPriceText, allowZero: true),
      'quantity': quantity(quantityText, allowZero: true),
      'minQuantity': quantity(minQuantityText, allowZero: true),
      'priceComparison': sellingPriceVsCost(sellingPriceText, costPriceText),
    };
  }

  // ===============================
  // دوال مساعدة
  // ===============================

  /// التحقق من وجود أخطاء في خريطة النتائج
  static bool hasErrors(Map<String, String?> validationResults) {
    return validationResults.values.any((error) => error != null);
  }

  /// الحصول على أول خطأ من خريطة النتائج
  static String? getFirstError(Map<String, String?> validationResults) {
    for (final error in validationResults.values) {
      if (error != null) return error;
    }
    return null;
  }
}
