/// حوار إدارة الدفعات للفواتير
/// يتيح إضافة وتعديل وحذف الدفعات مع تتبع المدفوعات الجزئية
library;

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../models/payment.dart';
import '../models/invoice.dart';
import '../models/payment_schedule.dart';
import '../services/payment_service.dart';
import '../services/payment_schedule_service.dart';
import '../services/payment_reminder_service.dart';
import '../constants/revolutionary_design_colors.dart';
import '../widgets/loading_widget.dart';

class PaymentManagementDialog extends StatefulWidget {
  final Invoice invoice;
  final Function()? onPaymentAdded;

  const PaymentManagementDialog({
    super.key,
    required this.invoice,
    this.onPaymentAdded,
  });

  @override
  State<PaymentManagementDialog> createState() =>
      _PaymentManagementDialogState();
}

class _PaymentManagementDialogState extends State<PaymentManagementDialog>
    with TickerProviderStateMixin {
  final PaymentService _paymentService = PaymentService();
  final PaymentScheduleService _scheduleService = PaymentScheduleService();
  final PaymentReminderService _reminderService = PaymentReminderService();

  final TextEditingController _amountController = TextEditingController();
  final TextEditingController _referenceController = TextEditingController();
  final TextEditingController _bankNameController = TextEditingController();
  final TextEditingController _accountNumberController =
      TextEditingController();
  final TextEditingController _notesController = TextEditingController();

  // متحكمات الجدولة
  final TextEditingController _scheduleNameController = TextEditingController();
  final TextEditingController _installmentAmountController =
      TextEditingController();
  final TextEditingController _totalInstallmentsController =
      TextEditingController();

  PaymentMethod _selectedMethod = PaymentMethod.cash;
  DateTime _selectedDate = DateTime.now();
  PaymentSummary? _paymentSummary;
  PaymentSchedule? _activeSchedule;
  bool _isLoading = true;
  bool _isSubmitting = false;

  // متغيرات الجدولة
  PaymentFrequency _selectedFrequency = PaymentFrequency.monthly;
  DateTime _scheduleStartDate = DateTime.now();
  DateTime? _scheduleEndDate;
  bool _autoPayment = false;

  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadPaymentSummary();
    _loadActiveSchedule();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _amountController.dispose();
    _referenceController.dispose();
    _bankNameController.dispose();
    _accountNumberController.dispose();
    _notesController.dispose();
    _scheduleNameController.dispose();
    _installmentAmountController.dispose();
    _totalInstallmentsController.dispose();
    super.dispose();
  }

  Future<void> _loadPaymentSummary() async {
    try {
      final summary = await _paymentService.getPaymentSummary(
        widget.invoice.id!,
      );
      setState(() {
        _paymentSummary = summary;
        _isLoading = false;
        // تعيين المبلغ المتبقي كقيمة افتراضية
        _amountController.text = summary.remainingAmount.toStringAsFixed(2);
      });
    } catch (e) {
      setState(() => _isLoading = false);
      if (mounted) {
        _showError('خطأ في تحميل بيانات الدفعات: $e');
      }
    }
  }

  Future<void> _loadActiveSchedule() async {
    try {
      final schedule = await _scheduleService.getActiveScheduleByInvoiceId(
        widget.invoice.id!,
      );
      setState(() {
        _activeSchedule = schedule;
        if (schedule != null) {
          _scheduleNameController.text = schedule.scheduleName;
          _installmentAmountController.text = schedule.installmentAmount
              .toStringAsFixed(2);
          _totalInstallmentsController.text = schedule.totalInstallments
              .toString();
          _selectedFrequency = schedule.frequency;
          _scheduleStartDate = schedule.startDate;
          _scheduleEndDate = schedule.endDate;
          _autoPayment = schedule.autoPayment;
        }
      });
    } catch (e) {
      if (mounted) {
        _showError('خطأ في تحميل بيانات الجدولة: $e');
      }
    }
  }

  Future<void> _addPayment() async {
    if (!_validateInput()) return;

    setState(() => _isSubmitting = true);

    try {
      final amount = double.parse(_amountController.text);

      final payment = Payment(
        invoiceId: widget.invoice.id!,
        amount: amount,
        method: _selectedMethod,
        paymentDate: _selectedDate,
        reference: _referenceController.text.isNotEmpty
            ? _referenceController.text
            : null,
        bankName: _bankNameController.text.isNotEmpty
            ? _bankNameController.text
            : null,
        accountNumber: _accountNumberController.text.isNotEmpty
            ? _accountNumberController.text
            : null,
        notes: _notesController.text.isNotEmpty ? _notesController.text : null,
        status: PaymentStatus.confirmed, // تأكيد مباشر للدفعات
      );

      await _paymentService.addPayment(payment);

      // إعادة تحميل البيانات
      await _loadPaymentSummary();

      // مسح النموذج
      _clearForm();

      // إشعار الوالد
      widget.onPaymentAdded?.call();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('تم إضافة الدفعة بنجاح'),
            backgroundColor: RevolutionaryColors.successGlow,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        _showError('خطأ في إضافة الدفعة: $e');
      }
    } finally {
      setState(() => _isSubmitting = false);
    }
  }

  bool _validateInput() {
    final amount = double.tryParse(_amountController.text);

    if (amount == null || amount <= 0) {
      _showError('يرجى إدخال مبلغ صحيح');
      return false;
    }

    if (_paymentSummary != null &&
        amount > _paymentSummary!.remainingAmount + 0.01) {
      _showError('المبلغ المدخل أكبر من المبلغ المتبقي');
      return false;
    }

    if (_selectedMethod.requiresAdditionalInfo &&
        _referenceController.text.isEmpty) {
      _showError('يرجى إدخال المرجع لطريقة الدفع المختارة');
      return false;
    }

    return true;
  }

  void _clearForm() {
    _amountController.text =
        _paymentSummary?.remainingAmount.toStringAsFixed(2) ?? '0.00';
    _referenceController.clear();
    _bankNameController.clear();
    _accountNumberController.clear();
    _notesController.clear();
    _selectedMethod = PaymentMethod.cash;
    _selectedDate = DateTime.now();
  }

  void _showError(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message), backgroundColor: Colors.red),
    );
  }

  void _showSuccess(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: RevolutionaryColors.successGlow,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  Future<void> _selectDate() async {
    final date = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime(2020),
      lastDate: DateTime.now().add(const Duration(days: 30)),
    );

    if (date != null) {
      setState(() => _selectedDate = date);
    }
  }

  Future<void> _selectScheduleStartDate() async {
    final date = await showDatePicker(
      context: context,
      initialDate: _scheduleStartDate,
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );

    if (date != null) {
      setState(() => _scheduleStartDate = date);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        height: MediaQuery.of(context).size.height * 0.8,
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildHeader(),
            const SizedBox(height: 16),
            if (_isLoading)
              const Expanded(child: LoadingWidget())
            else ...[
              _buildPaymentSummary(),
              const SizedBox(height: 16),
              // التبويبات الجديدة
              TabBar(
                controller: _tabController,
                labelColor: RevolutionaryColors.damascusSky,
                unselectedLabelColor: RevolutionaryColors.textSecondary,
                indicatorColor: RevolutionaryColors.damascusSky,
                tabs: const [
                  Tab(text: 'الدفعات'),
                  Tab(text: 'الجدولة'),
                  Tab(text: 'التذكيرات'),
                ],
              ),
              const SizedBox(height: 16),
              Expanded(
                child: TabBarView(
                  controller: _tabController,
                  children: [
                    _buildPaymentsTab(),
                    _buildSchedulingTab(),
                    _buildRemindersTab(),
                  ],
                ),
              ),
            ],
            const SizedBox(height: 16),
            _buildButtons(),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      children: [
        Icon(Icons.payment, color: RevolutionaryColors.damascusSky),
        const SizedBox(width: 8),
        Text(
          'إدارة دفعات الفاتورة ${widget.invoice.invoiceNumber}',
          style: Theme.of(
            context,
          ).textTheme.headlineSmall?.copyWith(fontWeight: FontWeight.bold),
        ),
        const Spacer(),
        IconButton(
          onPressed: () => Navigator.of(context).pop(),
          icon: const Icon(Icons.close),
        ),
      ],
    );
  }

  Widget _buildPaymentSummary() {
    if (_paymentSummary == null) return const SizedBox.shrink();

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'ملخص الدفعات',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: _buildSummaryItem(
                    'إجمالي الفاتورة',
                    _paymentSummary!.totalAmount,
                    RevolutionaryColors.damascusSky,
                  ),
                ),
                Expanded(
                  child: _buildSummaryItem(
                    'المبلغ المدفوع',
                    _paymentSummary!.paidAmount,
                    RevolutionaryColors.successGlow,
                  ),
                ),
                Expanded(
                  child: _buildSummaryItem(
                    'المبلغ المتبقي',
                    _paymentSummary!.remainingAmount,
                    _paymentSummary!.remainingAmount > 0
                        ? Colors.orange
                        : RevolutionaryColors.successGlow,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            LinearProgressIndicator(
              value: _paymentSummary!.paidPercentage / 100,
              backgroundColor: Colors.grey[300],
              valueColor: AlwaysStoppedAnimation<Color>(
                _paymentSummary!.isFullyPaid
                    ? RevolutionaryColors.successGlow
                    : Colors.orange,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              'نسبة الدفع: ${_paymentSummary!.paidPercentage.toStringAsFixed(1)}%',
              style: Theme.of(context).textTheme.bodySmall,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryItem(String label, double amount, Color color) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall,
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 4),
        Text(
          '${amount.toStringAsFixed(2)} ل.س',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: color,
            fontSize: 16,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  // التبويبات الجديدة
  Widget _buildPaymentsTab() {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Expanded(flex: 2, child: _buildPaymentForm()),
        const SizedBox(width: 16),
        Expanded(flex: 3, child: _buildPaymentsList()),
      ],
    );
  }

  Widget _buildSchedulingTab() {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (_activeSchedule != null) ...[
            _buildActiveScheduleCard(),
            const SizedBox(height: 16),
          ],
          _buildScheduleForm(),
        ],
      ),
    );
  }

  Widget _buildRemindersTab() {
    return FutureBuilder<List<PaymentNotification>>(
      future: _reminderService.getAllNotifications(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const LoadingWidget();
        }

        if (snapshot.hasError) {
          return Center(child: Text('خطأ: ${snapshot.error}'));
        }

        final notifications = snapshot.data ?? [];

        if (notifications.isEmpty) {
          return const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.notifications_none,
                  size: 64,
                  color: RevolutionaryColors.textSecondary,
                ),
                SizedBox(height: 16),
                Text(
                  'لا توجد تذكيرات',
                  style: TextStyle(
                    fontSize: 18,
                    color: RevolutionaryColors.textSecondary,
                  ),
                ),
              ],
            ),
          );
        }

        return ListView.builder(
          itemCount: notifications.length,
          itemBuilder: (context, index) {
            final notification = notifications[index];
            return _buildNotificationCard(notification);
          },
        );
      },
    );
  }

  Widget _buildPaymentForm() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'إضافة دفعة جديدة',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: _amountController,
              keyboardType: TextInputType.number,
              inputFormatters: [
                FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d{0,2}')),
              ],
              decoration: InputDecoration(
                labelText: 'المبلغ',
                suffixText: 'ل.س',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
            const SizedBox(height: 12),
            DropdownButtonFormField<PaymentMethod>(
              value: _selectedMethod,
              decoration: InputDecoration(
                labelText: 'طريقة الدفع',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              items: PaymentMethod.values.map((method) {
                return DropdownMenuItem(
                  value: method,
                  child: Row(
                    children: [
                      Icon(Icons.payment, size: 20),
                      const SizedBox(width: 8),
                      Text(method.displayName),
                    ],
                  ),
                );
              }).toList(),
              onChanged: (method) {
                setState(() => _selectedMethod = method!);
              },
            ),
            const SizedBox(height: 12),
            InkWell(
              onTap: _selectDate,
              child: InputDecorator(
                decoration: InputDecoration(
                  labelText: 'تاريخ الدفع',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: Row(
                  children: [
                    Icon(Icons.calendar_today, size: 20),
                    const SizedBox(width: 8),
                    Text(
                      '${_selectedDate.day}/${_selectedDate.month}/${_selectedDate.year}',
                    ),
                  ],
                ),
              ),
            ),
            if (_selectedMethod.requiresAdditionalInfo) ...[
              const SizedBox(height: 12),
              TextField(
                controller: _referenceController,
                decoration: InputDecoration(
                  labelText: 'المرجع (رقم الشيك/التحويل)',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
              ),
              if (_selectedMethod == PaymentMethod.bankTransfer) ...[
                const SizedBox(height: 12),
                TextField(
                  controller: _bankNameController,
                  decoration: InputDecoration(
                    labelText: 'اسم البنك',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                ),
                const SizedBox(height: 12),
                TextField(
                  controller: _accountNumberController,
                  decoration: InputDecoration(
                    labelText: 'رقم الحساب',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                ),
              ],
            ],
            const SizedBox(height: 12),
            TextField(
              controller: _notesController,
              maxLines: 2,
              decoration: InputDecoration(
                labelText: 'ملاحظات',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
            const SizedBox(height: 16),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _isSubmitting ? null : _addPayment,
                style: ElevatedButton.styleFrom(
                  backgroundColor: RevolutionaryColors.damascusSky,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
                child: _isSubmitting
                    ? const SizedBox(
                        height: 20,
                        width: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(
                            Colors.white,
                          ),
                        ),
                      )
                    : const Text('إضافة الدفعة'),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPaymentsList() {
    if (_paymentSummary == null || _paymentSummary!.payments.isEmpty) {
      return Card(
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.payment_outlined, size: 48, color: Colors.grey),
              const SizedBox(height: 8),
              Text(
                'لا توجد دفعات مسجلة',
                style: TextStyle(color: Colors.grey[600]),
              ),
            ],
          ),
        ),
      );
    }

    return Card(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Text(
              'الدفعات المسجلة (${_paymentSummary!.payments.length})',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(
            child: ListView.builder(
              itemCount: _paymentSummary!.payments.length,
              itemBuilder: (context, index) {
                final payment = _paymentSummary!.payments[index];
                return _buildPaymentItem(payment);
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPaymentItem(Payment payment) {
    return ListTile(
      leading: CircleAvatar(
        backgroundColor: Color(
          int.parse('0xFF${payment.status.colorCode.substring(1)}'),
        ),
        child: Icon(Icons.payment, color: Colors.white, size: 20),
      ),
      title: Text(
        '${payment.amount.toStringAsFixed(2)} ل.س',
        style: const TextStyle(fontWeight: FontWeight.bold),
      ),
      subtitle: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(payment.method.displayName),
          Text(
            '${payment.paymentDate.day}/${payment.paymentDate.month}/${payment.paymentDate.year}',
          ),
          if (payment.reference != null) Text('المرجع: ${payment.reference}'),
        ],
      ),
      trailing: Chip(
        label: Text(
          payment.status.displayName,
          style: const TextStyle(fontSize: 12),
        ),
        backgroundColor: Color(
          int.parse('0xFF${payment.status.colorCode.substring(1)}'),
        ).withValues(alpha: 0.2),
      ),
    );
  }

  Widget _buildButtons() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('إغلاق'),
        ),
      ],
    );
  }

  // دوال الجدولة والتذكيرات
  Widget _buildActiveScheduleCard() {
    if (_activeSchedule == null) return const SizedBox.shrink();

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.schedule, color: RevolutionaryColors.damascusSky),
                const SizedBox(width: 8),
                Text(
                  'الجدولة النشطة: ${_activeSchedule!.scheduleName}',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: _buildScheduleInfo(
                    'التكرار',
                    _activeSchedule!.frequency.displayName,
                  ),
                ),
                Expanded(
                  child: _buildScheduleInfo(
                    'قيمة القسط',
                    '${_activeSchedule!.installmentAmount.toStringAsFixed(2)} ل.س',
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Expanded(
                  child: _buildScheduleInfo(
                    'الأقساط المكتملة',
                    '${_activeSchedule!.completedInstallments}/${_activeSchedule!.totalInstallments}',
                  ),
                ),
                Expanded(
                  child: _buildScheduleInfo(
                    'الدفعة التالية',
                    _formatDate(_activeSchedule!.nextPaymentDate),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            LinearProgressIndicator(
              value: _activeSchedule!.completionPercentage / 100,
              backgroundColor: RevolutionaryColors.borderLight,
              valueColor: AlwaysStoppedAnimation<Color>(
                RevolutionaryColors.damascusSky,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'نسبة الإكمال: ${_activeSchedule!.completionPercentage.toStringAsFixed(1)}%',
              style: const TextStyle(
                fontSize: 12,
                color: RevolutionaryColors.textSecondary,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildScheduleInfo(String label, String value) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: const TextStyle(
            fontSize: 12,
            color: RevolutionaryColors.textSecondary,
          ),
        ),
        Text(
          value,
          style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
        ),
      ],
    );
  }

  Widget _buildScheduleForm() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              _activeSchedule != null ? 'تعديل الجدولة' : 'إنشاء جدولة جديدة',
              style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: _scheduleNameController,
              decoration: const InputDecoration(
                labelText: 'اسم الجدولة',
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: TextField(
                    controller: _installmentAmountController,
                    decoration: const InputDecoration(
                      labelText: 'قيمة القسط',
                      border: OutlineInputBorder(),
                      suffixText: 'ل.س',
                    ),
                    keyboardType: TextInputType.number,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: TextField(
                    controller: _totalInstallmentsController,
                    decoration: const InputDecoration(
                      labelText: 'عدد الأقساط',
                      border: OutlineInputBorder(),
                    ),
                    keyboardType: TextInputType.number,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            DropdownButtonFormField<PaymentFrequency>(
              value: _selectedFrequency,
              decoration: const InputDecoration(
                labelText: 'تكرار الدفع',
                border: OutlineInputBorder(),
              ),
              items: PaymentFrequency.values.map((frequency) {
                return DropdownMenuItem(
                  value: frequency,
                  child: Text(frequency.displayName),
                );
              }).toList(),
              onChanged: (value) {
                if (value != null) {
                  setState(() => _selectedFrequency = value);
                }
              },
            ),
            const SizedBox(height: 12),
            InkWell(
              onTap: _selectScheduleStartDate,
              child: InputDecorator(
                decoration: const InputDecoration(
                  labelText: 'تاريخ بدء الجدولة',
                  border: OutlineInputBorder(),
                ),
                child: Row(
                  children: [
                    const Icon(Icons.calendar_today, size: 20),
                    const SizedBox(width: 8),
                    Text(
                      '${_scheduleStartDate.day}/${_scheduleStartDate.month}/${_scheduleStartDate.year}',
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 12),
            SwitchListTile(
              title: const Text('دفع تلقائي'),
              subtitle: const Text('تفعيل الدفع التلقائي للأقساط'),
              value: _autoPayment,
              onChanged: (value) {
                setState(() => _autoPayment = value);
              },
            ),
            const SizedBox(height: 16),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _activeSchedule != null
                    ? _updateSchedule
                    : _createSchedule,
                style: ElevatedButton.styleFrom(
                  backgroundColor: RevolutionaryColors.damascusSky,
                  foregroundColor: RevolutionaryColors.textOnDark,
                ),
                child: Text(
                  _activeSchedule != null ? 'تحديث الجدولة' : 'إنشاء الجدولة',
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNotificationCard(PaymentNotification notification) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: _getNotificationColor(notification.type),
          child: Icon(
            _getNotificationIcon(notification.type),
            color: RevolutionaryColors.textOnDark,
            size: 20,
          ),
        ),
        title: Text(
          notification.title,
          style: TextStyle(
            fontWeight: notification.isRead
                ? FontWeight.normal
                : FontWeight.bold,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(notification.message),
            const SizedBox(height: 4),
            Text(
              _formatDate(notification.dueDate),
              style: const TextStyle(
                fontSize: 12,
                color: RevolutionaryColors.textSecondary,
              ),
            ),
          ],
        ),
        trailing: notification.isRead
            ? null
            : Container(
                width: 8,
                height: 8,
                decoration: const BoxDecoration(
                  color: RevolutionaryColors.errorCoral,
                  shape: BoxShape.circle,
                ),
              ),
        onTap: () => _markNotificationAsRead(notification),
      ),
    );
  }

  Color _getNotificationColor(NotificationType type) {
    switch (type) {
      case NotificationType.reminder:
        return RevolutionaryColors.damascusSky;
      case NotificationType.warning:
        return RevolutionaryColors.warningAmber;
      case NotificationType.urgent:
        return RevolutionaryColors.errorCoral;
      case NotificationType.overdue:
        return RevolutionaryColors.errorCoral;
    }
  }

  IconData _getNotificationIcon(NotificationType type) {
    switch (type) {
      case NotificationType.reminder:
        return Icons.notifications;
      case NotificationType.warning:
        return Icons.warning;
      case NotificationType.urgent:
        return Icons.priority_high;
      case NotificationType.overdue:
        return Icons.error;
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day.toString().padLeft(2, '0')}/'
        '${date.month.toString().padLeft(2, '0')}/'
        '${date.year}';
  }

  // دوال الإجراءات
  void _createSchedule() async {
    if (!_validateScheduleInput()) return;

    setState(() => _isSubmitting = true);

    try {
      final installmentAmount = double.parse(_installmentAmountController.text);
      final totalInstallments = int.parse(_totalInstallmentsController.text);

      // حساب تاريخ الدفعة التالية بناءً على التكرار
      final nextPaymentDate = _calculateNextPaymentDate();

      // إنشاء جدولة جديدة
      final schedule = PaymentSchedule(
        invoiceId: widget.invoice.id!,
        scheduleName: _scheduleNameController.text,
        totalAmount:
            _paymentSummary?.remainingAmount ?? widget.invoice.totalAmount,
        frequency: _selectedFrequency,
        startDate: _scheduleStartDate,
        endDate: _scheduleEndDate,
        nextPaymentDate: nextPaymentDate,
        installmentAmount: installmentAmount,
        totalInstallments: totalInstallments,
        autoPayment: _autoPayment,
        notes: 'جدولة دفعات للفاتورة ${widget.invoice.invoiceNumber}',
        reminders: _createDefaultReminders(),
      );

      // إنشاء الجدولة في قاعدة البيانات
      await _scheduleService.createPaymentSchedule(schedule);

      // إعادة تحميل البيانات
      await _loadActiveSchedule();

      // مسح النموذج
      _clearScheduleForm();

      _showSuccess('تم إنشاء جدولة الدفعات بنجاح');

      // الانتقال إلى تبويب التذكيرات لعرض الجدولة الجديدة
      _tabController.animateTo(1);
    } catch (e) {
      _showError('خطأ في إنشاء جدولة الدفعات: $e');
    } finally {
      setState(() => _isSubmitting = false);
    }
  }

  void _updateSchedule() async {
    if (_activeSchedule == null) {
      _showError('لا توجد جدولة نشطة للتحديث');
      return;
    }

    if (!_validateScheduleInput()) return;

    setState(() => _isSubmitting = true);

    try {
      final installmentAmount = double.parse(_installmentAmountController.text);
      final totalInstallments = int.parse(_totalInstallmentsController.text);

      // حساب تاريخ الدفعة التالية بناءً على التكرار
      final nextPaymentDate = _calculateNextPaymentDate();

      // تحديث الجدولة الحالية
      final updatedSchedule = _activeSchedule!.copyWith(
        scheduleName: _scheduleNameController.text,
        frequency: _selectedFrequency,
        startDate: _scheduleStartDate,
        endDate: _scheduleEndDate,
        nextPaymentDate: nextPaymentDate,
        installmentAmount: installmentAmount,
        totalInstallments: totalInstallments,
        autoPayment: _autoPayment,
        notes: 'تم تحديث جدولة دفعات الفاتورة ${widget.invoice.invoiceNumber}',
        reminders: _createDefaultReminders()
            .map(
              (reminder) => reminder.copyWith(scheduleId: _activeSchedule!.id!),
            )
            .toList(),
      );

      // تحديث الجدولة في قاعدة البيانات
      await _scheduleService.updatePaymentSchedule(updatedSchedule);

      // إعادة تحميل البيانات
      await _loadActiveSchedule();

      _showSuccess('تم تحديث جدولة الدفعات بنجاح');
    } catch (e) {
      _showError('خطأ في تحديث جدولة الدفعات: $e');
    } finally {
      setState(() => _isSubmitting = false);
    }
  }

  // دوال مساعدة للجدولة
  bool _validateScheduleInput() {
    if (_scheduleNameController.text.isEmpty) {
      _showError('يرجى إدخال اسم الجدولة');
      return false;
    }

    final installmentAmount = double.tryParse(
      _installmentAmountController.text,
    );
    if (installmentAmount == null || installmentAmount <= 0) {
      _showError('يرجى إدخال قيمة قسط صحيحة');
      return false;
    }

    final totalInstallments = int.tryParse(_totalInstallmentsController.text);
    if (totalInstallments == null || totalInstallments <= 0) {
      _showError('يرجى إدخال عدد أقساط صحيح');
      return false;
    }

    final remainingAmount =
        _paymentSummary?.remainingAmount ?? widget.invoice.totalAmount;
    final totalScheduleAmount = installmentAmount * totalInstallments;

    if (totalScheduleAmount > remainingAmount + 0.01) {
      _showError('إجمالي مبلغ الأقساط أكبر من المبلغ المتبقي');
      return false;
    }

    if (_scheduleStartDate.isAfter(
      DateTime.now().add(const Duration(days: 365)),
    )) {
      _showError('تاريخ البداية بعيد جداً');
      return false;
    }

    return true;
  }

  DateTime _calculateNextPaymentDate() {
    DateTime nextDate = _scheduleStartDate;

    switch (_selectedFrequency) {
      case PaymentFrequency.daily:
        nextDate = _scheduleStartDate.add(const Duration(days: 1));
        break;
      case PaymentFrequency.weekly:
        nextDate = _scheduleStartDate.add(const Duration(days: 7));
        break;
      case PaymentFrequency.biweekly:
        nextDate = _scheduleStartDate.add(const Duration(days: 14));
        break;
      case PaymentFrequency.monthly:
        nextDate = DateTime(
          _scheduleStartDate.year,
          _scheduleStartDate.month + 1,
          _scheduleStartDate.day,
        );
        break;
      case PaymentFrequency.quarterly:
        nextDate = DateTime(
          _scheduleStartDate.year,
          _scheduleStartDate.month + 3,
          _scheduleStartDate.day,
        );
        break;
      case PaymentFrequency.semiAnnual:
        nextDate = DateTime(
          _scheduleStartDate.year,
          _scheduleStartDate.month + 6,
          _scheduleStartDate.day,
        );
        break;
      case PaymentFrequency.annual:
        nextDate = DateTime(
          _scheduleStartDate.year + 1,
          _scheduleStartDate.month,
          _scheduleStartDate.day,
        );
        break;
      case PaymentFrequency.once:
        nextDate = _scheduleStartDate;
        break;
    }

    return nextDate;
  }

  List<PaymentReminder> _createDefaultReminders() {
    return [
      PaymentReminder(
        scheduleId: 0, // سيتم تحديثه عند الحفظ
        type: ReminderType.notification,
        daysBefore: 3,
        title: 'تذكير دفعة قادمة',
        message:
            'لديك دفعة مستحقة خلال 3 أيام للفاتورة ${widget.invoice.invoiceNumber}',
        isActive: true,
      ),
      PaymentReminder(
        scheduleId: 0, // سيتم تحديثه عند الحفظ
        type: ReminderType.inApp,
        daysBefore: 1,
        title: 'تحذير دفعة مستحقة',
        message:
            'لديك دفعة مستحقة غداً للفاتورة ${widget.invoice.invoiceNumber}',
        isActive: true,
      ),
    ];
  }

  void _clearScheduleForm() {
    _scheduleNameController.clear();
    _installmentAmountController.clear();
    _totalInstallmentsController.clear();
    _selectedFrequency = PaymentFrequency.monthly;
    _scheduleStartDate = DateTime.now();
    _scheduleEndDate = null;
    _autoPayment = false;
  }

  void _markNotificationAsRead(PaymentNotification notification) async {
    try {
      await _reminderService.markAsRead(notification.id!);
      setState(() {}); // إعادة تحميل التذكيرات
    } catch (e) {
      _showError('خطأ في تحديث التذكير: $e');
    }
  }
}
