/// شاشة إضافة عرض سعر جديد
/// تسمح بإنشاء عرض سعر جديد مع الأصناف والتفاصيل
library;

import 'package:flutter/material.dart';
import '../models/quotation.dart';
import '../models/customer.dart';
import '../models/supplier.dart';
import '../models/item.dart';
import '../services/quotation_service.dart';
import '../services/customer_service.dart';
import '../services/supplier_service.dart';
import '../services/logging_service.dart';
import '../constants/revolutionary_design_colors.dart';
import '../constants/app_constants.dart';
import '../widgets/loading_widget.dart';
import '../widgets/item_selection_dialog.dart';

class AddQuotationScreen extends StatefulWidget {
  final Quotation? quotation; // للتعديل

  const AddQuotationScreen({super.key, this.quotation});

  @override
  State<AddQuotationScreen> createState() => _AddQuotationScreenState();
}

class _AddQuotationScreenState extends State<AddQuotationScreen> {
  final QuotationService _quotationService = QuotationService();
  final CustomerService _customerService = CustomerService();
  final SupplierService _supplierService = SupplierService();

  final _formKey = GlobalKey<FormState>();
  final _quotationNumberController = TextEditingController();
  final _notesController = TextEditingController();
  final _termsController = TextEditingController();
  final _referenceController = TextEditingController();

  List<Customer> _customers = [];
  List<Supplier> _suppliers = [];
  List<QuotationItem> _quotationItems = [];

  DateTime _quotationDate = DateTime.now();
  DateTime? _validUntil;
  Customer? _selectedCustomer;
  Supplier? _selectedSupplier;
  String _quotationType = 'sale'; // sale or purchase
  bool _isLoading = true;
  bool _isSaving = false;

  double _subtotal = 0.0;
  double _taxAmount = 0.0;
  double _discountAmount = 0.0;
  double _totalAmount = 0.0;

  @override
  void initState() {
    super.initState();
    _loadData();
    if (widget.quotation != null) {
      _loadQuotationData();
    } else {
      _generateQuotationNumber();
    }
  }

  @override
  void dispose() {
    _quotationNumberController.dispose();
    _notesController.dispose();
    _termsController.dispose();
    _referenceController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    try {
      setState(() => _isLoading = true);

      final customers = await _customerService.getActiveCustomers();
      final suppliers = await _supplierService.getActiveSuppliers();

      setState(() {
        _customers = customers;
        _suppliers = suppliers;
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      _showErrorSnackBar('خطأ في تحميل البيانات: $e');
    }
  }

  void _loadQuotationData() {
    final quotation = widget.quotation!;
    _quotationNumberController.text = quotation.quotationNumber;
    _quotationDate = quotation.quotationDate;
    _validUntil = quotation.validUntil;
    _notesController.text = quotation.notes ?? '';
    _termsController.text = quotation.terms ?? '';
    _referenceController.text = quotation.reference ?? '';
    _quotationItems = List.from(quotation.items);

    if (quotation.customerId != null) {
      _quotationType = 'sale';
      _selectedCustomer = _customers.firstWhere(
        (c) => c.id == quotation.customerId,
        orElse: () => _customers.first,
      );
    } else if (quotation.supplierId != null) {
      _quotationType = 'purchase';
      _selectedSupplier = _suppliers.firstWhere(
        (s) => s.id == quotation.supplierId,
        orElse: () => _suppliers.first,
      );
    }

    _calculateTotals();
  }

  Future<void> _generateQuotationNumber() async {
    try {
      final quotationNumber = await _quotationService.generateQuotationNumber();
      _quotationNumberController.text = quotationNumber;
    } catch (e) {
      _showErrorSnackBar('خطأ في إنشاء رقم عرض السعر: $e');
    }
  }

  void _calculateTotals() {
    double subtotal = 0.0;
    double taxAmount = 0.0;
    double discountAmount = 0.0;

    for (final item in _quotationItems) {
      subtotal += item.totalPrice;
      taxAmount += item.taxAmount;
      discountAmount += item.discountAmount;
    }

    setState(() {
      _subtotal = subtotal;
      _taxAmount = taxAmount;
      _discountAmount = discountAmount;
      _totalAmount = subtotal + taxAmount - discountAmount;
    });
  }

  Future<void> _addItem() async {
    final selectedItem = await showDialog<Item>(
      context: context,
      builder: (context) =>
          ItemSelectionDialog(invoiceType: AppConstants.invoiceTypeSale),
    );

    if (selectedItem != null) {
      final quotationItem = QuotationItem(
        quotationId: 0,
        itemId: selectedItem.id!,
        itemName: selectedItem.name,
        itemCode: selectedItem.code,
        quantity: 1.0,
        unitPrice: selectedItem.sellingPrice,
        totalPrice: selectedItem.sellingPrice,
        netAmount: selectedItem.sellingPrice,
      );

      setState(() {
        _quotationItems.add(quotationItem);
      });
      _calculateTotals();
    }
  }

  void _removeItem(int index) {
    setState(() {
      _quotationItems.removeAt(index);
    });
    _calculateTotals();
  }

  void _updateItemQuantity(int index, double quantity) {
    final item = _quotationItems[index];
    final totalPrice = quantity * item.unitPrice;
    final updatedItem = item.copyWith(
      quantity: quantity,
      totalPrice: totalPrice,
      netAmount: totalPrice - item.discountAmount + item.taxAmount,
    );

    setState(() {
      _quotationItems[index] = updatedItem;
    });
    _calculateTotals();
  }

  Future<void> _saveQuotation() async {
    if (!_formKey.currentState!.validate()) return;

    if (_quotationItems.isEmpty) {
      _showErrorSnackBar('يجب إضافة صنف واحد على الأقل');
      return;
    }

    if (_quotationType == 'sale' && _selectedCustomer == null) {
      _showErrorSnackBar('يجب اختيار عميل');
      return;
    }

    if (_quotationType == 'purchase' && _selectedSupplier == null) {
      _showErrorSnackBar('يجب اختيار مورد');
      return;
    }

    setState(() => _isSaving = true);

    try {
      final quotation = Quotation(
        id: widget.quotation?.id,
        quotationNumber: _quotationNumberController.text,
        quotationDate: _quotationDate,
        validUntil: _validUntil,
        customerId: _quotationType == 'sale' ? _selectedCustomer?.id : null,
        supplierId: _quotationType == 'purchase' ? _selectedSupplier?.id : null,
        subtotal: _subtotal,
        taxAmount: _taxAmount,
        discountAmount: _discountAmount,
        totalAmount: _totalAmount,
        currencyId: 1, // افتراضي للعملة السورية
        notes: _notesController.text.trim().isEmpty
            ? null
            : _notesController.text.trim(),
        terms: _termsController.text.trim().isEmpty
            ? null
            : _termsController.text.trim(),
        reference: _referenceController.text.trim().isEmpty
            ? null
            : _referenceController.text.trim(),
        items: _quotationItems,
      );

      if (widget.quotation == null) {
        await _quotationService.insertQuotation(quotation);
        _showSuccessSnackBar('تم إنشاء عرض السعر بنجاح');
      } else {
        await _quotationService.updateQuotation(quotation);
        _showSuccessSnackBar('تم تحديث عرض السعر بنجاح');
      }

      if (mounted) {
        Navigator.of(context).pop(true);
      }
    } catch (e) {
      _showErrorSnackBar('خطأ في حفظ عرض السعر: $e');
      LoggingService.error(
        'خطأ في حفظ عرض السعر',
        category: 'AddQuotationScreen',
        data: {'error': e.toString()},
      );
    } finally {
      setState(() => _isSaving = false);
    }
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: RevolutionaryColors.successGlow,
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: RevolutionaryColors.errorCoral,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          widget.quotation == null ? 'إضافة عرض سعر' : 'تعديل عرض السعر',
        ),
        backgroundColor: RevolutionaryColors.damascusSky,
        foregroundColor: Colors.white,
        actions: [
          if (_isSaving)
            const Padding(
              padding: EdgeInsets.all(16),
              child: SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              ),
            )
          else
            TextButton(
              onPressed: _saveQuotation,
              child: const Text('حفظ', style: TextStyle(color: Colors.white)),
            ),
        ],
      ),
      body: _isLoading
          ? const LoadingWidget()
          : Form(
              key: _formKey,
              child: Column(
                children: [
                  Expanded(
                    child: SingleChildScrollView(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          _buildBasicInfo(),
                          const SizedBox(height: 24),
                          _buildCustomerSupplierSelection(),
                          const SizedBox(height: 24),
                          _buildItemsSection(),
                          const SizedBox(height: 24),
                          _buildTotalsSection(),
                          const SizedBox(height: 24),
                          _buildNotesSection(),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
    );
  }

  Widget _buildBasicInfo() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'المعلومات الأساسية',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _quotationNumberController,
              decoration: const InputDecoration(
                labelText: 'رقم عرض السعر',
                border: OutlineInputBorder(),
              ),
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'رقم عرض السعر مطلوب';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: InkWell(
                    onTap: () async {
                      final date = await showDatePicker(
                        context: context,
                        initialDate: _quotationDate,
                        firstDate: DateTime(2020),
                        lastDate: DateTime(2030),
                      );
                      if (date != null) {
                        setState(() => _quotationDate = date);
                      }
                    },
                    child: InputDecorator(
                      decoration: const InputDecoration(
                        labelText: 'تاريخ عرض السعر',
                        border: OutlineInputBorder(),
                      ),
                      child: Text(
                        '${_quotationDate.day}/${_quotationDate.month}/${_quotationDate.year}',
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: InkWell(
                    onTap: () async {
                      final date = await showDatePicker(
                        context: context,
                        initialDate:
                            _validUntil ??
                            DateTime.now().add(const Duration(days: 30)),
                        firstDate: DateTime.now(),
                        lastDate: DateTime(2030),
                      );
                      if (date != null) {
                        setState(() => _validUntil = date);
                      }
                    },
                    child: InputDecorator(
                      decoration: const InputDecoration(
                        labelText: 'صالح حتى (اختياري)',
                        border: OutlineInputBorder(),
                      ),
                      child: Text(
                        _validUntil != null
                            ? '${_validUntil!.day}/${_validUntil!.month}/${_validUntil!.year}'
                            : 'اختر التاريخ',
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCustomerSupplierSelection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'العميل/المورد',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: RadioListTile<String>(
                    title: const Text('عميل'),
                    value: 'sale',
                    groupValue: _quotationType,
                    onChanged: (value) {
                      setState(() {
                        _quotationType = value!;
                        _selectedCustomer = null;
                        _selectedSupplier = null;
                      });
                    },
                  ),
                ),
                Expanded(
                  child: RadioListTile<String>(
                    title: const Text('مورد'),
                    value: 'purchase',
                    groupValue: _quotationType,
                    onChanged: (value) {
                      setState(() {
                        _quotationType = value!;
                        _selectedCustomer = null;
                        _selectedSupplier = null;
                      });
                    },
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            if (_quotationType == 'sale')
              DropdownButtonFormField<Customer>(
                value: _selectedCustomer,
                decoration: const InputDecoration(
                  labelText: 'اختر العميل',
                  border: OutlineInputBorder(),
                ),
                items: _customers.map((customer) {
                  return DropdownMenuItem<Customer>(
                    value: customer,
                    child: Text(customer.name),
                  );
                }).toList(),
                onChanged: (customer) {
                  setState(() => _selectedCustomer = customer);
                },
                validator: (value) {
                  if (_quotationType == 'sale' && value == null) {
                    return 'يجب اختيار عميل';
                  }
                  return null;
                },
              )
            else
              DropdownButtonFormField<Supplier>(
                value: _selectedSupplier,
                decoration: const InputDecoration(
                  labelText: 'اختر المورد',
                  border: OutlineInputBorder(),
                ),
                items: _suppliers.map((supplier) {
                  return DropdownMenuItem<Supplier>(
                    value: supplier,
                    child: Text(supplier.name),
                  );
                }).toList(),
                onChanged: (supplier) {
                  setState(() => _selectedSupplier = supplier);
                },
                validator: (value) {
                  if (_quotationType == 'purchase' && value == null) {
                    return 'يجب اختيار مورد';
                  }
                  return null;
                },
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildItemsSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'الأصناف',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
                ElevatedButton.icon(
                  onPressed: _addItem,
                  icon: const Icon(Icons.add),
                  label: const Text('إضافة صنف'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: RevolutionaryColors.damascusSky,
                    foregroundColor: Colors.white,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            if (_quotationItems.isEmpty)
              Container(
                padding: const EdgeInsets.all(32),
                child: Center(
                  child: Column(
                    children: [
                      Icon(
                        Icons.inventory_2_outlined,
                        size: 48,
                        color: Colors.grey[400],
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'لم يتم إضافة أصناف بعد',
                        style: TextStyle(color: Colors.grey[600], fontSize: 16),
                      ),
                    ],
                  ),
                ),
              )
            else
              ListView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: _quotationItems.length,
                itemBuilder: (context, index) {
                  final item = _quotationItems[index];
                  return Card(
                    margin: const EdgeInsets.only(bottom: 8),
                    child: Padding(
                      padding: const EdgeInsets.all(12),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Expanded(
                                child: Text(
                                  '${item.itemCode} - ${item.itemName}',
                                  style: const TextStyle(
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ),
                              IconButton(
                                onPressed: () => _removeItem(index),
                                icon: const Icon(
                                  Icons.delete,
                                  color: Colors.red,
                                ),
                                tooltip: 'حذف الصنف',
                              ),
                            ],
                          ),
                          const SizedBox(height: 8),
                          Row(
                            children: [
                              Expanded(
                                child: TextFormField(
                                  initialValue: item.quantity.toString(),
                                  decoration: const InputDecoration(
                                    labelText: 'الكمية',
                                    border: OutlineInputBorder(),
                                  ),
                                  keyboardType: TextInputType.number,
                                  onChanged: (value) {
                                    final quantity =
                                        double.tryParse(value) ?? 0.0;
                                    _updateItemQuantity(index, quantity);
                                  },
                                ),
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                child: TextFormField(
                                  initialValue: item.unitPrice.toString(),
                                  decoration: const InputDecoration(
                                    labelText: 'السعر',
                                    border: OutlineInputBorder(),
                                  ),
                                  keyboardType: TextInputType.number,
                                  readOnly: true,
                                ),
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                child: TextFormField(
                                  initialValue: item.totalPrice.toString(),
                                  decoration: const InputDecoration(
                                    labelText: 'الإجمالي',
                                    border: OutlineInputBorder(),
                                  ),
                                  readOnly: true,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  );
                },
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildTotalsSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'الإجماليات',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            _buildTotalRow('المجموع الفرعي:', _subtotal),
            _buildTotalRow('الضريبة:', _taxAmount),
            _buildTotalRow('الخصم:', _discountAmount),
            const Divider(),
            _buildTotalRow('الإجمالي النهائي:', _totalAmount, isTotal: true),
          ],
        ),
      ),
    );
  }

  Widget _buildTotalRow(String label, double amount, {bool isTotal = false}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: isTotal ? 16 : 14,
              fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
            ),
          ),
          Text(
            '${amount.toStringAsFixed(2)} ل.س',
            style: TextStyle(
              fontSize: isTotal ? 16 : 14,
              fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
              color: isTotal ? RevolutionaryColors.damascusSky : null,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNotesSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'ملاحظات وشروط',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _notesController,
              decoration: const InputDecoration(
                labelText: 'ملاحظات',
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _termsController,
              decoration: const InputDecoration(
                labelText: 'شروط الدفع',
                border: OutlineInputBorder(),
              ),
              maxLines: 2,
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _referenceController,
              decoration: const InputDecoration(
                labelText: 'المرجع',
                border: OutlineInputBorder(),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
