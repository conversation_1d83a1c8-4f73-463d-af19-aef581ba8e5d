# 🔧 دليل الصيانة والدعم - Smart Ledger

**الإصدار:** 1.0.0  
**التاريخ:** 15 يوليو 2025  
**المطور:** مجد محمد زياد يسير  

---

## 🎯 نظرة عامة

هذا الدليل يوفر إرشادات شاملة لصيانة ودعم تطبيق Smart Ledger، بما في ذلك الصيانة الدورية، حل المشاكل الشائعة، والدعم الفني.

---

## 🔄 الصيانة الدورية

### 1. **الصيانة اليومية**

#### فحص النظام اليومي
```bash
# فحص حالة التطبيق
flutter doctor

# فحص مساحة القرص
dir C:\ /s

# فحص استهلاك الذاكرة
tasklist /fi "imagename eq smart_ledger.exe"
```

#### النسخ الاحتياطية اليومية
- **التوقيت:** كل يوم في الساعة 2:00 صباحاً
- **المحتوى:** قاعدة البيانات الكاملة + ملفات الإعدادات
- **المكان:** مجلد منفصل مع التشفير
- **الاحتفاظ:** آخر 30 نسخة

```dart
// كود النسخ الاحتياطي التلقائي
class AutoBackupService {
  static Future<void> performDailyBackup() async {
    try {
      final backupService = BackupService();
      final timestamp = DateTime.now().toIso8601String();
      
      // إنشاء نسخة احتياطية مشفرة
      final backupPath = await backupService.createEncryptedBackup(
        'daily_backup_$timestamp',
        includeSettings: true,
        includeAuditLog: true,
      );
      
      // تنظيف النسخ القديمة (أكثر من 30 يوم)
      await backupService.cleanOldBackups(30);
      
      LoggingService.info('تم إنشاء النسخة الاحتياطية اليومية: $backupPath');
    } catch (e) {
      LoggingService.error('فشل في النسخ الاحتياطي اليومي: $e');
    }
  }
}
```

### 2. **الصيانة الأسبوعية**

#### تحسين قاعدة البيانات
```sql
-- إعادة فهرسة الجداول
REINDEX;

-- تحليل الإحصائيات
ANALYZE;

-- تنظيف المساحة الفارغة
VACUUM;
```

#### فحص سلامة البيانات
```dart
class WeeklyMaintenance {
  static Future<void> performWeeklyChecks() async {
    // 1. فحص سلامة قاعدة البيانات
    final dbHealth = await DatabaseHelper().checkDatabaseHealth();
    if (!dbHealth.isHealthy) {
      LoggingService.warning('مشاكل في قاعدة البيانات: ${dbHealth.issues}');
    }
    
    // 2. فحص توازن الحسابات
    final balanceCheck = await AccountService().checkAccountsBalance();
    if (!balanceCheck.isBalanced) {
      LoggingService.error('عدم توازن في الحسابات');
    }
    
    // 3. فحص المخزون
    final stockCheck = await ItemService().checkStockConsistency();
    if (stockCheck.hasIssues) {
      LoggingService.warning('مشاكل في المخزون: ${stockCheck.issues}');
    }
    
    // 4. تنظيف الملفات المؤقتة
    await _cleanTempFiles();
    
    // 5. تحديث الإحصائيات
    await _updateSystemStatistics();
  }
  
  static Future<void> _cleanTempFiles() async {
    final tempDir = Directory.systemTemp;
    final smartLedgerTemp = Directory('${tempDir.path}/smart_ledger');
    
    if (await smartLedgerTemp.exists()) {
      await smartLedgerTemp.delete(recursive: true);
    }
  }
  
  static Future<void> _updateSystemStatistics() async {
    final stats = {
      'totalAccounts': await AccountService().getAccountsCount(),
      'totalInvoices': await InvoiceService().getInvoicesCount(),
      'totalItems': await ItemService().getItemsCount(),
      'databaseSize': await DatabaseHelper().getDatabaseSize(),
      'lastUpdate': DateTime.now().toIso8601String(),
    };
    
    await SettingsService().saveSystemStatistics(stats);
  }
}
```

### 3. **الصيانة الشهرية**

#### تحليل الأداء
- **مراجعة سجلات الأداء** للشهر الماضي
- **تحديد الاختناقات** في النظام
- **تحسين الاستعلامات** البطيئة
- **مراجعة استهلاك الموارد**

#### تحديث النظام
- **فحص التحديثات المتاحة**
- **اختبار التحديثات** في بيئة منفصلة
- **تطبيق التحديثات** المعتمدة
- **توثيق التغييرات**

---

## 🚨 حل المشاكل الشائعة

### 1. **مشاكل قاعدة البيانات**

#### المشكلة: "Database is locked"
**الأعراض:**
- رسائل خطأ عند محاولة الوصول للبيانات
- تجمد التطبيق عند العمليات

**الحلول:**
```dart
// 1. إغلاق جميع الاتصالات المفتوحة
await DatabaseHelper().closeAllConnections();

// 2. إعادة تشغيل التطبيق
await SystemNavigator.pop();

// 3. فحص العمليات المعلقة
final activeTransactions = await DatabaseHelper().getActiveTransactions();
for (final transaction in activeTransactions) {
  await transaction.rollback();
}
```

#### المشكلة: "Database corruption detected"
**الأعراض:**
- أخطاء في قراءة البيانات
- بيانات مفقودة أو تالفة

**الحلول:**
```dart
// 1. استعادة من النسخة الاحتياطية
final backupService = BackupService();
final latestBackup = await backupService.getLatestBackup();
await backupService.restoreFromBackup(latestBackup);

// 2. إعادة بناء قاعدة البيانات
await DatabaseHelper().rebuildDatabase();

// 3. إعادة فهرسة الجداول
await DatabaseHelper().reindexAllTables();
```

### 2. **مشاكل الأداء**

#### المشكلة: "التطبيق بطيء"
**التشخيص:**
```dart
class PerformanceDiagnostics {
  static Future<PerformanceReport> diagnosePerformance() async {
    final report = PerformanceReport();
    
    // فحص استهلاك الذاكرة
    report.memoryUsage = await _getMemoryUsage();
    
    // فحص سرعة قاعدة البيانات
    report.databaseSpeed = await _testDatabaseSpeed();
    
    // فحص حجم الملفات
    report.filesSizes = await _getFilesSizes();
    
    // فحص العمليات البطيئة
    report.slowOperations = await _getSlowOperations();
    
    return report;
  }
  
  static Future<double> _testDatabaseSpeed() async {
    final stopwatch = Stopwatch()..start();
    await AccountService().getAllAccounts();
    stopwatch.stop();
    return stopwatch.elapsedMilliseconds.toDouble();
  }
}
```

**الحلول:**
```dart
// 1. تنظيف البيانات القديمة
await DataCleanupService().cleanOldData(
  olderThanDays: 365,
  keepImportantRecords: true,
);

// 2. ضغط قاعدة البيانات
await DatabaseHelper().compressDatabase();

// 3. تحسين الفهارس
await DatabaseHelper().optimizeIndexes();

// 4. تنظيف الذاكرة
await MemoryManager().clearCache();
```

### 3. **مشاكل التشفير**

#### المشكلة: "Invalid encryption key"
**الأعراض:**
- فشل في فتح قاعدة البيانات
- رسائل خطأ في التشفير

**الحلول:**
```dart
// 1. التحقق من صحة المفتاح
final isValidKey = await EncryptionService.validateKey(userKey);
if (!isValidKey) {
  // طلب إدخال المفتاح مرة أخرى
  await _promptForCorrectKey();
}

// 2. استعادة المفتاح من النسخة الاحتياطية
final backupKey = await EncryptionService.recoverKeyFromBackup();
if (backupKey != null) {
  await EncryptionService.updateKey(backupKey);
}

// 3. إعادة تشفير قاعدة البيانات
await EncryptionService.reencryptDatabase(newKey);
```

---

## 📊 مراقبة النظام

### 1. **مؤشرات الأداء الرئيسية (KPIs)**

```dart
class SystemMonitoring {
  static Future<SystemHealth> getSystemHealth() async {
    return SystemHealth(
      // أداء قاعدة البيانات
      databaseResponseTime: await _measureDatabaseResponse(),
      databaseSize: await DatabaseHelper().getDatabaseSize(),
      
      // استهلاك الموارد
      memoryUsage: await _getMemoryUsage(),
      diskUsage: await _getDiskUsage(),
      cpuUsage: await _getCpuUsage(),
      
      // إحصائيات الاستخدام
      activeUsers: await UserService().getActiveUsersCount(),
      dailyTransactions: await _getDailyTransactionsCount(),
      
      // حالة الخدمات
      servicesStatus: await _checkServicesStatus(),
      
      // أمان النظام
      lastBackup: await BackupService().getLastBackupTime(),
      encryptionStatus: await EncryptionService.getEncryptionStatus(),
    );
  }
  
  static Future<Map<String, bool>> _checkServicesStatus() async {
    return {
      'AccountService': await AccountService().isHealthy(),
      'InvoiceService': await InvoiceService().isHealthy(),
      'ItemService': await ItemService().isHealthy(),
      'BackupService': await BackupService().isHealthy(),
      'EncryptionService': await EncryptionService.isHealthy(),
    };
  }
}
```

### 2. **تنبيهات النظام**

```dart
class SystemAlerts {
  static Future<void> checkAndSendAlerts() async {
    final health = await SystemMonitoring.getSystemHealth();
    
    // تنبيه استهلاك الذاكرة
    if (health.memoryUsage > 80) {
      await _sendAlert(
        type: AlertType.warning,
        message: 'استهلاك الذاكرة مرتفع: ${health.memoryUsage}%',
        action: 'تنظيف الذاكرة مطلوب',
      );
    }
    
    // تنبيه حجم قاعدة البيانات
    if (health.databaseSize > 1000) { // 1GB
      await _sendAlert(
        type: AlertType.info,
        message: 'حجم قاعدة البيانات كبير: ${health.databaseSize}MB',
        action: 'فكر في أرشفة البيانات القديمة',
      );
    }
    
    // تنبيه النسخ الاحتياطية
    final daysSinceBackup = DateTime.now().difference(health.lastBackup).inDays;
    if (daysSinceBackup > 7) {
      await _sendAlert(
        type: AlertType.critical,
        message: 'لم يتم إنشاء نسخة احتياطية منذ $daysSinceBackup أيام',
        action: 'إنشاء نسخة احتياطية فوراً',
      );
    }
  }
}
```

---

## 🛠️ أدوات الصيانة

### 1. **أداة التشخيص الشامل**

```dart
class DiagnosticTool {
  static Future<DiagnosticReport> runFullDiagnostic() async {
    final report = DiagnosticReport();
    
    print('🔍 بدء التشخيص الشامل...');
    
    // 1. فحص قاعدة البيانات
    print('📊 فحص قاعدة البيانات...');
    report.databaseHealth = await DatabaseHelper().checkDatabaseHealth();
    
    // 2. فحص الخدمات
    print('⚙️ فحص الخدمات...');
    report.servicesHealth = await _checkAllServices();
    
    // 3. فحص الأداء
    print('⚡ فحص الأداء...');
    report.performanceMetrics = await PerformanceDiagnostics.diagnosePerformance();
    
    // 4. فحص الأمان
    print('🔒 فحص الأمان...');
    report.securityStatus = await SecurityDiagnostics.checkSecurity();
    
    // 5. فحص سلامة البيانات
    print('✅ فحص سلامة البيانات...');
    report.dataIntegrity = await DataIntegrityChecker.checkIntegrity();
    
    print('✅ انتهى التشخيص الشامل');
    return report;
  }
}
```

### 2. **أداة الإصلاح التلقائي**

```dart
class AutoRepairTool {
  static Future<RepairResult> autoRepair() async {
    final result = RepairResult();
    
    try {
      // 1. إصلاح قاعدة البيانات
      if (await _needsDatabaseRepair()) {
        await _repairDatabase();
        result.databaseRepaired = true;
      }
      
      // 2. إصلاح الفهارس
      await DatabaseHelper().rebuildIndexes();
      result.indexesRebuilt = true;
      
      // 3. تنظيف الملفات التالفة
      await _cleanCorruptedFiles();
      result.filesCleanedUp = true;
      
      // 4. إعادة تشغيل الخدمات
      await _restartServices();
      result.servicesRestarted = true;
      
      result.success = true;
      result.message = 'تم الإصلاح التلقائي بنجاح';
      
    } catch (e) {
      result.success = false;
      result.message = 'فشل في الإصلاح التلقائي: $e';
    }
    
    return result;
  }
}
```

---

## 📞 الدعم الفني

### 1. **مستويات الدعم**

#### المستوى الأول: الدعم الأساسي
- **الوقت:** 9:00 ص - 5:00 م (الأحد - الخميس)
- **القنوات:** هاتف، إيميل، دردشة
- **المشاكل:** استفسارات عامة، مشاكل بسيطة
- **وقت الاستجابة:** 4 ساعات

#### المستوى الثاني: الدعم المتقدم
- **الوقت:** 24/7
- **القنوات:** هاتف، إيميل، اتصال مباشر
- **المشاكل:** مشاكل تقنية معقدة
- **وقت الاستجابة:** ساعة واحدة

#### المستوى الثالث: الدعم الطارئ
- **الوقت:** 24/7
- **القنوات:** هاتف طوارئ، اتصال مباشر
- **المشاكل:** أعطال حرجة، فقدان بيانات
- **وقت الاستجابة:** 15 دقيقة

### 2. **إجراءات الدعم**

#### عند تلقي طلب دعم:
1. **تسجيل الطلب** في نظام إدارة التذاكر
2. **تصنيف الأولوية** حسب خطورة المشكلة
3. **تعيين المختص** المناسب
4. **بدء التشخيص** والحل
5. **متابعة العميل** حتى الحل النهائي
6. **توثيق الحل** لمرجع مستقبلي

#### معلومات مطلوبة من العميل:
- **وصف المشكلة** بالتفصيل
- **خطوات إعادة إنتاج المشكلة**
- **رسائل الخطأ** إن وجدت
- **إصدار التطبيق** المستخدم
- **نظام التشغيل** والمواصفات
- **ملفات السجل** إن أمكن

---

## 📋 قوائم المراجعة

### ✅ قائمة الصيانة اليومية
- [ ] فحص حالة النظام العامة
- [ ] مراجعة سجلات الأخطاء
- [ ] التحقق من النسخ الاحتياطية
- [ ] مراقبة استهلاك الموارد
- [ ] فحص التنبيهات الجديدة

### ✅ قائمة الصيانة الأسبوعية
- [ ] تحسين قاعدة البيانات
- [ ] فحص سلامة البيانات
- [ ] تنظيف الملفات المؤقتة
- [ ] مراجعة الأداء
- [ ] تحديث الإحصائيات

### ✅ قائمة الصيانة الشهرية
- [ ] تحليل الأداء الشامل
- [ ] مراجعة أمان النظام
- [ ] فحص التحديثات المتاحة
- [ ] أرشفة البيانات القديمة
- [ ] تدريب المستخدمين الجدد

---

## 📚 الموارد الإضافية

### 🔗 روابط مفيدة
- **دليل المستخدم:** [user_guide_final.md](user_guide_final.md)
- **الدليل التقني:** [developer_guide_technical.md](developer_guide_technical.md)
- **توثيق APIs:** [api_services_documentation.md](api_services_documentation.md)

### 📞 معلومات الاتصال
- **الدعم العام:** <EMAIL>
- **الدعم التقني:** <EMAIL>
- **الطوارئ:** +963-11-EMERGENCY
- **الموقع:** www.smartledger.sy

### 📖 مراجع تقنية
- **Flutter Debugging:** https://flutter.dev/docs/testing/debugging
- **SQLite Maintenance:** https://sqlite.org/pragma.html
- **Database Optimization:** Best practices guide

---

## ✅ الخلاصة

هذا الدليل يوفر إطار عمل شامل لصيانة ودعم Smart Ledger. اتبع هذه الإرشادات لضمان:

- **استقرار النظام** وموثوقيته
- **أداء ممتاز** ومستمر
- **أمان البيانات** وحمايتها
- **رضا المستخدمين** وثقتهم

**تذكر دائماً:**
- الوقاية خير من العلاج
- النسخ الاحتياطية أساسية
- المراقبة المستمرة ضرورية
- التوثيق مهم للمستقبل

**نجاح Smart Ledger يعتمد على صيانة ممتازة! 🎯**
