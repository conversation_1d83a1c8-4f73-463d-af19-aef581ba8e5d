import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../constants/revolutionary_design_colors.dart';
import '../models/journal_entry.dart';
import '../models/account.dart';
import '../services/journal_entry_service.dart';
import '../services/account_service.dart';

class AddJournalEntryScreen extends StatefulWidget {
  final JournalEntry? journalEntryToEdit;

  const AddJournalEntryScreen({super.key, this.journalEntryToEdit});

  @override
  State<AddJournalEntryScreen> createState() => _AddJournalEntryScreenState();
}

class _AddJournalEntryScreenState extends State<AddJournalEntryScreen> {
  final _formKey = GlobalKey<FormState>();
  final _journalEntryService = JournalEntryService();
  final _accountService = AccountService();

  // Controllers
  final _entryNumberController = TextEditingController();
  final _descriptionController = TextEditingController();

  // Form data
  DateTime _entryDate = DateTime.now();
  String _selectedType = 'general';
  bool _isLoading = false;
  List<Account> _accounts = [];
  List<JournalEntryDetailForm> _entryDetails = [];

  @override
  void initState() {
    super.initState();
    _loadAccounts();
    if (widget.journalEntryToEdit != null) {
      _populateFormWithExistingEntry();
    } else {
      _generateEntryNumber();
      _addEmptyDetail();
      _addEmptyDetail(); // Add two empty details by default
    }
  }

  @override
  void dispose() {
    _entryNumberController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  Future<void> _loadAccounts() async {
    try {
      final accounts = await _accountService.getAllAccounts();
      setState(() {
        _accounts = accounts;
      });
    } catch (e) {
      _showErrorSnackBar('حدث خطأ في تحميل الحسابات: $e');
    }
  }

  Future<void> _generateEntryNumber() async {
    try {
      final entryNumber = await _journalEntryService.generateEntryNumber();
      setState(() {
        _entryNumberController.text = entryNumber;
      });
    } catch (e) {
      _showErrorSnackBar('حدث خطأ في إنشاء رقم القيد: $e');
    }
  }

  void _populateFormWithExistingEntry() {
    final entry = widget.journalEntryToEdit!;
    _entryNumberController.text = entry.entryNumber;
    _descriptionController.text = entry.description;
    _entryDate = entry.entryDate;
    _selectedType = entry.type;

    _entryDetails = entry.details.map((detail) {
      return JournalEntryDetailForm(
        accountId: detail.accountId,
        debitAmount: detail.debitAmount,
        creditAmount: detail.creditAmount,
        description: detail.description ?? '',
      );
    }).toList();

    if (_entryDetails.isEmpty) {
      _addEmptyDetail();
      _addEmptyDetail();
    }
  }

  void _addEmptyDetail() {
    setState(() {
      _entryDetails.add(JournalEntryDetailForm());
    });
  }

  void _removeDetail(int index) {
    if (_entryDetails.length > 2) {
      setState(() {
        _entryDetails.removeAt(index);
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          widget.journalEntryToEdit != null
              ? 'تعديل قيد محاسبي'
              : 'قيد محاسبي جديد',
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.save),
            onPressed: _saveJournalEntry,
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : Form(
              key: _formKey,
              child: Column(
                children: [
                  Expanded(
                    child: SingleChildScrollView(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          _buildHeaderSection(),
                          const SizedBox(height: 24),
                          _buildDetailsSection(),
                          const SizedBox(height: 24),
                          _buildSummarySection(),
                        ],
                      ),
                    ),
                  ),
                  _buildBottomBar(),
                ],
              ),
            ),
    );
  }

  Widget _buildHeaderSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'معلومات القيد',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: TextFormField(
                    controller: _entryNumberController,
                    decoration: const InputDecoration(
                      labelText: 'رقم القيد',
                      border: OutlineInputBorder(),
                    ),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'يرجى إدخال رقم القيد';
                      }
                      return null;
                    },
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: InkWell(
                    onTap: _selectDate,
                    child: InputDecorator(
                      decoration: const InputDecoration(
                        labelText: 'تاريخ القيد',
                        border: OutlineInputBorder(),
                        suffixIcon: Icon(Icons.calendar_today),
                      ),
                      child: Text(DateFormat('dd/MM/yyyy').format(_entryDate)),
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            DropdownButtonFormField<String>(
              value: _selectedType,
              decoration: const InputDecoration(
                labelText: 'نوع القيد',
                border: OutlineInputBorder(),
              ),
              items: const [
                DropdownMenuItem(value: 'general', child: Text('قيد عام')),
                DropdownMenuItem(value: 'sale', child: Text('قيد مبيعات')),
                DropdownMenuItem(value: 'purchase', child: Text('قيد مشتريات')),
                DropdownMenuItem(value: 'payment', child: Text('قيد دفع')),
                DropdownMenuItem(value: 'receipt', child: Text('قيد قبض')),
              ],
              onChanged: (value) {
                setState(() {
                  _selectedType = value!;
                });
              },
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _descriptionController,
              decoration: const InputDecoration(
                labelText: 'وصف القيد',
                border: OutlineInputBorder(),
              ),
              maxLines: 2,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'يرجى إدخال وصف القيد';
                }
                return null;
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDetailsSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Text(
                  'تفاصيل القيد',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
                const Spacer(),
                ElevatedButton.icon(
                  onPressed: _addEmptyDetail,
                  icon: const Icon(Icons.add),
                  label: const Text('إضافة سطر'),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ..._entryDetails.asMap().entries.map((entry) {
              final index = entry.key;
              final detail = entry.value;
              return _buildDetailRow(index, detail);
            }),
          ],
        ),
      ),
    );
  }

  Widget _buildDetailRow(int index, JournalEntryDetailForm detail) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          children: [
            Row(
              children: [
                Text(
                  'السطر ${index + 1}',
                  style: Theme.of(context).textTheme.titleSmall,
                ),
                const Spacer(),
                if (_entryDetails.length > 2)
                  IconButton(
                    icon: const Icon(Icons.delete, color: Colors.red),
                    onPressed: () => _removeDetail(index),
                  ),
              ],
            ),
            const SizedBox(height: 12),
            DropdownButtonFormField<int>(
              value: detail.accountId,
              decoration: const InputDecoration(
                labelText: 'الحساب',
                border: OutlineInputBorder(),
              ),
              items: _accounts.map((account) {
                return DropdownMenuItem(
                  value: account.id,
                  child: Text('${account.code} - ${account.name}'),
                );
              }).toList(),
              onChanged: (value) {
                setState(() {
                  detail.accountId = value;
                });
              },
              validator: (value) {
                if (value == null) {
                  return 'يرجى اختيار الحساب';
                }
                return null;
              },
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: TextFormField(
                    initialValue: detail.debitAmount > 0
                        ? detail.debitAmount.toString()
                        : '',
                    decoration: const InputDecoration(
                      labelText: 'مدين',
                      border: OutlineInputBorder(),
                    ),
                    keyboardType: TextInputType.number,
                    onChanged: (value) {
                      detail.debitAmount = double.tryParse(value) ?? 0.0;
                      if (detail.debitAmount > 0) {
                        detail.creditAmount = 0.0;
                      }
                      setState(() {});
                    },
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: TextFormField(
                    initialValue: detail.creditAmount > 0
                        ? detail.creditAmount.toString()
                        : '',
                    decoration: const InputDecoration(
                      labelText: 'دائن',
                      border: OutlineInputBorder(),
                    ),
                    keyboardType: TextInputType.number,
                    onChanged: (value) {
                      detail.creditAmount = double.tryParse(value) ?? 0.0;
                      if (detail.creditAmount > 0) {
                        detail.debitAmount = 0.0;
                      }
                      setState(() {});
                    },
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            TextFormField(
              initialValue: detail.description,
              decoration: const InputDecoration(
                labelText: 'وصف السطر (اختياري)',
                border: OutlineInputBorder(),
              ),
              onChanged: (value) {
                detail.description = value;
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSummarySection() {
    final totalDebit = _entryDetails.fold<double>(
      0.0,
      (sum, detail) => sum + detail.debitAmount,
    );
    final totalCredit = _entryDetails.fold<double>(
      0.0,
      (sum, detail) => sum + detail.creditAmount,
    );
    final difference = totalDebit - totalCredit;
    final isBalanced = difference.abs() < 0.01;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('ملخص القيد', style: Theme.of(context).textTheme.titleLarge),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildSummaryItem(
                    'إجمالي المدين',
                    totalDebit,
                    RevolutionaryColors.successGlow,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildSummaryItem(
                    'إجمالي الدائن',
                    totalCredit,
                    RevolutionaryColors.errorCoral,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: isBalanced
                    ? RevolutionaryColors.successGlow.withValues(alpha: 0.1)
                    : RevolutionaryColors.errorCoral.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: isBalanced ? RevolutionaryColors.successGlow : RevolutionaryColors.errorCoral,
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    isBalanced ? Icons.check_circle : Icons.error,
                    color: isBalanced ? RevolutionaryColors.successGlow : RevolutionaryColors.errorCoral,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    isBalanced
                        ? 'القيد متوازن'
                        : 'القيد غير متوازن (الفرق: ${difference.toStringAsFixed(2)})',
                    style: TextStyle(
                      color: isBalanced ? RevolutionaryColors.successGlow : RevolutionaryColors.errorCoral,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryItem(String title, double amount, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: Theme.of(
              context,
            ).textTheme.bodySmall?.copyWith(color: color),
          ),
          const SizedBox(height: 4),
          Text(
            NumberFormat('#,##0.00').format(amount),
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: color,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBottomBar() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: RevolutionaryColors.backgroundCard,
        boxShadow: [
          BoxShadow(
            color: RevolutionaryColors.shadowMedium,
            blurRadius: 4,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Row(
        children: [
          Expanded(
            child: OutlinedButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('إلغاء'),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: ElevatedButton(
              onPressed: _saveJournalEntry,
              child: const Text('حفظ'),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _selectDate() async {
    final date = await showDatePicker(
      context: context,
      initialDate: _entryDate,
      firstDate: DateTime(2020),
      lastDate: DateTime(2030),
    );

    if (date != null) {
      setState(() {
        _entryDate = date;
      });
    }
  }

  Future<void> _saveJournalEntry() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    // Validate that we have at least 2 details with amounts
    final validDetails = _entryDetails
        .where(
          (detail) =>
              detail.accountId != null &&
              (detail.debitAmount > 0 || detail.creditAmount > 0),
        )
        .toList();

    if (validDetails.length < 2) {
      _showErrorSnackBar('يجب إدخال سطرين على الأقل بمبالغ صحيحة');
      return;
    }

    // Check if balanced
    final totalDebit = validDetails.fold<double>(
      0.0,
      (sum, detail) => sum + detail.debitAmount,
    );
    final totalCredit = validDetails.fold<double>(
      0.0,
      (sum, detail) => sum + detail.creditAmount,
    );
    final difference = totalDebit - totalCredit;

    if (difference.abs() >= 0.01) {
      _showErrorSnackBar(
        'القيد غير متوازن - يجب أن يكون مجموع المدين = مجموع الدائن',
      );
      return;
    }

    setState(() => _isLoading = true);

    try {
      final journalEntryDetails = validDetails.map((detail) {
        return JournalEntryDetail(
          journalEntryId: 0, // Will be set by the service
          accountId: detail.accountId!,
          debitAmount: detail.debitAmount,
          creditAmount: detail.creditAmount,
          description: detail.description.isNotEmpty
              ? detail.description
              : null,
        );
      }).toList();

      final journalEntry = JournalEntry(
        id: widget.journalEntryToEdit?.id,
        entryNumber: _entryNumberController.text,
        entryDate: _entryDate,
        description: _descriptionController.text,
        type: _selectedType,
        totalDebit: totalDebit,
        totalCredit: totalCredit,
        currencyId: 1, // Default currency
        details: journalEntryDetails,
      );

      if (widget.journalEntryToEdit != null) {
        await _journalEntryService.updateJournalEntry(journalEntry);
      } else {
        await _journalEntryService.insertJournalEntry(journalEntry);
      }

      if (mounted) {
        Navigator.pop(context, true);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              widget.journalEntryToEdit != null
                  ? 'تم تحديث القيد بنجاح'
                  : 'تم حفظ القيد بنجاح',
            ),
            backgroundColor: RevolutionaryColors.successGlow,
          ),
        );
      }
    } catch (e) {
      setState(() => _isLoading = false);
      _showErrorSnackBar('حدث خطأ في حفظ القيد: $e');
    }
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: RevolutionaryColors.errorCoral,
      ),
    );
  }
}

class JournalEntryDetailForm {
  int? accountId;
  double debitAmount;
  double creditAmount;
  String description;

  JournalEntryDetailForm({
    this.accountId,
    this.debitAmount = 0.0,
    this.creditAmount = 0.0,
    this.description = '',
  });
}
