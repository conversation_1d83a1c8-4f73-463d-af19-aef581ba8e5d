import 'package:flutter/material.dart';
import '../constants/revolutionary_design_colors.dart';
import '../widgets/revolutionary_ui_components.dart';
import '../services/account_service.dart';
import '../services/customer_service.dart';
import '../services/invoice_service.dart';
import '../services/item_service.dart';
import '../services/reports_service.dart';
import '../screens/accounts_screen.dart';
import '../screens/invoices_screen.dart';
import '../screens/warehouse_screen.dart';
import '../screens/reports_screen.dart';
import '../screens/customers_suppliers_screen.dart';
import '../screens/hr_management_screen.dart';
import '../screens/settings_screen.dart';
import '../screens/interactive_report_screen.dart';
import '../screens/advanced_dashboard_screen.dart';
import '../screens/enhanced_dashboard_screen.dart';
import '../screens/report_templates_screen.dart';
import '../screens/syrian_tax_screen.dart';
import '../screens/smart_notifications_screen.dart';

import '../services/logging_service.dart';
import '../services/advanced_dashboard_service.dart';

/// الشاشة الرئيسية الثورية لـ Smart Ledger
class RevolutionaryHomeScreen extends StatefulWidget {
  const RevolutionaryHomeScreen({super.key});

  @override
  State<RevolutionaryHomeScreen> createState() =>
      _RevolutionaryHomeScreenState();
}

class _RevolutionaryHomeScreenState extends State<RevolutionaryHomeScreen>
    with TickerProviderStateMixin {
  late AnimationController _headerAnimationController;
  late AnimationController _cardsAnimationController;
  late Animation<double> _headerAnimation;
  late Animation<double> _cardsAnimation;

  int _selectedNavIndex = 0;

  // الخدمات
  final AccountService _accountService = AccountService();
  final CustomerService _customerService = CustomerService();
  final InvoiceService _invoiceService = InvoiceService();
  final ItemService _itemService = ItemService();
  final ReportsService _reportsService = ReportsService();
  final AdvancedDashboardService _advancedDashboardService =
      AdvancedDashboardService();

  // البيانات الحقيقية
  double _totalProfit = 0.0;
  double _todaySales = 0.0;
  int _activeCustomers = 0;
  int _totalAccounts = 0;
  int _totalInvoices = 0;
  int _totalItems = 0;
  int _lowStockItems = 0;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _startAnimations();
    _loadDashboardData();
  }

  /// تحميل بيانات لوحة التحكم
  Future<void> _loadDashboardData() async {
    try {
      setState(() => _isLoading = true);

      // تهيئة خدمة التقارير
      await _reportsService.initialize();

      // تحميل البيانات بشكل متوازي
      final results = await Future.wait([
        _accountService.getAllAccounts(),
        _customerService.getAllCustomers(),
        _invoiceService.getAllInvoices(),
        _itemService.getAllItems(),
        _reportsService.getInventoryReport(),
      ]);

      final accounts = results[0] as List;
      final customers = results[1] as List;
      final invoices = results[2] as List;
      final items = results[3] as List;
      final inventoryReport = results[4] as List;

      // حساب الإحصائيات الأساسية
      _totalAccounts = accounts.length;
      _activeCustomers = customers.where((c) => c.isActive).length;
      _totalInvoices = invoices.length;
      _totalItems = items.length;

      // حساب الأصناف منخفضة المخزون
      _lowStockItems = inventoryReport
          .where(
            (item) =>
                item.stockStatus == 'low_stock' ||
                item.stockStatus == 'out_of_stock',
          )
          .length;

      // حساب إجمالي المبيعات اليوم
      final today = DateTime.now();
      final todayInvoices = invoices.where((invoice) {
        final invoiceDate = DateTime.parse(invoice.invoiceDate);
        return invoiceDate.year == today.year &&
            invoiceDate.month == today.month &&
            invoiceDate.day == today.day;
      });

      _todaySales = todayInvoices.fold(
        0.0,
        (sum, invoice) => sum + invoice.totalAmount,
      );

      // استخدام خدمة التقارير لحساب الأرباح بدقة أكبر
      try {
        final startOfMonth = DateTime(today.year, today.month, 1);
        final profitLossReport = await _reportsService.getProfitLoss(
          startOfMonth,
          today,
        );
        _totalProfit = profitLossReport.netProfit;
      } catch (e) {
        // في حالة فشل تقرير الأرباح، استخدم الحساب المبسط
        final salesInvoices = invoices.where((i) => i.type == 'sale');
        final purchaseInvoices = invoices.where((i) => i.type == 'purchase');

        final totalSales = salesInvoices.fold(
          0.0,
          (sum, i) => sum + i.totalAmount,
        );
        final totalPurchases = purchaseInvoices.fold(
          0.0,
          (sum, i) => sum + i.totalAmount,
        );

        _totalProfit = totalSales - totalPurchases;
      }

      setState(() => _isLoading = false);
    } catch (e) {
      setState(() => _isLoading = false);
      LoggingService.error(
        'خطأ في تحميل بيانات لوحة التحكم',
        category: 'RevolutionaryHomeScreen',
        data: {'error': e.toString()},
      );
    }
  }

  void _setupAnimations() {
    _headerAnimationController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );

    _cardsAnimationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _headerAnimation = CurvedAnimation(
      parent: _headerAnimationController,
      curve: Curves.elasticOut,
    );

    _cardsAnimation = CurvedAnimation(
      parent: _cardsAnimationController,
      curve: Curves.elasticOut,
    );
  }

  void _startAnimations() {
    _headerAnimationController.forward();
    Future.delayed(const Duration(milliseconds: 300), () {
      _cardsAnimationController.forward();
    });
  }

  @override
  void dispose() {
    _headerAnimationController.dispose();
    _cardsAnimationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: RevolutionaryColors.jasmineGradient,
        ),
        child: SafeArea(
          child: Column(
            children: [
              _buildRevolutionaryHeader(),
              Expanded(child: _buildMainContent()),
            ],
          ),
        ),
      ),
      bottomNavigationBar: _buildFloatingNavBar(),
    );
  }

  /// رأس الصفحة الثوري
  Widget _buildRevolutionaryHeader() {
    return AnimatedBuilder(
      animation: _headerAnimation,
      builder: (context, child) {
        return Transform.translate(
          offset: Offset(0, -50 * (1 - _headerAnimation.value)),
          child: Opacity(
            opacity: _headerAnimation.value,
            child: Container(
              padding: const EdgeInsets.all(20),
              decoration: const BoxDecoration(
                gradient: RevolutionaryColors.damascusGradient,
                borderRadius: BorderRadius.only(
                  bottomLeft: Radius.circular(30),
                  bottomRight: Radius.circular(30),
                ),
                boxShadow: [
                  BoxShadow(
                    color: RevolutionaryColors.shadowMedium,
                    blurRadius: 15,
                    offset: Offset(0, 5),
                  ),
                ],
              ),
              child: Column(
                children: [
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: RevolutionaryColors.syrianGold.withValues(
                            alpha: 0.2,
                          ),
                          borderRadius: BorderRadius.circular(15),
                        ),
                        child: const Icon(
                          Icons.account_balance,
                          color: RevolutionaryColors.syrianGold,
                          size: 32,
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Text(
                              'Smart Ledger',
                              style: TextStyle(
                                fontSize: 24,
                                fontWeight: FontWeight.bold,
                                color: RevolutionaryColors.textOnDark,
                                fontFamily: 'Cairo',
                              ),
                            ),
                            Text(
                              'دفتر الحسابات الذكي',
                              style: TextStyle(
                                fontSize: 14,
                                color: RevolutionaryColors.textOnDark
                                    .withValues(alpha: 0.8),
                                fontFamily: 'Cairo',
                              ),
                            ),
                          ],
                        ),
                      ),
                      IconButton(
                        onPressed: () {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) =>
                                  const SmartNotificationsScreen(),
                            ),
                          );
                        },
                        icon: const Icon(
                          Icons.notifications_outlined,
                          color: RevolutionaryColors.syrianGold,
                          size: 28,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 20),
                  _buildQuickStats(),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  /// إحصائيات سريعة
  Widget _buildQuickStats() {
    if (_isLoading) {
      return Row(
        children: [
          Expanded(child: _buildLoadingStatItem()),
          const SizedBox(width: 12),
          Expanded(child: _buildLoadingStatItem()),
          const SizedBox(width: 12),
          Expanded(child: _buildLoadingStatItem()),
        ],
      );
    }

    return Row(
      children: [
        Expanded(
          child: _buildQuickStatItem(
            'إجمالي الأرباح',
            '${_totalProfit.toStringAsFixed(0)} ل.س',
            Icons.trending_up,
            RevolutionaryColors.successGlow,
            onTap: () => _showProfitLossDetails(),
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildQuickStatItem(
            'المبيعات اليوم',
            '${_todaySales.toStringAsFixed(0)} ل.س',
            Icons.point_of_sale,
            RevolutionaryColors.syrianGold,
            onTap: () => _showTodaySalesDetails(),
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildQuickStatItem(
            'العملاء النشطين',
            '$_activeCustomers',
            Icons.people,
            RevolutionaryColors.infoTurquoise,
            onTap: () => _showCustomersDetails(),
          ),
        ),
      ],
    );
  }

  Widget _buildQuickStatItem(
    String title,
    String value,
    IconData icon,
    Color color, {
    VoidCallback? onTap,
  }) {
    Widget content = Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: RevolutionaryColors.jasmineWhite.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3), width: 1),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 20),
          const SizedBox(height: 4),
          Text(
            value,
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: color,
              fontFamily: 'Cairo',
            ),
          ),
          Text(
            title,
            style: TextStyle(
              fontSize: 10,
              color: RevolutionaryColors.textOnDark.withValues(alpha: 0.7),
              fontFamily: 'Cairo',
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );

    if (onTap != null) {
      return GestureDetector(onTap: onTap, child: content);
    }

    return content;
  }

  /// عنصر تحميل للإحصائيات
  Widget _buildLoadingStatItem() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: RevolutionaryColors.jasmineWhite.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: RevolutionaryColors.textHint.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          Container(
            width: 20,
            height: 20,
            decoration: BoxDecoration(
              color: RevolutionaryColors.textHint.withValues(alpha: 0.3),
              borderRadius: BorderRadius.circular(10),
            ),
          ),
          const SizedBox(height: 4),
          Container(
            width: 40,
            height: 14,
            decoration: BoxDecoration(
              color: RevolutionaryColors.textHint.withValues(alpha: 0.3),
              borderRadius: BorderRadius.circular(7),
            ),
          ),
          const SizedBox(height: 2),
          Container(
            width: 60,
            height: 10,
            decoration: BoxDecoration(
              color: RevolutionaryColors.textHint.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(5),
            ),
          ),
        ],
      ),
    );
  }

  /// المحتوى الرئيسي
  Widget _buildMainContent() {
    return AnimatedBuilder(
      animation: _cardsAnimation,
      builder: (context, child) {
        return Transform.translate(
          offset: Offset(0, 30 * (1 - _cardsAnimation.value)),
          child: Opacity(
            opacity: _cardsAnimation.value,
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'الوحدات الرئيسية',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: RevolutionaryColors.textPrimary,
                      fontFamily: 'Cairo',
                    ),
                  ),
                  const SizedBox(height: 16),
                  _buildMainModulesGrid(),
                  const SizedBox(height: 24),
                  const Text(
                    'الإحصائيات المتقدمة',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: RevolutionaryColors.textPrimary,
                      fontFamily: 'Cairo',
                    ),
                  ),
                  const SizedBox(height: 16),
                  _buildAdvancedStats(),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  /// شبكة الوحدات الرئيسية
  Widget _buildMainModulesGrid() {
    final modules = [
      ModuleItem(
        'دليل الحسابات',
        Icons.account_tree,
        RevolutionaryColors.damascusSky,
      ),
      ModuleItem('الفواتير', Icons.description, RevolutionaryColors.syrianGold),
      ModuleItem('المخزون', Icons.inventory, RevolutionaryColors.oliveBranch),
      ModuleItem('التقارير', Icons.analytics, RevolutionaryColors.errorCoral),
      ModuleItem('العملاء', Icons.people, RevolutionaryColors.infoTurquoise),
      ModuleItem(
        'الموارد البشرية',
        Icons.badge,
        RevolutionaryColors.successGlow,
      ),
      ModuleItem(
        'لوحة التحكم المتقدمة',
        Icons.dashboard,
        RevolutionaryColors.damascusSky,
      ),
      ModuleItem(
        'منشئ التقارير',
        Icons.design_services,
        RevolutionaryColors.infoTurquoise,
      ),
      ModuleItem('النظام الضريبي', Icons.account_balance, Colors.purple),
      ModuleItem(
        'التنبيهات الذكية',
        Icons.notifications_active,
        RevolutionaryColors.warningAmber,
      ),
      ModuleItem(
        'الإعدادات',
        Icons.settings,
        RevolutionaryColors.textSecondary,
      ),
    ];

    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 1.2,
        crossAxisSpacing: 12,
        mainAxisSpacing: 12,
      ),
      itemCount: modules.length,
      itemBuilder: (context, index) {
        final module = modules[index];
        return RevolutionaryUI.floating3DCard(
          title: module.title,
          subtitle: 'إدارة ${module.title}',
          icon: module.icon,
          accentColor: module.color,
          onTap: () => _navigateToModule(module.title),
          child: const SizedBox.shrink(),
        );
      },
    );
  }

  /// الإحصائيات المتقدمة
  Widget _buildAdvancedStats() {
    if (_isLoading) {
      return Column(
        children: [
          Row(
            children: [
              Expanded(child: _buildLoadingAdvancedStatItem()),
              const SizedBox(width: 12),
              Expanded(child: _buildLoadingAdvancedStatItem()),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(child: _buildLoadingAdvancedStatItem()),
              const SizedBox(width: 12),
              Expanded(child: _buildLoadingAdvancedStatItem()),
            ],
          ),
        ],
      );
    }

    return Column(
      children: [
        Row(
          children: [
            Expanded(
              child: RevolutionaryUI.animatedStatsCard(
                title: 'إجمالي الحسابات',
                value: '$_totalAccounts',
                icon: Icons.account_tree,
                color: RevolutionaryColors.damascusSky,
                percentage: 0.0,
                isIncreasing: true,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: RevolutionaryUI.animatedStatsCard(
                title: 'إجمالي الأصناف',
                value: '$_totalItems',
                icon: Icons.inventory,
                color: RevolutionaryColors.oliveBranch,
                percentage: 0.0,
                isIncreasing: true,
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: RevolutionaryUI.animatedStatsCard(
                title: 'إجمالي الفواتير',
                value: '$_totalInvoices',
                icon: Icons.description,
                color: RevolutionaryColors.syrianGold,
                percentage: 0.0,
                isIncreasing: true,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: GestureDetector(
                onTap: () => _showLowStockDetails(),
                child: RevolutionaryUI.animatedStatsCard(
                  title: 'أصناف منخفضة المخزون',
                  value: '$_lowStockItems',
                  icon: Icons.warning,
                  color: _lowStockItems > 0
                      ? RevolutionaryColors.errorCoral
                      : RevolutionaryColors.successGlow,
                  percentage: 0.0,
                  isIncreasing: false,
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// عنصر تحميل للإحصائيات المتقدمة
  Widget _buildLoadingAdvancedStatItem() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: RevolutionaryColors.jasmineWhite.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: RevolutionaryColors.textHint.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: RevolutionaryColors.textHint.withValues(alpha: 0.3),
              borderRadius: BorderRadius.circular(20),
            ),
          ),
          const SizedBox(height: 8),
          Container(
            width: 80,
            height: 20,
            decoration: BoxDecoration(
              color: RevolutionaryColors.textHint.withValues(alpha: 0.3),
              borderRadius: BorderRadius.circular(10),
            ),
          ),
          const SizedBox(height: 4),
          Container(
            width: 100,
            height: 14,
            decoration: BoxDecoration(
              color: RevolutionaryColors.textHint.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(7),
            ),
          ),
        ],
      ),
    );
  }

  /// شريط التنقل العائم
  Widget _buildFloatingNavBar() {
    final navItems = [
      NavigationItem(icon: Icons.home, label: 'الرئيسية'),
      NavigationItem(icon: Icons.analytics, label: 'التقارير'),
      NavigationItem(icon: Icons.inventory, label: 'المخزون'),
      NavigationItem(icon: Icons.people, label: 'العملاء'),
      NavigationItem(icon: Icons.speed, label: 'الأداء'),
    ];

    return RevolutionaryUI.floatingNavigationBar(
      items: navItems,
      currentIndex: _selectedNavIndex,
      onTap: (index) {
        setState(() {
          _selectedNavIndex = index;
        });
        _handleNavigation(index);
      },
    );
  }

  void _navigateToModule(String moduleName) {
    Widget? targetScreen;

    switch (moduleName) {
      case 'دليل الحسابات':
        targetScreen = const AccountsScreen();
        break;
      case 'الفواتير':
        targetScreen = const InvoicesScreen();
        break;
      case 'المخزون':
        targetScreen = const WarehouseScreen();
        break;
      case 'التقارير':
        targetScreen = const ReportsScreen();
        break;
      case 'العملاء':
        targetScreen = const CustomersSuppliersScreen();
        break;
      case 'الموارد البشرية':
        targetScreen = const HRManagementScreen();
        break;
      case 'لوحة التحكم المتقدمة':
        targetScreen = const EnhancedDashboardScreen();
        break;
      case 'منشئ التقارير':
        targetScreen = const ReportTemplatesScreen();
        break;
      case 'النظام الضريبي':
        targetScreen = const SyrianTaxScreen();
        break;
      case 'التنبيهات الذكية':
        targetScreen = const SmartNotificationsScreen();
        break;
      case 'الإعدادات':
        targetScreen = const SettingsScreen();
        break;
    }

    if (targetScreen != null) {
      Navigator.push(
        context,
        MaterialPageRoute(builder: (context) => targetScreen!),
      );
    } else {
      // عرض رسالة في حالة عدم وجود الشاشة
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('الانتقال إلى $moduleName'),
          backgroundColor: RevolutionaryColors.damascusSky,
        ),
      );
    }
  }

  /// التعامل مع التنقل من شريط التنقل السفلي
  void _handleNavigation(int index) {
    switch (index) {
      case 0: // الرئيسية
        // البقاء في الشاشة الحالية
        break;
      case 1: // التقارير
        Navigator.push(
          context,
          MaterialPageRoute(builder: (context) => const ReportsScreen()),
        );
        break;
      case 2: // المخزون
        Navigator.push(
          context,
          MaterialPageRoute(builder: (context) => const WarehouseScreen()),
        );
        break;
      case 3: // العملاء
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => const CustomersSuppliersScreen(),
          ),
        );
        break;
      case 4: // الأداء
        _showPerformanceDashboard();
        break;
    }
  }

  /// عرض لوحة تحكم الأداء
  void _showPerformanceDashboard() async {
    try {
      final performanceStats = await _advancedDashboardService
          .getPerformanceStatistics();

      if (!mounted) return;

      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text(
            'إحصائيات الأداء',
            style: TextStyle(fontFamily: 'Cairo'),
          ),
          content: SizedBox(
            width: double.maxFinite,
            height: 400,
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildPerformanceCard(
                    'نقاط الأداء العامة',
                    '${performanceStats['performance_score']?.toStringAsFixed(1) ?? '0'}/100',
                    Icons.speed,
                    RevolutionaryColors.syrianGold,
                  ),
                  const SizedBox(height: 12),
                  _buildPerformanceCard(
                    'متوسط وقت الاستجابة',
                    '${performanceStats['average_response_time'] ?? 0} مللي ثانية',
                    Icons.timer,
                    RevolutionaryColors.infoTurquoise,
                  ),
                  const SizedBox(height: 12),
                  _buildPerformanceCard(
                    'استخدام الذاكرة',
                    '${performanceStats['memory_usage']?.toStringAsFixed(1) ?? '0'} MB',
                    Icons.memory,
                    RevolutionaryColors.oliveBranch,
                  ),
                  const SizedBox(height: 12),
                  _buildPerformanceCard(
                    'العمليات النشطة',
                    '${performanceStats['active_operations_count'] ?? 0}',
                    Icons.play_circle,
                    RevolutionaryColors.damascusSky,
                  ),
                  const SizedBox(height: 12),
                  _buildPerformanceCard(
                    'معدل نجاح التخزين المؤقت',
                    '${(performanceStats['cache_hit_rate'] * 100).toStringAsFixed(1)}%',
                    Icons.cached,
                    RevolutionaryColors.successGlow,
                  ),
                ],
              ),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('إغلاق'),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.pop(context);
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const AdvancedDashboardScreen(),
                  ),
                );
              },
              child: const Text('لوحة التحكم المتقدمة'),
            ),
          ],
        ),
      );
    } catch (e) {
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('خطأ في تحميل إحصائيات الأداء: $e'),
          backgroundColor: RevolutionaryColors.errorCoral,
        ),
      );
    }
  }

  /// بناء بطاقة الأداء
  Widget _buildPerformanceCard(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Row(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontFamily: 'Cairo',
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Text(
                  value,
                  style: TextStyle(
                    fontFamily: 'Cairo',
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: color,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// عرض تفاصيل الأرباح والخسائر
  void _showProfitLossDetails() async {
    try {
      final today = DateTime.now();
      final startOfMonth = DateTime(today.year, today.month, 1);
      final profitLossReport = await _reportsService.getProfitLoss(
        startOfMonth,
        today,
      );

      if (!mounted) return;

      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text(
            'تفاصيل الأرباح والخسائر',
            style: TextStyle(fontFamily: 'Cairo'),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildDetailRow(
                'إجمالي الإيرادات',
                '${profitLossReport.totalRevenue.toStringAsFixed(2)} ل.س',
              ),
              _buildDetailRow(
                'إجمالي المصروفات',
                '${profitLossReport.totalExpense.toStringAsFixed(2)} ل.س',
              ),
              const Divider(),
              _buildDetailRow(
                'صافي الربح/الخسارة',
                '${profitLossReport.netProfit.toStringAsFixed(2)} ل.س',
                isTotal: true,
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('إغلاق'),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.pop(context);
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const InteractiveReportScreen(
                      reportType: 'profit_loss',
                      reportTitle: 'قائمة الدخل',
                    ),
                  ),
                );
              },
              child: const Text('عرض التقرير الكامل'),
            ),
          ],
        ),
      );
    } catch (e) {
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('خطأ في تحميل تفاصيل الأرباح: $e'),
          backgroundColor: RevolutionaryColors.errorCoral,
        ),
      );
    }
  }

  /// عرض تفاصيل مبيعات اليوم
  void _showTodaySalesDetails() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text(
          'تفاصيل مبيعات اليوم',
          style: TextStyle(fontFamily: 'Cairo'),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildDetailRow(
              'إجمالي المبيعات',
              '${_todaySales.toStringAsFixed(2)} ل.س',
            ),
            _buildDetailRow('عدد الفواتير', '$_totalInvoices'),
            if (_totalInvoices > 0)
              _buildDetailRow(
                'متوسط الفاتورة',
                '${(_todaySales / _totalInvoices).toStringAsFixed(2)} ل.س',
              ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              Navigator.push(
                context,
                MaterialPageRoute(builder: (context) => const InvoicesScreen()),
              );
            },
            child: const Text('عرض الفواتير'),
          ),
        ],
      ),
    );
  }

  /// عرض تفاصيل العملاء
  void _showCustomersDetails() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text(
          'تفاصيل العملاء',
          style: TextStyle(fontFamily: 'Cairo'),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildDetailRow('العملاء النشطين', '$_activeCustomers'),
            _buildDetailRow('إجمالي الحسابات', '$_totalAccounts'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const CustomersSuppliersScreen(),
                ),
              );
            },
            child: const Text('إدارة العملاء'),
          ),
        ],
      ),
    );
  }

  /// عرض تفاصيل الأصناف منخفضة المخزون
  void _showLowStockDetails() async {
    try {
      final inventoryReport = await _reportsService.getInventoryReport();
      final lowStockItems = inventoryReport
          .where(
            (item) =>
                item.stockStatus == 'low_stock' ||
                item.stockStatus == 'out_of_stock',
          )
          .toList();

      if (!mounted) return;

      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text(
            'الأصناف منخفضة المخزون',
            style: TextStyle(fontFamily: 'Cairo'),
          ),
          content: SizedBox(
            width: double.maxFinite,
            height: 300,
            child: lowStockItems.isEmpty
                ? const Center(
                    child: Text(
                      'لا توجد أصناف منخفضة المخزون',
                      style: TextStyle(fontFamily: 'Cairo'),
                    ),
                  )
                : ListView.builder(
                    itemCount: lowStockItems.length,
                    itemBuilder: (context, index) {
                      final item = lowStockItems[index];
                      return ListTile(
                        leading: Icon(
                          item.stockStatus == 'out_of_stock'
                              ? Icons.error
                              : Icons.warning,
                          color: item.stockStatus == 'out_of_stock'
                              ? RevolutionaryColors.errorCoral
                              : RevolutionaryColors.syrianGold,
                        ),
                        title: Text(
                          item.name,
                          style: const TextStyle(fontFamily: 'Cairo'),
                        ),
                        subtitle: Text(
                          'الكمية: ${item.quantity} - الحد الأدنى: ${item.minQuantity}',
                          style: const TextStyle(fontFamily: 'Cairo'),
                        ),
                        trailing: Text(
                          item.stockStatus == 'out_of_stock' ? 'نفد' : 'منخفض',
                          style: TextStyle(
                            color: item.stockStatus == 'out_of_stock'
                                ? RevolutionaryColors.errorCoral
                                : RevolutionaryColors.syrianGold,
                            fontFamily: 'Cairo',
                          ),
                        ),
                      );
                    },
                  ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('إغلاق'),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.pop(context);
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const WarehouseScreen(),
                  ),
                );
              },
              child: const Text('إدارة المخزون'),
            ),
          ],
        ),
      );
    } catch (e) {
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('خطأ في تحميل بيانات المخزون: $e'),
          backgroundColor: RevolutionaryColors.errorCoral,
        ),
      );
    }
  }

  /// بناء صف التفاصيل
  Widget _buildDetailRow(String label, String value, {bool isTotal = false}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontFamily: 'Cairo',
              fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
            ),
          ),
          Text(
            value,
            style: TextStyle(
              fontFamily: 'Cairo',
              fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
              color: isTotal ? RevolutionaryColors.syrianGold : null,
            ),
          ),
        ],
      ),
    );
  }
}

/// عنصر الوحدة
class ModuleItem {
  final String title;
  final IconData icon;
  final Color color;

  ModuleItem(this.title, this.icon, this.color);
}
