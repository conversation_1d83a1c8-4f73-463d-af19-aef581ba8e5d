import 'package:flutter/material.dart';
import '../constants/revolutionary_design_colors.dart';
import '../models/account.dart';
import '../services/account_service.dart';
import '../services/screen_reader_service.dart';
import '../widgets/account_card.dart';
import '../widgets/loading_widget.dart';
import '../responsive/responsive.dart';
import 'add_account_screen.dart';
import 'account_details_screen.dart';

/// شاشة الحسابات محسنة للأجهزة اللوحية
/// تستخدم تخطيط Master-Detail لاستغلال المساحة بشكل أفضل
class AccountsScreenTablet extends StatefulWidget {
  const AccountsScreenTablet({super.key});

  @override
  State<AccountsScreenTablet> createState() => _AccountsScreenTabletState();
}

class _AccountsScreenTabletState extends State<AccountsScreenTablet>
    with TickerProviderStateMixin {
  final AccountService _accountService = AccountService();
  final TextEditingController _searchController = TextEditingController();

  List<Account> _accounts = [];
  List<Account> _filteredAccounts = [];
  bool _isLoading = true;
  String _selectedType = 'all';
  Account? _selectedAccount;

  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    _loadAccounts();
  }

  @override
  void dispose() {
    _animationController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _loadAccounts() async {
    try {
      final accounts = await _accountService.getAllAccounts();
      setState(() {
        _accounts = accounts;
        _filteredAccounts = accounts;
        _isLoading = false;
      });
      _animationController.forward();
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('خطأ في تحميل الحسابات: $e')));
      }
    }
  }

  void _filterAccounts() {
    setState(() {
      _filteredAccounts = _accounts.where((account) {
        final matchesSearch =
            account.name.toLowerCase().contains(
              _searchController.text.toLowerCase(),
            ) ||
            account.code.contains(_searchController.text);

        final matchesType =
            _selectedType == 'all' ||
            account.type.toString().split('.').last == _selectedType;

        return matchesSearch && matchesType;
      }).toList();
    });
  }

  void _selectAccount(Account account) {
    setState(() {
      _selectedAccount = account;
    });
  }

  @override
  Widget build(BuildContext context) {
    return ScreenInfoProvider(
      child: ResponsiveLayout(
        mobile: _buildMobileLayout(),
        tablet: _buildTabletLayout(),
        desktop: _buildTabletLayout(), // نفس تخطيط الجهاز اللوحي
      ),
    );
  }

  Widget _buildMobileLayout() {
    // التخطيط العادي للهواتف
    return Scaffold(
      appBar: AppBar(
        title: const ResponsiveText.h3('دليل الحسابات'),
        backgroundColor: RevolutionaryColors.damascusSky,
        foregroundColor: Colors.white,
      ),
      body: _isLoading ? const LoadingWidget() : _buildAccountsList(),
      floatingActionButton: FloatingActionButton(
        onPressed: _navigateToAddAccount,
        backgroundColor: RevolutionaryColors.damascusSky,
        child: const ResponsiveIcon.medium(Icons.add, color: Colors.white),
      ),
    );
  }

  Widget _buildTabletLayout() {
    return TabletMasterDetailLayout(
      title: 'دليل الحسابات',
      sidebar: _buildSidebar(),
      content: _buildDetailContent(),
    );
  }

  Widget _buildSidebar() {
    return Column(
      children: [
        // شريط البحث والفلاتر
        _buildSearchAndFilters(),

        // قائمة الحسابات
        Expanded(
          child: _isLoading ? const LoadingWidget() : _buildAccountsList(),
        ),

        // أزرار الإجراءات
        _buildActionButtons(),
      ],
    );
  }

  Widget _buildSearchAndFilters() {
    final dimensions = context.dimensions;

    return ResponsiveContainer(
      padding: EdgeInsets.all(dimensions.paddingM),
      child: Column(
        children: [
          // حقل البحث
          Semantics(
            label: 'حقل البحث في الحسابات',
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'البحث في الحسابات...',
                prefixIcon: const ResponsiveIcon.medium(Icons.search),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(dimensions.borderRadiusM),
                ),
                filled: true,
                fillColor: Theme.of(context).colorScheme.surface,
              ),
              onChanged: (value) => _filterAccounts(),
            ),
          ),

          ResponsiveSpacing.medium(),

          // فلتر نوع الحساب
          Semantics(
            label: 'فلتر نوع الحساب',
            child: DropdownButtonFormField<String>(
              value: _selectedType,
              decoration: InputDecoration(
                labelText: 'نوع الحساب',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(dimensions.borderRadiusM),
                ),
                filled: true,
                fillColor: Theme.of(context).colorScheme.surface,
              ),
              items: const [
                DropdownMenuItem(value: 'all', child: Text('جميع الأنواع')),
                DropdownMenuItem(value: 'asset', child: Text('أصول')),
                DropdownMenuItem(value: 'liability', child: Text('خصوم')),
                DropdownMenuItem(value: 'equity', child: Text('حقوق ملكية')),
                DropdownMenuItem(value: 'revenue', child: Text('إيرادات')),
                DropdownMenuItem(value: 'expense', child: Text('مصروفات')),
              ],
              onChanged: (value) {
                setState(() {
                  _selectedType = value!;
                });
                _filterAccounts();
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAccountsList() {
    if (_filteredAccounts.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            ResponsiveIcon.extraLarge(
              Icons.account_tree_outlined,
              color: Theme.of(context).colorScheme.outline,
            ),
            ResponsiveSpacing.medium(),
            ResponsiveText.body(
              'لا توجد حسابات',
              style: TextStyle(color: Theme.of(context).colorScheme.outline),
            ),
          ],
        ),
      );
    }

    return FadeTransition(
      opacity: _fadeAnimation,
      child: ListView.builder(
        padding: EdgeInsets.all(context.dimensions.paddingS),
        itemCount: _filteredAccounts.length,
        itemBuilder: (context, index) {
          final account = _filteredAccounts[index];
          final isSelected = _selectedAccount?.id == account.id;

          return Container(
            margin: EdgeInsets.only(bottom: context.dimensions.spacingS),
            decoration: BoxDecoration(
              color: isSelected
                  ? Theme.of(context).colorScheme.primaryContainer
                  : null,
              borderRadius: BorderRadius.circular(
                context.dimensions.borderRadiusM,
              ),
            ),
            child: AccountCard(
              account: account,
              onTap: () => _selectAccount(account),
              onEdit: () => _navigateToEditAccount(account),
              onDelete: () => _deleteAccount(account),
            ),
          );
        },
      ),
    );
  }

  Widget _buildActionButtons() {
    final dimensions = context.dimensions;

    return ResponsiveContainer(
      padding: EdgeInsets.all(dimensions.paddingM),
      child: Column(
        children: [
          ResponsiveButton.elevated(
            text: 'إضافة حساب جديد',
            icon: const ResponsiveIcon.small(Icons.add),
            onPressed: _navigateToAddAccount,
            isExpanded: true,
          ),
          ResponsiveSpacing.small(),
          ResponsiveButton.outlined(
            text: 'تحديث القائمة',
            icon: const ResponsiveIcon.small(Icons.refresh),
            onPressed: _loadAccounts,
            isExpanded: true,
          ),
        ],
      ),
    );
  }

  Widget _buildDetailContent() {
    if (_selectedAccount == null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            ResponsiveIcon.extraLarge(
              Icons.account_balance_outlined,
              color: Theme.of(context).colorScheme.outline,
            ),
            ResponsiveSpacing.large(),
            ResponsiveText.h3(
              'اختر حساباً لعرض التفاصيل',
              style: TextStyle(color: Theme.of(context).colorScheme.outline),
              textAlign: TextAlign.center,
            ),
            ResponsiveSpacing.medium(),
            ResponsiveText.body(
              'انقر على أي حساب من القائمة الجانبية لعرض تفاصيله وإجراء العمليات عليه',
              style: TextStyle(color: Theme.of(context).colorScheme.outline),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return AccountDetailsScreen(account: _selectedAccount!);
  }

  void _navigateToAddAccount() {
    ScreenReaderService.announceScreenChange('إضافة حساب جديد');
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const AddAccountScreen()),
    ).then((_) => _loadAccounts());
  }

  void _navigateToEditAccount(Account account) {
    ScreenReaderService.announceScreenChange('تعديل الحساب');
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => AddAccountScreen(accountToEdit: account),
      ),
    ).then((_) => _loadAccounts());
  }

  Future<void> _deleteAccount(Account account) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const ResponsiveText.h3('تأكيد الحذف'),
        content: ResponsiveText.body(
          'هل أنت متأكد من حذف الحساب "${account.name}"؟',
        ),
        actions: [
          ResponsiveButton.text(
            text: 'إلغاء',
            onPressed: () => Navigator.pop(context, false),
          ),
          ResponsiveButton.elevated(
            text: 'حذف',
            onPressed: () => Navigator.pop(context, true),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await _accountService.deleteAccount(account.id!);
        if (mounted) {
          ScaffoldMessenger.of(
            context,
          ).showSnackBar(const SnackBar(content: Text('تم حذف الحساب بنجاح')));
          _loadAccounts();
          if (_selectedAccount?.id == account.id) {
            setState(() {
              _selectedAccount = null;
            });
          }
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(
            context,
          ).showSnackBar(SnackBar(content: Text('خطأ في حذف الحساب: $e')));
        }
      }
    }
  }
}
