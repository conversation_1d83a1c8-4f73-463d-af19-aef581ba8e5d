/// خدمة جدولة الدفعات المتقدمة
/// تتعامل مع إنشاء وإدارة جداول الدفعات والتذكيرات التلقائية
library;

import '../database/database_helper.dart';
import '../models/payment_schedule.dart';
import '../models/payment.dart';
import '../services/payment_service.dart';
import '../services/invoice_service.dart';
import '../services/logging_service.dart';
import '../services/audit_service.dart';
import '../constants/app_constants.dart';

class PaymentScheduleService {
  final DatabaseHelper _databaseHelper = DatabaseHelper();
  final PaymentService _paymentService = PaymentService();
  final InvoiceService _invoiceService = InvoiceService();

  /// إنشاء جدولة دفعات جديدة
  Future<int> createPaymentSchedule(PaymentSchedule schedule) async {
    try {
      final db = await _databaseHelper.database;

      // التحقق من صحة البيانات
      if (!schedule.isValid) {
        throw Exception('بيانات جدولة الدفعات غير صحيحة');
      }

      // التحقق من وجود الفاتورة
      final invoice = await _invoiceService.getInvoiceById(schedule.invoiceId);
      if (invoice == null) {
        throw Exception('الفاتورة غير موجودة');
      }

      // التحقق من عدم وجود جدولة نشطة للفاتورة
      final existingSchedule = await getActiveScheduleByInvoiceId(
        schedule.invoiceId,
      );
      if (existingSchedule != null) {
        throw Exception('توجد جدولة نشطة للفاتورة بالفعل');
      }

      return await db.transaction((txn) async {
        // إدراج الجدولة
        final scheduleData = schedule.toMap();
        scheduleData.remove('id');

        final scheduleId = await txn.insert(
          AppConstants.paymentSchedulesTable,
          scheduleData,
        );

        // إدراج التذكيرات
        for (final reminder in schedule.reminders) {
          final reminderData = reminder.toMap();
          reminderData.remove('id');
          reminderData['schedule_id'] = scheduleId;

          await txn.insert(AppConstants.paymentRemindersTable, reminderData);
        }

        // تسجيل العملية
        await AuditService.log(
          action: 'CREATE',
          entityType: 'payment_schedules',
          entityId: scheduleId,
          entityName: schedule.scheduleName,
          description: 'إنشاء جدولة دفعات جديدة: ${schedule.scheduleName}',
          newValues: scheduleData,
        );

        LoggingService.info(
          'تم إنشاء جدولة دفعات جديدة',
          category: 'PaymentScheduleService',
          data: {
            'schedule_id': scheduleId,
            'invoice_id': schedule.invoiceId,
            'schedule_name': schedule.scheduleName,
          },
        );

        return scheduleId;
      });
    } catch (e) {
      LoggingService.error(
        'خطأ في إنشاء جدولة الدفعات',
        category: 'PaymentScheduleService',
        data: {'error': e.toString()},
      );
      rethrow;
    }
  }

  /// الحصول على جدولة دفعات بالمعرف
  Future<PaymentSchedule?> getPaymentScheduleById(int id) async {
    try {
      final db = await _databaseHelper.database;

      final maps = await db.query(
        AppConstants.paymentSchedulesTable,
        where: 'id = ?',
        whereArgs: [id],
      );

      if (maps.isEmpty) return null;

      final schedule = PaymentSchedule.fromMap(maps.first);

      // تحميل التذكيرات
      final reminders = await _getScheduleReminders(id);

      return schedule.copyWith(reminders: reminders);
    } catch (e) {
      LoggingService.error(
        'خطأ في الحصول على جدولة الدفعات',
        category: 'PaymentScheduleService',
        data: {'id': id, 'error': e.toString()},
      );
      return null;
    }
  }

  /// الحصول على الجدولة النشطة للفاتورة
  Future<PaymentSchedule?> getActiveScheduleByInvoiceId(int invoiceId) async {
    try {
      final db = await _databaseHelper.database;

      final maps = await db.query(
        AppConstants.paymentSchedulesTable,
        where: 'invoice_id = ? AND status = ?',
        whereArgs: [invoiceId, ScheduleStatus.active.code],
      );

      if (maps.isEmpty) return null;

      final schedule = PaymentSchedule.fromMap(maps.first);

      // تحميل التذكيرات
      final reminders = await _getScheduleReminders(schedule.id!);

      return schedule.copyWith(reminders: reminders);
    } catch (e) {
      LoggingService.error(
        'خطأ في الحصول على الجدولة النشطة',
        category: 'PaymentScheduleService',
        data: {'invoice_id': invoiceId, 'error': e.toString()},
      );
      return null;
    }
  }

  /// الحصول على جميع جداول الدفعات
  Future<List<PaymentSchedule>> getAllPaymentSchedules({
    ScheduleStatus? status,
    int? limit,
    int? offset,
  }) async {
    try {
      final db = await _databaseHelper.database;

      String whereClause = '1=1';
      List<dynamic> whereArgs = [];

      if (status != null) {
        whereClause += ' AND status = ?';
        whereArgs.add(status.code);
      }

      final maps = await db.query(
        AppConstants.paymentSchedulesTable,
        where: whereClause,
        whereArgs: whereArgs.isNotEmpty ? whereArgs : null,
        orderBy: 'next_payment_date ASC',
        limit: limit,
        offset: offset,
      );

      final schedules = <PaymentSchedule>[];

      for (final map in maps) {
        final schedule = PaymentSchedule.fromMap(map);
        final reminders = await _getScheduleReminders(schedule.id!);
        schedules.add(schedule.copyWith(reminders: reminders));
      }

      return schedules;
    } catch (e) {
      LoggingService.error(
        'خطأ في الحصول على جداول الدفعات',
        category: 'PaymentScheduleService',
        data: {'error': e.toString()},
      );
      return [];
    }
  }

  /// الحصول على الجداول المستحقة
  Future<List<PaymentSchedule>> getDueSchedules() async {
    try {
      final db = await _databaseHelper.database;
      final now = DateTime.now();

      final maps = await db.query(
        AppConstants.paymentSchedulesTable,
        where: 'status = ? AND next_payment_date <= ?',
        whereArgs: [ScheduleStatus.active.code, now.toIso8601String()],
        orderBy: 'next_payment_date ASC',
      );

      final schedules = <PaymentSchedule>[];

      for (final map in maps) {
        final schedule = PaymentSchedule.fromMap(map);
        final reminders = await _getScheduleReminders(schedule.id!);
        schedules.add(schedule.copyWith(reminders: reminders));
      }

      return schedules;
    } catch (e) {
      LoggingService.error(
        'خطأ في الحصول على الجداول المستحقة',
        category: 'PaymentScheduleService',
        data: {'error': e.toString()},
      );
      return [];
    }
  }

  /// الحصول على الجداول المستحقة قريباً
  Future<List<PaymentSchedule>> getUpcomingSchedules({
    int daysAhead = 7,
  }) async {
    try {
      final db = await _databaseHelper.database;
      final now = DateTime.now();
      final futureDate = now.add(Duration(days: daysAhead));

      final maps = await db.query(
        AppConstants.paymentSchedulesTable,
        where: 'status = ? AND next_payment_date BETWEEN ? AND ?',
        whereArgs: [
          ScheduleStatus.active.code,
          now.toIso8601String(),
          futureDate.toIso8601String(),
        ],
        orderBy: 'next_payment_date ASC',
      );

      final schedules = <PaymentSchedule>[];

      for (final map in maps) {
        final schedule = PaymentSchedule.fromMap(map);
        final reminders = await _getScheduleReminders(schedule.id!);
        schedules.add(schedule.copyWith(reminders: reminders));
      }

      return schedules;
    } catch (e) {
      LoggingService.error(
        'خطأ في الحصول على الجداول المستحقة قريباً',
        category: 'PaymentScheduleService',
        data: {'error': e.toString()},
      );
      return [];
    }
  }

  /// تحديث جدولة الدفعات
  Future<void> updatePaymentSchedule(PaymentSchedule schedule) async {
    try {
      final db = await _databaseHelper.database;

      if (!schedule.isValid) {
        throw Exception('بيانات جدولة الدفعات غير صحيحة');
      }

      await db.transaction((txn) async {
        // تحديث الجدولة
        final scheduleData = schedule.toMap();
        scheduleData['updated_at'] = DateTime.now().toIso8601String();

        await txn.update(
          AppConstants.paymentSchedulesTable,
          scheduleData,
          where: 'id = ?',
          whereArgs: [schedule.id],
        );

        // حذف التذكيرات القديمة
        await txn.delete(
          AppConstants.paymentRemindersTable,
          where: 'schedule_id = ?',
          whereArgs: [schedule.id],
        );

        // إدراج التذكيرات الجديدة
        for (final reminder in schedule.reminders) {
          final reminderData = reminder.toMap();
          reminderData.remove('id');
          reminderData['schedule_id'] = schedule.id;

          await txn.insert(AppConstants.paymentRemindersTable, reminderData);
        }

        // تسجيل العملية
        await AuditService.log(
          action: 'UPDATE',
          entityType: 'payment_schedules',
          entityId: schedule.id!,
          entityName: schedule.scheduleName,
          description: 'تحديث جدولة الدفعات: ${schedule.scheduleName}',
          newValues: scheduleData,
        );
      });

      LoggingService.info(
        'تم تحديث جدولة الدفعات',
        category: 'PaymentScheduleService',
        data: {
          'schedule_id': schedule.id,
          'schedule_name': schedule.scheduleName,
        },
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في تحديث جدولة الدفعات',
        category: 'PaymentScheduleService',
        data: {'schedule_id': schedule.id, 'error': e.toString()},
      );
      rethrow;
    }
  }

  /// تسجيل دفعة في الجدولة
  Future<void> recordScheduledPayment(
    int scheduleId,
    double amount, {
    String? paymentMethod,
    String? reference,
    String? notes,
  }) async {
    try {
      final db = await _databaseHelper.database;

      await db.transaction((txn) async {
        // الحصول على الجدولة
        final schedule = await getPaymentScheduleById(scheduleId);
        if (schedule == null) {
          throw Exception('جدولة الدفعات غير موجودة');
        }

        // الحصول على الفاتورة
        final invoice = await _invoiceService.getInvoiceById(
          schedule.invoiceId,
        );
        if (invoice == null) {
          throw Exception('الفاتورة المرتبطة بالجدولة غير موجودة');
        }

        // إنشاء دفعة فعلية باستخدام PaymentService
        final payment = Payment(
          invoiceId: schedule.invoiceId,
          amount: amount,
          paymentDate: DateTime.now(),
          method: PaymentMethod.fromCode(paymentMethod ?? 'cash'),
          reference:
              reference ??
              'جدولة-${schedule.id}-${DateTime.now().millisecondsSinceEpoch}',
          notes: notes ?? 'دفعة من جدولة: ${schedule.scheduleName}',
          status: PaymentStatus.confirmed,
        );

        // إضافة الدفعة باستخدام PaymentService
        final paymentId = await _paymentService.addPayment(payment);

        // تحديث الجدولة
        final updatedSchedule = schedule.recordPayment(amount);
        final nextSchedule = updatedSchedule.updateNextPaymentDate();

        // حفظ التحديث
        final scheduleData = nextSchedule.toMap();
        scheduleData['updated_at'] = DateTime.now().toIso8601String();

        await txn.update(
          AppConstants.paymentSchedulesTable,
          scheduleData,
          where: 'id = ?',
          whereArgs: [scheduleId],
        );

        // تسجيل العملية
        await AuditService.log(
          action: 'PAYMENT',
          entityType: 'payment_schedules',
          entityId: scheduleId,
          entityName: schedule.scheduleName,
          description: 'تسجيل دفعة في الجدولة: ${schedule.scheduleName}',
          newValues: {
            'payment_id': paymentId,
            'payment_amount': amount,
            'new_paid_amount': nextSchedule.paidAmount,
            'completed_installments': nextSchedule.completedInstallments,
            'next_payment_date': nextSchedule.nextPaymentDate.toIso8601String(),
          },
        );
      });

      LoggingService.info(
        'تم تسجيل دفعة في الجدولة',
        category: 'PaymentScheduleService',
        data: {'schedule_id': scheduleId, 'payment_amount': amount},
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في تسجيل الدفعة',
        category: 'PaymentScheduleService',
        data: {'schedule_id': scheduleId, 'error': e.toString()},
      );
      rethrow;
    }
  }

  /// إيقاف جدولة الدفعات
  Future<void> pauseSchedule(int scheduleId, {String? reason}) async {
    try {
      await _updateScheduleStatus(
        scheduleId,
        ScheduleStatus.paused,
        reason: reason ?? 'تم إيقاف الجدولة مؤقتاً',
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في إيقاف الجدولة',
        category: 'PaymentScheduleService',
        data: {'schedule_id': scheduleId, 'error': e.toString()},
      );
      rethrow;
    }
  }

  /// استئناف جدولة الدفعات
  Future<void> resumeSchedule(int scheduleId, {String? reason}) async {
    try {
      await _updateScheduleStatus(
        scheduleId,
        ScheduleStatus.active,
        reason: reason ?? 'تم استئناف الجدولة',
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في استئناف الجدولة',
        category: 'PaymentScheduleService',
        data: {'schedule_id': scheduleId, 'error': e.toString()},
      );
      rethrow;
    }
  }

  /// إلغاء جدولة الدفعات
  Future<void> cancelSchedule(int scheduleId, {String? reason}) async {
    try {
      await _updateScheduleStatus(
        scheduleId,
        ScheduleStatus.cancelled,
        reason: reason ?? 'تم إلغاء الجدولة',
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في إلغاء الجدولة',
        category: 'PaymentScheduleService',
        data: {'schedule_id': scheduleId, 'error': e.toString()},
      );
      rethrow;
    }
  }

  /// حذف جدولة الدفعات
  Future<void> deleteSchedule(int scheduleId) async {
    try {
      final db = await _databaseHelper.database;

      await db.transaction((txn) async {
        // الحصول على الجدولة للتسجيل
        final schedule = await getPaymentScheduleById(scheduleId);

        // حذف التذكيرات
        await txn.delete(
          AppConstants.paymentRemindersTable,
          where: 'schedule_id = ?',
          whereArgs: [scheduleId],
        );

        // حذف الجدولة
        await txn.delete(
          AppConstants.paymentSchedulesTable,
          where: 'id = ?',
          whereArgs: [scheduleId],
        );

        // تسجيل العملية
        if (schedule != null) {
          await AuditService.log(
            action: 'DELETE',
            entityType: 'payment_schedules',
            entityId: scheduleId,
            entityName: schedule.scheduleName,
            description: 'حذف جدولة الدفعات: ${schedule.scheduleName}',
          );
        }
      });

      LoggingService.info(
        'تم حذف جدولة الدفعات',
        category: 'PaymentScheduleService',
        data: {'schedule_id': scheduleId},
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في حذف جدولة الدفعات',
        category: 'PaymentScheduleService',
        data: {'schedule_id': scheduleId, 'error': e.toString()},
      );
      rethrow;
    }
  }

  /// معالجة الجداول المستحقة تلقائياً
  Future<void> processScheduledPayments() async {
    try {
      final dueSchedules = await getDueSchedules();

      for (final schedule in dueSchedules) {
        if (schedule.autoPayment) {
          // معالجة الدفع التلقائي
          await _processAutoPayment(schedule);
        } else {
          // إرسال تذكير
          await _sendPaymentReminder(schedule);
        }
      }

      LoggingService.info(
        'تم معالجة الجداول المستحقة',
        category: 'PaymentScheduleService',
        data: {'processed_count': dueSchedules.length},
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في معالجة الجداول المستحقة',
        category: 'PaymentScheduleService',
        data: {'error': e.toString()},
      );
    }
  }

  /// الحصول على تذكيرات الجدولة
  Future<List<PaymentReminder>> _getScheduleReminders(int scheduleId) async {
    try {
      final db = await _databaseHelper.database;

      final maps = await db.query(
        AppConstants.paymentRemindersTable,
        where: 'schedule_id = ?',
        whereArgs: [scheduleId],
        orderBy: 'days_before ASC',
      );

      return maps.map((map) => PaymentReminder.fromMap(map)).toList();
    } catch (e) {
      LoggingService.error(
        'خطأ في الحصول على التذكيرات',
        category: 'PaymentScheduleService',
        data: {'schedule_id': scheduleId, 'error': e.toString()},
      );
      return [];
    }
  }

  /// تحديث حالة الجدولة
  Future<void> _updateScheduleStatus(
    int scheduleId,
    ScheduleStatus status, {
    String? reason,
  }) async {
    try {
      final db = await _databaseHelper.database;

      await db.update(
        AppConstants.paymentSchedulesTable,
        {'status': status.code, 'updated_at': DateTime.now().toIso8601String()},
        where: 'id = ?',
        whereArgs: [scheduleId],
      );

      // تسجيل العملية
      await AuditService.log(
        action: 'STATUS_CHANGE',
        entityType: 'payment_schedules',
        entityId: scheduleId,
        description: reason ?? 'تغيير حالة الجدولة إلى ${status.displayName}',
        newValues: {'status': status.code},
      );

      LoggingService.info(
        'تم تحديث حالة الجدولة',
        category: 'PaymentScheduleService',
        data: {
          'schedule_id': scheduleId,
          'new_status': status.code,
          'reason': reason,
        },
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في تحديث حالة الجدولة',
        category: 'PaymentScheduleService',
        data: {'schedule_id': scheduleId, 'error': e.toString()},
      );
      rethrow;
    }
  }

  /// معالجة الدفع التلقائي
  Future<void> _processAutoPayment(PaymentSchedule schedule) async {
    try {
      // معالجة الدفع التلقائي باستخدام PaymentService
      await recordScheduledPayment(
        schedule.id!,
        schedule.installmentAmount,
        paymentMethod: 'auto', // طريقة دفع تلقائية
        reference:
            'AUTO-${schedule.id}-${DateTime.now().millisecondsSinceEpoch}',
        notes: 'دفع تلقائي من جدولة: ${schedule.scheduleName}',
      );

      LoggingService.info(
        'تم معالجة دفع تلقائي',
        category: 'PaymentScheduleService',
        data: {
          'schedule_id': schedule.id,
          'amount': schedule.installmentAmount,
        },
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في معالجة الدفع التلقائي',
        category: 'PaymentScheduleService',
        data: {'schedule_id': schedule.id, 'error': e.toString()},
      );

      // في حالة فشل الدفع التلقائي، إرسال تذكير
      await _sendPaymentReminder(schedule);
    }
  }

  /// تحديث تذكير الدفع
  Future<void> updateReminder(PaymentReminder reminder) async {
    try {
      final db = await _databaseHelper.database;

      // التحقق من وجود التذكير
      if (reminder.id == null) {
        throw Exception('معرف التذكير مطلوب للتحديث');
      }

      // التحقق من صحة البيانات
      if (reminder.daysBefore < 0) {
        throw Exception('عدد الأيام قبل الاستحقاق يجب أن يكون موجباً');
      }

      if (reminder.title.trim().isEmpty) {
        throw Exception('عنوان التذكير مطلوب');
      }

      if (reminder.message.trim().isEmpty) {
        throw Exception('رسالة التذكير مطلوبة');
      }

      await db.transaction((txn) async {
        // تحديث التذكير
        final reminderData = reminder.toMap();
        reminderData['updated_at'] = DateTime.now().toIso8601String();

        final updatedRows = await txn.update(
          AppConstants.paymentRemindersTable,
          reminderData,
          where: 'id = ?',
          whereArgs: [reminder.id],
        );

        if (updatedRows == 0) {
          throw Exception('التذكير غير موجود');
        }

        // تسجيل العملية
        await AuditService.log(
          action: 'UPDATE',
          entityType: 'payment_reminders',
          entityId: reminder.id!,
          entityName: reminder.title,
          description: 'تحديث تذكير الدفع: ${reminder.title}',
          newValues: reminderData,
        );
      });

      LoggingService.info(
        'تم تحديث التذكير بنجاح',
        category: 'PaymentScheduleService',
        data: {
          'reminder_id': reminder.id,
          'schedule_id': reminder.scheduleId,
          'is_active': reminder.isActive,
        },
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في تحديث التذكير',
        category: 'PaymentScheduleService',
        data: {'reminder_id': reminder.id, 'error': e.toString()},
      );
      rethrow;
    }
  }

  /// إرسال تذكير الدفع
  Future<void> _sendPaymentReminder(PaymentSchedule schedule) async {
    try {
      final dueReminders = schedule.getDueReminders();

      for (final reminder in dueReminders) {
        // هنا يمكن إضافة منطق إرسال التذكيرات
        // مثل الإشعارات أو البريد الإلكتروني

        LoggingService.info(
          'إرسال تذكير دفع',
          category: 'PaymentScheduleService',
          data: {
            'schedule_id': schedule.id,
            'reminder_type': reminder.type.code,
            'days_before': reminder.daysBefore,
          },
        );
      }
    } catch (e) {
      LoggingService.error(
        'خطأ في إرسال تذكير الدفع',
        category: 'PaymentScheduleService',
        data: {'schedule_id': schedule.id, 'error': e.toString()},
      );
    }
  }

  /// البحث في جداول الدفعات
  Future<List<PaymentSchedule>> searchSchedules({
    String? searchTerm,
    ScheduleStatus? status,
    PaymentFrequency? frequency,
    DateTime? fromDate,
    DateTime? toDate,
    int? limit,
    int? offset,
  }) async {
    try {
      final db = await _databaseHelper.database;

      String whereClause = '1=1';
      List<dynamic> whereArgs = [];

      if (searchTerm != null && searchTerm.isNotEmpty) {
        whereClause += ' AND schedule_name LIKE ?';
        whereArgs.add('%$searchTerm%');
      }

      if (status != null) {
        whereClause += ' AND status = ?';
        whereArgs.add(status.code);
      }

      if (frequency != null) {
        whereClause += ' AND frequency = ?';
        whereArgs.add(frequency.code);
      }

      if (fromDate != null) {
        whereClause += ' AND next_payment_date >= ?';
        whereArgs.add(fromDate.toIso8601String());
      }

      if (toDate != null) {
        whereClause += ' AND next_payment_date <= ?';
        whereArgs.add(toDate.toIso8601String());
      }

      final maps = await db.query(
        AppConstants.paymentSchedulesTable,
        where: whereClause,
        whereArgs: whereArgs.isNotEmpty ? whereArgs : null,
        orderBy: 'next_payment_date ASC',
        limit: limit,
        offset: offset,
      );

      final schedules = <PaymentSchedule>[];

      for (final map in maps) {
        final schedule = PaymentSchedule.fromMap(map);
        final reminders = await _getScheduleReminders(schedule.id!);
        schedules.add(schedule.copyWith(reminders: reminders));
      }

      return schedules;
    } catch (e) {
      LoggingService.error(
        'خطأ في البحث في الجداول',
        category: 'PaymentScheduleService',
        data: {'error': e.toString()},
      );
      return [];
    }
  }

  /// الحصول على تاريخ الدفعات لجدولة معينة
  Future<List<Payment>> getSchedulePaymentHistory(int scheduleId) async {
    try {
      final schedule = await getPaymentScheduleById(scheduleId);
      if (schedule == null) {
        throw Exception('جدولة الدفعات غير موجودة');
      }

      // الحصول على جميع دفعات الفاتورة المرتبطة بالجدولة
      final payments = await _paymentService.getInvoicePayments(
        schedule.invoiceId,
      );

      // تصفية الدفعات التي تحتوي على مرجع الجدولة في الملاحظات
      final schedulePayments = payments.where((payment) {
        return payment.notes?.contains('جدولة: ${schedule.scheduleName}') ==
                true ||
            payment.reference?.contains('جدولة-${schedule.id}') == true ||
            payment.reference?.contains('AUTO-${schedule.id}') == true;
      }).toList();

      // ترتيب الدفعات حسب التاريخ
      schedulePayments.sort((a, b) => b.paymentDate.compareTo(a.paymentDate));

      return schedulePayments;
    } catch (e) {
      LoggingService.error(
        'خطأ في الحصول على تاريخ دفعات الجدولة',
        category: 'PaymentScheduleService',
        data: {'schedule_id': scheduleId, 'error': e.toString()},
      );
      return [];
    }
  }

  /// الحصول على ملخص الدفعات للجدولة
  Future<Map<String, dynamic>> getSchedulePaymentSummary(int scheduleId) async {
    try {
      final schedule = await getPaymentScheduleById(scheduleId);
      if (schedule == null) {
        throw Exception('جدولة الدفعات غير موجودة');
      }

      final payments = await getSchedulePaymentHistory(scheduleId);
      final totalPaidFromSchedule = payments
          .where((p) => p.status == PaymentStatus.confirmed)
          .fold(0.0, (sum, payment) => sum + payment.amount);

      return {
        'schedule_id': scheduleId,
        'total_amount': schedule.totalAmount,
        'paid_amount': schedule.paidAmount,
        'remaining_amount': schedule.totalAmount - schedule.paidAmount,
        'payments_from_schedule': totalPaidFromSchedule,
        'total_installments': schedule.totalInstallments,
        'completed_installments': schedule.completedInstallments,
        'remaining_installments':
            schedule.totalInstallments - schedule.completedInstallments,
        'next_payment_date': schedule.nextPaymentDate.toIso8601String(),
        'payment_count': payments.length,
        'confirmed_payment_count': payments
            .where((p) => p.status == PaymentStatus.confirmed)
            .length,
        'completion_percentage': schedule.totalAmount > 0
            ? (schedule.paidAmount / schedule.totalAmount) * 100
            : 0.0,
      };
    } catch (e) {
      LoggingService.error(
        'خطأ في الحصول على ملخص دفعات الجدولة',
        category: 'PaymentScheduleService',
        data: {'schedule_id': scheduleId, 'error': e.toString()},
      );
      return {};
    }
  }

  /// الحصول على إحصائيات الجداول
  Future<Map<String, dynamic>> getScheduleStatistics() async {
    try {
      final db = await _databaseHelper.database;

      // إجمالي الجداول
      final totalResult = await db.rawQuery(
        'SELECT COUNT(*) as total FROM ${AppConstants.paymentSchedulesTable}',
      );
      final total = totalResult.first['total'] as int;

      // الجداول النشطة
      final activeResult = await db.rawQuery(
        'SELECT COUNT(*) as active FROM ${AppConstants.paymentSchedulesTable} WHERE status = ?',
        [ScheduleStatus.active.code],
      );
      final active = activeResult.first['active'] as int;

      // الجداول المكتملة
      final completedResult = await db.rawQuery(
        'SELECT COUNT(*) as completed FROM ${AppConstants.paymentSchedulesTable} WHERE status = ?',
        [ScheduleStatus.completed.code],
      );
      final completed = completedResult.first['completed'] as int;

      // الجداول المستحقة
      final dueResult = await db.rawQuery(
        '''SELECT COUNT(*) as due FROM ${AppConstants.paymentSchedulesTable}
           WHERE status = ? AND next_payment_date <= ?''',
        [ScheduleStatus.active.code, DateTime.now().toIso8601String()],
      );
      final due = dueResult.first['due'] as int;

      // إجمالي المبالغ
      final amountResult = await db.rawQuery(
        '''SELECT
             SUM(total_amount) as total_amount,
             SUM(paid_amount) as paid_amount
           FROM ${AppConstants.paymentSchedulesTable}
           WHERE status != ?''',
        [ScheduleStatus.cancelled.code],
      );

      final totalAmount = amountResult.first['total_amount'] as double? ?? 0.0;
      final paidAmount = amountResult.first['paid_amount'] as double? ?? 0.0;

      return {
        'total_schedules': total,
        'active_schedules': active,
        'completed_schedules': completed,
        'due_schedules': due,
        'total_amount': totalAmount,
        'paid_amount': paidAmount,
        'remaining_amount': totalAmount - paidAmount,
        'completion_percentage': totalAmount > 0
            ? (paidAmount / totalAmount) * 100
            : 0.0,
      };
    } catch (e) {
      LoggingService.error(
        'خطأ في الحصول على إحصائيات الجداول',
        category: 'PaymentScheduleService',
        data: {'error': e.toString()},
      );
      return {};
    }
  }
}
