import 'package:flutter/material.dart';
import '../constants/revolutionary_design_colors.dart';
import '../models/customer.dart';

class CustomerCard extends StatelessWidget {
  final Customer customer;
  final VoidCallback onTap;
  final VoidCallback onEdit;
  final VoidCallback onDelete;

  const CustomerCard({
    super.key,
    required this.customer,
    required this.onTap,
    required this.onEdit,
    required this.onDelete,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: InkWell(
        borderRadius: BorderRadius.circular(12),
        onTap: onTap,
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  // أيقونة العميل
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: RevolutionaryColors.damascusSky.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Icon(
                      Icons.person,
                      color: RevolutionaryColors.damascusSky,
                      size: 20,
                    ),
                  ),
                  const SizedBox(width: 12),
                  // معلومات العميل
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Expanded(
                              child: Text(
                                customer.name,
                                style: Theme.of(context).textTheme.titleMedium
                                    ?.copyWith(
                                      fontWeight: FontWeight.w600,
                                      color: RevolutionaryColors.textPrimary,
                                    ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                            if (!customer.isActive)
                              Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 8,
                                  vertical: 2,
                                ),
                                decoration: BoxDecoration(
                                  color: RevolutionaryColors.errorCoral.withValues(alpha: 0.1),
                                  borderRadius: BorderRadius.circular(4),
                                ),
                                child: Text(
                                  'غير نشط',
                                  style: Theme.of(context).textTheme.bodySmall
                                      ?.copyWith(
                                        color: RevolutionaryColors.errorCoral,
                                        fontSize: 10,
                                      ),
                                ),
                              ),
                          ],
                        ),
                        const SizedBox(height: 4),
                        Row(
                          children: [
                            Text(
                              'كود: ${customer.code}',
                              style: Theme.of(context).textTheme.bodySmall
                                  ?.copyWith(color: RevolutionaryColors.textSecondary),
                            ),
                            if (customer.phone != null &&
                                customer.phone!.isNotEmpty) ...[
                              const SizedBox(width: 16),
                              Icon(
                                Icons.phone,
                                size: 12,
                                color: RevolutionaryColors.textSecondary,
                              ),
                              const SizedBox(width: 4),
                              Text(
                                customer.phone!,
                                style: Theme.of(context).textTheme.bodySmall
                                    ?.copyWith(color: RevolutionaryColors.textSecondary),
                              ),
                            ],
                          ],
                        ),
                      ],
                    ),
                  ),
                  // قائمة الخيارات
                  PopupMenuButton<String>(
                    onSelected: (value) {
                      switch (value) {
                        case 'edit':
                          onEdit();
                          break;
                        case 'delete':
                          onDelete();
                          break;
                      }
                    },
                    itemBuilder: (context) => [
                      const PopupMenuItem(
                        value: 'edit',
                        child: Row(
                          children: [
                            Icon(Icons.edit, size: 18),
                            SizedBox(width: 8),
                            Text('تعديل'),
                          ],
                        ),
                      ),
                      const PopupMenuItem(
                        value: 'delete',
                        child: Row(
                          children: [
                            Icon(Icons.delete, size: 18, color: Colors.red),
                            SizedBox(width: 8),
                            Text('حذف', style: TextStyle(color: Colors.red)),
                          ],
                        ),
                      ),
                    ],
                    child: const Icon(
                      Icons.more_vert,
                      color: RevolutionaryColors.textSecondary,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              // الرصيد وحالة العميل
              Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'الرصيد:',
                          style: Theme.of(context).textTheme.bodySmall
                              ?.copyWith(color: RevolutionaryColors.textSecondary),
                        ),
                        Text(
                          '${customer.displayBalance} ل.س',
                          style: Theme.of(context).textTheme.titleMedium
                              ?.copyWith(
                                fontWeight: FontWeight.w600,
                                color: customer.balance >= 0
                                    ? RevolutionaryColors.successGlow
                                    : RevolutionaryColors.errorCoral,
                              ),
                        ),
                      ],
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: _getBalanceStatusColor().withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(6),
                    ),
                    child: Text(
                      customer.balanceStatus,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: _getBalanceStatusColor(),
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ],
              ),
              // حد الائتمان (إذا كان موجوداً)
              if (customer.creditLimit > 0) ...[
                const SizedBox(height: 8),
                Row(
                  children: [
                    Icon(
                      Icons.credit_card,
                      size: 16,
                      color: RevolutionaryColors.textSecondary,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      'حد الائتمان: ${customer.creditLimit.toStringAsFixed(2)} ل.س',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: RevolutionaryColors.textSecondary,
                      ),
                    ),
                    if (customer.isOverCreditLimit) ...[
                      const SizedBox(width: 8),
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 6,
                          vertical: 2,
                        ),
                        decoration: BoxDecoration(
                          color: RevolutionaryColors.errorCoral.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Text(
                          'تجاوز الحد',
                          style: Theme.of(context).textTheme.bodySmall
                              ?.copyWith(color: RevolutionaryColors.errorCoral, fontSize: 10),
                        ),
                      ),
                    ],
                  ],
                ),
              ],
              // العنوان (إذا كان موجوداً)
              if (customer.address != null && customer.address!.isNotEmpty) ...[
                const SizedBox(height: 8),
                Row(
                  children: [
                    Icon(
                      Icons.location_on,
                      size: 16,
                      color: RevolutionaryColors.textSecondary,
                    ),
                    const SizedBox(width: 4),
                    Expanded(
                      child: Text(
                        customer.address!,
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: RevolutionaryColors.textSecondary,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Color _getBalanceStatusColor() {
    if (customer.balance > 0) {
      return RevolutionaryColors.successGlow; // مدين
    } else if (customer.balance < 0) {
      return RevolutionaryColors.errorCoral; // دائن
    } else {
      return RevolutionaryColors.textSecondary; // متوازن
    }
  }
}
