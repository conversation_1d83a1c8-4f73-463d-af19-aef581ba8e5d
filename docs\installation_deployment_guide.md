# 🚀 دليل التثبيت والنشر - Smart Ledger

**الإصدار:** 1.0.0  
**التاريخ:** 15 يوليو 2025  
**المطور:** مجد محمد زياد يسير  

---

## 🎯 نظرة عامة

هذا الدليل يوفر إرشادات شاملة لتثبيت ونشر تطبيق Smart Ledger على أنظمة Windows و Android، بما في ذلك إعداد البيئة والتوزيع والصيانة.

---

## 📋 متطلبات النظام

### 🖥️ Windows

#### الحد الأدنى
- **نظام التشغيل:** Windows 10 (64-bit)
- **المعالج:** Intel Core i3 أو AMD Ryzen 3
- **الذاكرة:** 4 GB RAM
- **التخزين:** 500 MB مساحة فارغة
- **الشاشة:** 1366x768 دقة

#### المُوصى به
- **نظام التشغيل:** Windows 11 (64-bit)
- **المعالج:** Intel Core i5 أو AMD Ryzen 5
- **الذاكرة:** 8 GB RAM
- **التخزين:** 1 GB مساحة فارغة + SSD
- **الشاشة:** 1920x1080 دقة أو أعلى

### 📱 Android

#### الحد الأدنى
- **نظام التشغيل:** Android 7.0 (API level 24)
- **الذاكرة:** 2 GB RAM
- **التخزين:** 200 MB مساحة فارغة
- **الشاشة:** 5 بوصة، 720p

#### المُوصى به
- **نظام التشغيل:** Android 10.0 أو أحدث
- **الذاكرة:** 4 GB RAM أو أكثر
- **التخزين:** 500 MB مساحة فارغة
- **الشاشة:** 6 بوصة، 1080p أو أعلى

---

## 🔧 إعداد بيئة التطوير

### للمطورين فقط

#### 1. تثبيت Flutter
```bash
# تحميل Flutter SDK
# من https://flutter.dev/docs/get-started/install

# إضافة Flutter إلى PATH
export PATH="$PATH:`pwd`/flutter/bin"

# التحقق من التثبيت
flutter doctor
```

#### 2. إعداد Android Studio
```bash
# تثبيت Android Studio
# تثبيت Android SDK
# إعداد AVD للاختبار

# التحقق من إعداد Android
flutter doctor --android-licenses
```

#### 3. إعداد Visual Studio (للـ Windows)
```bash
# تثبيت Visual Studio 2022
# تثبيت C++ build tools
# تثبيت Windows SDK

# التحقق من إعداد Windows
flutter config --enable-windows-desktop
```

---

## 📦 بناء التطبيق للإنتاج

### 🔨 البناء التلقائي

#### استخدام سكريبت البناء
```bash
# تشغيل سكريبت البناء الشامل
scripts/build_production.bat

# أو للـ Linux/Mac
scripts/build_production.sh
```

### 🔧 البناء اليدوي

#### 1. تنظيف المشروع
```bash
flutter clean
flutter pub get
```

#### 2. تشغيل الاختبارات
```bash
flutter test
flutter analyze
```

#### 3. بناء Windows
```bash
flutter build windows --release
```

#### 4. بناء Android
```bash
# APK للتوزيع المباشر
flutter build apk --release --split-per-abi

# App Bundle لـ Google Play Store
flutter build appbundle --release
```

#### 5. بناء الويب (اختياري)
```bash
flutter build web --release
```

---

## 📥 تثبيت التطبيق

### 🖥️ تثبيت Windows

#### الطريقة الأولى: المثبت التلقائي
1. **تحميل المثبت**
   - حمل ملف `smart_ledger_installer.exe`
   - تأكد من أن الملف من مصدر موثوق

2. **تشغيل المثبت**
   ```cmd
   # تشغيل كمدير
   smart_ledger_installer.exe
   ```

3. **اتباع التعليمات**
   - اختر مجلد التثبيت
   - اقبل اتفاقية الترخيص
   - انتظر انتهاء التثبيت

#### الطريقة الثانية: التثبيت اليدوي
1. **استخراج الملفات**
   ```cmd
   # استخراج smart_ledger_windows.zip
   # إلى مجلد مناسب مثل:
   C:\Program Files\Smart Ledger\
   ```

2. **إنشاء اختصار**
   ```cmd
   # إنشاء اختصار على سطح المكتب
   # يشير إلى smart_ledger.exe
   ```

3. **تشغيل التطبيق**
   ```cmd
   # تشغيل من الاختصار أو مباشرة
   smart_ledger.exe
   ```

### 📱 تثبيت Android

#### الطريقة الأولى: تثبيت APK
1. **تمكين المصادر غير المعروفة**
   - الإعدادات > الأمان > مصادر غير معروفة
   - أو الإعدادات > التطبيقات > وصول خاص > تثبيت تطبيقات غير معروفة

2. **تثبيت APK**
   ```bash
   # نسخ ملف APK إلى الجهاز
   # النقر على الملف لبدء التثبيت
   # اتباع التعليمات على الشاشة
   ```

#### الطريقة الثانية: Google Play Store
1. **رفع App Bundle**
   - رفع ملف `.aab` إلى Google Play Console
   - إكمال معلومات التطبيق
   - نشر التطبيق

2. **تثبيت من المتجر**
   - البحث عن "Smart Ledger"
   - النقر على "تثبيت"

---

## ⚙️ الإعداد الأولي

### 🔐 إعداد الأمان

#### 1. إنشاء كلمة مرور رئيسية
```
عند التشغيل الأول:
1. اختر كلمة مرور قوية (8 أحرف على الأقل)
2. تأكد من حفظ كلمة المرور في مكان آمن
3. لا يمكن استرداد كلمة المرور إذا نُسيت
```

#### 2. إعداد النسخ الاحتياطية
```
الإعدادات > النسخ الاحتياطية:
1. تمكين النسخ الاحتياطية التلقائية
2. اختيار مجلد الحفظ
3. تحديد تكرار النسخ (يومي/أسبوعي)
4. اختبار النسخ الاحتياطي
```

### 🏢 إعداد الشركة

#### 1. معلومات الشركة الأساسية
```
الإعدادات > معلومات الشركة:
- اسم الشركة
- العنوان الكامل
- الهاتف والفاكس
- البريد الإلكتروني
- الموقع الإلكتروني
- الرقم الضريبي
- رقم السجل التجاري
```

#### 2. إعدادات المحاسبة
```
الإعدادات > المحاسبة:
- العملة الأساسية (الليرة السورية)
- عدد الخانات العشرية
- السنة المالية
- طريقة تقييم المخزون
- إعدادات الضرائب
```

### 📊 إعداد دليل الحسابات

#### 1. استيراد دليل الحسابات الافتراضي
```
الحسابات > استيراد:
1. اختيار "دليل الحسابات السوري الموحد"
2. مراجعة الحسابات المستوردة
3. تخصيص الحسابات حسب الحاجة
4. حفظ التغييرات
```

#### 2. إضافة حسابات مخصصة
```
الحسابات > إضافة جديد:
- رمز الحساب (تلقائي)
- اسم الحساب
- نوع الحساب
- الحساب الأب (إن وجد)
- الرصيد الافتتاحي
```

---

## 🔄 نظام التحديثات

### 🔍 فحص التحديثات

#### تحديثات تلقائية
```
الإعدادات > التحديثات:
1. تمكين فحص التحديثات التلقائي
2. اختيار تكرار الفحص
3. تمكين التنبيهات
```

#### تحديثات يدوية
```
المساعدة > فحص التحديثات:
1. النقر على "فحص الآن"
2. مراجعة التحديثات المتاحة
3. تحميل وتثبيت التحديثات
4. إعادة تشغيل التطبيق
```

### 📋 إجراءات التحديث

#### قبل التحديث
1. **إنشاء نسخة احتياطية كاملة**
2. **إغلاق جميع النوافذ**
3. **التأكد من حفظ جميع البيانات**

#### أثناء التحديث
1. **عدم إيقاف التطبيق**
2. **عدم إيقاف تشغيل الجهاز**
3. **انتظار انتهاء التحديث**

#### بعد التحديث
1. **إعادة تشغيل التطبيق**
2. **التحقق من البيانات**
3. **اختبار الوظائف الأساسية**

---

## 🛠️ استكشاف الأخطاء وإصلاحها

### ❌ مشاكل التثبيت الشائعة

#### Windows
```
المشكلة: "لا يمكن تشغيل التطبيق"
الحل:
1. تثبيت Visual C++ Redistributable
2. تحديث Windows
3. تشغيل كمدير
4. فحص مكافح الفيروسات

المشكلة: "ملف مفقود"
الحل:
1. إعادة تثبيت التطبيق
2. استثناء مجلد التطبيق من مكافح الفيروسات
3. تحميل نسخة جديدة
```

#### Android
```
المشكلة: "فشل التثبيت"
الحل:
1. تمكين مصادر غير معروفة
2. توفير مساحة تخزين كافية
3. إعادة تشغيل الجهاز
4. تحديث Android

المشكلة: "التطبيق يتوقف"
الحل:
1. مسح ذاكرة التطبيق المؤقتة
2. إعادة تثبيت التطبيق
3. التحقق من توافق الإصدار
```

### 🔧 أدوات التشخيص

#### سجلات النظام
```
Windows: %APPDATA%\Smart Ledger\logs\
Android: /Android/data/com.smartledger/files/logs/

ملفات السجل:
- app.log (سجل التطبيق العام)
- database.log (سجل قاعدة البيانات)
- error.log (سجل الأخطاء)
- performance.log (سجل الأداء)
```

#### أدوات الفحص المدمجة
```
المساعدة > تشخيص النظام:
1. فحص قاعدة البيانات
2. فحص الملفات
3. فحص الأداء
4. تصدير تقرير التشخيص
```

---

## 📞 الدعم والمساعدة

### 🆘 طلب الدعم

#### معلومات مطلوبة
```
عند طلب الدعم، يرجى تقديم:
1. إصدار التطبيق
2. نظام التشغيل والإصدار
3. وصف مفصل للمشكلة
4. خطوات إعادة إنتاج المشكلة
5. رسائل الخطأ (إن وجدت)
6. ملفات السجل
```

#### قنوات الدعم
```
- البريد الإلكتروني: <EMAIL>
- الهاتف: +963-11-1234567
- الموقع الإلكتروني: www.smartledger.sy
- المنتدى: forum.smartledger.sy
```

### 📚 الموارد الإضافية

#### التوثيق
- [دليل المستخدم](user_guide_final.md)
- [الدليل التقني](developer_guide_technical.md)
- [توثيق APIs](api_services_documentation.md)
- [دليل الصيانة](maintenance_support_guide.md)

#### التدريب
- فيديوهات تعليمية على YouTube
- ورش عمل أونلاين
- دورات تدريبية متخصصة
- دليل البدء السريع

---

## ✅ قائمة التحقق النهائية

### ☑️ قبل النشر
- [ ] اختبار جميع الوظائف الأساسية
- [ ] فحص الأمان والتشفير
- [ ] اختبار النسخ الاحتياطية
- [ ] مراجعة التوثيق
- [ ] اختبار التثبيت على أجهزة مختلفة
- [ ] فحص الأداء تحت الضغط
- [ ] التحقق من التوافق مع أنظمة مختلفة

### ☑️ بعد النشر
- [ ] مراقبة التقارير الأولية
- [ ] الاستجابة لطلبات الدعم
- [ ] جمع ملاحظات المستخدمين
- [ ] تحديث التوثيق حسب الحاجة
- [ ] تخطيط التحديثات المستقبلية

---

## 🎉 الخلاصة

تم إعداد دليل شامل لتثبيت ونشر Smart Ledger بنجاح. اتبع هذه الإرشادات لضمان:

- **تثبيت سلس** على جميع المنصات
- **إعداد صحيح** للنظام
- **أداء مثالي** للتطبيق
- **دعم فعال** للمستخدمين

**Smart Ledger جاهز للانطلاق! 🚀**

---

**تم إعداد الدليل بواسطة:** مجد محمد زياد يسير  
**التاريخ:** 15 يوليو 2025  
**الإصدار:** 1.0.0
