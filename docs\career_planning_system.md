# نظام التخطيط الوظيفي - Smart Ledger

## 📋 نظرة عامة

تم تطوير نظام شامل للتخطيط الوظيفي والتطوير المهني في Smart Ledger يوفر أدوات متقدمة لإدارة المسارات الوظيفية وخطط التطوير المهني للموظفين.

## 🎯 الأهداف الرئيسية

- **تخطيط مهني منهجي**: نظام شامل لتخطيط المسارات الوظيفية
- **تطوير مستمر**: خطط تطوير مخصصة لكل موظف
- **تتبع التقدم**: مراقبة تطور الموظفين في مساراتهم المهنية
- **توجيه ومرشدين**: نظام إرشاد مهني متكامل
- **تحليل الفجوات**: تحديد فجوات المهارات وخطط سدها

## 🏗️ مكونات النظام

### 1. النماذج (Models)

#### CareerPath - المسار الوظيفي
```dart
class CareerPath {
  final String name;                    // اسم المسار
  final String description;             // وصف المسار
  final String department;              // القسم
  final String level;                   // المستوى الوظيفي
  final List<String> requiredSkills;    // المهارات المطلوبة
  final double minSalary;               // الحد الأدنى للراتب
  final double maxSalary;               // الحد الأقصى للراتب
  final int experienceYears;            // سنوات الخبرة المطلوبة
  final String? nextLevelPath;          // المسار التالي
}
```

#### CareerDevelopmentPlan - خطة التطوير الوظيفي
```dart
class CareerDevelopmentPlan {
  final int employeeId;                 // معرف الموظف
  final int currentPathId;              // المسار الحالي
  final int? targetPathId;              // المسار المستهدف
  final String status;                  // حالة الخطة
  final DateTime startDate;             // تاريخ البداية
  final DateTime targetDate;            // التاريخ المستهدف
  final String goals;                   // الأهداف
  final String skillGaps;               // فجوات المهارات
  final String developmentActions;      // إجراءات التطوير
  final double progressPercentage;      // نسبة التقدم
  final int? mentorId;                  // معرف الموجه
}
```

#### CareerReview - مراجعة التطوير الوظيفي
```dart
class CareerReview {
  final int planId;                     // معرف الخطة
  final int reviewerId;                 // معرف المراجع
  final DateTime reviewDate;            // تاريخ المراجعة
  final String reviewType;              // نوع المراجعة
  final double progressRating;          // تقييم التقدم
  final String achievements;            // الإنجازات
  final String challenges;              // التحديات
  final String recommendations;         // التوصيات
}
```

### 2. الخدمات (Services)

#### CareerPlanningService
خدمة شاملة توفر جميع العمليات المتعلقة بالتخطيط الوظيفي:

- **إدارة المسارات**: إضافة وتعديل المسارات الوظيفية
- **خطط التطوير**: إنشاء وإدارة خطط التطوير الشخصية
- **تتبع التقدم**: مراقبة تقدم الموظفين في خططهم
- **المراجعات**: نظام مراجعات دورية للتطوير
- **الإحصائيات**: تقارير شاملة عن التطوير المهني
- **اقتراح المسارات**: اقتراح مسارات مناسبة للموظفين

### 3. الواجهات (Screens)

#### CareerPlanningScreen
الشاشة الرئيسية لنظام التخطيط الوظيفي تحتوي على:

- **نظرة عامة**: إحصائيات ومؤشرات التطوير
- **المسارات الوظيفية**: إدارة المسارات المتاحة
- **خطط التطوير**: عرض وإدارة خطط التطوير
- **المراجعات**: مراجعات دورية للتقدم
- **التقارير**: تقارير وتحليلات شاملة

#### CareerDevelopmentPlanFormScreen
شاشة إنشاء وتعديل خطط التطوير تتضمن:

- **المعلومات الأساسية**: بيانات الموظف والحالة
- **المسارات الوظيفية**: المسار الحالي والمستهدف
- **التواريخ**: فترة تنفيذ الخطة
- **الأهداف والمهارات**: تحديد الأهداف وفجوات المهارات
- **إجراءات التطوير**: الأنشطة والمعالم المطلوبة
- **الموجه والملاحظات**: تعيين موجه وإضافة ملاحظات
- **تتبع التقدم**: مراقبة نسبة الإنجاز

## 📊 المسارات الوظيفية الافتراضية

### 1. مسار المحاسبة
- **محاسب مبتدئ** (Entry Level)
  - الراتب: 800,000 - 1,200,000 ل.س
  - المهارات: أساسيات المحاسبة، Excel، إدخال البيانات
  - الخبرة: 0 سنوات

- **محاسب** (Mid Level)
  - الراتب: 1,200,000 - 1,800,000 ل.س
  - المهارات: المحاسبة المتقدمة، التقارير المالية، التحليل المالي
  - الخبرة: 2-5 سنوات

- **محاسب أول** (Senior Level)
  - الراتب: 1,800,000 - 2,500,000 ل.س
  - المهارات: المحاسبة المتقدمة، الإشراف، التدقيق الداخلي
  - الخبرة: 5+ سنوات

- **مدير محاسبة** (Manager Level)
  - الراتب: 2,500,000 - 3,500,000 ل.س
  - المهارات: الإدارة، التخطيط المالي، القيادة
  - الخبرة: 8+ سنوات

### 2. مسار تقنية المعلومات
- **مطور برمجيات مبتدئ** (Entry Level)
  - الراتب: 1,000,000 - 1,500,000 ل.س
  - المهارات: البرمجة الأساسية، قواعد البيانات، Git
  - الخبرة: 0 سنوات

- **مطور برمجيات** (Mid Level)
  - الراتب: 1,500,000 - 2,200,000 ل.س
  - المهارات: البرمجة المتقدمة، تصميم الأنظمة، اختبار البرمجيات
  - الخبرة: 2-5 سنوات

## 🔄 دورة التطوير الوظيفي

### 1. التقييم الأولي
- تحليل المهارات الحالية
- تحديد الأهداف المهنية
- اختيار المسار المناسب

### 2. وضع الخطة
- تحديد المسار المستهدف
- وضع جدول زمني
- تحديد إجراءات التطوير
- تعيين موجه مهني

### 3. التنفيذ
- تنفيذ أنشطة التطوير
- متابعة التقدم
- تحديث الخطة حسب الحاجة

### 4. المراجعة والتقييم
- مراجعات دورية
- تقييم التقدم
- تعديل الخطة
- تحديد الخطوات التالية

## 📈 نظام التقييم والمراجعة

### أنواع المراجعات
- **ربع سنوية**: مراجعة سريعة للتقدم
- **نصف سنوية**: تقييم متوسط مع تعديلات
- **سنوية**: مراجعة شاملة وتخطيط للعام القادم
- **معالم مهمة**: مراجعة عند إنجاز معالم محددة

### معايير التقييم
- **التقدم في المهارات**: 1-5 نقاط
- **تحقيق الأهداف**: نسبة الإنجاز
- **الالتزام بالخطة**: مدى الالتزام بالجدول الزمني
- **التطوير الذاتي**: المبادرات الشخصية

## 🛠️ الميزات المتقدمة

### 1. اقتراح المسارات الذكي
- تحليل المهارات الحالية
- مطابقة مع متطلبات المسارات
- ترتيب المسارات حسب التطابق

### 2. نظام الإرشاد
- تعيين موجهين مهنيين
- متابعة دورية
- تبادل الخبرات

### 3. تتبع التقدم المرئي
- مؤشرات تقدم تفاعلية
- رسوم بيانية للتطور
- معالم الإنجاز

### 4. التكامل مع الأنظمة الأخرى
- ربط مع نظام تقييم الأداء
- تكامل مع نظام التدريب
- ربط مع نظام الرواتب

## 🔒 الأمان والخصوصية

### 1. التحكم في الوصول
- صلاحيات محددة للمستخدمين
- حماية خطط التطوير الشخصية
- تشفير البيانات الحساسة

### 2. سجل المراجعة
- تسجيل جميع التغييرات
- تتبع المراجعات
- مراجعة الأنشطة

## 📋 خطة التطوير المستقبلية

### المرحلة الثانية
- [ ] نظام التخطيط للخلافة الوظيفية
- [ ] تحليل الفجوات على مستوى المؤسسة
- [ ] خطط التطوير الجماعية
- [ ] تكامل مع منصات التعلم الإلكتروني

### المرحلة الثالثة
- [ ] الذكاء الاصطناعي لاقتراح المسارات
- [ ] التنبؤ بالاحتياجات المستقبلية
- [ ] نظام التوصيات الذكية
- [ ] تحليل اتجاهات السوق

## ✅ الخلاصة

تم بنجاح تطوير نظام شامل للتخطيط الوظيفي في Smart Ledger يوفر:

- **أدوات متقدمة** لإدارة المسارات الوظيفية
- **خطط تطوير مخصصة** لكل موظف
- **نظام مراجعة دوري** لضمان التقدم
- **واجهات سهلة الاستخدام** للموظفين والمدراء
- **تقارير شاملة** لدعم اتخاذ القرارات
- **تكامل كامل** مع أنظمة الموارد البشرية الأخرى

هذا النظام يضع Smart Ledger في المقدمة من ناحية إدارة التطوير المهني والتخطيط الوظيفي.

---

**المطور:** مجد محمد زياد يسير  
**التاريخ:** 16 يوليو 2025  
**الحالة:** مكتمل ✅
