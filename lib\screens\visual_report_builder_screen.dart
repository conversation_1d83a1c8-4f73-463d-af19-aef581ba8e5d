/// شاشة منشئ التقارير المرئي
/// واجهة سحب وإفلات لإنشاء تقارير مخصصة
library;

import 'package:flutter/material.dart';
import '../models/visual_report_builder.dart';
import '../services/visual_report_builder_service.dart';
import '../services/auth_service.dart';
import '../constants/revolutionary_design_colors.dart';
import '../widgets/loading_widget.dart';

class VisualReportBuilderScreen extends StatefulWidget {
  final ReportTemplate? template;

  const VisualReportBuilderScreen({super.key, this.template});

  @override
  State<VisualReportBuilderScreen> createState() =>
      _VisualReportBuilderScreenState();
}

class _VisualReportBuilderScreenState extends State<VisualReportBuilderScreen>
    with TickerProviderStateMixin {
  final VisualReportBuilderService _builderService =
      VisualReportBuilderService();

  late TabController _tabController;

  // متحكمات النموذج
  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _descriptionController = TextEditingController();
  final TextEditingController _categoryController = TextEditingController();

  // حالة التطبيق
  List<ReportElement> _elements = [];
  ReportSettings _settings = const ReportSettings();
  bool _isLoading = false;
  bool _isDirty = false;

  // عناصر متاحة للسحب
  final List<ReportElementType> _availableElements = [
    ReportElementType.header,
    ReportElementType.text,
    ReportElementType.table,
    ReportElementType.chart,
    ReportElementType.divider,
    ReportElementType.spacer,
    ReportElementType.filter,
    ReportElementType.calculation,
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadTemplate();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _nameController.dispose();
    _descriptionController.dispose();
    _categoryController.dispose();
    super.dispose();
  }

  /// تحميل القالب إذا كان موجوداً
  void _loadTemplate() {
    if (widget.template != null) {
      _nameController.text = widget.template!.name;
      _descriptionController.text = widget.template!.description;
      _categoryController.text = widget.template!.category;
      _elements = List.from(widget.template!.elements);
      _settings = ReportSettings.fromJson(widget.template!.settings);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          widget.template != null ? 'تحرير القالب' : 'منشئ التقارير المرئي',
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        backgroundColor: RevolutionaryColors.damascusSky,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            onPressed: _previewReport,
            icon: const Icon(Icons.preview),
            tooltip: 'معاينة التقرير',
          ),
          IconButton(
            onPressed: _saveTemplate,
            icon: const Icon(Icons.save),
            tooltip: 'حفظ القالب',
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          indicatorColor: Colors.white,
          tabs: const [
            Tab(text: 'التصميم', icon: Icon(Icons.design_services)),
            Tab(text: 'البيانات', icon: Icon(Icons.data_usage)),
            Tab(text: 'الإعدادات', icon: Icon(Icons.settings)),
          ],
        ),
      ),
      body: _isLoading
          ? const LoadingWidget()
          : TabBarView(
              controller: _tabController,
              children: [
                _buildDesignTab(),
                _buildDataTab(),
                _buildSettingsTab(),
              ],
            ),
    );
  }

  /// تبويب التصميم
  Widget _buildDesignTab() {
    return Row(
      children: [
        // لوحة العناصر
        Container(
          width: 250,
          decoration: BoxDecoration(
            color: Colors.grey[100],
            border: Border(right: BorderSide(color: Colors.grey[300]!)),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: RevolutionaryColors.damascusSky,
                ),
                child: const Text(
                  'عناصر التقرير',
                  style: TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
              ),
              Expanded(
                child: ListView.builder(
                  padding: const EdgeInsets.all(8),
                  itemCount: _availableElements.length,
                  itemBuilder: (context, index) {
                    final elementType = _availableElements[index];
                    return _buildDraggableElement(elementType);
                  },
                ),
              ),
            ],
          ),
        ),
        // منطقة التصميم
        Expanded(
          child: Container(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // معلومات القالب
                Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'معلومات القالب',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 12),
                        Row(
                          children: [
                            Expanded(
                              child: TextField(
                                controller: _nameController,
                                decoration: const InputDecoration(
                                  labelText: 'اسم القالب',
                                  border: OutlineInputBorder(),
                                ),
                                onChanged: (_) => _markDirty(),
                              ),
                            ),
                            const SizedBox(width: 12),
                            Expanded(
                              child: TextField(
                                controller: _categoryController,
                                decoration: const InputDecoration(
                                  labelText: 'الفئة',
                                  border: OutlineInputBorder(),
                                ),
                                onChanged: (_) => _markDirty(),
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 12),
                        TextField(
                          controller: _descriptionController,
                          decoration: const InputDecoration(
                            labelText: 'الوصف',
                            border: OutlineInputBorder(),
                          ),
                          maxLines: 2,
                          onChanged: (_) => _markDirty(),
                        ),
                      ],
                    ),
                  ),
                ),
                const SizedBox(height: 16),
                // منطقة إسقاط العناصر
                Expanded(
                  child: Card(
                    child: Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(16),
                      child: DragTarget<ReportElementType>(
                        onAcceptWithDetails: (details) {
                          _addElement(details.data);
                        },
                        builder: (context, candidateData, rejectedData) {
                          return Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                children: [
                                  const Text(
                                    'تصميم التقرير',
                                    style: TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                  const Spacer(),
                                  Text(
                                    'العناصر: ${_elements.length}',
                                    style: TextStyle(color: Colors.grey[600]),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 16),
                              if (_elements.isEmpty)
                                Expanded(
                                  child: Container(
                                    decoration: BoxDecoration(
                                      border: Border.all(
                                        color: Colors.grey[300]!,
                                        style: BorderStyle.solid,
                                      ),
                                      borderRadius: BorderRadius.circular(8),
                                    ),
                                    child: const Center(
                                      child: Column(
                                        mainAxisAlignment:
                                            MainAxisAlignment.center,
                                        children: [
                                          Icon(
                                            Icons.drag_indicator,
                                            size: 48,
                                            color: Colors.grey,
                                          ),
                                          SizedBox(height: 16),
                                          Text(
                                            'اسحب العناصر هنا لبناء التقرير',
                                            style: TextStyle(
                                              fontSize: 16,
                                              color: Colors.grey,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                )
                              else
                                Expanded(
                                  child: ReorderableListView.builder(
                                    itemCount: _elements.length,
                                    onReorder: _reorderElements,
                                    itemBuilder: (context, index) {
                                      final element = _elements[index];
                                      return _buildElementCard(element, index);
                                    },
                                  ),
                                ),
                            ],
                          );
                        },
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  /// تبويب البيانات
  Widget _buildDataTab() {
    return const Center(
      child: Text(
        'تبويب البيانات - قيد التطوير',
        style: TextStyle(fontSize: 18),
      ),
    );
  }

  /// تبويب الإعدادات
  Widget _buildSettingsTab() {
    return const Center(
      child: Text(
        'تبويب الإعدادات - قيد التطوير',
        style: TextStyle(fontSize: 18),
      ),
    );
  }

  /// بناء عنصر قابل للسحب
  Widget _buildDraggableElement(ReportElementType elementType) {
    return Draggable<ReportElementType>(
      data: elementType,
      feedback: Material(
        elevation: 4,
        child: Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: RevolutionaryColors.damascusSky,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(_getElementIcon(elementType), color: Colors.white, size: 20),
              const SizedBox(width: 8),
              Text(
                _getElementName(elementType),
                style: const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ),
      ),
      child: Container(
        margin: const EdgeInsets.only(bottom: 8),
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: Colors.white,
          border: Border.all(color: Colors.grey[300]!),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          children: [
            Icon(
              _getElementIcon(elementType),
              color: RevolutionaryColors.damascusSky,
              size: 20,
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                _getElementName(elementType),
                style: const TextStyle(fontWeight: FontWeight.w500),
              ),
            ),
            const Icon(Icons.drag_handle, color: Colors.grey, size: 16),
          ],
        ),
      ),
    );
  }

  /// بناء بطاقة العنصر
  Widget _buildElementCard(ReportElement element, int index) {
    return Card(
      key: ValueKey(element.id),
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: Icon(
          _getElementIcon(element.type),
          color: RevolutionaryColors.damascusSky,
        ),
        title: Text(element.title),
        subtitle: Text(_getElementName(element.type)),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            IconButton(
              onPressed: () => _editElement(index),
              icon: const Icon(Icons.edit),
              tooltip: 'تحرير',
            ),
            IconButton(
              onPressed: () => _removeElement(index),
              icon: const Icon(Icons.delete),
              tooltip: 'حذف',
            ),
            const Icon(Icons.drag_handle),
          ],
        ),
      ),
    );
  }

  /// الحصول على أيقونة العنصر
  IconData _getElementIcon(ReportElementType type) {
    switch (type) {
      case ReportElementType.header:
        return Icons.title;
      case ReportElementType.text:
        return Icons.text_fields;
      case ReportElementType.table:
        return Icons.table_chart;
      case ReportElementType.chart:
        return Icons.bar_chart;
      case ReportElementType.divider:
        return Icons.horizontal_rule;
      case ReportElementType.spacer:
        return Icons.space_bar;
      case ReportElementType.filter:
        return Icons.filter_alt;
      case ReportElementType.calculation:
        return Icons.calculate;
      default:
        return Icons.widgets;
    }
  }

  /// الحصول على اسم العنصر
  String _getElementName(ReportElementType type) {
    switch (type) {
      case ReportElementType.header:
        return 'رأس الصفحة';
      case ReportElementType.text:
        return 'نص';
      case ReportElementType.table:
        return 'جدول';
      case ReportElementType.chart:
        return 'رسم بياني';
      case ReportElementType.divider:
        return 'خط فاصل';
      case ReportElementType.spacer:
        return 'مساحة فارغة';
      case ReportElementType.filter:
        return 'فلتر';
      case ReportElementType.calculation:
        return 'حساب';
      default:
        return 'عنصر';
    }
  }

  /// إضافة عنصر جديد
  void _addElement(ReportElementType elementType) {
    final element = ReportElement(
      id: 'element_${DateTime.now().millisecondsSinceEpoch}',
      type: elementType,
      title: _getElementName(elementType),
      order: _elements.length,
    );

    setState(() {
      _elements.add(element);
      _markDirty();
    });
  }

  /// إعادة ترتيب العناصر
  void _reorderElements(int oldIndex, int newIndex) {
    setState(() {
      if (newIndex > oldIndex) {
        newIndex -= 1;
      }
      final element = _elements.removeAt(oldIndex);
      _elements.insert(newIndex, element);
      _markDirty();
    });
  }

  /// تحرير عنصر
  void _editElement(int index) {
    final element = _elements[index];
    _showElementEditDialog(element, index);
  }

  /// عرض حوار تحرير العنصر
  void _showElementEditDialog(ReportElement element, int index) {
    final titleController = TextEditingController(text: element.title);
    final propertiesControllers = <String, TextEditingController>{};
    final stylingControllers = <String, TextEditingController>{};

    // إنشاء controllers للخصائص
    element.properties.forEach((key, value) {
      propertiesControllers[key] = TextEditingController(
        text: value.toString(),
      );
    });

    // إنشاء controllers للتنسيق
    element.styling.forEach((key, value) {
      stylingControllers[key] = TextEditingController(text: value.toString());
    });

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('تحرير ${_getElementTypeName(element.type)}'),
        content: SizedBox(
          width: 400,
          height: 500,
          child: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // العنوان
                TextField(
                  controller: titleController,
                  decoration: const InputDecoration(
                    labelText: 'العنوان',
                    border: OutlineInputBorder(),
                  ),
                ),
                const SizedBox(height: 16),

                // الخصائص
                if (element.properties.isNotEmpty) ...[
                  const Text(
                    'الخصائص:',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 8),
                  ...propertiesControllers.entries.map(
                    (entry) => Padding(
                      padding: const EdgeInsets.only(bottom: 8),
                      child: TextField(
                        controller: entry.value,
                        decoration: InputDecoration(
                          labelText: _getPropertyDisplayName(entry.key),
                          border: const OutlineInputBorder(),
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),
                ],

                // التنسيق
                if (element.styling.isNotEmpty) ...[
                  const Text(
                    'التنسيق:',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 8),
                  ...stylingControllers.entries.map(
                    (entry) => Padding(
                      padding: const EdgeInsets.only(bottom: 8),
                      child: TextField(
                        controller: entry.value,
                        decoration: InputDecoration(
                          labelText: _getStylingDisplayName(entry.key),
                          border: const OutlineInputBorder(),
                        ),
                      ),
                    ),
                  ),
                ],

                // إعدادات إضافية حسب نوع العنصر
                _buildElementSpecificSettings(element),
              ],
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              _updateElement(
                index,
                titleController,
                propertiesControllers,
                stylingControllers,
              );
              Navigator.pop(context);
            },
            child: const Text('حفظ'),
          ),
        ],
      ),
    );
  }

  /// حذف عنصر
  void _removeElement(int index) {
    setState(() {
      _elements.removeAt(index);
      _markDirty();
    });
  }

  /// معاينة التقرير
  void _previewReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('معاينة التقرير - قيد التطوير')),
    );
  }

  /// حفظ القالب
  Future<void> _saveTemplate() async {
    if (_nameController.text.isEmpty) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text('يرجى إدخال اسم القالب')));
      return;
    }

    setState(() => _isLoading = true);

    try {
      final template = ReportTemplate(
        id: widget.template?.id,
        name: _nameController.text,
        description: _descriptionController.text,
        category: _categoryController.text.isEmpty
            ? 'عام'
            : _categoryController.text,
        elements: _elements,
        settings: _settings.toJson(),
        createdAt: widget.template?.createdAt ?? DateTime.now(),
        updatedAt: DateTime.now(),
        createdBy: AuthService.currentUser?.username ?? 'مجهول',
      );

      await _builderService.saveTemplate(template);

      setState(() {
        _isDirty = false;
        _isLoading = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم حفظ القالب بنجاح'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('خطأ في حفظ القالب: $e')));
      }
    }
  }

  /// تمييز التغييرات
  void _markDirty() {
    if (!_isDirty) {
      setState(() => _isDirty = true);
    }
  }

  /// الحصول على اسم نوع العنصر
  String _getElementTypeName(ReportElementType type) {
    switch (type) {
      case ReportElementType.text:
        return 'نص';
      case ReportElementType.table:
        return 'جدول';
      case ReportElementType.chart:
        return 'رسم بياني';
      case ReportElementType.image:
        return 'صورة';
      case ReportElementType.spacer:
        return 'مساحة فارغة';
      case ReportElementType.divider:
        return 'فاصل';
      case ReportElementType.header:
        return 'رأس الصفحة';
      case ReportElementType.footer:
        return 'تذييل الصفحة';
      case ReportElementType.calculation:
        return 'حساب';
      case ReportElementType.filter:
        return 'فلتر';
    }
  }

  /// الحصول على اسم الخاصية للعرض
  String _getPropertyDisplayName(String key) {
    switch (key) {
      case 'text':
        return 'النص';
      case 'fontSize':
        return 'حجم الخط';
      case 'alignment':
        return 'المحاذاة';
      case 'dataSource':
        return 'مصدر البيانات';
      case 'columns':
        return 'الأعمدة';
      case 'showTotals':
        return 'إظهار المجاميع';
      case 'chartType':
        return 'نوع الرسم البياني';
      case 'xAxis':
        return 'المحور السيني';
      case 'yAxis':
        return 'المحور الصادي';
      case 'filterType':
        return 'نوع الفلتر';
      case 'label':
        return 'التسمية';
      default:
        return key;
    }
  }

  /// الحصول على اسم خاصية التنسيق للعرض
  String _getStylingDisplayName(String key) {
    switch (key) {
      case 'color':
        return 'اللون';
      case 'backgroundColor':
        return 'لون الخلفية';
      case 'fontWeight':
        return 'سمك الخط';
      case 'fontStyle':
        return 'نمط الخط';
      case 'padding':
        return 'الحشو';
      case 'margin':
        return 'الهامش';
      case 'border':
        return 'الحدود';
      case 'borderRadius':
        return 'انحناء الحدود';
      default:
        return key;
    }
  }

  /// بناء إعدادات خاصة بنوع العنصر
  Widget _buildElementSpecificSettings(ReportElement element) {
    switch (element.type) {
      case ReportElementType.chart:
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 16),
            const Text(
              'إعدادات الرسم البياني:',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            DropdownButtonFormField<String>(
              value: element.properties['chartType']?.toString(),
              decoration: const InputDecoration(
                labelText: 'نوع الرسم البياني',
                border: OutlineInputBorder(),
              ),
              items: const [
                DropdownMenuItem(value: 'pie', child: Text('دائري')),
                DropdownMenuItem(value: 'bar', child: Text('أعمدة')),
                DropdownMenuItem(value: 'line', child: Text('خطي')),
                DropdownMenuItem(value: 'area', child: Text('منطقة')),
              ],
              onChanged: (value) {
                // سيتم تحديث القيمة عند الحفظ
              },
            ),
          ],
        );
      case ReportElementType.table:
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 16),
            const Text(
              'إعدادات الجدول:',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            CheckboxListTile(
              title: const Text('إظهار المجاميع'),
              value: element.properties['showTotals'] == true,
              onChanged: (value) {
                // سيتم تحديث القيمة عند الحفظ
              },
            ),
          ],
        );
      default:
        return const SizedBox.shrink();
    }
  }

  /// تحديث العنصر
  void _updateElement(
    int index,
    TextEditingController titleController,
    Map<String, TextEditingController> propertiesControllers,
    Map<String, TextEditingController> stylingControllers,
  ) {
    final element = _elements[index];

    // تحديث الخصائص
    final updatedProperties = <String, dynamic>{};
    propertiesControllers.forEach((key, controller) {
      final value = controller.text;
      // محاولة تحويل القيم الرقمية
      if (double.tryParse(value) != null) {
        updatedProperties[key] = double.parse(value);
      } else if (value.toLowerCase() == 'true') {
        updatedProperties[key] = true;
      } else if (value.toLowerCase() == 'false') {
        updatedProperties[key] = false;
      } else {
        updatedProperties[key] = value;
      }
    });

    // تحديث التنسيق
    final updatedStyling = <String, dynamic>{};
    stylingControllers.forEach((key, controller) {
      final value = controller.text;
      if (double.tryParse(value) != null) {
        updatedStyling[key] = double.parse(value);
      } else {
        updatedStyling[key] = value;
      }
    });

    // إنشاء عنصر محدث
    final updatedElement = element.copyWith(
      title: titleController.text,
      properties: {...element.properties, ...updatedProperties},
      styling: {...element.styling, ...updatedStyling},
    );

    setState(() {
      _elements[index] = updatedElement;
      _markDirty();
    });
  }
}
