/// نماذج النظام الضريبي السوري
/// تدعم جميع أنواع الضرائب والإقرارات المطلوبة في سوريا
library;



/// أنواع الضرائب في سوريا
enum SyrianTaxType {
  incomeTax, // ضريبة الدخل
  salesTax, // ضريبة المبيعات
  servicesFeeTax, // ضريبة رسم الخدمات
  realEstateTax, // ضريبة العقارات
  vehicleTax, // ضريبة المركبات
  professionalTax, // ضريبة المهن الحرة
  commercialTax, // ضريبة التجارة
  industrialTax, // ضريبة الصناعة
  stampDuty, // رسم الطابع
  customsDuty, // الرسوم الجمركية
}

/// فئات دافعي الضرائب
enum TaxpayerCategory {
  individual, // فرد
  company, // شركة
  partnership, // شراكة
  cooperative, // تعاونية
  association, // جمعية
  foundation, // مؤسسة
  government, // حكومي
}

/// حالة الإقرار الضريبي
enum TaxDeclarationStatus {
  draft, // مسودة
  submitted, // مقدم
  underReview, // قيد المراجعة
  approved, // موافق عليه
  rejected, // مرفوض
  amended, // معدل
  paid, // مدفوع
  overdue, // متأخر
}

/// نوع الإقرار الضريبي
enum TaxDeclarationType {
  monthly, // شهري
  quarterly, // ربع سنوي
  annual, // سنوي
  finalDeclaration, // نهائي
  amended, // معدل
  supplementary, // تكميلي
}

/// معلومات دافع الضرائب
class TaxpayerInfo {
  final String id;
  final String name;
  final String taxNumber;
  final TaxpayerCategory category;
  final String address;
  final String phone;
  final String email;
  final String businessActivity;
  final DateTime registrationDate;
  final bool isActive;
  final Map<String, dynamic> additionalInfo;

  const TaxpayerInfo({
    required this.id,
    required this.name,
    required this.taxNumber,
    required this.category,
    required this.address,
    required this.phone,
    required this.email,
    required this.businessActivity,
    required this.registrationDate,
    this.isActive = true,
    this.additionalInfo = const {},
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'taxNumber': taxNumber,
      'category': category.name,
      'address': address,
      'phone': phone,
      'email': email,
      'businessActivity': businessActivity,
      'registrationDate': registrationDate.toIso8601String(),
      'isActive': isActive,
      'additionalInfo': additionalInfo,
    };
  }

  factory TaxpayerInfo.fromJson(Map<String, dynamic> json) {
    return TaxpayerInfo(
      id: json['id'],
      name: json['name'],
      taxNumber: json['taxNumber'],
      category: TaxpayerCategory.values.firstWhere(
        (e) => e.name == json['category'],
      ),
      address: json['address'],
      phone: json['phone'],
      email: json['email'],
      businessActivity: json['businessActivity'],
      registrationDate: DateTime.parse(json['registrationDate']),
      isActive: json['isActive'] ?? true,
      additionalInfo: Map<String, dynamic>.from(json['additionalInfo'] ?? {}),
    );
  }
}

/// حساب الضريبة
class TaxCalculation {
  final String id;
  final SyrianTaxType taxType;
  final double taxableAmount;
  final double taxRate;
  final double taxAmount;
  final double exemptAmount;
  final double deductibleAmount;
  final DateTime calculationDate;
  final String period;
  final Map<String, dynamic> details;

  const TaxCalculation({
    required this.id,
    required this.taxType,
    required this.taxableAmount,
    required this.taxRate,
    required this.taxAmount,
    this.exemptAmount = 0.0,
    this.deductibleAmount = 0.0,
    required this.calculationDate,
    required this.period,
    this.details = const {},
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'taxType': taxType.name,
      'taxableAmount': taxableAmount,
      'taxRate': taxRate,
      'taxAmount': taxAmount,
      'exemptAmount': exemptAmount,
      'deductibleAmount': deductibleAmount,
      'calculationDate': calculationDate.toIso8601String(),
      'period': period,
      'details': details,
    };
  }

  factory TaxCalculation.fromJson(Map<String, dynamic> json) {
    return TaxCalculation(
      id: json['id'],
      taxType: SyrianTaxType.values.firstWhere(
        (e) => e.name == json['taxType'],
      ),
      taxableAmount: json['taxableAmount'].toDouble(),
      taxRate: json['taxRate'].toDouble(),
      taxAmount: json['taxAmount'].toDouble(),
      exemptAmount: json['exemptAmount']?.toDouble() ?? 0.0,
      deductibleAmount: json['deductibleAmount']?.toDouble() ?? 0.0,
      calculationDate: DateTime.parse(json['calculationDate']),
      period: json['period'],
      details: Map<String, dynamic>.from(json['details'] ?? {}),
    );
  }
}

/// الإقرار الضريبي
class TaxDeclaration {
  final int? id;
  final String declarationNumber;
  final TaxDeclarationType type;
  final SyrianTaxType taxType;
  final String taxpayerId;
  final String period;
  final DateTime fromDate;
  final DateTime toDate;
  final DateTime submissionDate;
  final DateTime? dueDate;
  final TaxDeclarationStatus status;
  final List<TaxCalculation> calculations;
  final double totalTaxAmount;
  final double totalPaidAmount;
  final double remainingAmount;
  final String notes;
  final Map<String, dynamic> attachments;

  const TaxDeclaration({
    this.id,
    required this.declarationNumber,
    required this.type,
    required this.taxType,
    required this.taxpayerId,
    required this.period,
    required this.fromDate,
    required this.toDate,
    required this.submissionDate,
    this.dueDate,
    required this.status,
    required this.calculations,
    required this.totalTaxAmount,
    this.totalPaidAmount = 0.0,
    required this.remainingAmount,
    this.notes = '',
    this.attachments = const {},
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'declarationNumber': declarationNumber,
      'type': type.name,
      'taxType': taxType.name,
      'taxpayerId': taxpayerId,
      'period': period,
      'fromDate': fromDate.toIso8601String(),
      'toDate': toDate.toIso8601String(),
      'submissionDate': submissionDate.toIso8601String(),
      'dueDate': dueDate?.toIso8601String(),
      'status': status.name,
      'calculations': calculations.map((c) => c.toJson()).toList(),
      'totalTaxAmount': totalTaxAmount,
      'totalPaidAmount': totalPaidAmount,
      'remainingAmount': remainingAmount,
      'notes': notes,
      'attachments': attachments,
    };
  }

  factory TaxDeclaration.fromJson(Map<String, dynamic> json) {
    return TaxDeclaration(
      id: json['id'],
      declarationNumber: json['declarationNumber'],
      type: TaxDeclarationType.values.firstWhere((e) => e.name == json['type']),
      taxType: SyrianTaxType.values.firstWhere(
        (e) => e.name == json['taxType'],
      ),
      taxpayerId: json['taxpayerId'],
      period: json['period'],
      fromDate: DateTime.parse(json['fromDate']),
      toDate: DateTime.parse(json['toDate']),
      submissionDate: DateTime.parse(json['submissionDate']),
      dueDate: json['dueDate'] != null ? DateTime.parse(json['dueDate']) : null,
      status: TaxDeclarationStatus.values.firstWhere(
        (e) => e.name == json['status'],
      ),
      calculations: (json['calculations'] as List)
          .map((c) => TaxCalculation.fromJson(c))
          .toList(),
      totalTaxAmount: json['totalTaxAmount'].toDouble(),
      totalPaidAmount: json['totalPaidAmount']?.toDouble() ?? 0.0,
      remainingAmount: json['remainingAmount'].toDouble(),
      notes: json['notes'] ?? '',
      attachments: Map<String, dynamic>.from(json['attachments'] ?? {}),
    );
  }
}

/// معدلات الضرائب السورية
class SyrianTaxRates {
  static const Map<SyrianTaxType, Map<String, dynamic>> rates = {
    SyrianTaxType.incomeTax: {
      'individual': [
        {'min': 0, 'max': 600000, 'rate': 0.0},
        {'min': 600001, 'max': 1200000, 'rate': 0.05},
        {'min': 1200001, 'max': 2400000, 'rate': 0.10},
        {'min': 2400001, 'max': 4800000, 'rate': 0.15},
        {'min': 4800001, 'max': double.infinity, 'rate': 0.20},
      ],
      'company': 0.25, // 25% للشركات
    },
    SyrianTaxType.salesTax: {
      'standard': 0.11, // 11% ضريبة مبيعات عامة
      'luxury': 0.15, // 15% للسلع الكمالية
      'essential': 0.0, // معفاة للسلع الأساسية
    },
    SyrianTaxType.servicesFeeTax: {
      'standard': 0.05, // 5% رسم خدمات
    },
    SyrianTaxType.professionalTax: {
      'standard': 0.10, // 10% للمهن الحرة
    },
  };

  /// الحصول على معدل الضريبة
  static double getTaxRate(
    SyrianTaxType taxType, {
    String category = 'standard',
    double? income,
  }) {
    final taxRates = rates[taxType];
    if (taxRates == null) return 0.0;

    if (taxType == SyrianTaxType.incomeTax &&
        category == 'individual' &&
        income != null) {
      final brackets = taxRates['individual'] as List;
      for (final bracket in brackets) {
        if (income >= bracket['min'] && income <= bracket['max']) {
          return bracket['rate'];
        }
      }
    }

    final rate = taxRates[category];
    return rate is double ? rate : 0.0;
  }
}

/// تقرير ضريبي
class TaxReport {
  final String id;
  final String title;
  final SyrianTaxType taxType;
  final String period;
  final DateTime fromDate;
  final DateTime toDate;
  final DateTime generatedDate;
  final Map<String, dynamic> data;
  final List<TaxCalculation> calculations;
  final double totalTaxAmount;
  final String generatedBy;

  const TaxReport({
    required this.id,
    required this.title,
    required this.taxType,
    required this.period,
    required this.fromDate,
    required this.toDate,
    required this.generatedDate,
    required this.data,
    required this.calculations,
    required this.totalTaxAmount,
    required this.generatedBy,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'taxType': taxType.name,
      'period': period,
      'fromDate': fromDate.toIso8601String(),
      'toDate': toDate.toIso8601String(),
      'generatedDate': generatedDate.toIso8601String(),
      'data': data,
      'calculations': calculations.map((c) => c.toJson()).toList(),
      'totalTaxAmount': totalTaxAmount,
      'generatedBy': generatedBy,
    };
  }

  factory TaxReport.fromJson(Map<String, dynamic> json) {
    return TaxReport(
      id: json['id'],
      title: json['title'],
      taxType: SyrianTaxType.values.firstWhere(
        (e) => e.name == json['taxType'],
      ),
      period: json['period'],
      fromDate: DateTime.parse(json['fromDate']),
      toDate: DateTime.parse(json['toDate']),
      generatedDate: DateTime.parse(json['generatedDate']),
      data: Map<String, dynamic>.from(json['data']),
      calculations: (json['calculations'] as List)
          .map((c) => TaxCalculation.fromJson(c))
          .toList(),
      totalTaxAmount: json['totalTaxAmount'].toDouble(),
      generatedBy: json['generatedBy'],
    );
  }
}

/// إعدادات الضرائب
class TaxSettings {
  final bool enableAutomaticCalculation;
  final bool enableTaxReminders;
  final int reminderDaysBefore;
  final String defaultTaxpayerId;
  final Map<SyrianTaxType, bool> enabledTaxTypes;
  final Map<String, dynamic> customRates;

  const TaxSettings({
    this.enableAutomaticCalculation = true,
    this.enableTaxReminders = true,
    this.reminderDaysBefore = 7,
    this.defaultTaxpayerId = '',
    this.enabledTaxTypes = const {},
    this.customRates = const {},
  });

  Map<String, dynamic> toJson() {
    return {
      'enableAutomaticCalculation': enableAutomaticCalculation,
      'enableTaxReminders': enableTaxReminders,
      'reminderDaysBefore': reminderDaysBefore,
      'defaultTaxpayerId': defaultTaxpayerId,
      'enabledTaxTypes': enabledTaxTypes.map(
        (key, value) => MapEntry(key.name, value),
      ),
      'customRates': customRates,
    };
  }

  factory TaxSettings.fromJson(Map<String, dynamic> json) {
    return TaxSettings(
      enableAutomaticCalculation: json['enableAutomaticCalculation'] ?? true,
      enableTaxReminders: json['enableTaxReminders'] ?? true,
      reminderDaysBefore: json['reminderDaysBefore'] ?? 7,
      defaultTaxpayerId: json['defaultTaxpayerId'] ?? '',
      enabledTaxTypes:
          (json['enabledTaxTypes'] as Map<String, dynamic>?)?.map(
            (key, value) => MapEntry(
              SyrianTaxType.values.firstWhere((e) => e.name == key),
              value as bool,
            ),
          ) ??
          {},
      customRates: Map<String, dynamic>.from(json['customRates'] ?? {}),
    );
  }
}
