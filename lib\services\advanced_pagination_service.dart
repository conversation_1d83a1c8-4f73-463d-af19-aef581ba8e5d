/// خدمة التحميل التدريجي المتقدمة - Smart Ledger
/// تدير تحميل البيانات بشكل تدريجي مع ذاكرة مؤقتة لتحسين الأداء
library;

import 'dart:async';
import 'package:sqflite/sqflite.dart';
import '../database/database_helper.dart';
import '../services/logging_service.dart';
import '../constants/app_constants.dart';

/// خدمة التحميل التدريجي المتقدمة
class AdvancedPaginationService {
  static final AdvancedPaginationService _instance = 
      AdvancedPaginationService._internal();
  
  AdvancedPaginationService._internal();
  factory AdvancedPaginationService() => _instance;

  final DatabaseHelper _databaseHelper = DatabaseHelper();
  final Map<String, PaginationCache> _cache = {};

  static const int defaultPageSize = 20;
  static const int maxPageSize = 100;

  /// الحصول على البيانات مع التحميل التدريجي
  Future<PaginatedResult<T>> getPaginatedData<T>({
    required String tableName,
    required T Function(Map<String, dynamic>) fromMap,
    int page = 1,
    int pageSize = defaultPageSize,
    String? whereClause,
    List<dynamic>? whereArgs,
    String? orderBy,
    List<String>? columns,
    bool useCache = true,
  }) async {
    try {
      // التحقق من صحة المعاملات
      if (page < 1) page = 1;
      if (pageSize < 1) pageSize = defaultPageSize;
      if (pageSize > maxPageSize) pageSize = maxPageSize;

      final cacheKey = _generateCacheKey(
        tableName, page, pageSize, whereClause, orderBy,
      );

      // التحقق من الذاكرة المؤقتة
      if (useCache && _cache.containsKey(cacheKey)) {
        final cachedData = _cache[cacheKey]!;
        if (!cachedData.isExpired()) {
          LoggingService.info(
            'تم استرجاع البيانات من الذاكرة المؤقتة',
            category: 'Pagination',
            data: {'table': tableName, 'page': page},
          );
          return cachedData.result as PaginatedResult<T>;
        }
      }

      final db = await _databaseHelper.database;
      final offset = (page - 1) * pageSize;

      // حساب إجمالي عدد السجلات
      final totalCount = await _getTotalCount(
        db, tableName, whereClause, whereArgs,
      );

      // استرجاع البيانات للصفحة المطلوبة
      final query = '''
        SELECT ${columns?.join(', ') ?? '*'} 
        FROM $tableName 
        ${whereClause != null ? 'WHERE $whereClause' : ''}
        ${orderBy != null ? 'ORDER BY $orderBy' : ''}
        LIMIT $pageSize OFFSET $offset
      ''';

      final List<Map<String, dynamic>> maps = await db.rawQuery(
        query,
        whereArgs,
      );

      final items = maps.map((map) => fromMap(map)).toList();
      
      final result = PaginatedResult<T>(
        items: items,
        currentPage: page,
        pageSize: pageSize,
        totalCount: totalCount,
        totalPages: (totalCount / pageSize).ceil(),
        hasNextPage: page < (totalCount / pageSize).ceil(),
        hasPreviousPage: page > 1,
      );

      // حفظ في الذاكرة المؤقتة
      if (useCache) {
        _cache[cacheKey] = PaginationCache(
          result: result,
          timestamp: DateTime.now(),
        );
        _cleanupExpiredCache();
      }

      LoggingService.info(
        'تم استرجاع البيانات مع التحميل التدريجي',
        category: 'Pagination',
        data: {
          'table': tableName,
          'page': page,
          'page_size': pageSize,
          'total_count': totalCount,
          'items_count': items.length,
        },
      );

      return result;

    } catch (e) {
      LoggingService.error(
        'خطأ في التحميل التدريجي',
        category: 'Pagination',
        data: {
          'table': tableName,
          'page': page,
          'error': e.toString(),
        },
      );
      rethrow;
    }
  }

  /// البحث مع التحميل التدريجي
  Future<PaginatedResult<T>> searchPaginated<T>({
    required String tableName,
    required T Function(Map<String, dynamic>) fromMap,
    required String searchQuery,
    required List<String> searchColumns,
    int page = 1,
    int pageSize = defaultPageSize,
    String? additionalWhere,
    List<dynamic>? additionalArgs,
    String? orderBy,
  }) async {
    try {
      // بناء شرط البحث
      final searchConditions = searchColumns
          .map((column) => '$column LIKE ?')
          .join(' OR ');
      
      final searchArgs = searchColumns
          .map((_) => '%$searchQuery%')
          .toList();

      String whereClause = '($searchConditions)';
      List<dynamic> whereArgs = searchArgs;

      // إضافة شروط إضافية
      if (additionalWhere != null) {
        whereClause += ' AND $additionalWhere';
        if (additionalArgs != null) {
          whereArgs.addAll(additionalArgs);
        }
      }

      return await getPaginatedData<T>(
        tableName: tableName,
        fromMap: fromMap,
        page: page,
        pageSize: pageSize,
        whereClause: whereClause,
        whereArgs: whereArgs,
        orderBy: orderBy,
        useCache: false, // لا نستخدم الذاكرة المؤقتة للبحث
      );

    } catch (e) {
      LoggingService.error(
        'خطأ في البحث مع التحميل التدريجي',
        category: 'Pagination',
        data: {
          'table': tableName,
          'search_query': searchQuery,
          'error': e.toString(),
        },
      );
      rethrow;
    }
  }

  /// التحميل التدريجي للحسابات
  Future<PaginatedResult<Map<String, dynamic>>> getPaginatedAccounts({
    int page = 1,
    int pageSize = defaultPageSize,
    String? accountType,
    bool? isActive,
    String? searchQuery,
  }) async {
    String? whereClause;
    List<dynamic>? whereArgs;

    if (accountType != null || isActive != null || searchQuery != null) {
      final conditions = <String>[];
      whereArgs = <dynamic>[];

      if (accountType != null) {
        conditions.add('type = ?');
        whereArgs.add(accountType);
      }

      if (isActive != null) {
        conditions.add('is_active = ?');
        whereArgs.add(isActive ? 1 : 0);
      }

      if (searchQuery != null && searchQuery.isNotEmpty) {
        conditions.add('(name LIKE ? OR code LIKE ?)');
        whereArgs.addAll(['%$searchQuery%', '%$searchQuery%']);
      }

      whereClause = conditions.join(' AND ');
    }

    return await getPaginatedData<Map<String, dynamic>>(
      tableName: AppConstants.accountsTable,
      fromMap: (map) => map,
      page: page,
      pageSize: pageSize,
      whereClause: whereClause,
      whereArgs: whereArgs,
      orderBy: 'code ASC',
    );
  }

  /// التحميل التدريجي للفواتير
  Future<PaginatedResult<Map<String, dynamic>>> getPaginatedInvoices({
    int page = 1,
    int pageSize = defaultPageSize,
    String? status,
    String? type,
    DateTime? fromDate,
    DateTime? toDate,
    String? searchQuery,
  }) async {
    String? whereClause;
    List<dynamic>? whereArgs;

    if (status != null || type != null || fromDate != null || 
        toDate != null || searchQuery != null) {
      final conditions = <String>[];
      whereArgs = <dynamic>[];

      if (status != null) {
        conditions.add('status = ?');
        whereArgs.add(status);
      }

      if (type != null) {
        conditions.add('type = ?');
        whereArgs.add(type);
      }

      if (fromDate != null) {
        conditions.add('invoice_date >= ?');
        whereArgs.add(fromDate.toIso8601String());
      }

      if (toDate != null) {
        conditions.add('invoice_date <= ?');
        whereArgs.add(toDate.toIso8601String());
      }

      if (searchQuery != null && searchQuery.isNotEmpty) {
        conditions.add('(invoice_number LIKE ? OR notes LIKE ?)');
        whereArgs.addAll(['%$searchQuery%', '%$searchQuery%']);
      }

      whereClause = conditions.join(' AND ');
    }

    return await getPaginatedData<Map<String, dynamic>>(
      tableName: AppConstants.invoicesTable,
      fromMap: (map) => map,
      page: page,
      pageSize: pageSize,
      whereClause: whereClause,
      whereArgs: whereArgs,
      orderBy: 'invoice_date DESC',
    );
  }

  /// حساب إجمالي عدد السجلات
  Future<int> _getTotalCount(
    Database db,
    String tableName,
    String? whereClause,
    List<dynamic>? whereArgs,
  ) async {
    final query = '''
      SELECT COUNT(*) as count 
      FROM $tableName 
      ${whereClause != null ? 'WHERE $whereClause' : ''}
    ''';

    final result = await db.rawQuery(query, whereArgs);
    return (result.first['count'] as int?) ?? 0;
  }

  /// توليد مفتاح الذاكرة المؤقتة
  String _generateCacheKey(
    String tableName,
    int page,
    int pageSize,
    String? whereClause,
    String? orderBy,
  ) {
    return '$tableName-$page-$pageSize-${whereClause ?? 'null'}-${orderBy ?? 'null'}';
  }

  /// تنظيف الذاكرة المؤقتة المنتهية الصلاحية
  void _cleanupExpiredCache() {
    final now = DateTime.now();
    _cache.removeWhere((key, cache) => cache.isExpired(now));
  }

  /// مسح الذاكرة المؤقتة
  void clearCache() {
    _cache.clear();
    LoggingService.info(
      'تم مسح ذاكرة التحميل التدريجي المؤقتة',
      category: 'Pagination',
    );
  }
}

/// نتيجة التحميل التدريجي
class PaginatedResult<T> {
  final List<T> items;
  final int currentPage;
  final int pageSize;
  final int totalCount;
  final int totalPages;
  final bool hasNextPage;
  final bool hasPreviousPage;

  const PaginatedResult({
    required this.items,
    required this.currentPage,
    required this.pageSize,
    required this.totalCount,
    required this.totalPages,
    required this.hasNextPage,
    required this.hasPreviousPage,
  });

  Map<String, dynamic> toMap() {
    return {
      'current_page': currentPage,
      'page_size': pageSize,
      'total_count': totalCount,
      'total_pages': totalPages,
      'has_next_page': hasNextPage,
      'has_previous_page': hasPreviousPage,
      'items_count': items.length,
    };
  }
}

/// ذاكرة مؤقتة للتحميل التدريجي
class PaginationCache {
  final PaginatedResult result;
  final DateTime timestamp;
  static const Duration cacheExpiry = Duration(minutes: 5);

  const PaginationCache({
    required this.result,
    required this.timestamp,
  });

  bool isExpired([DateTime? now]) {
    now ??= DateTime.now();
    return now.difference(timestamp) > cacheExpiry;
  }
}
