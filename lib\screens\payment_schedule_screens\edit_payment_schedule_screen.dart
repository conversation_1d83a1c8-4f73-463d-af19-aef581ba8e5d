/// شاشة تعديل جدولة الدفعات
/// تسمح للمستخدم بتعديل جدولة دفعات موجودة
library;

import 'package:flutter/material.dart';
import '../../models/payment_schedule.dart';
import '../../services/payment_schedule_service.dart';
import '../../constants/revolutionary_design_colors.dart';


class EditPaymentScheduleScreen extends StatefulWidget {
  final PaymentSchedule schedule;
  final VoidCallback onScheduleUpdated;

  const EditPaymentScheduleScreen({
    super.key,
    required this.schedule,
    required this.onScheduleUpdated,
  });

  @override
  State<EditPaymentScheduleScreen> createState() =>
      _EditPaymentScheduleScreenState();
}

class _EditPaymentScheduleScreenState extends State<EditPaymentScheduleScreen> {
  final _formKey = GlobalKey<FormState>();
  final PaymentScheduleService _scheduleService = PaymentScheduleService();

  // Controllers
  late final TextEditingController _scheduleNameController;
  late final TextEditingController _installmentAmountController;
  late final TextEditingController _totalInstallmentsController;
  late final TextEditingController _notesController;

  // State variables
  bool _isSubmitting = false;
  late PaymentFrequency _selectedFrequency;
  late DateTime _startDate;
  DateTime? _endDate;
  late bool _autoPayment;
  List<PaymentReminder> _reminders = [];

  @override
  void initState() {
    super.initState();
    _initializeControllers();
  }

  void _initializeControllers() {
    _scheduleNameController = TextEditingController(
      text: widget.schedule.scheduleName,
    );
    _installmentAmountController = TextEditingController(
      text: widget.schedule.installmentAmount.toStringAsFixed(2),
    );
    _totalInstallmentsController = TextEditingController(
      text: widget.schedule.totalInstallments.toString(),
    );
    _notesController = TextEditingController(text: widget.schedule.notes ?? '');

    _selectedFrequency = widget.schedule.frequency;
    _startDate = widget.schedule.startDate;
    _endDate = widget.schedule.endDate;
    _autoPayment = widget.schedule.autoPayment;
    _reminders = List.from(widget.schedule.reminders);
  }

  @override
  void dispose() {
    _scheduleNameController.dispose();
    _installmentAmountController.dispose();
    _totalInstallmentsController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  Future<void> _updateSchedule() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isSubmitting = true);

    try {
      final installmentAmount = double.parse(_installmentAmountController.text);
      final totalInstallments = int.parse(_totalInstallmentsController.text);

      final updatedSchedule = widget.schedule.copyWith(
        scheduleName: _scheduleNameController.text,
        installmentAmount: installmentAmount,
        totalInstallments: totalInstallments,
        frequency: _selectedFrequency,
        startDate: _startDate,
        endDate: _endDate,
        autoPayment: _autoPayment,
        notes: _notesController.text.isNotEmpty ? _notesController.text : null,
        reminders: _reminders,
        updatedAt: DateTime.now(),
      );

      await _scheduleService.updatePaymentSchedule(updatedSchedule);

      widget.onScheduleUpdated();
      if (mounted) {
        Navigator.of(context).pop();
      }
    } catch (e) {
      setState(() => _isSubmitting = false);
      _showError('خطأ في تحديث الجدولة: $e');
    }
  }

  void _showError(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message), backgroundColor: Colors.red),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: RevolutionaryColors.backgroundPrimary,
      appBar: AppBar(
        title: const Text('تعديل جدولة الدفعات'),
        backgroundColor: RevolutionaryColors.damascusSky,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildBasicInfo(),
              const SizedBox(height: 24),
              _buildScheduleSettings(),
              const SizedBox(height: 24),
              _buildRemindersSection(),
              const SizedBox(height: 24),
              _buildNotesSection(),
              const SizedBox(height: 32),
              _buildSubmitButton(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildBasicInfo() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'المعلومات الأساسية',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: RevolutionaryColors.damascusSky,
              ),
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _scheduleNameController,
              decoration: InputDecoration(
                labelText: 'اسم الجدولة *',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'يرجى إدخال اسم الجدولة';
                }
                return null;
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildScheduleSettings() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'إعدادات الجدولة',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: RevolutionaryColors.damascusSky,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: TextFormField(
                    controller: _installmentAmountController,
                    keyboardType: TextInputType.number,
                    decoration: InputDecoration(
                      labelText: 'مبلغ القسط *',
                      suffixText: 'ل.س',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'يرجى إدخال مبلغ القسط';
                      }
                      final amount = double.tryParse(value);
                      if (amount == null || amount <= 0) {
                        return 'يرجى إدخال مبلغ صحيح';
                      }
                      return null;
                    },
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: TextFormField(
                    controller: _totalInstallmentsController,
                    keyboardType: TextInputType.number,
                    decoration: InputDecoration(
                      labelText: 'عدد الأقساط *',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'يرجى إدخال عدد الأقساط';
                      }
                      final count = int.tryParse(value);
                      if (count == null || count <= 0) {
                        return 'يرجى إدخال عدد صحيح';
                      }
                      return null;
                    },
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            DropdownButtonFormField<PaymentFrequency>(
              value: _selectedFrequency,
              decoration: InputDecoration(
                labelText: 'تكرار الدفع',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              items: PaymentFrequency.values.map((frequency) {
                return DropdownMenuItem(
                  value: frequency,
                  child: Text(frequency.displayName),
                );
              }).toList(),
              onChanged: (value) {
                setState(() {
                  _selectedFrequency = value!;
                });
              },
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: InkWell(
                    onTap: () => _selectStartDate(),
                    child: InputDecorator(
                      decoration: InputDecoration(
                        labelText: 'تاريخ البداية',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: Text(_formatDate(_startDate)),
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: InkWell(
                    onTap: () => _selectEndDate(),
                    child: InputDecorator(
                      decoration: InputDecoration(
                        labelText: 'تاريخ النهاية (اختياري)',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: Text(
                        _endDate != null ? _formatDate(_endDate!) : 'غير محدد',
                      ),
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            SwitchListTile(
              title: const Text('الدفع التلقائي'),
              subtitle: const Text('تفعيل الدفع التلقائي عند استحقاق القسط'),
              value: _autoPayment,
              onChanged: (value) {
                setState(() {
                  _autoPayment = value;
                });
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRemindersSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Text(
                  'التذكيرات',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: RevolutionaryColors.damascusSky,
                  ),
                ),
                const Spacer(),
                TextButton.icon(
                  onPressed: _addReminder,
                  icon: const Icon(Icons.add),
                  label: const Text('إضافة تذكير'),
                ),
              ],
            ),
            const SizedBox(height: 16),
            if (_reminders.isEmpty)
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: RevolutionaryColors.backgroundSecondary,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Center(child: Text('لا توجد تذكيرات مضافة')),
              )
            else
              ListView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: _reminders.length,
                itemBuilder: (context, index) {
                  final reminder = _reminders[index];
                  return Card(
                    child: ListTile(
                      title: Text(reminder.title),
                      subtitle: Text(
                        '${reminder.daysBefore} أيام قبل الاستحقاق',
                      ),
                      trailing: IconButton(
                        icon: const Icon(Icons.delete),
                        onPressed: () => _removeReminder(index),
                      ),
                    ),
                  );
                },
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildNotesSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'ملاحظات',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: RevolutionaryColors.damascusSky,
              ),
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _notesController,
              maxLines: 3,
              decoration: InputDecoration(
                labelText: 'ملاحظات إضافية',
                hintText: 'أدخل أي ملاحظات حول الجدولة...',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSubmitButton() {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: _isSubmitting ? null : _updateSchedule,
        style: ElevatedButton.styleFrom(
          backgroundColor: RevolutionaryColors.damascusSky,
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(vertical: 16),
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        ),
        child: _isSubmitting
            ? const SizedBox(
                height: 20,
                width: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              )
            : const Text(
                'تحديث الجدولة',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
              ),
      ),
    );
  }

  Future<void> _selectStartDate() async {
    final date = await showDatePicker(
      context: context,
      initialDate: _startDate,
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365 * 5)),
    );
    if (date != null) {
      setState(() {
        _startDate = date;
      });
    }
  }

  Future<void> _selectEndDate() async {
    final date = await showDatePicker(
      context: context,
      initialDate: _endDate ?? _startDate.add(const Duration(days: 365)),
      firstDate: _startDate,
      lastDate: DateTime.now().add(const Duration(days: 365 * 10)),
    );
    if (date != null) {
      setState(() {
        _endDate = date;
      });
    }
  }

  void _addReminder() {
    showDialog(
      context: context,
      builder: (context) => _ReminderDialog(
        onReminderAdded: (reminder) {
          setState(() {
            _reminders.add(reminder);
          });
        },
      ),
    );
  }

  void _removeReminder(int index) {
    setState(() {
      _reminders.removeAt(index);
    });
  }

  String _formatDate(DateTime date) {
    return '${date.day.toString().padLeft(2, '0')}/'
        '${date.month.toString().padLeft(2, '0')}/'
        '${date.year}';
  }
}

class _ReminderDialog extends StatefulWidget {
  final Function(PaymentReminder) onReminderAdded;

  const _ReminderDialog({required this.onReminderAdded});

  @override
  State<_ReminderDialog> createState() => _ReminderDialogState();
}

class _ReminderDialogState extends State<_ReminderDialog> {
  final _titleController = TextEditingController();
  final _messageController = TextEditingController();
  final _daysController = TextEditingController(text: '3');
  ReminderType _selectedType = ReminderType.notification;

  @override
  void dispose() {
    _titleController.dispose();
    _messageController.dispose();
    _daysController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('إضافة تذكير'),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          TextFormField(
            controller: _titleController,
            decoration: const InputDecoration(
              labelText: 'عنوان التذكير',
              border: OutlineInputBorder(),
            ),
          ),
          const SizedBox(height: 16),
          TextFormField(
            controller: _messageController,
            decoration: const InputDecoration(
              labelText: 'رسالة التذكير',
              border: OutlineInputBorder(),
            ),
            maxLines: 2,
          ),
          const SizedBox(height: 16),
          TextFormField(
            controller: _daysController,
            keyboardType: TextInputType.number,
            decoration: const InputDecoration(
              labelText: 'عدد الأيام قبل الاستحقاق',
              border: OutlineInputBorder(),
            ),
          ),
          const SizedBox(height: 16),
          DropdownButtonFormField<ReminderType>(
            value: _selectedType,
            decoration: const InputDecoration(
              labelText: 'نوع التذكير',
              border: OutlineInputBorder(),
            ),
            items: ReminderType.values.map((type) {
              return DropdownMenuItem(
                value: type,
                child: Text(type.displayName),
              );
            }).toList(),
            onChanged: (value) {
              setState(() {
                _selectedType = value!;
              });
            },
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('إلغاء'),
        ),
        ElevatedButton(
          onPressed: () {
            if (_titleController.text.isNotEmpty &&
                _messageController.text.isNotEmpty &&
                _daysController.text.isNotEmpty) {
              final reminder = PaymentReminder(
                scheduleId: 0, // سيتم تحديثه عند الحفظ
                type: _selectedType,
                daysBefore: int.parse(_daysController.text),
                title: _titleController.text,
                message: _messageController.text,
              );
              widget.onReminderAdded(reminder);
              Navigator.of(context).pop();
            }
          },
          child: const Text('إضافة'),
        ),
      ],
    );
  }
}
