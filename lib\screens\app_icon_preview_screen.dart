/// شاشة معاينة أيقونة التطبيق
/// تعرض الأيقونة بأحجام مختلفة وتتيح التصدير
library;

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:ui' as ui;
import '../widgets/app_icon_generator.dart';
import '../constants/revolutionary_design_colors.dart';

class AppIconPreviewScreen extends StatefulWidget {
  const AppIconPreviewScreen({super.key});

  @override
  State<AppIconPreviewScreen> createState() => _AppIconPreviewScreenState();
}

class _AppIconPreviewScreenState extends State<AppIconPreviewScreen> {
  bool _showBackground = true;
  double _selectedSize = 512;
  Uint8List? _iconBytes;

  final List<double> _iconSizes = [16, 24, 32, 48, 64, 96, 128, 256, 512, 1024];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'أيقونة التطبيق',
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        backgroundColor: RevolutionaryColors.damascusSky,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            onPressed: _exportIcon,
            icon: const Icon(Icons.download),
            tooltip: 'تصدير الأيقونة',
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // معاينة الأيقونة الرئيسية
            _buildMainPreview(),
            const SizedBox(height: 32),

            // إعدادات الأيقونة
            _buildIconSettings(),
            const SizedBox(height: 32),

            // معاينة الأحجام المختلفة
            _buildSizePreview(),
            const SizedBox(height: 32),

            // معاينة الاستخدامات
            _buildUsagePreview(),
          ],
        ),
      ),
    );
  }

  /// بناء المعاينة الرئيسية
  Widget _buildMainPreview() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'المعاينة الرئيسية',
              style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Center(
              child: Container(
                padding: const EdgeInsets.all(24),
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(16),
                ),
                child: AppIconGenerator.createAppIcon(
                  size: _selectedSize,
                  showBackground: _showBackground,
                ),
              ),
            ),
            const SizedBox(height: 16),
            Text(
              'الحجم: ${_selectedSize.toInt()}x${_selectedSize.toInt()} بكسل',
              style: const TextStyle(color: Colors.grey),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  /// بناء إعدادات الأيقونة
  Widget _buildIconSettings() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'إعدادات الأيقونة',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),

            // تبديل الخلفية
            SwitchListTile(
              title: const Text('إظهار الخلفية'),
              subtitle: const Text('إظهار أو إخفاء خلفية الأيقونة'),
              value: _showBackground,
              onChanged: (value) {
                setState(() {
                  _showBackground = value;
                });
              },
              activeColor: RevolutionaryColors.damascusSky,
            ),

            const SizedBox(height: 16),

            // اختيار الحجم
            const Text(
              'حجم المعاينة:',
              style: TextStyle(fontWeight: FontWeight.w500),
            ),
            const SizedBox(height: 8),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: _iconSizes.map((size) {
                final isSelected = size == _selectedSize;
                return FilterChip(
                  label: Text('${size.toInt()}'),
                  selected: isSelected,
                  onSelected: (selected) {
                    if (selected) {
                      setState(() {
                        _selectedSize = size;
                      });
                    }
                  },
                  selectedColor: RevolutionaryColors.damascusSky.withValues(
                    alpha: 0.2,
                  ),
                  checkmarkColor: RevolutionaryColors.damascusSky,
                );
              }).toList(),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء معاينة الأحجام
  Widget _buildSizePreview() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'معاينة الأحجام المختلفة',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Wrap(
              spacing: 16,
              runSpacing: 16,
              children: [16, 24, 32, 48, 64, 96, 128].map((size) {
                return Column(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.grey[100],
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: AppIconGenerator.createAppIcon(
                        size: size.toDouble(),
                        showBackground: _showBackground,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      '${size}px',
                      style: const TextStyle(fontSize: 12, color: Colors.grey),
                    ),
                  ],
                );
              }).toList(),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء معاينة الاستخدامات
  Widget _buildUsagePreview() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'معاينة الاستخدامات',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),

            // في شريط التطبيقات
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: RevolutionaryColors.damascusSky,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  AppIconGenerator.createSimpleIcon(size: 24),
                  const SizedBox(width: 12),
                  const Text(
                    'Smart Ledger',
                    style: TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const Spacer(),
                  const Icon(Icons.more_vert, color: Colors.white),
                ],
              ),
            ),

            const SizedBox(height: 16),

            // في قائمة التطبيقات
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  AppIconGenerator.createSimpleIcon(size: 48),
                  const SizedBox(width: 12),
                  const Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Smart Ledger',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                          ),
                        ),
                        Text(
                          'دفتر الحسابات الذكي',
                          style: TextStyle(color: Colors.grey, fontSize: 12),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 16),

            // في الإشعارات
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey[300]!),
              ),
              child: Row(
                children: [
                  AppIconGenerator.createSimpleIcon(size: 32),
                  const SizedBox(width: 12),
                  const Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Smart Ledger',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 14,
                          ),
                        ),
                        Text(
                          'لديك فاتورة مستحقة اليوم',
                          style: TextStyle(color: Colors.grey, fontSize: 12),
                        ),
                      ],
                    ),
                  ),
                  const Text(
                    'الآن',
                    style: TextStyle(color: Colors.grey, fontSize: 12),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// تصدير الأيقونة
  Future<void> _exportIcon() async {
    try {
      // إنشاء الأيقونة كصورة
      final recorder = ui.PictureRecorder();
      final canvas = Canvas(recorder);
      final size = Size(_selectedSize, _selectedSize);

      final painter = AppIconPainter(showBackground: _showBackground);
      painter.paint(canvas, size);

      final picture = recorder.endRecording();
      final image = await picture.toImage(
        _selectedSize.toInt(),
        _selectedSize.toInt(),
      );

      final byteData = await image.toByteData(format: ui.ImageByteFormat.png);
      final bytes = byteData!.buffer.asUint8List();

      // حفظ بيانات الأيقونة للاستخدام لاحقاً
      _iconBytes = bytes;

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'تم إنشاء الأيقونة بحجم ${_selectedSize.toInt()}x${_selectedSize.toInt()}',
            ),
            backgroundColor: Colors.green,
            action: SnackBarAction(
              label: 'حفظ',
              onPressed: () => _saveIconToClipboard(),
            ),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تصدير الأيقونة: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// حفظ الأيقونة في الحافظة
  Future<void> _saveIconToClipboard() async {
    try {
      if (_iconBytes != null) {
        // نسخ معلومات الأيقونة إلى الحافظة
        await Clipboard.setData(
          ClipboardData(
            text:
                'تم حفظ أيقونة Smart Ledger بحجم ${_selectedSize.toInt()}x${_selectedSize.toInt()} بكسل\n'
                'حجم الملف: ${(_iconBytes!.length / 1024).toStringAsFixed(1)} KB\n'
                'التنسيق: PNG\n'
                'الخلفية: ${_showBackground ? "مفعلة" : "معطلة"}',
          ),
        );

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم نسخ معلومات الأيقونة إلى الحافظة'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('يرجى إنشاء الأيقونة أولاً'),
              backgroundColor: Colors.orange,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في حفظ الأيقونة: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
