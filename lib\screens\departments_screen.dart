/// شاشة إدارة الأقسام
/// توفر واجهة لإدارة الأقسام في نظام الموارد البشرية
library;

import 'package:flutter/material.dart';
import '../models/hr_models.dart';
import '../services/department_service.dart';
import '../services/logging_service.dart';
import '../widgets/loading_widget.dart';
import '../widgets/custom_error_widget.dart' as custom_widgets;

class DepartmentsScreen extends StatefulWidget {
  const DepartmentsScreen({super.key});

  @override
  State<DepartmentsScreen> createState() => _DepartmentsScreenState();
}

class _DepartmentsScreenState extends State<DepartmentsScreen> {
  final DepartmentService _departmentService = DepartmentService();
  List<Department> _departments = [];
  bool _isLoading = true;
  String? _error;
  bool _showActiveOnly = true;

  @override
  void initState() {
    super.initState();
    _loadDepartments();
  }

  Future<void> _loadDepartments() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final departments = await _departmentService.getAllDepartments(
        activeOnly: _showActiveOnly,
      );

      setState(() {
        _departments = departments;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });

      LoggingService.error(
        'خطأ في تحميل الأقسام',
        category: 'DepartmentsScreen',
        data: {'error': e.toString()},
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إدارة الأقسام'),
        backgroundColor: Colors.blue[700],
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: Icon(
              _showActiveOnly ? Icons.visibility : Icons.visibility_off,
            ),
            onPressed: () {
              setState(() {
                _showActiveOnly = !_showActiveOnly;
              });
              _loadDepartments();
            },
            tooltip: _showActiveOnly
                ? 'إظهار جميع الأقسام'
                : 'إظهار الأقسام النشطة فقط',
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadDepartments,
            tooltip: 'تحديث',
          ),
        ],
      ),
      body: _buildBody(),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _showAddDepartmentDialog(),
        backgroundColor: Colors.blue[700],
        tooltip: 'إضافة قسم جديد',
        child: const Icon(Icons.add, color: Colors.white),
      ),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const LoadingWidget();
    }

    if (_error != null) {
      return custom_widgets.ErrorWidget(
        message: _error!,
        onRetry: _loadDepartments,
      );
    }

    if (_departments.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.business, size: 64, color: Colors.grey[400]),
            const SizedBox(height: 16),
            Text(
              _showActiveOnly ? 'لا توجد أقسام نشطة' : 'لا توجد أقسام',
              style: TextStyle(fontSize: 18, color: Colors.grey[600]),
            ),
            const SizedBox(height: 16),
            ElevatedButton.icon(
              onPressed: () => _showAddDepartmentDialog(),
              icon: const Icon(Icons.add),
              label: const Text('إضافة قسم جديد'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue[700],
                foregroundColor: Colors.white,
              ),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _loadDepartments,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: _departments.length,
        itemBuilder: (context, index) {
          final department = _departments[index];
          return _buildDepartmentCard(department);
        },
      ),
    );
  }

  Widget _buildDepartmentCard(Department department) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: department.isActive ? Colors.blue[700] : Colors.grey,
          child: Text(
            department.code.substring(0, 2).toUpperCase(),
            style: const TextStyle(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        title: Text(
          department.name,
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: department.isActive ? Colors.black87 : Colors.grey[600],
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('الرمز: ${department.code}'),
            if (department.description != null)
              Text(
                department.description!,
                style: TextStyle(color: Colors.grey[600]),
              ),
            Row(
              children: [
                Icon(
                  department.isActive ? Icons.check_circle : Icons.cancel,
                  size: 16,
                  color: department.isActive ? Colors.green : Colors.red,
                ),
                const SizedBox(width: 4),
                Text(
                  department.isActive ? 'نشط' : 'غير نشط',
                  style: TextStyle(
                    color: department.isActive ? Colors.green : Colors.red,
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ],
        ),
        trailing: PopupMenuButton<String>(
          onSelected: (value) => _handleMenuAction(value, department),
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'edit',
              child: ListTile(
                leading: Icon(Icons.edit),
                title: Text('تعديل'),
                contentPadding: EdgeInsets.zero,
              ),
            ),
            PopupMenuItem(
              value: 'toggle_status',
              child: ListTile(
                leading: Icon(
                  department.isActive ? Icons.visibility_off : Icons.visibility,
                ),
                title: Text(department.isActive ? 'إلغاء التفعيل' : 'تفعيل'),
                contentPadding: EdgeInsets.zero,
              ),
            ),
            const PopupMenuItem(
              value: 'delete',
              child: ListTile(
                leading: Icon(Icons.delete, color: Colors.red),
                title: Text('حذف', style: TextStyle(color: Colors.red)),
                contentPadding: EdgeInsets.zero,
              ),
            ),
          ],
        ),
        onTap: () => _showDepartmentDetails(department),
      ),
    );
  }

  void _handleMenuAction(String action, Department department) {
    switch (action) {
      case 'edit':
        _showEditDepartmentDialog(department);
        break;
      case 'toggle_status':
        _toggleDepartmentStatus(department);
        break;
      case 'delete':
        _deleteDepartment(department);
        break;
    }
  }

  void _showAddDepartmentDialog() {
    showDialog(
      context: context,
      builder: (context) => _DepartmentFormDialog(
        onSave: (name, code, description) async {
          final scaffoldMessenger = ScaffoldMessenger.of(context);
          try {
            await _departmentService.createDepartment(
              name: name,
              code: code,
              description: description.isEmpty ? null : description,
            );

            if (mounted) {
              scaffoldMessenger.showSnackBar(
                const SnackBar(
                  content: Text('تم إضافة القسم بنجاح'),
                  backgroundColor: Colors.green,
                ),
              );
              _loadDepartments();
            }
          } catch (e) {
            if (mounted) {
              scaffoldMessenger.showSnackBar(
                SnackBar(
                  content: Text('خطأ في إضافة القسم: ${e.toString()}'),
                  backgroundColor: Colors.red,
                ),
              );
            }
          }
        },
      ),
    );
  }

  void _showEditDepartmentDialog(Department department) {
    showDialog(
      context: context,
      builder: (context) => _DepartmentFormDialog(
        department: department,
        onSave: (name, code, description) async {
          final scaffoldMessenger = ScaffoldMessenger.of(context);
          try {
            final updatedDepartment = department.copyWith(
              name: name,
              code: code,
              description: description.isEmpty ? null : description,
            );

            await _departmentService.updateDepartment(updatedDepartment);

            if (mounted) {
              scaffoldMessenger.showSnackBar(
                const SnackBar(
                  content: Text('تم تحديث القسم بنجاح'),
                  backgroundColor: Colors.green,
                ),
              );
              _loadDepartments();
            }
          } catch (e) {
            if (mounted) {
              scaffoldMessenger.showSnackBar(
                SnackBar(
                  content: Text('خطأ في تحديث القسم: ${e.toString()}'),
                  backgroundColor: Colors.red,
                ),
              );
            }
          }
        },
      ),
    );
  }

  void _toggleDepartmentStatus(Department department) async {
    final scaffoldMessenger = ScaffoldMessenger.of(context);
    try {
      await _departmentService.toggleDepartmentStatus(department.id!);

      if (mounted) {
        scaffoldMessenger.showSnackBar(
          SnackBar(
            content: Text(
              department.isActive
                  ? 'تم إلغاء تفعيل القسم بنجاح'
                  : 'تم تفعيل القسم بنجاح',
            ),
            backgroundColor: Colors.green,
          ),
        );
        _loadDepartments();
      }
    } catch (e) {
      if (mounted) {
        scaffoldMessenger.showSnackBar(
          SnackBar(
            content: Text('خطأ في تغيير حالة القسم: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _deleteDepartment(Department department) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text('هل أنت متأكد من حذف القسم "${department.name}"؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              final navigator = Navigator.of(context);
              final scaffoldMessenger = ScaffoldMessenger.of(context);
              navigator.pop();

              try {
                await _departmentService.deleteDepartment(department.id!);

                if (mounted) {
                  scaffoldMessenger.showSnackBar(
                    const SnackBar(
                      content: Text('تم حذف القسم بنجاح'),
                      backgroundColor: Colors.green,
                    ),
                  );
                  _loadDepartments();
                }
              } catch (e) {
                if (mounted) {
                  scaffoldMessenger.showSnackBar(
                    SnackBar(
                      content: Text('خطأ في حذف القسم: ${e.toString()}'),
                      backgroundColor: Colors.red,
                    ),
                  );
                }
              }
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('حذف', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );
  }

  void _showDepartmentDetails(Department department) {
    // يمكن إضافة شاشة تفاصيل القسم هنا
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(SnackBar(content: Text('تفاصيل القسم: ${department.name}')));
  }
}

class _DepartmentFormDialog extends StatefulWidget {
  final Department? department;
  final Function(String name, String code, String description) onSave;

  const _DepartmentFormDialog({this.department, required this.onSave});

  @override
  State<_DepartmentFormDialog> createState() => _DepartmentFormDialogState();
}

class _DepartmentFormDialogState extends State<_DepartmentFormDialog> {
  final _formKey = GlobalKey<FormState>();
  late final TextEditingController _nameController;
  late final TextEditingController _codeController;
  late final TextEditingController _descriptionController;

  @override
  void initState() {
    super.initState();
    _nameController = TextEditingController(
      text: widget.department?.name ?? '',
    );
    _codeController = TextEditingController(
      text: widget.department?.code ?? '',
    );
    _descriptionController = TextEditingController(
      text: widget.department?.description ?? '',
    );
  }

  @override
  void dispose() {
    _nameController.dispose();
    _codeController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(widget.department == null ? 'إضافة قسم جديد' : 'تعديل القسم'),
      content: Form(
        key: _formKey,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextFormField(
              controller: _nameController,
              decoration: const InputDecoration(
                labelText: 'اسم القسم *',
                border: OutlineInputBorder(),
              ),
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'اسم القسم مطلوب';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _codeController,
              decoration: const InputDecoration(
                labelText: 'رمز القسم *',
                border: OutlineInputBorder(),
              ),
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'رمز القسم مطلوب';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _descriptionController,
              decoration: const InputDecoration(
                labelText: 'الوصف',
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('إلغاء'),
        ),
        ElevatedButton(
          onPressed: () {
            if (_formKey.currentState!.validate()) {
              widget.onSave(
                _nameController.text.trim(),
                _codeController.text.trim(),
                _descriptionController.text.trim(),
              );
              Navigator.of(context).pop();
            }
          },
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.blue[700],
            foregroundColor: Colors.white,
          ),
          child: Text(widget.department == null ? 'إضافة' : 'تحديث'),
        ),
      ],
    );
  }
}
