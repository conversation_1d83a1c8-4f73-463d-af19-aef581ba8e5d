/// شاشة إدارة العقود المتقدمة
/// واجهة شاملة لإدارة عقود الموظفين مع التنبيهات والتجديد
library;

import 'package:flutter/material.dart';
import '../constants/revolutionary_design_colors.dart';
import '../constants/app_constants.dart';
import '../models/hr_models.dart';
import '../services/advanced_contract_service.dart';
import '../services/employee_service.dart';
import '../services/logging_service.dart';
import '../widgets/loading_widget.dart';

class AdvancedContractsScreen extends StatefulWidget {
  const AdvancedContractsScreen({super.key});

  @override
  State<AdvancedContractsScreen> createState() =>
      _AdvancedContractsScreenState();
}

class _AdvancedContractsScreenState extends State<AdvancedContractsScreen>
    with TickerProviderStateMixin {
  final AdvancedContractService _contractService = AdvancedContractService();
  final EmployeeService _employeeService = EmployeeService();

  late TabController _tabController;

  List<EmployeeContract> _allContracts = [];
  List<EmployeeContract> _activeContracts = [];
  List<EmployeeContract> _expiringSoonContracts = [];
  List<Employee> _employees = [];
  Map<String, dynamic> _statistics = {};

  bool _isLoading = true;
  String? _error;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _loadData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    try {
      setState(() {
        _isLoading = true;
        _error = null;
      });

      // تحميل البيانات بشكل متوازي
      final results = await Future.wait([
        _contractService.getAllContracts(),
        _contractService.getActiveContracts(),
        _contractService.getExpiringSoonContracts(),
        _employeeService.getAllEmployees(),
        _contractService.getContractsStatistics(),
      ]);

      setState(() {
        _allContracts = results[0] as List<EmployeeContract>;
        _activeContracts = results[1] as List<EmployeeContract>;
        _expiringSoonContracts = results[2] as List<EmployeeContract>;
        _employees = results[3] as List<Employee>;
        _statistics = results[4] as Map<String, dynamic>;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });

      LoggingService.error(
        'خطأ في تحميل بيانات العقود',
        category: 'AdvancedContractsScreen',
        data: {'error': e.toString()},
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إدارة العقود المتقدمة'),
        backgroundColor: RevolutionaryColors.infoTurquoise,
        foregroundColor: Colors.white,
        actions: [
          IconButton(icon: const Icon(Icons.refresh), onPressed: _loadData),
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: _showAddContractDialog,
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          indicatorColor: Colors.white,
          tabs: const [
            Tab(icon: Icon(Icons.dashboard), text: 'نظرة عامة'),
            Tab(icon: Icon(Icons.description), text: 'جميع العقود'),
            Tab(icon: Icon(Icons.check_circle), text: 'العقود النشطة'),
            Tab(icon: Icon(Icons.warning), text: 'تنتهي قريباً'),
          ],
        ),
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const LoadingWidget(message: 'جاري تحميل بيانات العقود...');
    }

    if (_error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 64, color: Colors.red[400]),
            const SizedBox(height: 16),
            Text(
              'حدث خطأ',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.red[600],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              _error!,
              style: TextStyle(fontSize: 14, color: Colors.grey[600]),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadData,
              child: const Text('إعادة المحاولة'),
            ),
          ],
        ),
      );
    }

    return TabBarView(
      controller: _tabController,
      children: [
        _buildOverviewTab(),
        _buildAllContractsTab(),
        _buildActiveContractsTab(),
        _buildExpiringSoonTab(),
      ],
    );
  }

  Widget _buildOverviewTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildStatisticsCards(),
          const SizedBox(height: 24),
          _buildQuickActions(),
          const SizedBox(height: 24),
          _buildRecentContracts(),
        ],
      ),
    );
  }

  Widget _buildStatisticsCards() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'إحصائيات العقود',
          style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 12),
        GridView.count(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisCount: 2,
          crossAxisSpacing: 12,
          mainAxisSpacing: 12,
          childAspectRatio: 1.5,
          children: [
            _buildStatCard(
              'إجمالي العقود',
              _statistics['totalContracts']?.toString() ?? '0',
              Icons.description,
              RevolutionaryColors.infoTurquoise,
            ),
            _buildStatCard(
              'العقود النشطة',
              _statistics['activeContracts']?.toString() ?? '0',
              Icons.check_circle,
              RevolutionaryColors.successGlow,
            ),
            _buildStatCard(
              'تنتهي قريباً',
              _statistics['expiringSoonContracts']?.toString() ?? '0',
              Icons.warning,
              RevolutionaryColors.warningAmber,
            ),
            _buildStatCard(
              'متوسط الراتب',
              '${(_statistics['averageSalary'] ?? 0.0).toStringAsFixed(0)} ل.س',
              Icons.account_balance_wallet,
              RevolutionaryColors.syrianGold,
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildStatCard(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, color: color, size: 32),
            const SizedBox(height: 8),
            Text(
              title,
              style: const TextStyle(fontSize: 12, color: Colors.grey),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 4),
            Text(
              value,
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: color,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickActions() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'الإجراءات السريعة',
          style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: _buildActionCard(
                'إضافة عقد جديد',
                Icons.add,
                RevolutionaryColors.successGlow,
                _showAddContractDialog,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildActionCard(
                'فحص العقود المنتهية',
                Icons.search,
                RevolutionaryColors.warningAmber,
                _checkExpiringContracts,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildActionCard(
    String title,
    IconData icon,
    Color color,
    VoidCallback onTap,
  ) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              Icon(icon, color: color, size: 32),
              const SizedBox(height: 8),
              Text(
                title,
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildRecentContracts() {
    final recentContracts = _allContracts.take(5).toList();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'العقود الحديثة',
          style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 12),
        if (recentContracts.isEmpty)
          const Card(
            child: Padding(
              padding: EdgeInsets.all(20),
              child: Center(child: Text('لا توجد عقود')),
            ),
          )
        else
          Column(
            children: recentContracts.map((contract) {
              final employee = _employees.firstWhere(
                (emp) => emp.id == contract.employeeId,
                orElse: () => Employee(
                  employeeNumber: 'غير معروف',
                  nationalId: '',
                  firstName: 'موظف',
                  lastName: 'محذوف',
                  fullName: 'موظف محذوف',
                  hireDate: DateTime.now(),
                  basicSalary: 0,
                  status: 'inactive',
                  createdAt: DateTime.now(),
                  updatedAt: DateTime.now(),
                ),
              );

              return _buildContractCard(contract, employee);
            }).toList(),
          ),
      ],
    );
  }

  Widget _buildAllContractsTab() {
    return _buildContractsList(_allContracts, 'لا توجد عقود');
  }

  Widget _buildActiveContractsTab() {
    return _buildContractsList(_activeContracts, 'لا توجد عقود نشطة');
  }

  Widget _buildExpiringSoonTab() {
    return _buildContractsList(
      _expiringSoonContracts,
      'لا توجد عقود تنتهي قريباً',
    );
  }

  Widget _buildContractsList(
    List<EmployeeContract> contracts,
    String emptyMessage,
  ) {
    if (contracts.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.description_outlined, size: 64, color: Colors.grey[400]),
            const SizedBox(height: 16),
            Text(
              emptyMessage,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.grey,
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: contracts.length,
      itemBuilder: (context, index) {
        final contract = contracts[index];
        final employee = _employees.firstWhere(
          (emp) => emp.id == contract.employeeId,
          orElse: () => Employee(
            employeeNumber: 'غير معروف',
            nationalId: '',
            firstName: 'موظف',
            lastName: 'محذوف',
            fullName: 'موظف محذوف',
            hireDate: DateTime.now(),
            basicSalary: 0,
            status: 'inactive',
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          ),
        );

        return _buildContractCard(contract, employee);
      },
    );
  }

  Widget _buildContractCard(EmployeeContract contract, Employee employee) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                CircleAvatar(
                  backgroundColor: _getContractStatusColor(contract.status),
                  child: Icon(
                    _getContractStatusIcon(contract.status),
                    color: Colors.white,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        employee.displayName,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        _getContractTypeText(contract.contractType),
                        style: TextStyle(fontSize: 14, color: Colors.grey[600]),
                      ),
                    ],
                  ),
                ),
                PopupMenuButton<String>(
                  onSelected: (value) => _handleContractAction(value, contract),
                  itemBuilder: (context) => [
                    const PopupMenuItem(
                      value: 'view',
                      child: Row(
                        children: [
                          Icon(Icons.visibility),
                          SizedBox(width: 8),
                          Text('عرض التفاصيل'),
                        ],
                      ),
                    ),
                    const PopupMenuItem(
                      value: 'edit',
                      child: Row(
                        children: [
                          Icon(Icons.edit),
                          SizedBox(width: 8),
                          Text('تعديل'),
                        ],
                      ),
                    ),
                    if (contract.status == AppConstants.contractStatusActive)
                      const PopupMenuItem(
                        value: 'renew',
                        child: Row(
                          children: [
                            Icon(Icons.refresh),
                            SizedBox(width: 8),
                            Text('تجديد'),
                          ],
                        ),
                      ),
                    const PopupMenuItem(
                      value: 'terminate',
                      child: Row(
                        children: [
                          Icon(Icons.cancel, color: Colors.red),
                          SizedBox(width: 8),
                          Text('إنهاء', style: TextStyle(color: Colors.red)),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: _buildInfoItem(
                    'الراتب',
                    '${contract.salary.toStringAsFixed(0)} ل.س',
                    Icons.account_balance_wallet,
                  ),
                ),
                Expanded(
                  child: _buildInfoItem(
                    'ساعات العمل',
                    contract.workingHours,
                    Icons.access_time,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoItem(String label, String value, IconData icon) {
    return Row(
      children: [
        Icon(icon, size: 16, color: Colors.grey[600]),
        const SizedBox(width: 4),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: TextStyle(fontSize: 12, color: Colors.grey[600]),
              ),
              Text(
                value,
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Color _getContractStatusColor(String status) {
    switch (status) {
      case AppConstants.contractStatusActive:
        return RevolutionaryColors.successGlow;
      case AppConstants.contractStatusExpired:
        return RevolutionaryColors.warningAmber;
      case AppConstants.contractStatusTerminated:
        return RevolutionaryColors.errorCoral;
      default:
        return RevolutionaryColors.damascusSky;
    }
  }

  IconData _getContractStatusIcon(String status) {
    switch (status) {
      case AppConstants.contractStatusActive:
        return Icons.check_circle;
      case AppConstants.contractStatusExpired:
        return Icons.schedule;
      case AppConstants.contractStatusTerminated:
        return Icons.cancel;
      default:
        return Icons.description;
    }
  }

  String _getContractTypeText(String type) {
    switch (type) {
      case AppConstants.contractTypePermanent:
        return 'دائم';
      case AppConstants.contractTypeTemporary:
        return 'مؤقت';
      case AppConstants.contractTypePartTime:
        return 'دوام جزئي';
      case AppConstants.contractTypeContract:
        return 'تعاقد';
      default:
        return type;
    }
  }

  void _handleContractAction(String action, EmployeeContract contract) {
    switch (action) {
      case 'view':
        _showContractDetails(contract);
        break;
      case 'edit':
        _showEditContractDialog(contract);
        break;
      case 'renew':
        _showRenewContractDialog(contract);
        break;
      case 'terminate':
        _showTerminateContractDialog(contract);
        break;
    }
  }

  void _showAddContractDialog() {
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('إضافة عقد جديد - قريباً')));
  }

  void _showContractDetails(EmployeeContract contract) {
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('عرض تفاصيل العقد - قريباً')));
  }

  void _showEditContractDialog(EmployeeContract contract) {
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('تعديل العقد - قريباً')));
  }

  void _showRenewContractDialog(EmployeeContract contract) {
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('تجديد العقد - قريباً')));
  }

  void _showTerminateContractDialog(EmployeeContract contract) {
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('إنهاء العقد - قريباً')));
  }

  void _checkExpiringContracts() async {
    try {
      await _contractService.checkExpiringContracts();
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم فحص العقود المنتهية وإنشاء التنبيهات'),
            backgroundColor: RevolutionaryColors.successGlow,
          ),
        );
        _loadData();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في فحص العقود: $e'),
            backgroundColor: RevolutionaryColors.errorCoral,
          ),
        );
      }
    }
  }
}
