/// شاشة نموذج خطة التطوير الوظيفي
/// توفر واجهة لإنشاء وتعديل خطط التطوير الوظيفي
library;

import 'package:flutter/material.dart';
import '../models/career_planning_models.dart';
import '../models/hr_models.dart';
import '../services/career_planning_service.dart';
import '../services/employee_service.dart';
import '../services/logging_service.dart';
import '../widgets/loading_widget.dart';
import '../constants/revolutionary_design_colors.dart';

class CareerDevelopmentPlanFormScreen extends StatefulWidget {
  final Employee? employee;
  final CareerDevelopmentPlan? existingPlan;

  const CareerDevelopmentPlanFormScreen({
    super.key,
    this.employee,
    this.existingPlan,
  });

  @override
  State<CareerDevelopmentPlanFormScreen> createState() =>
      _CareerDevelopmentPlanFormScreenState();
}

class _CareerDevelopmentPlanFormScreenState
    extends State<CareerDevelopmentPlanFormScreen> {
  final CareerPlanningService _careerService = CareerPlanningService();
  final EmployeeService _employeeService = EmployeeService();
  final _formKey = GlobalKey<FormState>();

  List<Employee> _employees = [];
  List<CareerPath> _careerPaths = [];
  Employee? _selectedEmployee;
  CareerPath? _selectedCurrentPath;
  CareerPath? _selectedTargetPath;
  Employee? _selectedMentor;

  final _goalsController = TextEditingController();
  final _currentSkillsController = TextEditingController();
  final _skillGapsController = TextEditingController();
  final _developmentActionsController = TextEditingController();
  final _milestonesController = TextEditingController();
  final _managerNotesController = TextEditingController();
  final _employeeNotesController = TextEditingController();

  DateTime _startDate = DateTime.now();
  DateTime _targetDate = DateTime.now().add(const Duration(days: 365));
  String _status = 'draft';
  double _progressPercentage = 0.0;

  bool _isLoading = true;
  bool _isSaving = false;
  String? _error;

  @override
  void initState() {
    super.initState();
    _loadData();
    _initializeExistingData();
  }

  @override
  void dispose() {
    _goalsController.dispose();
    _currentSkillsController.dispose();
    _skillGapsController.dispose();
    _developmentActionsController.dispose();
    _milestonesController.dispose();
    _managerNotesController.dispose();
    _employeeNotesController.dispose();
    super.dispose();
  }

  void _initializeExistingData() {
    if (widget.existingPlan != null) {
      final plan = widget.existingPlan!;
      _goalsController.text = plan.goals;
      _currentSkillsController.text = plan.currentSkills;
      _skillGapsController.text = plan.skillGaps;
      _developmentActionsController.text = plan.developmentActions;
      _milestonesController.text = plan.milestones;
      _managerNotesController.text = plan.managerNotes ?? '';
      _employeeNotesController.text = plan.employeeNotes ?? '';
      _startDate = plan.startDate;
      _targetDate = plan.targetDate;
      _status = plan.status;
      _progressPercentage = plan.progressPercentage;
    }

    if (widget.employee != null) {
      _selectedEmployee = widget.employee;
    }
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final results = await Future.wait([
        _employeeService.getAllEmployees(),
        _careerService.getAllCareerPaths(isActive: true),
      ]);

      setState(() {
        _employees = results[0] as List<Employee>;
        _careerPaths = results[1] as List<CareerPath>;
        _isLoading = false;
      });

      // تحديد المسارات إذا كانت الخطة موجودة
      if (widget.existingPlan != null) {
        final plan = widget.existingPlan!;
        _selectedEmployee = _employees.firstWhere(
          (emp) => emp.id == plan.employeeId,
          orElse: () => _employees.first,
        );
        _selectedCurrentPath = _careerPaths.firstWhere(
          (path) => path.id == plan.currentPathId,
          orElse: () => _careerPaths.first,
        );
        if (plan.targetPathId != null) {
          _selectedTargetPath = _careerPaths.firstWhere(
            (path) => path.id == plan.targetPathId,
            orElse: () => _careerPaths.first,
          );
        }
        if (plan.mentorId != null) {
          _selectedMentor = _employees.firstWhere(
            (emp) => emp.id == plan.mentorId,
            orElse: () => _employees.first,
          );
        }
      }
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });

      LoggingService.error(
        'خطأ في تحميل بيانات خطة التطوير',
        category: 'CareerDevelopmentPlanFormScreen',
        data: {'error': e.toString()},
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          widget.existingPlan != null ? 'تعديل خطة التطوير' : 'إنشاء خطة تطوير',
        ),
        backgroundColor: RevolutionaryColors.damascusSky,
        foregroundColor: Colors.white,
        actions: [
          if (!_isLoading && _error == null)
            TextButton(
              onPressed: _isSaving ? null : _saveDraft,
              child: const Text(
                'حفظ مسودة',
                style: TextStyle(color: Colors.white),
              ),
            ),
        ],
      ),
      body: _buildBody(),
      bottomNavigationBar: _buildBottomBar(),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const LoadingWidget();
    }

    if (_error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 64, color: Colors.grey[400]),
            const SizedBox(height: 16),
            Text(
              'خطأ في تحميل البيانات',
              style: TextStyle(fontSize: 18, color: Colors.grey[600]),
            ),
            const SizedBox(height: 8),
            Text(
              _error!,
              style: TextStyle(fontSize: 14, color: Colors.grey[500]),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: _loadData,
              child: const Text('إعادة المحاولة'),
            ),
          ],
        ),
      );
    }

    return Form(
      key: _formKey,
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildBasicInfo(),
            const SizedBox(height: 24),
            _buildCareerPaths(),
            const SizedBox(height: 24),
            _buildDates(),
            const SizedBox(height: 24),
            _buildGoalsAndSkills(),
            const SizedBox(height: 24),
            _buildDevelopmentActions(),
            const SizedBox(height: 24),
            _buildMentorAndNotes(),
            const SizedBox(height: 24),
            _buildProgressSection(),
            const SizedBox(height: 100), // مساحة للشريط السفلي
          ],
        ),
      ),
    );
  }

  Widget _buildBasicInfo() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'المعلومات الأساسية',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            DropdownButtonFormField<Employee>(
              value: _selectedEmployee,
              decoration: const InputDecoration(
                labelText: 'الموظف',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.person),
              ),
              items: _employees
                  .map(
                    (employee) => DropdownMenuItem(
                      value: employee,
                      child: Text('${employee.firstName} ${employee.lastName}'),
                    ),
                  )
                  .toList(),
              onChanged: widget.employee != null
                  ? null
                  : (value) {
                      setState(() {
                        _selectedEmployee = value;
                      });
                    },
              validator: (value) {
                if (value == null) {
                  return 'يرجى اختيار الموظف';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),
            DropdownButtonFormField<String>(
              value: _status,
              decoration: const InputDecoration(
                labelText: 'حالة الخطة',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.flag),
              ),
              items: const [
                DropdownMenuItem(value: 'draft', child: Text('مسودة')),
                DropdownMenuItem(value: 'active', child: Text('نشط')),
                DropdownMenuItem(value: 'completed', child: Text('مكتمل')),
                DropdownMenuItem(value: 'on_hold', child: Text('معلق')),
                DropdownMenuItem(value: 'cancelled', child: Text('ملغي')),
              ],
              onChanged: (value) {
                if (value != null) {
                  setState(() {
                    _status = value;
                  });
                }
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCareerPaths() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'المسارات الوظيفية',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            DropdownButtonFormField<CareerPath>(
              value: _selectedCurrentPath,
              decoration: const InputDecoration(
                labelText: 'المسار الحالي',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.work),
              ),
              items: _careerPaths
                  .map(
                    (path) => DropdownMenuItem(
                      value: path,
                      child: Text('${path.name} - ${path.department}'),
                    ),
                  )
                  .toList(),
              onChanged: (value) {
                setState(() {
                  _selectedCurrentPath = value;
                });
              },
              validator: (value) {
                if (value == null) {
                  return 'يرجى اختيار المسار الحالي';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),
            DropdownButtonFormField<CareerPath>(
              value: _selectedTargetPath,
              decoration: const InputDecoration(
                labelText: 'المسار المستهدف (اختياري)',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.trending_up),
              ),
              items: _careerPaths
                  .map(
                    (path) => DropdownMenuItem(
                      value: path,
                      child: Text('${path.name} - ${path.department}'),
                    ),
                  )
                  .toList(),
              onChanged: (value) {
                setState(() {
                  _selectedTargetPath = value;
                });
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDates() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'التواريخ',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: InkWell(
                    onTap: () => _selectDate(true),
                    child: Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.grey),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'تاريخ البداية',
                            style: TextStyle(fontSize: 12),
                          ),
                          Text(
                            '${_startDate.day}/${_startDate.month}/${_startDate.year}',
                            style: const TextStyle(fontWeight: FontWeight.bold),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: InkWell(
                    onTap: () => _selectDate(false),
                    child: Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.grey),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'التاريخ المستهدف',
                            style: TextStyle(fontSize: 12),
                          ),
                          Text(
                            '${_targetDate.day}/${_targetDate.month}/${_targetDate.year}',
                            style: const TextStyle(fontWeight: FontWeight.bold),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildGoalsAndSkills() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'الأهداف والمهارات',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _goalsController,
              decoration: const InputDecoration(
                labelText: 'الأهداف',
                hintText: 'حدد الأهداف المراد تحقيقها...',
                border: OutlineInputBorder(),
                prefixIcon: Icon(
                  Icons.flag,
                  color: RevolutionaryColors.damascusSky,
                ),
              ),
              maxLines: 3,
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'يرجى إدخال الأهداف';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _currentSkillsController,
              decoration: const InputDecoration(
                labelText: 'المهارات الحالية',
                hintText: 'اذكر المهارات الحالية للموظف...',
                border: OutlineInputBorder(),
                prefixIcon: Icon(
                  Icons.star,
                  color: RevolutionaryColors.successGlow,
                ),
              ),
              maxLines: 3,
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _skillGapsController,
              decoration: const InputDecoration(
                labelText: 'فجوات المهارات',
                hintText: 'حدد المهارات المطلوب تطويرها...',
                border: OutlineInputBorder(),
                prefixIcon: Icon(
                  Icons.trending_up,
                  color: RevolutionaryColors.warningAmber,
                ),
              ),
              maxLines: 3,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDevelopmentActions() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'إجراءات التطوير',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _developmentActionsController,
              decoration: const InputDecoration(
                labelText: 'إجراءات التطوير',
                hintText: 'حدد الإجراءات والأنشطة المطلوبة للتطوير...',
                border: OutlineInputBorder(),
                prefixIcon: Icon(
                  Icons.build,
                  color: RevolutionaryColors.infoTurquoise,
                ),
              ),
              maxLines: 4,
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _milestonesController,
              decoration: const InputDecoration(
                labelText: 'المعالم المهمة',
                hintText: 'حدد المعالم والإنجازات المرحلية...',
                border: OutlineInputBorder(),
                prefixIcon: Icon(
                  Icons.flag_outlined,
                  color: RevolutionaryColors.damascusSky,
                ),
              ),
              maxLines: 3,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMentorAndNotes() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'الموجه والملاحظات',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            DropdownButtonFormField<Employee>(
              value: _selectedMentor,
              decoration: const InputDecoration(
                labelText: 'الموجه (اختياري)',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.supervisor_account),
              ),
              items: _employees
                  .map(
                    (employee) => DropdownMenuItem(
                      value: employee,
                      child: Text('${employee.firstName} ${employee.lastName}'),
                    ),
                  )
                  .toList(),
              onChanged: (value) {
                setState(() {
                  _selectedMentor = value;
                });
              },
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _managerNotesController,
              decoration: const InputDecoration(
                labelText: 'ملاحظات المدير',
                hintText: 'ملاحظات وتوجيهات من المدير...',
                border: OutlineInputBorder(),
                prefixIcon: Icon(
                  Icons.note,
                  color: RevolutionaryColors.warningAmber,
                ),
              ),
              maxLines: 3,
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _employeeNotesController,
              decoration: const InputDecoration(
                labelText: 'ملاحظات الموظف',
                hintText: 'ملاحظات وتعليقات من الموظف...',
                border: OutlineInputBorder(),
                prefixIcon: Icon(
                  Icons.comment,
                  color: RevolutionaryColors.infoTurquoise,
                ),
              ),
              maxLines: 3,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProgressSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'التقدم',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Text(
              'نسبة التقدم: ${_progressPercentage.toStringAsFixed(0)}%',
              style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
            ),
            const SizedBox(height: 8),
            Slider(
              value: _progressPercentage,
              min: 0,
              max: 100,
              divisions: 20,
              activeColor: RevolutionaryColors.successGlow,
              onChanged: (value) {
                setState(() {
                  _progressPercentage = value;
                });
              },
            ),
            const SizedBox(height: 8),
            LinearProgressIndicator(
              value: _progressPercentage / 100,
              backgroundColor: Colors.grey[300],
              valueColor: const AlwaysStoppedAnimation<Color>(
                RevolutionaryColors.successGlow,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBottomBar() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.3),
            spreadRadius: 1,
            blurRadius: 5,
            offset: const Offset(0, -3),
          ),
        ],
      ),
      child: Row(
        children: [
          Expanded(
            child: OutlinedButton(
              onPressed: _isSaving ? null : _saveDraft,
              child: _isSaving
                  ? const SizedBox(
                      height: 20,
                      width: 20,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    )
                  : const Text('حفظ مسودة'),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: ElevatedButton(
              onPressed: _isSaving ? null : _savePlan,
              style: ElevatedButton.styleFrom(
                backgroundColor: RevolutionaryColors.successGlow,
                foregroundColor: Colors.white,
              ),
              child: _isSaving
                  ? const SizedBox(
                      height: 20,
                      width: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                  : Text(
                      widget.existingPlan != null
                          ? 'تحديث الخطة'
                          : 'إنشاء الخطة',
                    ),
            ),
          ),
        ],
      ),
    );
  }

  // دوال الأحداث
  Future<void> _selectDate(bool isStartDate) async {
    final date = await showDatePicker(
      context: context,
      initialDate: isStartDate ? _startDate : _targetDate,
      firstDate: DateTime.now().subtract(const Duration(days: 365)),
      lastDate: DateTime.now().add(const Duration(days: 365 * 5)),
    );

    if (date != null) {
      setState(() {
        if (isStartDate) {
          _startDate = date;
          // التأكد من أن تاريخ النهاية بعد تاريخ البداية
          if (_targetDate.isBefore(_startDate)) {
            _targetDate = _startDate.add(const Duration(days: 365));
          }
        } else {
          _targetDate = date;
        }
      });
    }
  }

  Future<void> _saveDraft() async {
    await _savePlan(isDraft: true);
  }

  Future<void> _savePlan({bool isDraft = false}) async {
    if (!isDraft && !_formKey.currentState!.validate()) {
      return;
    }

    if (_selectedEmployee == null || _selectedCurrentPath == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يرجى اختيار الموظف والمسار الحالي'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    setState(() {
      _isSaving = true;
    });

    try {
      final plan = CareerDevelopmentPlan(
        id: widget.existingPlan?.id,
        employeeId: _selectedEmployee!.id!,
        currentPathId: _selectedCurrentPath!.id!,
        targetPathId: _selectedTargetPath?.id,
        status: isDraft ? 'draft' : _status,
        startDate: _startDate,
        targetDate: _targetDate,
        goals: _goalsController.text.trim(),
        currentSkills: _currentSkillsController.text.trim(),
        skillGaps: _skillGapsController.text.trim(),
        developmentActions: _developmentActionsController.text.trim(),
        milestones: _milestonesController.text.trim(),
        mentorId: _selectedMentor?.id,
        managerNotes: _managerNotesController.text.trim().isEmpty
            ? null
            : _managerNotesController.text.trim(),
        employeeNotes: _employeeNotesController.text.trim().isEmpty
            ? null
            : _employeeNotesController.text.trim(),
        progressPercentage: _progressPercentage,
        createdAt: widget.existingPlan?.createdAt ?? DateTime.now(),
        updatedAt: DateTime.now(),
      );

      CareerDevelopmentPlan savedPlan;
      if (widget.existingPlan != null) {
        // تحديث الخطة الموجودة
        savedPlan = await _careerService.updatePlanProgress(
          planId: plan.id!,
          progressPercentage: plan.progressPercentage,
          employeeNotes: plan.employeeNotes,
          managerNotes: plan.managerNotes,
        );
      } else {
        // إنشاء خطة جديدة
        savedPlan = await _careerService.createDevelopmentPlan(plan);
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              isDraft
                  ? 'تم حفظ المسودة بنجاح'
                  : widget.existingPlan != null
                  ? 'تم تحديث الخطة بنجاح'
                  : 'تم إنشاء الخطة بنجاح',
            ),
            backgroundColor: Colors.green,
          ),
        );

        Navigator.of(context).pop(savedPlan);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في حفظ الخطة: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSaving = false;
        });
      }
    }
  }
}
