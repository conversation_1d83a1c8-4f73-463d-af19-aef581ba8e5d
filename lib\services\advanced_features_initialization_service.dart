/// خدمة تهيئة الميزات المتقدمة
/// تدير تهيئة وربط جميع الميزات المتقدمة الجديدة
library;

import '../services/visual_report_builder_service.dart';
import '../services/syrian_tax_service.dart';
import '../services/smart_notification_service.dart';
import '../services/logging_service.dart';
import '../models/syrian_tax_models.dart';

class AdvancedFeaturesInitializationService {
  static final AdvancedFeaturesInitializationService _instance =
      AdvancedFeaturesInitializationService._internal();
  factory AdvancedFeaturesInitializationService() => _instance;
  AdvancedFeaturesInitializationService._internal();

  bool _isInitialized = false;

  /// تهيئة جميع الميزات المتقدمة
  Future<bool> initializeAllFeatures() async {
    if (_isInitialized) {
      LoggingService.info(
        'الميزات المتقدمة مهيأة مسبقاً',
        category: 'AdvancedFeatures',
      );
      return true;
    }

    try {
      LoggingService.info(
        'بدء تهيئة الميزات المتقدمة',
        category: 'AdvancedFeatures',
      );

      // تهيئة منشئ التقارير المرئي
      await _initializeVisualReportBuilder();

      // تهيئة النظام الضريبي السوري
      await _initializeSyrianTaxSystem();

      // تهيئة نظام التنبيهات الذكية
      await _initializeSmartNotifications();

      _isInitialized = true;

      LoggingService.info(
        'تم تهيئة جميع الميزات المتقدمة بنجاح',
        category: 'AdvancedFeatures',
      );

      return true;
    } catch (e) {
      LoggingService.error(
        'خطأ في تهيئة الميزات المتقدمة',
        category: 'AdvancedFeatures',
        data: {'error': e.toString()},
      );
      return false;
    }
  }

  /// تهيئة منشئ التقارير المرئي
  Future<void> _initializeVisualReportBuilder() async {
    try {
      LoggingService.info(
        'تهيئة منشئ التقارير المرئي',
        category: 'VisualReportBuilder',
      );

      final service = VisualReportBuilderService();

      // إنشاء القوالب الافتراضية إذا لم تكن موجودة
      final templates = await service.getAllTemplates();
      if (templates.isEmpty) {
        await service.createDefaultTemplates();
        LoggingService.info(
          'تم إنشاء القوالب الافتراضية لمنشئ التقارير',
          category: 'VisualReportBuilder',
        );
      }

      LoggingService.info(
        'تم تهيئة منشئ التقارير المرئي بنجاح',
        category: 'VisualReportBuilder',
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في تهيئة منشئ التقارير المرئي',
        category: 'VisualReportBuilder',
        data: {'error': e.toString()},
      );
      rethrow;
    }
  }

  /// تهيئة النظام الضريبي السوري
  Future<void> _initializeSyrianTaxSystem() async {
    try {
      LoggingService.info('تهيئة النظام الضريبي السوري', category: 'SyrianTax');

      final service = SyrianTaxService();

      // إنشاء معلومات دافع الضرائب الافتراضية إذا لم تكن موجودة
      final taxpayerInfo = await service.getTaxpayerInfo('default');
      if (taxpayerInfo == null) {
        await _createDefaultTaxpayerInfo(service);
        LoggingService.info(
          'تم إنشاء معلومات دافع الضرائب الافتراضية',
          category: 'SyrianTax',
        );
      }

      LoggingService.info(
        'تم تهيئة النظام الضريبي السوري بنجاح',
        category: 'SyrianTax',
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في تهيئة النظام الضريبي السوري',
        category: 'SyrianTax',
        data: {'error': e.toString()},
      );
      rethrow;
    }
  }

  /// تهيئة نظام التنبيهات الذكية
  Future<void> _initializeSmartNotifications() async {
    try {
      LoggingService.info(
        'تهيئة نظام التنبيهات الذكية',
        category: 'SmartNotifications',
      );

      final service = SmartNotificationService();

      // بدء خدمة التنبيهات
      await service.startNotificationService();

      // إنشاء تنبيهات الدفعات المستحقة
      await service.createPaymentDueNotifications();

      // إنشاء تنبيهات المخزون المنخفض
      await service.createLowStockNotifications();

      LoggingService.info(
        'تم تهيئة نظام التنبيهات الذكية بنجاح',
        category: 'SmartNotifications',
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في تهيئة نظام التنبيهات الذكية',
        category: 'SmartNotifications',
        data: {'error': e.toString()},
      );
      rethrow;
    }
  }

  /// إعادة تهيئة الميزات المتقدمة
  Future<bool> reinitializeFeatures() async {
    _isInitialized = false;
    return await initializeAllFeatures();
  }

  /// فحص حالة التهيئة
  bool get isInitialized => _isInitialized;

  /// إيقاف جميع الخدمات
  Future<void> stopAllServices() async {
    try {
      LoggingService.info(
        'إيقاف جميع خدمات الميزات المتقدمة',
        category: 'AdvancedFeatures',
      );

      // إيقاف خدمة التنبيهات
      final notificationService = SmartNotificationService();
      notificationService.stopNotificationService();

      _isInitialized = false;

      LoggingService.info(
        'تم إيقاف جميع خدمات الميزات المتقدمة',
        category: 'AdvancedFeatures',
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في إيقاف خدمات الميزات المتقدمة',
        category: 'AdvancedFeatures',
        data: {'error': e.toString()},
      );
    }
  }

  /// تحديث التنبيهات الدورية
  Future<void> updatePeriodicNotifications() async {
    try {
      if (!_isInitialized) {
        LoggingService.warning(
          'محاولة تحديث التنبيهات قبل التهيئة',
          category: 'SmartNotifications',
        );
        return;
      }

      final service = SmartNotificationService();

      // تحديث تنبيهات الدفعات المستحقة
      await service.createPaymentDueNotifications();

      // تحديث تنبيهات المخزون المنخفض
      await service.createLowStockNotifications();

      LoggingService.info(
        'تم تحديث التنبيهات الدورية',
        category: 'SmartNotifications',
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في تحديث التنبيهات الدورية',
        category: 'SmartNotifications',
        data: {'error': e.toString()},
      );
    }
  }

  /// إنشاء تقرير حالة الميزات المتقدمة
  Future<Map<String, dynamic>> getFeatureStatus() async {
    try {
      final status = <String, dynamic>{
        'isInitialized': _isInitialized,
        'timestamp': DateTime.now().toIso8601String(),
        'features': <String, dynamic>{},
      };

      if (_isInitialized) {
        // فحص حالة منشئ التقارير
        try {
          final reportBuilderService = VisualReportBuilderService();
          final templates = await reportBuilderService.getAllTemplates();
          status['features']['visualReportBuilder'] = {
            'status': 'active',
            'templatesCount': templates.length,
          };
        } catch (e) {
          status['features']['visualReportBuilder'] = {
            'status': 'error',
            'error': e.toString(),
          };
        }

        // فحص حالة النظام الضريبي
        try {
          final taxService = SyrianTaxService();
          final declarations = await taxService.getAllTaxDeclarations();
          status['features']['syrianTax'] = {
            'status': 'active',
            'declarationsCount': declarations.length,
          };
        } catch (e) {
          status['features']['syrianTax'] = {
            'status': 'error',
            'error': e.toString(),
          };
        }

        // فحص حالة التنبيهات
        try {
          final notificationService = SmartNotificationService();
          final notifications = await notificationService.getAllNotifications();
          status['features']['smartNotifications'] = {
            'status': 'active',
            'notificationsCount': notifications.length,
          };
        } catch (e) {
          status['features']['smartNotifications'] = {
            'status': 'error',
            'error': e.toString(),
          };
        }
      }

      return status;
    } catch (e) {
      LoggingService.error(
        'خطأ في الحصول على حالة الميزات المتقدمة',
        category: 'AdvancedFeatures',
        data: {'error': e.toString()},
      );

      return {
        'isInitialized': false,
        'error': e.toString(),
        'timestamp': DateTime.now().toIso8601String(),
      };
    }
  }

  /// إنشاء معلومات دافع الضرائب الافتراضية
  Future<void> _createDefaultTaxpayerInfo(SyrianTaxService service) async {
    try {
      final defaultTaxpayer = TaxpayerInfo(
        id: 'default',
        name: 'دفتر الأستاذ الذكي',
        taxNumber: '*********',
        category: TaxpayerCategory.company,
        address: 'سوريا - دمشق',
        phone: '+963-11-1234567',
        email: '<EMAIL>',
        businessActivity: 'برمجيات المحاسبة والإدارة المالية',
        registrationDate: DateTime.now(),
        isActive: true,
        additionalInfo: {
          'website': 'www.smartledger.sy',
          'registrationNumber': 'REG-2024-001',
          'establishmentDate': DateTime.now().toIso8601String(),
          'legalForm': 'شركة محدودة المسؤولية',
          'capital': '1000000',
          'currency': 'SYP',
          'notes': 'معلومات دافع الضرائب الافتراضية للنظام',
        },
      );

      await service.saveTaxpayerInfo(defaultTaxpayer);

      LoggingService.info(
        'تم إنشاء معلومات دافع الضرائب الافتراضية بنجاح',
        category: 'SyrianTax',
        data: {
          'taxpayerId': defaultTaxpayer.id,
          'name': defaultTaxpayer.name,
          'taxNumber': defaultTaxpayer.taxNumber,
        },
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في إنشاء معلومات دافع الضرائب الافتراضية',
        category: 'SyrianTax',
        data: {'error': e.toString()},
      );
      rethrow;
    }
  }
}
