/// نماذج نظام التخطيط الوظيفي
/// تحتوي على جميع النماذج المتعلقة بالتخطيط الوظيفي والتطوير المهني
library;

/// نموذج المسار الوظيفي
class CareerPath {
  final int? id;
  final String name;
  final String description;
  final String department;
  final String level; // entry, junior, mid, senior, lead, manager, director
  final List<String> requiredSkills;
  final List<String> preferredQualifications;
  final double minSalary;
  final double maxSalary;
  final int experienceYears;
  final String? nextLevelPath;
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;

  const CareerPath({
    this.id,
    required this.name,
    required this.description,
    required this.department,
    required this.level,
    this.requiredSkills = const [],
    this.preferredQualifications = const [],
    this.minSalary = 0,
    this.maxSalary = 0,
    this.experienceYears = 0,
    this.nextLevelPath,
    this.isActive = true,
    required this.createdAt,
    required this.updatedAt,
  });

  factory CareerPath.fromMap(Map<String, dynamic> map) {
    return CareerPath(
      id: map['id'] as int?,
      name: map['name'] as String,
      description: map['description'] as String,
      department: map['department'] as String,
      level: map['level'] as String,
      requiredSkills: (map['required_skills'] as String?)?.split(',') ?? [],
      preferredQualifications: (map['preferred_qualifications'] as String?)?.split(',') ?? [],
      minSalary: (map['min_salary'] as num?)?.toDouble() ?? 0,
      maxSalary: (map['max_salary'] as num?)?.toDouble() ?? 0,
      experienceYears: map['experience_years'] as int? ?? 0,
      nextLevelPath: map['next_level_path'] as String?,
      isActive: (map['is_active'] as int?) == 1,
      createdAt: DateTime.parse(map['created_at'] as String),
      updatedAt: DateTime.parse(map['updated_at'] as String),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      if (id != null) 'id': id,
      'name': name,
      'description': description,
      'department': department,
      'level': level,
      'required_skills': requiredSkills.join(','),
      'preferred_qualifications': preferredQualifications.join(','),
      'min_salary': minSalary,
      'max_salary': maxSalary,
      'experience_years': experienceYears,
      'next_level_path': nextLevelPath,
      'is_active': isActive ? 1 : 0,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  CareerPath copyWith({
    int? id,
    String? name,
    String? description,
    String? department,
    String? level,
    List<String>? requiredSkills,
    List<String>? preferredQualifications,
    double? minSalary,
    double? maxSalary,
    int? experienceYears,
    String? nextLevelPath,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return CareerPath(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      department: department ?? this.department,
      level: level ?? this.level,
      requiredSkills: requiredSkills ?? this.requiredSkills,
      preferredQualifications: preferredQualifications ?? this.preferredQualifications,
      minSalary: minSalary ?? this.minSalary,
      maxSalary: maxSalary ?? this.maxSalary,
      experienceYears: experienceYears ?? this.experienceYears,
      nextLevelPath: nextLevelPath ?? this.nextLevelPath,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  // خصائص مساعدة
  String get levelText {
    switch (level) {
      case 'entry': return 'مبتدئ';
      case 'junior': return 'مبتدئ متقدم';
      case 'mid': return 'متوسط';
      case 'senior': return 'كبير';
      case 'lead': return 'قائد فريق';
      case 'manager': return 'مدير';
      case 'director': return 'مدير عام';
      default: return level;
    }
  }

  String get salaryRange => '${minSalary.toStringAsFixed(0)} - ${maxSalary.toStringAsFixed(0)} ل.س';
}

/// نموذج خطة التطوير الوظيفي
class CareerDevelopmentPlan {
  final int? id;
  final int employeeId;
  final int currentPathId;
  final int? targetPathId;
  final String status; // draft, active, completed, cancelled, on_hold
  final DateTime startDate;
  final DateTime targetDate;
  final String goals;
  final String currentSkills;
  final String skillGaps;
  final String developmentActions;
  final String milestones;
  final int? mentorId;
  final String? managerNotes;
  final String? employeeNotes;
  final double progressPercentage;
  final DateTime? lastReviewDate;
  final DateTime? nextReviewDate;
  final DateTime createdAt;
  final DateTime updatedAt;

  const CareerDevelopmentPlan({
    this.id,
    required this.employeeId,
    required this.currentPathId,
    this.targetPathId,
    this.status = 'draft',
    required this.startDate,
    required this.targetDate,
    required this.goals,
    this.currentSkills = '',
    this.skillGaps = '',
    this.developmentActions = '',
    this.milestones = '',
    this.mentorId,
    this.managerNotes,
    this.employeeNotes,
    this.progressPercentage = 0.0,
    this.lastReviewDate,
    this.nextReviewDate,
    required this.createdAt,
    required this.updatedAt,
  });

  factory CareerDevelopmentPlan.fromMap(Map<String, dynamic> map) {
    return CareerDevelopmentPlan(
      id: map['id'] as int?,
      employeeId: map['employee_id'] as int,
      currentPathId: map['current_path_id'] as int,
      targetPathId: map['target_path_id'] as int?,
      status: map['status'] as String? ?? 'draft',
      startDate: DateTime.parse(map['start_date'] as String),
      targetDate: DateTime.parse(map['target_date'] as String),
      goals: map['goals'] as String,
      currentSkills: map['current_skills'] as String? ?? '',
      skillGaps: map['skill_gaps'] as String? ?? '',
      developmentActions: map['development_actions'] as String? ?? '',
      milestones: map['milestones'] as String? ?? '',
      mentorId: map['mentor_id'] as int?,
      managerNotes: map['manager_notes'] as String?,
      employeeNotes: map['employee_notes'] as String?,
      progressPercentage: (map['progress_percentage'] as num?)?.toDouble() ?? 0.0,
      lastReviewDate: map['last_review_date'] != null
          ? DateTime.parse(map['last_review_date'] as String)
          : null,
      nextReviewDate: map['next_review_date'] != null
          ? DateTime.parse(map['next_review_date'] as String)
          : null,
      createdAt: DateTime.parse(map['created_at'] as String),
      updatedAt: DateTime.parse(map['updated_at'] as String),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      if (id != null) 'id': id,
      'employee_id': employeeId,
      'current_path_id': currentPathId,
      'target_path_id': targetPathId,
      'status': status,
      'start_date': startDate.toIso8601String().split('T')[0],
      'target_date': targetDate.toIso8601String().split('T')[0],
      'goals': goals,
      'current_skills': currentSkills,
      'skill_gaps': skillGaps,
      'development_actions': developmentActions,
      'milestones': milestones,
      'mentor_id': mentorId,
      'manager_notes': managerNotes,
      'employee_notes': employeeNotes,
      'progress_percentage': progressPercentage,
      'last_review_date': lastReviewDate?.toIso8601String().split('T')[0],
      'next_review_date': nextReviewDate?.toIso8601String().split('T')[0],
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  CareerDevelopmentPlan copyWith({
    int? id,
    int? employeeId,
    int? currentPathId,
    int? targetPathId,
    String? status,
    DateTime? startDate,
    DateTime? targetDate,
    String? goals,
    String? currentSkills,
    String? skillGaps,
    String? developmentActions,
    String? milestones,
    int? mentorId,
    String? managerNotes,
    String? employeeNotes,
    double? progressPercentage,
    DateTime? lastReviewDate,
    DateTime? nextReviewDate,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return CareerDevelopmentPlan(
      id: id ?? this.id,
      employeeId: employeeId ?? this.employeeId,
      currentPathId: currentPathId ?? this.currentPathId,
      targetPathId: targetPathId ?? this.targetPathId,
      status: status ?? this.status,
      startDate: startDate ?? this.startDate,
      targetDate: targetDate ?? this.targetDate,
      goals: goals ?? this.goals,
      currentSkills: currentSkills ?? this.currentSkills,
      skillGaps: skillGaps ?? this.skillGaps,
      developmentActions: developmentActions ?? this.developmentActions,
      milestones: milestones ?? this.milestones,
      mentorId: mentorId ?? this.mentorId,
      managerNotes: managerNotes ?? this.managerNotes,
      employeeNotes: employeeNotes ?? this.employeeNotes,
      progressPercentage: progressPercentage ?? this.progressPercentage,
      lastReviewDate: lastReviewDate ?? this.lastReviewDate,
      nextReviewDate: nextReviewDate ?? this.nextReviewDate,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  // خصائص مساعدة
  bool get isDraft => status == 'draft';
  bool get isActive => status == 'active';
  bool get isCompleted => status == 'completed';
  bool get isCancelled => status == 'cancelled';
  bool get isOnHold => status == 'on_hold';
  
  String get statusText {
    switch (status) {
      case 'draft': return 'مسودة';
      case 'active': return 'نشط';
      case 'completed': return 'مكتمل';
      case 'cancelled': return 'ملغي';
      case 'on_hold': return 'معلق';
      default: return status;
    }
  }

  Duration get duration => targetDate.difference(startDate);
  int get durationInDays => duration.inDays;
  bool get isOverdue => DateTime.now().isAfter(targetDate) && !isCompleted;
  
  Duration get timeRemaining => targetDate.difference(DateTime.now());
  int get daysRemaining => timeRemaining.inDays;
}

/// نموذج مراجعة التطوير الوظيفي
class CareerReview {
  final int? id;
  final int planId;
  final int reviewerId;
  final DateTime reviewDate;
  final String reviewType; // quarterly, semi_annual, annual, milestone
  final double progressRating; // 1-5
  final String achievements;
  final String challenges;
  final String feedback;
  final String recommendations;
  final String nextSteps;
  final bool isApproved;
  final DateTime createdAt;
  final DateTime updatedAt;

  const CareerReview({
    this.id,
    required this.planId,
    required this.reviewerId,
    required this.reviewDate,
    this.reviewType = 'quarterly',
    this.progressRating = 3.0,
    this.achievements = '',
    this.challenges = '',
    this.feedback = '',
    this.recommendations = '',
    this.nextSteps = '',
    this.isApproved = false,
    required this.createdAt,
    required this.updatedAt,
  });

  factory CareerReview.fromMap(Map<String, dynamic> map) {
    return CareerReview(
      id: map['id'] as int?,
      planId: map['plan_id'] as int,
      reviewerId: map['reviewer_id'] as int,
      reviewDate: DateTime.parse(map['review_date'] as String),
      reviewType: map['review_type'] as String? ?? 'quarterly',
      progressRating: (map['progress_rating'] as num?)?.toDouble() ?? 3.0,
      achievements: map['achievements'] as String? ?? '',
      challenges: map['challenges'] as String? ?? '',
      feedback: map['feedback'] as String? ?? '',
      recommendations: map['recommendations'] as String? ?? '',
      nextSteps: map['next_steps'] as String? ?? '',
      isApproved: (map['is_approved'] as int?) == 1,
      createdAt: DateTime.parse(map['created_at'] as String),
      updatedAt: DateTime.parse(map['updated_at'] as String),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      if (id != null) 'id': id,
      'plan_id': planId,
      'reviewer_id': reviewerId,
      'review_date': reviewDate.toIso8601String().split('T')[0],
      'review_type': reviewType,
      'progress_rating': progressRating,
      'achievements': achievements,
      'challenges': challenges,
      'feedback': feedback,
      'recommendations': recommendations,
      'next_steps': nextSteps,
      'is_approved': isApproved ? 1 : 0,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  CareerReview copyWith({
    int? id,
    int? planId,
    int? reviewerId,
    DateTime? reviewDate,
    String? reviewType,
    double? progressRating,
    String? achievements,
    String? challenges,
    String? feedback,
    String? recommendations,
    String? nextSteps,
    bool? isApproved,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return CareerReview(
      id: id ?? this.id,
      planId: planId ?? this.planId,
      reviewerId: reviewerId ?? this.reviewerId,
      reviewDate: reviewDate ?? this.reviewDate,
      reviewType: reviewType ?? this.reviewType,
      progressRating: progressRating ?? this.progressRating,
      achievements: achievements ?? this.achievements,
      challenges: challenges ?? this.challenges,
      feedback: feedback ?? this.feedback,
      recommendations: recommendations ?? this.recommendations,
      nextSteps: nextSteps ?? this.nextSteps,
      isApproved: isApproved ?? this.isApproved,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  // خصائص مساعدة
  String get reviewTypeText {
    switch (reviewType) {
      case 'quarterly': return 'ربع سنوي';
      case 'semi_annual': return 'نصف سنوي';
      case 'annual': return 'سنوي';
      case 'milestone': return 'معلم مهم';
      default: return reviewType;
    }
  }

  String get ratingText {
    if (progressRating >= 4.5) return 'ممتاز';
    if (progressRating >= 3.5) return 'جيد جداً';
    if (progressRating >= 2.5) return 'جيد';
    if (progressRating >= 1.5) return 'مقبول';
    return 'ضعيف';
  }
}
