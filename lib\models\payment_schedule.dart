/// نموذج جدولة الدفعات المتقدمة
/// يحتوي على نظام جدولة ذكي للدفعات مع التذكيرات والتنبيهات
library;

/// تكرار الدفعات
enum PaymentFrequency {
  /// مرة واحدة
  once('once', 'مرة واحدة', 'دفعة واحدة فقط'),

  /// يومي
  daily('daily', 'يومي', 'كل يوم'),

  /// أسبوعي
  weekly('weekly', 'أسبوعي', 'كل أسبوع'),

  /// نصف شهري
  biweekly('biweekly', 'نصف شهري', 'كل أسبوعين'),

  /// شهري
  monthly('monthly', 'شهري', 'كل شهر'),

  /// ربع سنوي
  quarterly('quarterly', 'ربع سنوي', 'كل 3 أشهر'),

  /// نصف سنوي
  semiAnnual('semi_annual', 'نصف سنوي', 'كل 6 أشهر'),

  /// سنوي
  annual('annual', 'سنوي', 'كل سنة');

  const PaymentFrequency(this.code, this.displayName, this.description);

  final String code;
  final String displayName;
  final String description;

  static PaymentFrequency fromCode(String code) {
    return PaymentFrequency.values.firstWhere(
      (frequency) => frequency.code == code,
      orElse: () => PaymentFrequency.once,
    );
  }
}

/// حالة جدولة الدفعة
enum ScheduleStatus {
  /// نشط
  active('active', 'نشط', 'الجدولة نشطة'),

  /// معلق
  paused('paused', 'معلق', 'الجدولة معلقة مؤقتاً'),

  /// مكتمل
  completed('completed', 'مكتمل', 'تم إكمال جميع الدفعات'),

  /// ملغي
  cancelled('cancelled', 'ملغي', 'تم إلغاء الجدولة'),

  /// منتهي الصلاحية
  expired('expired', 'منتهي الصلاحية', 'انتهت صلاحية الجدولة');

  const ScheduleStatus(this.code, this.displayName, this.description);

  final String code;
  final String displayName;
  final String description;

  static ScheduleStatus fromCode(String code) {
    return ScheduleStatus.values.firstWhere(
      (status) => status.code == code,
      orElse: () => ScheduleStatus.active,
    );
  }
}

/// نوع التذكير
enum ReminderType {
  /// تنبيه داخل التطبيق
  inApp('in_app', 'تنبيه داخلي', 'تنبيه داخل التطبيق'),

  /// إشعار النظام
  notification('notification', 'إشعار النظام', 'إشعار نظام التشغيل'),

  /// بريد إلكتروني
  email('email', 'بريد إلكتروني', 'تذكير عبر البريد الإلكتروني'),

  /// رسالة نصية
  sms('sms', 'رسالة نصية', 'تذكير عبر الرسائل النصية');

  const ReminderType(this.code, this.displayName, this.description);

  final String code;
  final String displayName;
  final String description;

  static ReminderType fromCode(String code) {
    return ReminderType.values.firstWhere(
      (type) => type.code == code,
      orElse: () => ReminderType.inApp,
    );
  }
}

/// نموذج تذكير الدفعة
class PaymentReminder {
  final int? id;
  final int scheduleId;
  final ReminderType type;
  final int daysBefore;
  final String title;
  final String message;
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;

  PaymentReminder({
    this.id,
    required this.scheduleId,
    required this.type,
    required this.daysBefore,
    required this.title,
    required this.message,
    this.isActive = true,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) : createdAt = createdAt ?? DateTime.now(),
       updatedAt = updatedAt ?? DateTime.now();

  /// تحويل من Map
  factory PaymentReminder.fromMap(Map<String, dynamic> map) {
    return PaymentReminder(
      id: map['id'] as int?,
      scheduleId: map['schedule_id'] as int,
      type: ReminderType.fromCode(map['type'] as String),
      daysBefore: map['days_before'] as int,
      title: map['title'] as String,
      message: map['message'] as String,
      isActive: (map['is_active'] as int) == 1,
      createdAt: DateTime.parse(map['created_at'] as String),
      updatedAt: DateTime.parse(map['updated_at'] as String),
    );
  }

  /// تحويل إلى Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'schedule_id': scheduleId,
      'type': type.code,
      'days_before': daysBefore,
      'title': title,
      'message': message,
      'is_active': isActive ? 1 : 0,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  /// نسخ مع تعديل
  PaymentReminder copyWith({
    int? id,
    int? scheduleId,
    ReminderType? type,
    int? daysBefore,
    String? title,
    String? message,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return PaymentReminder(
      id: id ?? this.id,
      scheduleId: scheduleId ?? this.scheduleId,
      type: type ?? this.type,
      daysBefore: daysBefore ?? this.daysBefore,
      title: title ?? this.title,
      message: message ?? this.message,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}

/// نموذج جدولة الدفعات
class PaymentSchedule {
  final int? id;
  final int invoiceId;
  final String scheduleName;
  final double totalAmount;
  final double paidAmount;
  final PaymentFrequency frequency;
  final DateTime startDate;
  final DateTime? endDate;
  final DateTime nextPaymentDate;
  final DateTime? lastPaymentDate;
  final double installmentAmount;
  final int totalInstallments;
  final int completedInstallments;
  final ScheduleStatus status;
  final bool autoPayment;
  final String? notes;
  final List<PaymentReminder> reminders;
  final String? userId;
  final DateTime createdAt;
  final DateTime updatedAt;

  PaymentSchedule({
    this.id,
    required this.invoiceId,
    required this.scheduleName,
    required this.totalAmount,
    this.paidAmount = 0.0,
    required this.frequency,
    required this.startDate,
    this.endDate,
    required this.nextPaymentDate,
    this.lastPaymentDate,
    required this.installmentAmount,
    required this.totalInstallments,
    this.completedInstallments = 0,
    this.status = ScheduleStatus.active,
    this.autoPayment = false,
    this.notes,
    this.reminders = const [],
    this.userId,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) : createdAt = createdAt ?? DateTime.now(),
       updatedAt = updatedAt ?? DateTime.now();

  /// تحويل من Map
  factory PaymentSchedule.fromMap(Map<String, dynamic> map) {
    return PaymentSchedule(
      id: map['id'] as int?,
      invoiceId: map['invoice_id'] as int,
      scheduleName: map['schedule_name'] as String,
      totalAmount: map['total_amount'] as double,
      paidAmount: map['paid_amount'] as double? ?? 0.0,
      frequency: PaymentFrequency.fromCode(map['frequency'] as String),
      startDate: DateTime.parse(map['start_date'] as String),
      endDate: map['end_date'] != null
          ? DateTime.parse(map['end_date'] as String)
          : null,
      nextPaymentDate: DateTime.parse(map['next_payment_date'] as String),
      lastPaymentDate: map['last_payment_date'] != null
          ? DateTime.parse(map['last_payment_date'] as String)
          : null,
      installmentAmount: map['installment_amount'] as double,
      totalInstallments: map['total_installments'] as int,
      completedInstallments: map['completed_installments'] as int? ?? 0,
      status: ScheduleStatus.fromCode(map['status'] as String),
      autoPayment: (map['auto_payment'] as int?) == 1,
      notes: map['notes'] as String?,
      userId: map['user_id'] as String?,
      createdAt: DateTime.parse(map['created_at'] as String),
      updatedAt: DateTime.parse(map['updated_at'] as String),
    );
  }

  /// تحويل إلى Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'invoice_id': invoiceId,
      'schedule_name': scheduleName,
      'total_amount': totalAmount,
      'paid_amount': paidAmount,
      'frequency': frequency.code,
      'start_date': startDate.toIso8601String(),
      'end_date': endDate?.toIso8601String(),
      'next_payment_date': nextPaymentDate.toIso8601String(),
      'last_payment_date': lastPaymentDate?.toIso8601String(),
      'installment_amount': installmentAmount,
      'total_installments': totalInstallments,
      'completed_installments': completedInstallments,
      'status': status.code,
      'auto_payment': autoPayment ? 1 : 0,
      'notes': notes,
      'user_id': userId,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  /// نسخ مع تعديل
  PaymentSchedule copyWith({
    int? id,
    int? invoiceId,
    String? scheduleName,
    double? totalAmount,
    double? paidAmount,
    PaymentFrequency? frequency,
    DateTime? startDate,
    DateTime? endDate,
    DateTime? nextPaymentDate,
    DateTime? lastPaymentDate,
    double? installmentAmount,
    int? totalInstallments,
    int? completedInstallments,
    ScheduleStatus? status,
    bool? autoPayment,
    String? notes,
    List<PaymentReminder>? reminders,
    String? userId,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return PaymentSchedule(
      id: id ?? this.id,
      invoiceId: invoiceId ?? this.invoiceId,
      scheduleName: scheduleName ?? this.scheduleName,
      totalAmount: totalAmount ?? this.totalAmount,
      paidAmount: paidAmount ?? this.paidAmount,
      frequency: frequency ?? this.frequency,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      nextPaymentDate: nextPaymentDate ?? this.nextPaymentDate,
      lastPaymentDate: lastPaymentDate ?? this.lastPaymentDate,
      installmentAmount: installmentAmount ?? this.installmentAmount,
      totalInstallments: totalInstallments ?? this.totalInstallments,
      completedInstallments:
          completedInstallments ?? this.completedInstallments,
      status: status ?? this.status,
      autoPayment: autoPayment ?? this.autoPayment,
      notes: notes ?? this.notes,
      reminders: reminders ?? this.reminders,
      userId: userId ?? this.userId,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// المبلغ المتبقي
  double get remainingAmount => totalAmount - paidAmount;

  /// نسبة الإكمال
  double get completionPercentage {
    if (totalAmount <= 0) return 0.0;
    return (paidAmount / totalAmount) * 100;
  }

  /// نسبة الأقساط المكتملة
  double get installmentCompletionPercentage {
    if (totalInstallments <= 0) return 0.0;
    return (completedInstallments / totalInstallments) * 100;
  }

  /// هل الجدولة مكتملة
  bool get isCompleted =>
      completedInstallments >= totalInstallments || remainingAmount <= 0.01;

  /// هل الجدولة نشطة
  bool get isActive => status == ScheduleStatus.active;

  /// هل الدفعة التالية مستحقة
  bool get isNextPaymentDue => DateTime.now().isAfter(nextPaymentDate);

  /// عدد الأيام حتى الدفعة التالية
  int get daysUntilNextPayment {
    final now = DateTime.now();
    if (now.isAfter(nextPaymentDate)) return 0;
    return nextPaymentDate.difference(now).inDays;
  }

  /// عدد الأيام منذ آخر دفعة
  int? get daysSinceLastPayment {
    if (lastPaymentDate == null) return null;
    return DateTime.now().difference(lastPaymentDate!).inDays;
  }

  /// الأقساط المتبقية
  int get remainingInstallments => totalInstallments - completedInstallments;

  /// تحديث تاريخ الدفعة التالية
  PaymentSchedule updateNextPaymentDate() {
    DateTime newNextPaymentDate;

    switch (frequency) {
      case PaymentFrequency.daily:
        newNextPaymentDate = nextPaymentDate.add(const Duration(days: 1));
        break;
      case PaymentFrequency.weekly:
        newNextPaymentDate = nextPaymentDate.add(const Duration(days: 7));
        break;
      case PaymentFrequency.biweekly:
        newNextPaymentDate = nextPaymentDate.add(const Duration(days: 14));
        break;
      case PaymentFrequency.monthly:
        newNextPaymentDate = DateTime(
          nextPaymentDate.year,
          nextPaymentDate.month + 1,
          nextPaymentDate.day,
        );
        break;
      case PaymentFrequency.quarterly:
        newNextPaymentDate = DateTime(
          nextPaymentDate.year,
          nextPaymentDate.month + 3,
          nextPaymentDate.day,
        );
        break;
      case PaymentFrequency.semiAnnual:
        newNextPaymentDate = DateTime(
          nextPaymentDate.year,
          nextPaymentDate.month + 6,
          nextPaymentDate.day,
        );
        break;
      case PaymentFrequency.annual:
        newNextPaymentDate = DateTime(
          nextPaymentDate.year + 1,
          nextPaymentDate.month,
          nextPaymentDate.day,
        );
        break;
      case PaymentFrequency.once:
        newNextPaymentDate = nextPaymentDate;
        break;
    }

    return copyWith(
      nextPaymentDate: newNextPaymentDate,
      updatedAt: DateTime.now(),
    );
  }

  /// تسجيل دفعة جديدة
  PaymentSchedule recordPayment(double amount) {
    final newPaidAmount = paidAmount + amount;
    final newCompletedInstallments = completedInstallments + 1;

    // تحديد الحالة الجديدة
    ScheduleStatus newStatus = status;
    if (newCompletedInstallments >= totalInstallments ||
        newPaidAmount >= totalAmount) {
      newStatus = ScheduleStatus.completed;
    }

    return copyWith(
      paidAmount: newPaidAmount,
      completedInstallments: newCompletedInstallments,
      lastPaymentDate: DateTime.now(),
      status: newStatus,
      updatedAt: DateTime.now(),
    );
  }

  /// التحقق من صحة البيانات
  bool get isValid {
    return scheduleName.isNotEmpty &&
        totalAmount > 0 &&
        installmentAmount > 0 &&
        totalInstallments > 0 &&
        startDate.isBefore(nextPaymentDate) &&
        (endDate == null || startDate.isBefore(endDate!));
  }

  /// الحصول على التذكيرات النشطة
  List<PaymentReminder> get activeReminders {
    return reminders.where((reminder) => reminder.isActive).toList();
  }

  /// الحصول على التذكيرات المستحقة
  List<PaymentReminder> getDueReminders() {
    final now = DateTime.now();

    return activeReminders.where((reminder) {
      final reminderDueDate = nextPaymentDate.subtract(
        Duration(days: reminder.daysBefore),
      );
      return now.isAfter(reminderDueDate) && now.isBefore(nextPaymentDate);
    }).toList();
  }

  @override
  String toString() {
    return 'PaymentSchedule(id: $id, scheduleName: $scheduleName, '
        'totalAmount: $totalAmount, paidAmount: $paidAmount, '
        'frequency: ${frequency.displayName}, status: ${status.displayName})';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is PaymentSchedule && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
