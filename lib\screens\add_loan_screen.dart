/// شاشة إضافة قرض جديد
/// واجهة لإضافة قرض أو سلفة جديدة للموظف
library;

import 'package:flutter/material.dart';
import '../constants/app_constants.dart';
import '../constants/revolutionary_design_colors.dart';
import '../models/hr_models.dart';
import '../services/loan_service.dart';
import '../services/employee_service.dart';
import '../services/logging_service.dart';
import '../widgets/loading_widget.dart';

class AddLoanScreen extends StatefulWidget {
  final Employee? selectedEmployee;

  const AddLoanScreen({super.key, this.selectedEmployee});

  @override
  State<AddLoanScreen> createState() => _AddLoanScreenState();
}

class _AddLoanScreenState extends State<AddLoanScreen> {
  final _formKey = GlobalKey<FormState>();
  final LoanService _loanService = LoanService();
  final EmployeeService _employeeService = EmployeeService();

  // Controllers
  final _amountController = TextEditingController();
  final _installmentsController = TextEditingController();
  final _interestRateController = TextEditingController();
  final _purposeController = TextEditingController();
  final _notesController = TextEditingController();

  // Form data
  Employee? _selectedEmployee;
  String _loanType = AppConstants.loanTypeAdvance;
  DateTime _loanDate = DateTime.now();
  DateTime? _firstInstallmentDate;
  List<Employee> _employees = [];
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _selectedEmployee = widget.selectedEmployee;
    _firstInstallmentDate = DateTime.now().add(const Duration(days: 30));
    _interestRateController.text = '0';
    _installmentsController.text = '12';
    _loadEmployees();
  }

  @override
  void dispose() {
    _amountController.dispose();
    _installmentsController.dispose();
    _interestRateController.dispose();
    _purposeController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إضافة قرض جديد'),
        backgroundColor: RevolutionaryColors.damascusSky,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.save),
            onPressed: _saveLoan,
            tooltip: 'حفظ القرض',
          ),
        ],
      ),
      body: _isLoading
          ? const LoadingWidget(message: 'جاري تحميل البيانات...')
          : Form(
              key: _formKey,
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildEmployeeSelectionCard(),
                    const SizedBox(height: 16),
                    _buildLoanDetailsCard(),
                    const SizedBox(height: 16),
                    _buildInstallmentDetailsCard(),
                    const SizedBox(height: 16),
                    _buildAdditionalInfoCard(),
                    const SizedBox(height: 24),
                    _buildActionButtons(),
                  ],
                ),
              ),
            ),
    );
  }

  Widget _buildEmployeeSelectionCard() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.person, color: RevolutionaryColors.damascusSky),
                const SizedBox(width: 8),
                const Text(
                  'اختيار الموظف',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            DropdownButtonFormField<Employee>(
              value: _selectedEmployee,
              decoration: const InputDecoration(
                labelText: 'الموظف *',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.person_search),
              ),
              items: _employees.map((employee) {
                return DropdownMenuItem(
                  value: employee,
                  child: Text(employee.displayName),
                );
              }).toList(),
              onChanged: (value) {
                setState(() {
                  _selectedEmployee = value;
                });
              },
              validator: (value) {
                if (value == null) {
                  return 'يجب اختيار موظف';
                }
                return null;
              },
            ),
            if (_selectedEmployee != null) ...[
              const SizedBox(height: 12),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: RevolutionaryColors.damascusSky.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.info,
                      color: RevolutionaryColors.damascusSky,
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        'رقم الموظف: ${_selectedEmployee!.employeeNumber} | الراتب: ${_selectedEmployee!.basicSalary.toStringAsFixed(0)} ل.س',
                        style: TextStyle(
                          fontSize: 14,
                          color: RevolutionaryColors.damascusSky,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildLoanDetailsCard() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.money, color: RevolutionaryColors.damascusSky),
                const SizedBox(width: 8),
                const Text(
                  'تفاصيل القرض',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            DropdownButtonFormField<String>(
              value: _loanType,
              decoration: const InputDecoration(
                labelText: 'نوع القرض',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.category),
              ),
              items: const [
                DropdownMenuItem(
                  value: AppConstants.loanTypeAdvance,
                  child: Text('سلفة'),
                ),
                DropdownMenuItem(
                  value: AppConstants.loanTypeLoan,
                  child: Text('قرض'),
                ),
                DropdownMenuItem(
                  value: AppConstants.loanTypeEmergency,
                  child: Text('قرض طوارئ'),
                ),
              ],
              onChanged: (value) {
                setState(() {
                  _loanType = value!;
                });
              },
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _amountController,
              decoration: const InputDecoration(
                labelText: 'مبلغ القرض *',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.attach_money),
                suffixText: 'ل.س',
              ),
              keyboardType: TextInputType.number,
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'مبلغ القرض مطلوب';
                }
                final amount = double.tryParse(value);
                if (amount == null || amount <= 0) {
                  return 'يجب أن يكون المبلغ أكبر من الصفر';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _interestRateController,
              decoration: const InputDecoration(
                labelText: 'معدل الفائدة',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.percent),
                suffixText: '%',
              ),
              keyboardType: TextInputType.number,
              validator: (value) {
                if (value != null && value.isNotEmpty) {
                  final rate = double.tryParse(value);
                  if (rate == null || rate < 0) {
                    return 'معدل الفائدة يجب أن يكون صفر أو أكبر';
                  }
                }
                return null;
              },
            ),
            const SizedBox(height: 16),
            InkWell(
              onTap: () => _selectDate(context, true),
              child: InputDecorator(
                decoration: const InputDecoration(
                  labelText: 'تاريخ القرض',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.calendar_today),
                ),
                child: Text(
                  '${_loanDate.day}/${_loanDate.month}/${_loanDate.year}',
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInstallmentDetailsCard() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.schedule, color: RevolutionaryColors.damascusSky),
                const SizedBox(width: 8),
                const Text(
                  'تفاصيل الأقساط',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _installmentsController,
              decoration: const InputDecoration(
                labelText: 'عدد الأقساط *',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.format_list_numbered),
                suffixText: 'قسط',
              ),
              keyboardType: TextInputType.number,
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'عدد الأقساط مطلوب';
                }
                final installments = int.tryParse(value);
                if (installments == null || installments <= 0) {
                  return 'يجب أن يكون عدد الأقساط أكبر من الصفر';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),
            InkWell(
              onTap: () => _selectDate(context, false),
              child: InputDecorator(
                decoration: const InputDecoration(
                  labelText: 'تاريخ أول قسط',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.event),
                ),
                child: Text(
                  _firstInstallmentDate != null
                      ? '${_firstInstallmentDate!.day}/${_firstInstallmentDate!.month}/${_firstInstallmentDate!.year}'
                      : 'اختر تاريخ أول قسط',
                ),
              ),
            ),
            const SizedBox(height: 16),
            if (_amountController.text.isNotEmpty &&
                _installmentsController.text.isNotEmpty)
              _buildInstallmentPreview(),
          ],
        ),
      ),
    );
  }

  Widget _buildInstallmentPreview() {
    final amount = double.tryParse(_amountController.text) ?? 0;
    final installments = int.tryParse(_installmentsController.text) ?? 1;
    final interestRate = double.tryParse(_interestRateController.text) ?? 0;

    final totalAmount = amount + (amount * interestRate / 100);
    final monthlyInstallment = totalAmount / installments;

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: RevolutionaryColors.successGlow.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: RevolutionaryColors.successGlow.withValues(alpha: 0.3),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'معاينة الأقساط',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: RevolutionaryColors.successGlow,
            ),
          ),
          const SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text('المبلغ الإجمالي:'),
              Text('${totalAmount.toStringAsFixed(0)} ل.س'),
            ],
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text('القسط الشهري:'),
              Text(
                '${monthlyInstallment.toStringAsFixed(0)} ل.س',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: RevolutionaryColors.successGlow,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildAdditionalInfoCard() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.info, color: RevolutionaryColors.damascusSky),
                const SizedBox(width: 8),
                const Text(
                  'معلومات إضافية',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _purposeController,
              decoration: const InputDecoration(
                labelText: 'الغرض من القرض',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.description),
              ),
              maxLines: 2,
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _notesController,
              decoration: const InputDecoration(
                labelText: 'ملاحظات',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.note),
              ),
              maxLines: 3,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButtons() {
    return Row(
      children: [
        Expanded(
          child: OutlinedButton(
            onPressed: () => Navigator.pop(context),
            style: OutlinedButton.styleFrom(
              padding: const EdgeInsets.all(16),
              side: BorderSide(color: RevolutionaryColors.errorCoral),
            ),
            child: Text(
              'إلغاء',
              style: TextStyle(color: RevolutionaryColors.errorCoral),
            ),
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: ElevatedButton(
            onPressed: _saveLoan,
            style: ElevatedButton.styleFrom(
              backgroundColor: RevolutionaryColors.successGlow,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.all(16),
            ),
            child: const Text('حفظ القرض'),
          ),
        ),
      ],
    );
  }

  Future<void> _loadEmployees() async {
    try {
      setState(() {
        _isLoading = true;
      });

      final employees = await _employeeService.getAllEmployees(
        activeOnly: true,
      );
      setState(() {
        _employees = employees;
      });
    } catch (e) {
      LoggingService.error(
        'خطأ في تحميل قائمة الموظفين',
        category: 'AddLoanScreen',
        data: {'error': e.toString()},
      );
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _selectDate(BuildContext context, bool isLoanDate) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: isLoanDate
          ? _loanDate
          : (_firstInstallmentDate ?? DateTime.now()),
      firstDate: DateTime.now().subtract(const Duration(days: 365)),
      lastDate: DateTime.now().add(const Duration(days: 365 * 2)),
    );

    if (picked != null) {
      setState(() {
        if (isLoanDate) {
          _loanDate = picked;
        } else {
          _firstInstallmentDate = picked;
        }
      });
    }
  }

  Future<void> _saveLoan() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (_selectedEmployee == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يجب اختيار موظف'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    if (_firstInstallmentDate == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يجب تحديد تاريخ أول قسط'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      await _loanService.createLoan(
        employeeId: _selectedEmployee!.id!,
        amount: double.parse(_amountController.text.trim()),
        interestRate: double.tryParse(_interestRateController.text.trim()) ?? 0,
        installments: int.parse(_installmentsController.text.trim()),
        loanDate: _loanDate,
        firstInstallmentDate: _firstInstallmentDate!,
        purpose: _purposeController.text.trim().isNotEmpty
            ? _purposeController.text.trim()
            : null,
        notes: _notesController.text.trim().isNotEmpty
            ? _notesController.text.trim()
            : null,
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'تم إضافة القرض للموظف "${_selectedEmployee!.displayName}" بنجاح',
            ),
            backgroundColor: RevolutionaryColors.successGlow,
          ),
        );
        Navigator.pop(context, true);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في إضافة القرض: ${e.toString()}'),
            backgroundColor: RevolutionaryColors.errorCoral,
          ),
        );
      }

      LoggingService.error(
        'خطأ في إضافة القرض',
        category: 'AddLoanScreen',
        data: {
          'employeeId': _selectedEmployee?.id,
          'amount': _amountController.text,
          'error': e.toString(),
        },
      );
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }
}
