/// شاشة تقارير الموارد البشرية المتقدمة
/// واجهة لعرض وتصدير التقارير الشاملة
library;

import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import '../constants/revolutionary_design_colors.dart';
import '../services/hr_reports_service.dart';
import '../services/logging_service.dart';
import '../widgets/loading_widget.dart';

class HRReportsScreen extends StatefulWidget {
  const HRReportsScreen({super.key});

  @override
  State<HRReportsScreen> createState() => _HRReportsScreenState();
}

class _HRReportsScreenState extends State<HRReportsScreen>
    with TickerProviderStateMixin {
  final HRReportsService _reportsService = HRReportsService();

  late TabController _tabController;

  EmployeeReport? _employeeReport;
  AttendanceReport? _attendanceReport;
  PayrollReport? _payrollReport;
  LoansReport? _loansReport;

  bool _isLoading = true;
  String? _error;

  DateTime _startDate = DateTime.now().subtract(const Duration(days: 30));
  DateTime _endDate = DateTime.now();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _loadReports();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadReports() async {
    try {
      setState(() {
        _isLoading = true;
        _error = null;
      });

      // تحميل جميع التقارير بشكل متوازي
      final results = await Future.wait([
        _reportsService.generateEmployeeReport(
          startDate: _startDate,
          endDate: _endDate,
        ),
        _reportsService.generateAttendanceReport(
          startDate: _startDate,
          endDate: _endDate,
        ),
        _reportsService.generatePayrollReport(
          startDate: _startDate,
          endDate: _endDate,
        ),
        _reportsService.generateLoansReport(
          startDate: _startDate,
          endDate: _endDate,
        ),
      ]);

      setState(() {
        _employeeReport = results[0] as EmployeeReport;
        _attendanceReport = results[1] as AttendanceReport;
        _payrollReport = results[2] as PayrollReport;
        _loansReport = results[3] as LoansReport;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });

      LoggingService.error(
        'خطأ في تحميل التقارير',
        category: 'HRReportsScreen',
        data: {'error': e.toString()},
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('تقارير الموارد البشرية'),
        backgroundColor: RevolutionaryColors.damascusSky,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.date_range),
            onPressed: _showDateRangePicker,
          ),
          IconButton(icon: const Icon(Icons.refresh), onPressed: _loadReports),
        ],
        bottom: TabBar(
          controller: _tabController,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          indicatorColor: Colors.white,
          tabs: const [
            Tab(icon: Icon(Icons.people), text: 'الموظفين'),
            Tab(icon: Icon(Icons.access_time), text: 'الحضور'),
            Tab(icon: Icon(Icons.account_balance_wallet), text: 'الرواتب'),
            Tab(icon: Icon(Icons.money_off), text: 'القروض'),
          ],
        ),
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const LoadingWidget(message: 'جاري تحميل التقارير...');
    }

    if (_error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 64, color: Colors.red[400]),
            const SizedBox(height: 16),
            Text(
              'حدث خطأ',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.red[600],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              _error!,
              style: TextStyle(fontSize: 14, color: Colors.grey[600]),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadReports,
              child: const Text('إعادة المحاولة'),
            ),
          ],
        ),
      );
    }

    return TabBarView(
      controller: _tabController,
      children: [
        _buildEmployeeReportTab(),
        _buildAttendanceReportTab(),
        _buildPayrollReportTab(),
        _buildLoansReportTab(),
      ],
    );
  }

  Widget _buildEmployeeReportTab() {
    if (_employeeReport == null) return const SizedBox();

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildReportHeader('تقرير الموظفين', Icons.people),
          const SizedBox(height: 16),
          _buildEmployeeStatsCards(),
          const SizedBox(height: 24),
          _buildEmployeePieChart(),
          const SizedBox(height: 24),
          _buildDepartmentChart(),
          const SizedBox(height: 24),
          _buildExportButton('employees'),
        ],
      ),
    );
  }

  Widget _buildAttendanceReportTab() {
    if (_attendanceReport == null) return const SizedBox();

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildReportHeader('تقرير الحضور والانصراف', Icons.access_time),
          const SizedBox(height: 16),
          _buildAttendanceStatsCards(),
          const SizedBox(height: 24),
          _buildAttendanceChart(),
          const SizedBox(height: 24),
          _buildMonthlyAttendanceChart(),
          const SizedBox(height: 24),
          _buildExportButton('attendance'),
        ],
      ),
    );
  }

  Widget _buildPayrollReportTab() {
    if (_payrollReport == null) return const SizedBox();

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildReportHeader('تقرير الرواتب', Icons.account_balance_wallet),
          const SizedBox(height: 16),
          _buildPayrollStatsCards(),
          const SizedBox(height: 24),
          _buildPayrollBreakdownChart(),
          const SizedBox(height: 24),
          _buildMonthlySalariesChart(),
          const SizedBox(height: 24),
          _buildExportButton('payroll'),
        ],
      ),
    );
  }

  Widget _buildLoansReportTab() {
    if (_loansReport == null) return const SizedBox();

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildReportHeader('تقرير القروض والسلف', Icons.money_off),
          const SizedBox(height: 16),
          _buildLoansStatsCards(),
          const SizedBox(height: 24),
          _buildLoansStatusChart(),
          const SizedBox(height: 24),
          _buildMonthlyLoansChart(),
          const SizedBox(height: 24),
          _buildExportButton('loans'),
        ],
      ),
    );
  }

  Widget _buildReportHeader(String title, IconData icon) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            RevolutionaryColors.damascusSky,
            RevolutionaryColors.damascusSky.withValues(alpha: 0.8),
          ],
        ),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        children: [
          Icon(icon, color: Colors.white, size: 32),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'من ${_startDate.day}/${_startDate.month}/${_startDate.year} إلى ${_endDate.day}/${_endDate.month}/${_endDate.year}',
                  style: const TextStyle(fontSize: 14, color: Colors.white70),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmployeeStatsCards() {
    return Row(
      children: [
        Expanded(
          child: _buildStatCard(
            'إجمالي الموظفين',
            _employeeReport!.totalEmployees.toString(),
            Icons.people,
            RevolutionaryColors.infoTurquoise,
          ),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: _buildStatCard(
            'الموظفين النشطين',
            _employeeReport!.activeEmployees.toString(),
            Icons.check_circle,
            RevolutionaryColors.successGlow,
          ),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: _buildStatCard(
            'متوسط الراتب',
            '${_employeeReport!.averageSalary.toStringAsFixed(0)} ل.س',
            Icons.attach_money,
            RevolutionaryColors.syrianGold,
          ),
        ),
      ],
    );
  }

  Widget _buildStatCard(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Icon(icon, color: color, size: 32),
            const SizedBox(height: 8),
            Text(
              title,
              style: const TextStyle(fontSize: 12, color: Colors.grey),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 4),
            Text(
              value,
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: color,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmployeePieChart() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'توزيع الموظفين حسب الحالة',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            SizedBox(
              height: 200,
              child: PieChart(
                PieChartData(
                  sections: [
                    PieChartSectionData(
                      value: _employeeReport!.activeEmployees.toDouble(),
                      title: 'نشط',
                      color: RevolutionaryColors.successGlow,
                      radius: 60,
                    ),
                    PieChartSectionData(
                      value: _employeeReport!.inactiveEmployees.toDouble(),
                      title: 'غير نشط',
                      color: RevolutionaryColors.warningAmber,
                      radius: 60,
                    ),
                    PieChartSectionData(
                      value: _employeeReport!.terminatedEmployees.toDouble(),
                      title: 'منتهي',
                      color: RevolutionaryColors.errorCoral,
                      radius: 60,
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDepartmentChart() {
    if (_employeeReport!.departmentCounts.isEmpty) {
      return const SizedBox();
    }

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'توزيع الموظفين حسب الأقسام',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            SizedBox(
              height: 200,
              child: BarChart(
                BarChartData(
                  alignment: BarChartAlignment.spaceAround,
                  barGroups: _employeeReport!.departmentCounts.entries
                      .map(
                        (entry) => BarChartGroupData(
                          x: _employeeReport!.departmentCounts.keys
                              .toList()
                              .indexOf(entry.key),
                          barRods: [
                            BarChartRodData(
                              toY: entry.value.toDouble(),
                              color: RevolutionaryColors.infoTurquoise,
                              width: 20,
                            ),
                          ],
                        ),
                      )
                      .toList(),
                  titlesData: FlTitlesData(
                    bottomTitles: AxisTitles(
                      sideTitles: SideTitles(
                        showTitles: true,
                        getTitlesWidget: (value, meta) {
                          final departments = _employeeReport!
                              .departmentCounts
                              .keys
                              .toList();
                          if (value.toInt() < departments.length) {
                            return Text(
                              departments[value.toInt()],
                              style: const TextStyle(fontSize: 10),
                            );
                          }
                          return const Text('');
                        },
                      ),
                    ),
                    leftTitles: const AxisTitles(
                      sideTitles: SideTitles(showTitles: true),
                    ),
                    topTitles: const AxisTitles(
                      sideTitles: SideTitles(showTitles: false),
                    ),
                    rightTitles: const AxisTitles(
                      sideTitles: SideTitles(showTitles: false),
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAttendanceStatsCards() {
    return Column(
      children: [
        Row(
          children: [
            Expanded(
              child: _buildStatCard(
                'أيام العمل',
                _attendanceReport!.totalWorkingDays.toString(),
                Icons.calendar_today,
                RevolutionaryColors.infoTurquoise,
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: _buildStatCard(
                'أيام الحضور',
                _attendanceReport!.totalPresentDays.toString(),
                Icons.check_circle,
                RevolutionaryColors.successGlow,
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: _buildStatCard(
                'أيام الغياب',
                _attendanceReport!.totalAbsentDays.toString(),
                Icons.cancel,
                RevolutionaryColors.errorCoral,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            Expanded(
              child: _buildStatCard(
                'معدل الحضور',
                '${_attendanceReport!.attendanceRate.toStringAsFixed(1)}%',
                Icons.trending_up,
                RevolutionaryColors.successGlow,
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: _buildStatCard(
                'معدل الانضباط',
                '${_attendanceReport!.punctualityRate.toStringAsFixed(1)}%',
                Icons.schedule,
                RevolutionaryColors.damascusSky,
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: _buildStatCard(
                'ساعات إضافية',
                '${_attendanceReport!.totalOvertimeHours.toStringAsFixed(0)} ساعة',
                Icons.access_time,
                RevolutionaryColors.warningAmber,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildAttendanceChart() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'إحصائيات الحضور',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            SizedBox(
              height: 200,
              child: PieChart(
                PieChartData(
                  sections: [
                    PieChartSectionData(
                      value: _attendanceReport!.totalPresentDays.toDouble(),
                      title: 'حضور',
                      color: RevolutionaryColors.successGlow,
                      radius: 60,
                    ),
                    PieChartSectionData(
                      value: _attendanceReport!.totalAbsentDays.toDouble(),
                      title: 'غياب',
                      color: RevolutionaryColors.errorCoral,
                      radius: 60,
                    ),
                    PieChartSectionData(
                      value: _attendanceReport!.totalLateDays.toDouble(),
                      title: 'تأخير',
                      color: RevolutionaryColors.warningAmber,
                      radius: 60,
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMonthlyAttendanceChart() {
    if (_attendanceReport!.monthlyAttendance.isEmpty) {
      return const SizedBox();
    }

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'الحضور الشهري',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            SizedBox(
              height: 200,
              child: LineChart(
                LineChartData(
                  gridData: const FlGridData(show: true),
                  titlesData: FlTitlesData(
                    bottomTitles: AxisTitles(
                      sideTitles: SideTitles(
                        showTitles: true,
                        getTitlesWidget: (value, meta) {
                          final months = _attendanceReport!
                              .monthlyAttendance
                              .keys
                              .toList();
                          if (value.toInt() < months.length) {
                            return Text(
                              months[value.toInt()].split('-')[1],
                              style: const TextStyle(fontSize: 10),
                            );
                          }
                          return const Text('');
                        },
                      ),
                    ),
                    leftTitles: const AxisTitles(
                      sideTitles: SideTitles(showTitles: true),
                    ),
                    topTitles: const AxisTitles(
                      sideTitles: SideTitles(showTitles: false),
                    ),
                    rightTitles: const AxisTitles(
                      sideTitles: SideTitles(showTitles: false),
                    ),
                  ),
                  borderData: FlBorderData(show: true),
                  lineBarsData: [
                    LineChartBarData(
                      spots: _attendanceReport!.monthlyAttendance.entries
                          .toList()
                          .asMap()
                          .entries
                          .map(
                            (entry) => FlSpot(
                              entry.key.toDouble(),
                              entry.value.value.toDouble(),
                            ),
                          )
                          .toList(),
                      isCurved: true,
                      color: RevolutionaryColors.successGlow,
                      barWidth: 3,
                      dotData: const FlDotData(show: true),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPayrollStatsCards() {
    return Column(
      children: [
        Row(
          children: [
            Expanded(
              child: _buildStatCard(
                'إجمالي الراتب الإجمالي',
                '${_payrollReport!.totalGrossSalary.toStringAsFixed(0)} ل.س',
                Icons.account_balance_wallet,
                RevolutionaryColors.syrianGold,
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: _buildStatCard(
                'إجمالي الراتب الصافي',
                '${_payrollReport!.totalNetSalary.toStringAsFixed(0)} ل.س',
                Icons.money,
                RevolutionaryColors.successGlow,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            Expanded(
              child: _buildStatCard(
                'إجمالي الضرائب',
                '${_payrollReport!.totalTaxes.toStringAsFixed(0)} ل.س',
                Icons.receipt,
                RevolutionaryColors.errorCoral,
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: _buildStatCard(
                'إجمالي التأمين',
                '${_payrollReport!.totalInsurance.toStringAsFixed(0)} ل.س',
                Icons.security,
                RevolutionaryColors.infoTurquoise,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildPayrollBreakdownChart() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'تفصيل الرواتب',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            SizedBox(
              height: 200,
              child: PieChart(
                PieChartData(
                  sections: [
                    PieChartSectionData(
                      value: _payrollReport!.totalNetSalary,
                      title: 'صافي',
                      color: RevolutionaryColors.successGlow,
                      radius: 60,
                    ),
                    PieChartSectionData(
                      value: _payrollReport!.totalTaxes,
                      title: 'ضرائب',
                      color: RevolutionaryColors.errorCoral,
                      radius: 60,
                    ),
                    PieChartSectionData(
                      value: _payrollReport!.totalInsurance,
                      title: 'تأمين',
                      color: RevolutionaryColors.infoTurquoise,
                      radius: 60,
                    ),
                    PieChartSectionData(
                      value: _payrollReport!.totalDeductions,
                      title: 'خصومات',
                      color: RevolutionaryColors.warningAmber,
                      radius: 60,
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMonthlySalariesChart() {
    if (_payrollReport!.monthlySalaries.isEmpty) {
      return const SizedBox();
    }

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'الرواتب الشهرية',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            SizedBox(
              height: 200,
              child: BarChart(
                BarChartData(
                  alignment: BarChartAlignment.spaceAround,
                  barGroups: _payrollReport!.monthlySalaries.entries
                      .toList()
                      .asMap()
                      .entries
                      .map(
                        (entry) => BarChartGroupData(
                          x: entry.key,
                          barRods: [
                            BarChartRodData(
                              toY: entry.value.value,
                              color: RevolutionaryColors.syrianGold,
                              width: 20,
                            ),
                          ],
                        ),
                      )
                      .toList(),
                  titlesData: FlTitlesData(
                    bottomTitles: AxisTitles(
                      sideTitles: SideTitles(
                        showTitles: true,
                        getTitlesWidget: (value, meta) {
                          final months = _payrollReport!.monthlySalaries.keys
                              .toList();
                          if (value.toInt() < months.length) {
                            return Text(
                              months[value.toInt()].split('-')[1],
                              style: const TextStyle(fontSize: 10),
                            );
                          }
                          return const Text('');
                        },
                      ),
                    ),
                    leftTitles: const AxisTitles(
                      sideTitles: SideTitles(showTitles: true),
                    ),
                    topTitles: const AxisTitles(
                      sideTitles: SideTitles(showTitles: false),
                    ),
                    rightTitles: const AxisTitles(
                      sideTitles: SideTitles(showTitles: false),
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLoansStatsCards() {
    return Column(
      children: [
        Row(
          children: [
            Expanded(
              child: _buildStatCard(
                'إجمالي القروض',
                _loansReport!.totalLoans.toString(),
                Icons.money_off,
                RevolutionaryColors.infoTurquoise,
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: _buildStatCard(
                'القروض النشطة',
                _loansReport!.activeLoans.toString(),
                Icons.trending_up,
                RevolutionaryColors.successGlow,
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: _buildStatCard(
                'القروض المكتملة',
                _loansReport!.completedLoans.toString(),
                Icons.check_circle,
                RevolutionaryColors.damascusSky,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            Expanded(
              child: _buildStatCard(
                'إجمالي المبلغ',
                '${_loansReport!.totalLoanAmount.toStringAsFixed(0)} ل.س',
                Icons.account_balance,
                RevolutionaryColors.syrianGold,
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: _buildStatCard(
                'المبلغ المدفوع',
                '${_loansReport!.totalPaidAmount.toStringAsFixed(0)} ل.س',
                Icons.payment,
                RevolutionaryColors.successGlow,
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: _buildStatCard(
                'المبلغ المتبقي',
                '${_loansReport!.totalRemainingAmount.toStringAsFixed(0)} ل.س',
                Icons.pending,
                RevolutionaryColors.warningAmber,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildLoansStatusChart() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'حالة القروض',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            SizedBox(
              height: 200,
              child: PieChart(
                PieChartData(
                  sections: [
                    PieChartSectionData(
                      value: _loansReport!.activeLoans.toDouble(),
                      title: 'نشط',
                      color: RevolutionaryColors.successGlow,
                      radius: 60,
                    ),
                    PieChartSectionData(
                      value: _loansReport!.completedLoans.toDouble(),
                      title: 'مكتمل',
                      color: RevolutionaryColors.damascusSky,
                      radius: 60,
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMonthlyLoansChart() {
    if (_loansReport!.monthlyLoans.isEmpty) {
      return const SizedBox();
    }

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'القروض الشهرية',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            SizedBox(
              height: 200,
              child: LineChart(
                LineChartData(
                  gridData: const FlGridData(show: true),
                  titlesData: FlTitlesData(
                    bottomTitles: AxisTitles(
                      sideTitles: SideTitles(
                        showTitles: true,
                        getTitlesWidget: (value, meta) {
                          final months = _loansReport!.monthlyLoans.keys
                              .toList();
                          if (value.toInt() < months.length) {
                            return Text(
                              months[value.toInt()].split('-')[1],
                              style: const TextStyle(fontSize: 10),
                            );
                          }
                          return const Text('');
                        },
                      ),
                    ),
                    leftTitles: const AxisTitles(
                      sideTitles: SideTitles(showTitles: true),
                    ),
                    topTitles: const AxisTitles(
                      sideTitles: SideTitles(showTitles: false),
                    ),
                    rightTitles: const AxisTitles(
                      sideTitles: SideTitles(showTitles: false),
                    ),
                  ),
                  borderData: FlBorderData(show: true),
                  lineBarsData: [
                    LineChartBarData(
                      spots: _loansReport!.monthlyLoans.entries
                          .toList()
                          .asMap()
                          .entries
                          .map(
                            (entry) => FlSpot(
                              entry.key.toDouble(),
                              entry.value.value.toDouble(),
                            ),
                          )
                          .toList(),
                      isCurved: true,
                      color: RevolutionaryColors.warningAmber,
                      barWidth: 3,
                      dotData: const FlDotData(show: true),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildExportButton(String reportType) {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton.icon(
        onPressed: () => _exportReport(reportType),
        icon: const Icon(Icons.download),
        label: const Text('تصدير التقرير'),
        style: ElevatedButton.styleFrom(
          backgroundColor: RevolutionaryColors.damascusSky,
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(vertical: 12),
        ),
      ),
    );
  }

  Future<void> _showDateRangePicker() async {
    final DateTimeRange? picked = await showDateRangePicker(
      context: context,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
      initialDateRange: DateTimeRange(start: _startDate, end: _endDate),
    );

    if (picked != null) {
      setState(() {
        _startDate = picked.start;
        _endDate = picked.end;
      });
      _loadReports();
    }
  }

  Future<void> _exportReport(String reportType) async {
    try {
      Map<String, dynamic> data;

      switch (reportType) {
        case 'employees':
          data = {
            'totalEmployees': _employeeReport!.totalEmployees,
            'activeEmployees': _employeeReport!.activeEmployees,
            'inactiveEmployees': _employeeReport!.inactiveEmployees,
            'terminatedEmployees': _employeeReport!.terminatedEmployees,
          };
          break;
        case 'attendance':
          data = {
            'totalWorkingDays': _attendanceReport!.totalWorkingDays,
            'totalPresentDays': _attendanceReport!.totalPresentDays,
            'totalAbsentDays': _attendanceReport!.totalAbsentDays,
            'totalLateDays': _attendanceReport!.totalLateDays,
          };
          break;
        case 'payroll':
          data = {
            'totalGrossSalary': _payrollReport!.totalGrossSalary,
            'totalNetSalary': _payrollReport!.totalNetSalary,
            'totalTaxes': _payrollReport!.totalTaxes,
            'totalInsurance': _payrollReport!.totalInsurance,
          };
          break;
        case 'loans':
          data = {
            'totalLoans': _loansReport!.totalLoans,
            'activeLoans': _loansReport!.activeLoans,
            'completedLoans': _loansReport!.completedLoans,
            'totalLoanAmount': _loansReport!.totalLoanAmount,
          };
          break;
        default:
          return;
      }

      await _reportsService.exportReportToCSV(reportType, data);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('تم تصدير تقرير $reportType بنجاح'),
            backgroundColor: RevolutionaryColors.successGlow,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تصدير التقرير: $e'),
            backgroundColor: RevolutionaryColors.errorCoral,
          ),
        );
      }
    }
  }
}
