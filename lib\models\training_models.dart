/// نماذج نظام التدريب وتطوير الموظفين
/// تحتوي على جميع النماذج المتعلقة بالتدريب والتطوير المهني
library;



/// نموذج برنامج التدريب
class TrainingProgram {
  final int? id;
  final String name;
  final String description;
  final String category;
  final String level; // beginner, intermediate, advanced
  final int durationHours;
  final double cost;
  final String? provider;
  final String? location;
  final String deliveryMethod; // online, offline, hybrid
  final int maxParticipants;
  final String? prerequisites;
  final String? objectives;
  final String? materials;
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;

  const TrainingProgram({
    this.id,
    required this.name,
    required this.description,
    required this.category,
    required this.level,
    required this.durationHours,
    this.cost = 0,
    this.provider,
    this.location,
    this.deliveryMethod = 'offline',
    this.maxParticipants = 20,
    this.prerequisites,
    this.objectives,
    this.materials,
    this.isActive = true,
    required this.createdAt,
    required this.updatedAt,
  });

  factory TrainingProgram.fromMap(Map<String, dynamic> map) {
    return TrainingProgram(
      id: map['id'] as int?,
      name: map['name'] as String,
      description: map['description'] as String,
      category: map['category'] as String,
      level: map['level'] as String,
      durationHours: map['duration_hours'] as int,
      cost: (map['cost'] as num?)?.toDouble() ?? 0,
      provider: map['provider'] as String?,
      location: map['location'] as String?,
      deliveryMethod: map['delivery_method'] as String? ?? 'offline',
      maxParticipants: map['max_participants'] as int? ?? 20,
      prerequisites: map['prerequisites'] as String?,
      objectives: map['objectives'] as String?,
      materials: map['materials'] as String?,
      isActive: (map['is_active'] as int?) == 1,
      createdAt: DateTime.parse(map['created_at'] as String),
      updatedAt: DateTime.parse(map['updated_at'] as String),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      if (id != null) 'id': id,
      'name': name,
      'description': description,
      'category': category,
      'level': level,
      'duration_hours': durationHours,
      'cost': cost,
      'provider': provider,
      'location': location,
      'delivery_method': deliveryMethod,
      'max_participants': maxParticipants,
      'prerequisites': prerequisites,
      'objectives': objectives,
      'materials': materials,
      'is_active': isActive ? 1 : 0,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  TrainingProgram copyWith({
    int? id,
    String? name,
    String? description,
    String? category,
    String? level,
    int? durationHours,
    double? cost,
    String? provider,
    String? location,
    String? deliveryMethod,
    int? maxParticipants,
    String? prerequisites,
    String? objectives,
    String? materials,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return TrainingProgram(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      category: category ?? this.category,
      level: level ?? this.level,
      durationHours: durationHours ?? this.durationHours,
      cost: cost ?? this.cost,
      provider: provider ?? this.provider,
      location: location ?? this.location,
      deliveryMethod: deliveryMethod ?? this.deliveryMethod,
      maxParticipants: maxParticipants ?? this.maxParticipants,
      prerequisites: prerequisites ?? this.prerequisites,
      objectives: objectives ?? this.objectives,
      materials: materials ?? this.materials,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}

/// نموذج جلسة التدريب
class TrainingSession {
  final int? id;
  final int programId;
  final String title;
  final DateTime startDate;
  final DateTime endDate;
  final String? instructor;
  final String? location;
  final int maxParticipants;
  final String status; // scheduled, ongoing, completed, cancelled
  final String? notes;
  final DateTime createdAt;
  final DateTime updatedAt;

  const TrainingSession({
    this.id,
    required this.programId,
    required this.title,
    required this.startDate,
    required this.endDate,
    this.instructor,
    this.location,
    this.maxParticipants = 20,
    this.status = 'scheduled',
    this.notes,
    required this.createdAt,
    required this.updatedAt,
  });

  factory TrainingSession.fromMap(Map<String, dynamic> map) {
    return TrainingSession(
      id: map['id'] as int?,
      programId: map['program_id'] as int,
      title: map['title'] as String,
      startDate: DateTime.parse(map['start_date'] as String),
      endDate: DateTime.parse(map['end_date'] as String),
      instructor: map['instructor'] as String?,
      location: map['location'] as String?,
      maxParticipants: map['max_participants'] as int? ?? 20,
      status: map['status'] as String? ?? 'scheduled',
      notes: map['notes'] as String?,
      createdAt: DateTime.parse(map['created_at'] as String),
      updatedAt: DateTime.parse(map['updated_at'] as String),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      if (id != null) 'id': id,
      'program_id': programId,
      'title': title,
      'start_date': startDate.toIso8601String(),
      'end_date': endDate.toIso8601String(),
      'instructor': instructor,
      'location': location,
      'max_participants': maxParticipants,
      'status': status,
      'notes': notes,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  TrainingSession copyWith({
    int? id,
    int? programId,
    String? title,
    DateTime? startDate,
    DateTime? endDate,
    String? instructor,
    String? location,
    int? maxParticipants,
    String? status,
    String? notes,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return TrainingSession(
      id: id ?? this.id,
      programId: programId ?? this.programId,
      title: title ?? this.title,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      instructor: instructor ?? this.instructor,
      location: location ?? this.location,
      maxParticipants: maxParticipants ?? this.maxParticipants,
      status: status ?? this.status,
      notes: notes ?? this.notes,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  // خصائص مساعدة
  bool get isScheduled => status == 'scheduled';
  bool get isOngoing => status == 'ongoing';
  bool get isCompleted => status == 'completed';
  bool get isCancelled => status == 'cancelled';

  Duration get duration => endDate.difference(startDate);
  int get durationInHours => duration.inHours;
}

/// نموذج تسجيل الموظف في التدريب
class TrainingEnrollment {
  final int? id;
  final int employeeId;
  final int sessionId;
  final DateTime enrollmentDate;
  final String status; // enrolled, attended, completed, cancelled, no_show
  final double? score;
  final String? feedback;
  final String? certificate;
  final DateTime? completionDate;
  final DateTime createdAt;
  final DateTime updatedAt;

  const TrainingEnrollment({
    this.id,
    required this.employeeId,
    required this.sessionId,
    required this.enrollmentDate,
    this.status = 'enrolled',
    this.score,
    this.feedback,
    this.certificate,
    this.completionDate,
    required this.createdAt,
    required this.updatedAt,
  });

  factory TrainingEnrollment.fromMap(Map<String, dynamic> map) {
    return TrainingEnrollment(
      id: map['id'] as int?,
      employeeId: map['employee_id'] as int,
      sessionId: map['session_id'] as int,
      enrollmentDate: DateTime.parse(map['enrollment_date'] as String),
      status: map['status'] as String? ?? 'enrolled',
      score: (map['score'] as num?)?.toDouble(),
      feedback: map['feedback'] as String?,
      certificate: map['certificate'] as String?,
      completionDate: map['completion_date'] != null
          ? DateTime.parse(map['completion_date'] as String)
          : null,
      createdAt: DateTime.parse(map['created_at'] as String),
      updatedAt: DateTime.parse(map['updated_at'] as String),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      if (id != null) 'id': id,
      'employee_id': employeeId,
      'session_id': sessionId,
      'enrollment_date': enrollmentDate.toIso8601String().split('T')[0],
      'status': status,
      'score': score,
      'feedback': feedback,
      'certificate': certificate,
      'completion_date': completionDate?.toIso8601String().split('T')[0],
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  TrainingEnrollment copyWith({
    int? id,
    int? employeeId,
    int? sessionId,
    DateTime? enrollmentDate,
    String? status,
    double? score,
    String? feedback,
    String? certificate,
    DateTime? completionDate,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return TrainingEnrollment(
      id: id ?? this.id,
      employeeId: employeeId ?? this.employeeId,
      sessionId: sessionId ?? this.sessionId,
      enrollmentDate: enrollmentDate ?? this.enrollmentDate,
      status: status ?? this.status,
      score: score ?? this.score,
      feedback: feedback ?? this.feedback,
      certificate: certificate ?? this.certificate,
      completionDate: completionDate ?? this.completionDate,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  // خصائص مساعدة
  bool get isEnrolled => status == 'enrolled';
  bool get hasAttended => status == 'attended';
  bool get isCompleted => status == 'completed';
  bool get isCancelled => status == 'cancelled';
  bool get isNoShow => status == 'no_show';
  bool get hasCertificate => certificate != null && certificate!.isNotEmpty;
}

/// نموذج مهارة الموظف
class EmployeeSkill {
  final int? id;
  final int employeeId;
  final String skillName;
  final String category;
  final int level; // 1-5 (1=مبتدئ، 5=خبير)
  final String? description;
  final DateTime? acquiredDate;
  final DateTime? lastAssessedDate;
  final String? certificationPath;
  final bool isVerified;
  final DateTime createdAt;
  final DateTime updatedAt;

  const EmployeeSkill({
    this.id,
    required this.employeeId,
    required this.skillName,
    required this.category,
    required this.level,
    this.description,
    this.acquiredDate,
    this.lastAssessedDate,
    this.certificationPath,
    this.isVerified = false,
    required this.createdAt,
    required this.updatedAt,
  });

  factory EmployeeSkill.fromMap(Map<String, dynamic> map) {
    return EmployeeSkill(
      id: map['id'] as int?,
      employeeId: map['employee_id'] as int,
      skillName: map['skill_name'] as String,
      category: map['category'] as String,
      level: map['level'] as int,
      description: map['description'] as String?,
      acquiredDate: map['acquired_date'] != null
          ? DateTime.parse(map['acquired_date'] as String)
          : null,
      lastAssessedDate: map['last_assessed_date'] != null
          ? DateTime.parse(map['last_assessed_date'] as String)
          : null,
      certificationPath: map['certification_path'] as String?,
      isVerified: (map['is_verified'] as int?) == 1,
      createdAt: DateTime.parse(map['created_at'] as String),
      updatedAt: DateTime.parse(map['updated_at'] as String),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      if (id != null) 'id': id,
      'employee_id': employeeId,
      'skill_name': skillName,
      'category': category,
      'level': level,
      'description': description,
      'acquired_date': acquiredDate?.toIso8601String().split('T')[0],
      'last_assessed_date': lastAssessedDate?.toIso8601String().split('T')[0],
      'certification_path': certificationPath,
      'is_verified': isVerified ? 1 : 0,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  EmployeeSkill copyWith({
    int? id,
    int? employeeId,
    String? skillName,
    String? category,
    int? level,
    String? description,
    DateTime? acquiredDate,
    DateTime? lastAssessedDate,
    String? certificationPath,
    bool? isVerified,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return EmployeeSkill(
      id: id ?? this.id,
      employeeId: employeeId ?? this.employeeId,
      skillName: skillName ?? this.skillName,
      category: category ?? this.category,
      level: level ?? this.level,
      description: description ?? this.description,
      acquiredDate: acquiredDate ?? this.acquiredDate,
      lastAssessedDate: lastAssessedDate ?? this.lastAssessedDate,
      certificationPath: certificationPath ?? this.certificationPath,
      isVerified: isVerified ?? this.isVerified,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  // خصائص مساعدة
  String get levelText {
    switch (level) {
      case 1:
        return 'مبتدئ';
      case 2:
        return 'أساسي';
      case 3:
        return 'متوسط';
      case 4:
        return 'متقدم';
      case 5:
        return 'خبير';
      default:
        return 'غير محدد';
    }
  }

  bool get hasCertification =>
      certificationPath != null && certificationPath!.isNotEmpty;
}

/// نموذج خطة التطوير المهني
class DevelopmentPlan {
  final int? id;
  final int employeeId;
  final String title;
  final String description;
  final DateTime startDate;
  final DateTime targetDate;
  final String status; // draft, active, completed, cancelled
  final String? goals;
  final String? requiredSkills;
  final String? suggestedTraining;
  final int? mentorId;
  final String? progress;
  final DateTime createdAt;
  final DateTime updatedAt;

  const DevelopmentPlan({
    this.id,
    required this.employeeId,
    required this.title,
    required this.description,
    required this.startDate,
    required this.targetDate,
    this.status = 'draft',
    this.goals,
    this.requiredSkills,
    this.suggestedTraining,
    this.mentorId,
    this.progress,
    required this.createdAt,
    required this.updatedAt,
  });

  factory DevelopmentPlan.fromMap(Map<String, dynamic> map) {
    return DevelopmentPlan(
      id: map['id'] as int?,
      employeeId: map['employee_id'] as int,
      title: map['title'] as String,
      description: map['description'] as String,
      startDate: DateTime.parse(map['start_date'] as String),
      targetDate: DateTime.parse(map['target_date'] as String),
      status: map['status'] as String? ?? 'draft',
      goals: map['goals'] as String?,
      requiredSkills: map['required_skills'] as String?,
      suggestedTraining: map['suggested_training'] as String?,
      mentorId: map['mentor_id'] as int?,
      progress: map['progress'] as String?,
      createdAt: DateTime.parse(map['created_at'] as String),
      updatedAt: DateTime.parse(map['updated_at'] as String),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      if (id != null) 'id': id,
      'employee_id': employeeId,
      'title': title,
      'description': description,
      'start_date': startDate.toIso8601String().split('T')[0],
      'target_date': targetDate.toIso8601String().split('T')[0],
      'status': status,
      'goals': goals,
      'required_skills': requiredSkills,
      'suggested_training': suggestedTraining,
      'mentor_id': mentorId,
      'progress': progress,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  DevelopmentPlan copyWith({
    int? id,
    int? employeeId,
    String? title,
    String? description,
    DateTime? startDate,
    DateTime? targetDate,
    String? status,
    String? goals,
    String? requiredSkills,
    String? suggestedTraining,
    int? mentorId,
    String? progress,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return DevelopmentPlan(
      id: id ?? this.id,
      employeeId: employeeId ?? this.employeeId,
      title: title ?? this.title,
      description: description ?? this.description,
      startDate: startDate ?? this.startDate,
      targetDate: targetDate ?? this.targetDate,
      status: status ?? this.status,
      goals: goals ?? this.goals,
      requiredSkills: requiredSkills ?? this.requiredSkills,
      suggestedTraining: suggestedTraining ?? this.suggestedTraining,
      mentorId: mentorId ?? this.mentorId,
      progress: progress ?? this.progress,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  // خصائص مساعدة
  bool get isDraft => status == 'draft';
  bool get isActive => status == 'active';
  bool get isCompleted => status == 'completed';
  bool get isCancelled => status == 'cancelled';

  Duration get remainingTime => targetDate.difference(DateTime.now());
  int get remainingDays => remainingTime.inDays;
  bool get isOverdue => DateTime.now().isAfter(targetDate) && !isCompleted;
}
