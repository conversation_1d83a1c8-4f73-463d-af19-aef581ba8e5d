import 'package:flutter/material.dart';
import '../constants/revolutionary_design_colors.dart';
import '../services/chart_service.dart';

/// لوحة تخصيص الرسوم البيانية
class ChartCustomizationPanel extends StatefulWidget {
  final ChartConfiguration initialConfiguration;
  final Function(ChartConfiguration) onConfigurationChanged;

  const ChartCustomizationPanel({
    super.key,
    required this.initialConfiguration,
    required this.onConfigurationChanged,
  });

  @override
  State<ChartCustomizationPanel> createState() =>
      _ChartCustomizationPanelState();
}

class _ChartCustomizationPanelState extends State<ChartCustomizationPanel> {
  late ChartConfiguration _configuration;

  @override
  void initState() {
    super.initState();
    _configuration = widget.initialConfiguration;
  }

  void _updateConfiguration() {
    widget.onConfigurationChanged(_configuration);
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            spreadRadius: 1,
            blurRadius: 5,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Row(
            children: [
              Icon(Icons.tune, color: RevolutionaryColors.damascusSky),
              const SizedBox(width: 8),
              Text(
                'تخصيص الرسم البياني',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: RevolutionaryColors.damascusSky,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // Show Legend Toggle
          _buildToggleOption('إظهار وسيلة الإيضاح', _configuration.showLegend, (
            value,
          ) {
            setState(() {
              _configuration = ChartConfiguration(
                showLegend: value,
                showTooltips: _configuration.showTooltips,
                showGrid: _configuration.showGrid,
                primaryColor: _configuration.primaryColor,
                secondaryColor: _configuration.secondaryColor,
                height: _configuration.height,
                width: _configuration.width,
                customSettings: _configuration.customSettings,
              );
            });
            _updateConfiguration();
          }),

          // Show Tooltips Toggle
          _buildToggleOption('إظهار التلميحات', _configuration.showTooltips, (
            value,
          ) {
            setState(() {
              _configuration = ChartConfiguration(
                showLegend: _configuration.showLegend,
                showTooltips: value,
                showGrid: _configuration.showGrid,
                primaryColor: _configuration.primaryColor,
                secondaryColor: _configuration.secondaryColor,
                height: _configuration.height,
                width: _configuration.width,
                customSettings: _configuration.customSettings,
              );
            });
            _updateConfiguration();
          }),

          // Show Grid Toggle
          _buildToggleOption('إظهار الشبكة', _configuration.showGrid, (value) {
            setState(() {
              _configuration = ChartConfiguration(
                showLegend: _configuration.showLegend,
                showTooltips: _configuration.showTooltips,
                showGrid: value,
                primaryColor: _configuration.primaryColor,
                secondaryColor: _configuration.secondaryColor,
                height: _configuration.height,
                width: _configuration.width,
                customSettings: _configuration.customSettings,
              );
            });
            _updateConfiguration();
          }),

          const SizedBox(height: 16),

          // Color Selection
          _buildColorSection(),

          const SizedBox(height: 16),

          // Size Controls
          _buildSizeSection(),
        ],
      ),
    );
  }

  Widget _buildToggleOption(
    String title,
    bool value,
    Function(bool) onChanged,
  ) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(title, style: const TextStyle(fontSize: 14)),
          Switch(
            value: value,
            onChanged: onChanged,
            activeColor: RevolutionaryColors.damascusSky,
          ),
        ],
      ),
    );
  }

  Widget _buildColorSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'الألوان',
          style: TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            _buildColorPicker(
              'اللون الأساسي',
              _configuration.primaryColor ?? RevolutionaryColors.damascusSky,
              (color) {
                setState(() {
                  _configuration = ChartConfiguration(
                    showLegend: _configuration.showLegend,
                    showTooltips: _configuration.showTooltips,
                    showGrid: _configuration.showGrid,
                    primaryColor: color,
                    secondaryColor: _configuration.secondaryColor,
                    height: _configuration.height,
                    width: _configuration.width,
                    customSettings: _configuration.customSettings,
                  );
                });
                _updateConfiguration();
              },
            ),
            const SizedBox(width: 16),
            _buildColorPicker(
              'اللون الثانوي',
              _configuration.secondaryColor ?? RevolutionaryColors.syrianGold,
              (color) {
                setState(() {
                  _configuration = ChartConfiguration(
                    showLegend: _configuration.showLegend,
                    showTooltips: _configuration.showTooltips,
                    showGrid: _configuration.showGrid,
                    primaryColor: _configuration.primaryColor,
                    secondaryColor: color,
                    height: _configuration.height,
                    width: _configuration.width,
                    customSettings: _configuration.customSettings,
                  );
                });
                _updateConfiguration();
              },
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildColorPicker(
    String label,
    Color currentColor,
    Function(Color) onColorChanged,
  ) {
    return Expanded(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(label, style: const TextStyle(fontSize: 12)),
          const SizedBox(height: 4),
          InkWell(
            onTap: () => _showColorPicker(currentColor, onColorChanged),
            child: Container(
              height: 40,
              decoration: BoxDecoration(
                color: currentColor,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey.withValues(alpha: 0.3)),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSizeSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'الحجم',
          style: TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text('الارتفاع', style: TextStyle(fontSize: 12)),
                  Slider(
                    value: _configuration.height ?? 300,
                    min: 200,
                    max: 600,
                    divisions: 8,
                    label: '${(_configuration.height ?? 300).round()}',
                    onChanged: (value) {
                      setState(() {
                        _configuration = ChartConfiguration(
                          showLegend: _configuration.showLegend,
                          showTooltips: _configuration.showTooltips,
                          showGrid: _configuration.showGrid,
                          primaryColor: _configuration.primaryColor,
                          secondaryColor: _configuration.secondaryColor,
                          height: value,
                          width: _configuration.width,
                          customSettings: _configuration.customSettings,
                        );
                      });
                      _updateConfiguration();
                    },
                  ),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }

  void _showColorPicker(Color currentColor, Function(Color) onColorChanged) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('اختر اللون'),
        content: SingleChildScrollView(
          child: BlockPicker(
            pickerColor: currentColor,
            onColorChanged: onColorChanged,
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }
}

/// مكون اختيار الألوان البسيط
class BlockPicker extends StatelessWidget {
  final Color pickerColor;
  final Function(Color) onColorChanged;

  const BlockPicker({
    super.key,
    required this.pickerColor,
    required this.onColorChanged,
  });

  @override
  Widget build(BuildContext context) {
    final colors = [
      RevolutionaryColors.damascusSky,
      RevolutionaryColors.syrianGold,
      RevolutionaryColors.successGlow,
      RevolutionaryColors.warningAmber,
      RevolutionaryColors.errorCoral,
      RevolutionaryColors.infoTurquoise,
      Colors.purple,
      Colors.orange,
      Colors.teal,
      Colors.indigo,
      Colors.pink,
      Colors.brown,
    ];

    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children: colors.map((color) {
        final isSelected = color.toARGB32() == pickerColor.toARGB32();
        return InkWell(
          onTap: () => onColorChanged(color),
          child: Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: color,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: isSelected
                    ? Colors.black
                    : Colors.grey.withValues(alpha: 0.3),
                width: isSelected ? 3 : 1,
              ),
            ),
            child: isSelected
                ? const Icon(Icons.check, color: Colors.white)
                : null,
          ),
        );
      }).toList(),
    );
  }
}

/// مكون إعدادات الرسم البياني السريعة
class QuickChartSettings extends StatelessWidget {
  final Function(String) onPresetSelected;

  const QuickChartSettings({super.key, required this.onPresetSelected});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'إعدادات سريعة',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: RevolutionaryColors.damascusSky,
            ),
          ),
          const SizedBox(height: 16),
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: [
              _buildPresetChip('افتراضي', 'default'),
              _buildPresetChip('ملون', 'colorful'),
              _buildPresetChip('أحادي اللون', 'monochrome'),
              _buildPresetChip('احترافي', 'professional'),
              _buildPresetChip('مظلم', 'dark'),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildPresetChip(String label, String preset) {
    return ActionChip(
      label: Text(label),
      onPressed: () => onPresetSelected(preset),
      backgroundColor: RevolutionaryColors.damascusSky.withValues(alpha: 0.1),
      labelStyle: TextStyle(color: RevolutionaryColors.damascusSky),
    );
  }
}
