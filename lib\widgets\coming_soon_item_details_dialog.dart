/// حوار عرض تفاصيل الصنف القريب
/// يعرض جميع معلومات الصنف مع إمكانية التعديل والحذف
library;

import 'package:flutter/material.dart';
import '../models/coming_soon_item.dart';
import '../services/coming_soon_service.dart';
import '../constants/revolutionary_design_colors.dart';
import '../services/logging_service.dart';
import 'edit_coming_soon_item_dialog.dart';

class ComingSoonItemDetailsDialog extends StatefulWidget {
  final ComingSoonItem item;
  final VoidCallback? onItemUpdated;
  final VoidCallback? onItemDeleted;

  const ComingSoonItemDetailsDialog({
    super.key,
    required this.item,
    this.onItemUpdated,
    this.onItemDeleted,
  });

  @override
  State<ComingSoonItemDetailsDialog> createState() =>
      _ComingSoonItemDetailsDialogState();
}

class _ComingSoonItemDetailsDialogState
    extends State<ComingSoonItemDetailsDialog> {
  final ComingSoonService _comingSoonService = ComingSoonService();
  bool _isLoading = false;

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        constraints: const BoxConstraints(maxWidth: 500, maxHeight: 600),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildHeader(),
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(24),
                child: _buildContent(),
              ),
            ),
            _buildActions(),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    final statusColor = Color(
      int.parse(widget.item.statusColor.substring(1), radix: 16) + 0xFF000000,
    );

    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: RevolutionaryColors.damascusSky,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(16),
          topRight: Radius.circular(16),
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(
              Icons.schedule,
              color: Colors.white,
              size: 24,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  widget.item.name,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    fontFamily: 'Cairo',
                  ),
                ),
                const SizedBox(height: 4),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: statusColor,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    widget.item.availabilityStatus,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                      fontFamily: 'Cairo',
                    ),
                  ),
                ),
              ],
            ),
          ),
          IconButton(
            onPressed: () => Navigator.of(context).pop(),
            icon: const Icon(Icons.close, color: Colors.white),
          ),
        ],
      ),
    );
  }

  Widget _buildContent() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildInfoSection(),
        const SizedBox(height: 24),
        _buildPriceSection(),
        const SizedBox(height: 24),
        _buildDateSection(),
        const SizedBox(height: 24),
        _buildSupplierSection(),
        if (widget.item.notes?.isNotEmpty == true) ...[
          const SizedBox(height: 24),
          _buildNotesSection(),
        ],
      ],
    );
  }

  Widget _buildInfoSection() {
    return _buildSection(
      title: 'معلومات الصنف',
      icon: Icons.info_outline,
      children: [
        _buildInfoRow('الاسم', widget.item.name),
        _buildInfoRow('الوصف', widget.item.description),
        if (widget.item.category?.isNotEmpty == true)
          _buildInfoRow('الفئة', widget.item.category!),
        if (widget.item.unit?.isNotEmpty == true)
          _buildInfoRow('الوحدة', widget.item.unit!),
      ],
    );
  }

  Widget _buildPriceSection() {
    return _buildSection(
      title: 'معلومات السعر',
      icon: Icons.attach_money,
      children: [
        _buildInfoRow(
          'السعر المتوقع',
          '${widget.item.estimatedPrice.toStringAsFixed(2)} ل.س',
        ),
      ],
    );
  }

  Widget _buildDateSection() {
    return _buildSection(
      title: 'معلومات التوقيت',
      icon: Icons.schedule,
      children: [
        _buildInfoRow(
          'تاريخ التوفر المتوقع',
          _formatDate(widget.item.expectedAvailabilityDate),
        ),
        _buildInfoRow(
          'الأيام المتبقية',
          widget.item.daysRemaining >= 0
              ? '${widget.item.daysRemaining} يوم'
              : 'متأخر بـ ${widget.item.daysRemaining.abs()} يوم',
        ),
        _buildInfoRow(
          'تاريخ الإنشاء',
          _formatDate(widget.item.createdAt),
        ),
        _buildInfoRow(
          'آخر تحديث',
          _formatDate(widget.item.updatedAt),
        ),
      ],
    );
  }

  Widget _buildSupplierSection() {
    if (widget.item.supplierInfo?.isEmpty != false) {
      return const SizedBox.shrink();
    }

    return _buildSection(
      title: 'معلومات المورد',
      icon: Icons.business,
      children: [
        _buildInfoRow('المورد', widget.item.supplierInfo!),
      ],
    );
  }

  Widget _buildNotesSection() {
    return _buildSection(
      title: 'ملاحظات',
      icon: Icons.note,
      children: [
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: RevolutionaryColors.jasmineWhite.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: RevolutionaryColors.jasmineWhite.withValues(alpha: 0.3),
            ),
          ),
          child: Text(
            widget.item.notes!,
            style: const TextStyle(
              fontSize: 14,
              fontFamily: 'Cairo',
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildSection({
    required String title,
    required IconData icon,
    required List<Widget> children,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              icon,
              color: RevolutionaryColors.damascusSky,
              size: 20,
            ),
            const SizedBox(width: 8),
            Text(
              title,
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: RevolutionaryColors.damascusSky,
                fontFamily: 'Cairo',
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        ...children,
      ],
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: RevolutionaryColors.textSecondary,
                fontFamily: 'Cairo',
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(
                fontSize: 14,
                fontFamily: 'Cairo',
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActions() {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(16),
          bottomRight: Radius.circular(16),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: OutlinedButton.icon(
              onPressed: _isLoading ? null : _editItem,
              icon: const Icon(Icons.edit),
              label: const Text('تعديل'),
              style: OutlinedButton.styleFrom(
                foregroundColor: RevolutionaryColors.damascusSky,
                side: BorderSide(color: RevolutionaryColors.damascusSky),
                padding: const EdgeInsets.symmetric(vertical: 12),
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: OutlinedButton.icon(
              onPressed: _isLoading ? null : _deleteItem,
              icon: const Icon(Icons.delete),
              label: const Text('حذف'),
              style: OutlinedButton.styleFrom(
                foregroundColor: RevolutionaryColors.errorCoral,
                side: BorderSide(color: RevolutionaryColors.errorCoral),
                padding: const EdgeInsets.symmetric(vertical: 12),
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: ElevatedButton.icon(
              onPressed: () => Navigator.of(context).pop(),
              icon: const Icon(Icons.close),
              label: const Text('إغلاق'),
              style: ElevatedButton.styleFrom(
                backgroundColor: RevolutionaryColors.damascusSky,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 12),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _editItem() async {
    final result = await showDialog<bool>(
      context: context,
      builder: (context) => EditComingSoonItemDialog(item: widget.item),
    );

    if (result == true) {
      widget.onItemUpdated?.call();
      if (mounted) {
        Navigator.of(context).pop();
      }
    }
  }

  Future<void> _deleteItem() async {
    final confirmed = await _showDeleteConfirmation();
    if (!confirmed) return;

    setState(() => _isLoading = true);

    try {
      await _comingSoonService.deleteComingSoonItem(widget.item.id!);

      if (mounted) {
        Navigator.of(context).pop();
        widget.onItemDeleted?.call();
        _showSuccessSnackBar('تم حذف الصنف بنجاح');
      }
    } catch (e) {
      LoggingService.error(
        'خطأ في حذف الصنف القريب',
        category: 'ComingSoonItemDetailsDialog',
        data: {'error': e.toString()},
      );

      if (mounted) {
        _showErrorSnackBar('خطأ في حذف الصنف: $e');
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  Future<bool> _showDeleteConfirmation() async {
    return await showDialog<bool>(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('تأكيد الحذف'),
            content: Text('هل أنت متأكد من حذف "${widget.item.name}"؟'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: const Text('إلغاء'),
              ),
              ElevatedButton(
                onPressed: () => Navigator.of(context).pop(true),
                style: ElevatedButton.styleFrom(
                  backgroundColor: RevolutionaryColors.errorCoral,
                ),
                child: const Text('حذف'),
              ),
            ],
          ),
        ) ??
        false;
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: RevolutionaryColors.successGlow,
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: RevolutionaryColors.errorCoral,
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}
