/// شاشة النظام الضريبي السوري
/// إدارة شاملة للضرائب والإقرارات الضريبية
library;

import 'package:flutter/material.dart';
import '../models/syrian_tax_models.dart';
import '../services/syrian_tax_service.dart';
import '../constants/revolutionary_design_colors.dart';
import '../widgets/loading_widget.dart';

class SyrianTaxScreen extends StatefulWidget {
  const SyrianTaxScreen({super.key});

  @override
  State<SyrianTaxScreen> createState() => _SyrianTaxScreenState();
}

class _SyrianTaxScreenState extends State<SyrianTaxScreen>
    with TickerProviderStateMixin {
  final SyrianTaxService _taxService = SyrianTaxService();

  late TabController _tabController;

  List<TaxDeclaration> _declarations = [];
  final List<TaxReport> _reports = [];
  TaxpayerInfo? _taxpayerInfo;

  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _loadData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  /// تحميل البيانات
  Future<void> _loadData() async {
    setState(() => _isLoading = true);

    try {
      final declarations = await _taxService.getAllTaxDeclarations();
      final taxpayerInfo = await _taxService.getTaxpayerInfo('default');

      setState(() {
        _declarations = declarations;
        // التقارير ستكون متاحة في إصدار لاحق
        _reports.clear();
        _taxpayerInfo = taxpayerInfo;
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('خطأ في تحميل البيانات: $e')));
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'النظام الضريبي السوري',
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        backgroundColor: RevolutionaryColors.damascusSky,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            onPressed: _showTaxCalculator,
            icon: const Icon(Icons.calculate),
            tooltip: 'حاسبة الضرائب',
          ),
          IconButton(
            onPressed: _loadData,
            icon: const Icon(Icons.refresh),
            tooltip: 'تحديث',
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          indicatorColor: Colors.white,
          isScrollable: true,
          tabs: const [
            Tab(text: 'لوحة التحكم', icon: Icon(Icons.dashboard)),
            Tab(text: 'الإقرارات', icon: Icon(Icons.description)),
            Tab(text: 'التقارير', icon: Icon(Icons.assessment)),
            Tab(text: 'الإعدادات', icon: Icon(Icons.settings)),
          ],
        ),
      ),
      body: _isLoading
          ? const LoadingWidget()
          : TabBarView(
              controller: _tabController,
              children: [
                _buildDashboardTab(),
                _buildDeclarationsTab(),
                _buildReportsTab(),
                _buildSettingsTab(),
              ],
            ),
      floatingActionButton: FloatingActionButton(
        onPressed: _createNewDeclaration,
        backgroundColor: RevolutionaryColors.damascusSky,
        tooltip: 'إقرار جديد',
        child: const Icon(Icons.add, color: Colors.white),
      ),
    );
  }

  /// تبويب لوحة التحكم
  Widget _buildDashboardTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // بطاقات الإحصائيات
          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  'الإقرارات المقدمة',
                  _declarations.length.toString(),
                  Icons.description,
                  RevolutionaryColors.damascusSky,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildStatCard(
                  'الإقرارات المعلقة',
                  _declarations
                      .where((d) => d.status == TaxDeclarationStatus.draft)
                      .length
                      .toString(),
                  Icons.pending,
                  Colors.orange,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  'إجمالي الضرائب',
                  '${_calculateTotalTaxes().toStringAsFixed(0)} ل.س',
                  Icons.money,
                  RevolutionaryColors.successGlow,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildStatCard(
                  'المبلغ المستحق',
                  '${_calculateOutstandingTaxes().toStringAsFixed(0)} ل.س',
                  Icons.warning,
                  RevolutionaryColors.errorCoral,
                ),
              ),
            ],
          ),
          const SizedBox(height: 24),

          // الإقرارات الأخيرة
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'الإقرارات الأخيرة',
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 16),
                  if (_declarations.isEmpty)
                    const Center(
                      child: Text(
                        'لا توجد إقرارات',
                        style: TextStyle(color: Colors.grey),
                      ),
                    )
                  else
                    ListView.builder(
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      itemCount: _declarations.take(5).length,
                      itemBuilder: (context, index) {
                        final declaration = _declarations[index];
                        return _buildDeclarationListTile(declaration);
                      },
                    ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 24),

          // التذكيرات والمواعيد
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'التذكيرات والمواعيد',
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 16),
                  _buildReminderItem(
                    'إقرار ضريبة الدخل السنوي',
                    'مستحق في 31 مارس',
                    Icons.event,
                    Colors.orange,
                  ),
                  _buildReminderItem(
                    'إقرار ضريبة المبيعات الشهري',
                    'مستحق في 15 من كل شهر',
                    Icons.calendar_today,
                    RevolutionaryColors.damascusSky,
                  ),
                  _buildReminderItem(
                    'رسم الخدمات الربع سنوي',
                    'مستحق في نهاية كل ربع',
                    Icons.schedule,
                    RevolutionaryColors.infoTurquoise,
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// تبويب الإقرارات
  Widget _buildDeclarationsTab() {
    return Column(
      children: [
        // شريط الفلترة
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.grey[50],
            border: Border(bottom: BorderSide(color: Colors.grey[300]!)),
          ),
          child: Row(
            children: [
              Expanded(
                child: DropdownButtonFormField<TaxDeclarationStatus?>(
                  value: null,
                  decoration: const InputDecoration(
                    labelText: 'فلترة حسب الحالة',
                    border: OutlineInputBorder(),
                  ),
                  items: [
                    const DropdownMenuItem(
                      value: null,
                      child: Text('جميع الحالات'),
                    ),
                    ...TaxDeclarationStatus.values.map((status) {
                      return DropdownMenuItem(
                        value: status,
                        child: Text(_getStatusName(status)),
                      );
                    }),
                  ],
                  onChanged: (value) {
                    _filterDeclarations(value);
                  },
                ),
              ),
              const SizedBox(width: 16),
              ElevatedButton.icon(
                onPressed: _createNewDeclaration,
                icon: const Icon(Icons.add),
                label: const Text('إقرار جديد'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: RevolutionaryColors.damascusSky,
                  foregroundColor: Colors.white,
                ),
              ),
            ],
          ),
        ),

        // قائمة الإقرارات
        Expanded(
          child: _declarations.isEmpty
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.description_outlined,
                        size: 64,
                        color: Colors.grey[400],
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'لا توجد إقرارات ضريبية',
                        style: TextStyle(fontSize: 18, color: Colors.grey[600]),
                      ),
                      const SizedBox(height: 16),
                      ElevatedButton.icon(
                        onPressed: _createNewDeclaration,
                        icon: const Icon(Icons.add),
                        label: const Text('إنشاء إقرار جديد'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: RevolutionaryColors.damascusSky,
                          foregroundColor: Colors.white,
                        ),
                      ),
                    ],
                  ),
                )
              : ListView.builder(
                  padding: const EdgeInsets.all(16),
                  itemCount: _declarations.length,
                  itemBuilder: (context, index) {
                    final declaration = _declarations[index];
                    return _buildDeclarationCard(declaration);
                  },
                ),
        ),
      ],
    );
  }

  /// تبويب التقارير
  Widget _buildReportsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'التقارير الضريبية',
            style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),

          // تقارير سريعة
          GridView.count(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            crossAxisCount: 2,
            crossAxisSpacing: 16,
            mainAxisSpacing: 16,
            children: [
              _buildReportCard(
                'تقرير ضريبة الدخل السنوي',
                'تقرير شامل لضريبة الدخل',
                Icons.account_balance,
                () => _generateIncomeTaxReport(),
              ),
              _buildReportCard(
                'تقرير ضريبة المبيعات الشهري',
                'تقرير ضريبة المبيعات للشهر الحالي',
                Icons.point_of_sale,
                () => _generateSalesTaxReport(),
              ),
              _buildReportCard(
                'تقرير رسم الخدمات',
                'تقرير رسم الخدمات الربع سنوي',
                Icons.room_service,
                () => _generateServiceFeeReport(),
              ),
              _buildReportCard(
                'تقرير شامل للضرائب',
                'تقرير جميع أنواع الضرائب',
                Icons.assessment,
                () => _generateComprehensiveTaxReport(),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// تبويب الإعدادات
  Widget _buildSettingsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'إعدادات النظام الضريبي',
            style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),

          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'معلومات دافع الضرائب',
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 16),

                  // عرض معلومات دافع الضرائب الحالية
                  if (_taxpayerInfo != null) ...[
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.grey[50],
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.grey[300]!),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          _buildInfoRow('اسم المنشأة', _taxpayerInfo!.name),
                          _buildInfoRow(
                            'الرقم الضريبي',
                            _taxpayerInfo!.taxNumber,
                          ),
                          _buildInfoRow(
                            'نوع النشاط',
                            _taxpayerInfo!.businessActivity,
                          ),
                          if (_taxpayerInfo!.address.isNotEmpty)
                            _buildInfoRow('العنوان', _taxpayerInfo!.address),
                          if (_taxpayerInfo!.phone.isNotEmpty)
                            _buildInfoRow('رقم الهاتف', _taxpayerInfo!.phone),
                          if (_taxpayerInfo!.email.isNotEmpty)
                            _buildInfoRow(
                              'البريد الإلكتروني',
                              _taxpayerInfo!.email,
                            ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 16),
                  ],

                  ListTile(
                    leading: const Icon(Icons.business),
                    title: const Text('إعداد معلومات المنشأة'),
                    subtitle: Text(
                      _taxpayerInfo != null
                          ? 'تحديث بيانات دافع الضرائب'
                          : 'إضافة بيانات دافع الضرائب',
                    ),
                    trailing: const Icon(Icons.arrow_forward_ios),
                    onTap: _setupTaxpayerInfo,
                  ),
                  ListTile(
                    leading: const Icon(Icons.settings),
                    title: const Text('إعدادات الحساب التلقائي'),
                    subtitle: const Text('تفعيل الحساب التلقائي للضرائب'),
                    trailing: Switch(
                      value: true,
                      onChanged: (value) {
                        _updateAutoCalculationSetting(value);
                      },
                    ),
                  ),
                  ListTile(
                    leading: const Icon(Icons.notifications),
                    title: const Text('تذكيرات الضرائب'),
                    subtitle: const Text('إعداد التذكيرات والإشعارات'),
                    trailing: const Icon(Icons.arrow_forward_ios),
                    onTap: _setupTaxReminders,
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// بناء بطاقة إحصائية
  Widget _buildStatCard(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, color: color, size: 24),
                const Spacer(),
                Text(
                  value,
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: color,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              title,
              style: const TextStyle(fontSize: 14, color: Colors.grey),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء عنصر تذكير
  Widget _buildReminderItem(
    String title,
    String subtitle,
    IconData icon,
    Color color,
  ) {
    return ListTile(
      leading: CircleAvatar(
        backgroundColor: color.withValues(alpha: 0.1),
        child: Icon(icon, color: color, size: 20),
      ),
      title: Text(title),
      subtitle: Text(subtitle),
      trailing: const Icon(Icons.arrow_forward_ios, size: 16),
      onTap: () {
        _showReminderDetails(title, subtitle);
      },
    );
  }

  /// بناء عنصر قائمة الإقرار
  Widget _buildDeclarationListTile(TaxDeclaration declaration) {
    return ListTile(
      leading: CircleAvatar(
        backgroundColor: _getStatusColor(
          declaration.status,
        ).withValues(alpha: 0.1),
        child: Icon(
          _getTaxTypeIcon(declaration.taxType),
          color: _getStatusColor(declaration.status),
          size: 20,
        ),
      ),
      title: Text(declaration.declarationNumber),
      subtitle: Text(_getTaxTypeName(declaration.taxType)),
      trailing: Chip(
        label: Text(
          _getStatusName(declaration.status),
          style: const TextStyle(fontSize: 12),
        ),
        backgroundColor: _getStatusColor(
          declaration.status,
        ).withValues(alpha: 0.1),
      ),
      onTap: () => _viewDeclaration(declaration),
    );
  }

  /// بناء بطاقة الإقرار
  Widget _buildDeclarationCard(TaxDeclaration declaration) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  _getTaxTypeIcon(declaration.taxType),
                  color: RevolutionaryColors.damascusSky,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    declaration.declarationNumber,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                Chip(
                  label: Text(
                    _getStatusName(declaration.status),
                    style: const TextStyle(fontSize: 12),
                  ),
                  backgroundColor: _getStatusColor(
                    declaration.status,
                  ).withValues(alpha: 0.1),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              _getTaxTypeName(declaration.taxType),
              style: const TextStyle(fontSize: 14, color: Colors.grey),
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'الفترة',
                        style: TextStyle(fontSize: 12, color: Colors.grey),
                      ),
                      Text(declaration.period),
                    ],
                  ),
                ),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'المبلغ',
                        style: TextStyle(fontSize: 12, color: Colors.grey),
                      ),
                      Text(
                        '${declaration.totalTaxAmount.toStringAsFixed(0)} ل.س',
                      ),
                    ],
                  ),
                ),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'تاريخ التقديم',
                        style: TextStyle(fontSize: 12, color: Colors.grey),
                      ),
                      Text(_formatDate(declaration.submissionDate)),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton.icon(
                  onPressed: () => _viewDeclaration(declaration),
                  icon: const Icon(Icons.visibility),
                  label: const Text('عرض'),
                ),
                const SizedBox(width: 8),
                TextButton.icon(
                  onPressed: () => _editDeclaration(declaration),
                  icon: const Icon(Icons.edit),
                  label: const Text('تحرير'),
                ),
                const SizedBox(width: 8),
                TextButton.icon(
                  onPressed: () => _exportDeclaration(declaration),
                  icon: const Icon(Icons.download),
                  label: const Text('تصدير'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// بناء بطاقة التقرير
  Widget _buildReportCard(
    String title,
    String subtitle,
    IconData icon,
    VoidCallback onTap,
  ) {
    return Card(
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Icon(icon, size: 32, color: RevolutionaryColors.damascusSky),
              const SizedBox(height: 12),
              Text(
                title,
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 4),
              Text(
                subtitle,
                style: const TextStyle(fontSize: 12, color: Colors.grey),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// حساب إجمالي الضرائب
  double _calculateTotalTaxes() {
    return _declarations.fold(
      0.0,
      (sum, declaration) => sum + declaration.totalTaxAmount,
    );
  }

  /// حساب الضرائب المستحقة
  double _calculateOutstandingTaxes() {
    return _declarations
        .where((d) => d.status != TaxDeclarationStatus.paid)
        .fold(0.0, (sum, declaration) => sum + declaration.remainingAmount);
  }

  /// الحصول على لون الحالة
  Color _getStatusColor(TaxDeclarationStatus status) {
    switch (status) {
      case TaxDeclarationStatus.draft:
        return Colors.grey;
      case TaxDeclarationStatus.submitted:
        return Colors.blue;
      case TaxDeclarationStatus.underReview:
        return Colors.orange;
      case TaxDeclarationStatus.approved:
        return Colors.green;
      case TaxDeclarationStatus.rejected:
        return Colors.red;
      case TaxDeclarationStatus.amended:
        return Colors.purple;
      case TaxDeclarationStatus.paid:
        return RevolutionaryColors.successGlow;
      case TaxDeclarationStatus.overdue:
        return RevolutionaryColors.errorCoral;
    }
  }

  /// الحصول على اسم الحالة
  String _getStatusName(TaxDeclarationStatus status) {
    switch (status) {
      case TaxDeclarationStatus.draft:
        return 'مسودة';
      case TaxDeclarationStatus.submitted:
        return 'مقدم';
      case TaxDeclarationStatus.underReview:
        return 'قيد المراجعة';
      case TaxDeclarationStatus.approved:
        return 'موافق عليه';
      case TaxDeclarationStatus.rejected:
        return 'مرفوض';
      case TaxDeclarationStatus.amended:
        return 'معدل';
      case TaxDeclarationStatus.paid:
        return 'مدفوع';
      case TaxDeclarationStatus.overdue:
        return 'متأخر';
    }
  }

  /// الحصول على أيقونة نوع الضريبة
  IconData _getTaxTypeIcon(SyrianTaxType taxType) {
    switch (taxType) {
      case SyrianTaxType.incomeTax:
        return Icons.account_balance;
      case SyrianTaxType.salesTax:
        return Icons.point_of_sale;
      case SyrianTaxType.servicesFeeTax:
        return Icons.room_service;
      case SyrianTaxType.realEstateTax:
        return Icons.home;
      case SyrianTaxType.vehicleTax:
        return Icons.directions_car;
      case SyrianTaxType.professionalTax:
        return Icons.work;
      case SyrianTaxType.commercialTax:
        return Icons.business;
      case SyrianTaxType.industrialTax:
        return Icons.factory;
      case SyrianTaxType.stampDuty:
        return Icons.local_post_office;
      case SyrianTaxType.customsDuty:
        return Icons.local_shipping;
    }
  }

  /// الحصول على اسم نوع الضريبة
  String _getTaxTypeName(SyrianTaxType taxType) {
    switch (taxType) {
      case SyrianTaxType.incomeTax:
        return 'ضريبة الدخل';
      case SyrianTaxType.salesTax:
        return 'ضريبة المبيعات';
      case SyrianTaxType.servicesFeeTax:
        return 'رسم الخدمات';
      case SyrianTaxType.realEstateTax:
        return 'ضريبة العقارات';
      case SyrianTaxType.vehicleTax:
        return 'ضريبة المركبات';
      case SyrianTaxType.professionalTax:
        return 'ضريبة المهن الحرة';
      case SyrianTaxType.commercialTax:
        return 'ضريبة التجارة';
      case SyrianTaxType.industrialTax:
        return 'ضريبة الصناعة';
      case SyrianTaxType.stampDuty:
        return 'رسم الطابع';
      case SyrianTaxType.customsDuty:
        return 'الرسوم الجمركية';
    }
  }

  /// تنسيق التاريخ
  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  // الدوال التفاعلية

  /// عرض حاسبة الضرائب
  void _showTaxCalculator() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('حاسبة الضرائب - قيد التطوير')),
    );
  }

  /// إنشاء إقرار جديد
  void _createNewDeclaration() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('إنشاء إقرار جديد - قيد التطوير')),
    );
  }

  /// عرض الإقرار
  void _viewDeclaration(TaxDeclaration declaration) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('عرض الإقرار: ${declaration.declarationNumber}')),
    );
  }

  /// تحرير الإقرار
  void _editDeclaration(TaxDeclaration declaration) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('تحرير الإقرار: ${declaration.declarationNumber}'),
      ),
    );
  }

  /// تصدير الإقرار
  void _exportDeclaration(TaxDeclaration declaration) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('تصدير الإقرار: ${declaration.declarationNumber}'),
      ),
    );
  }

  /// إنشاء تقرير ضريبة الدخل
  void _generateIncomeTaxReport() async {
    try {
      await _taxService.generateAnnualIncomeTaxReport(
        taxpayerId: 'default',
        year: DateTime.now().year,
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم إنشاء تقرير ضريبة الدخل بنجاح'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('خطأ في إنشاء التقرير: $e')));
      }
    }
  }

  /// إنشاء تقرير ضريبة المبيعات
  void _generateSalesTaxReport() async {
    try {
      final now = DateTime.now();
      await _taxService.generateMonthlySalesTaxReport(
        year: now.year,
        month: now.month,
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم إنشاء تقرير ضريبة المبيعات بنجاح'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('خطأ في إنشاء التقرير: $e')));
      }
    }
  }

  /// إنشاء تقرير رسم الخدمات
  void _generateServiceFeeReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تقرير رسم الخدمات - قيد التطوير')),
    );
  }

  /// إنشاء تقرير شامل للضرائب
  void _generateComprehensiveTaxReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('التقرير الشامل للضرائب - قيد التطوير')),
    );
  }

  /// إعداد معلومات دافع الضرائب
  void _setupTaxpayerInfo() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('إعداد معلومات دافع الضرائب - قيد التطوير')),
    );
  }

  /// إعداد تذكيرات الضرائب
  void _setupTaxReminders() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('إعداد تذكيرات الضرائب - قيد التطوير')),
    );
  }

  /// فلترة الإقرارات حسب الحالة
  void _filterDeclarations(TaxDeclarationStatus? filterStatus) {
    setState(() {
      if (filterStatus == null) {
        // عرض جميع الإقرارات
        _loadData();
      } else {
        // فلترة حسب الحالة المحددة
        _declarations = _declarations.where((declaration) {
          return declaration.status == filterStatus;
        }).toList();
      }
    });
  }

  /// تحديث إعداد الحساب التلقائي
  void _updateAutoCalculationSetting(bool value) {
    setState(() {
      // تحديث الإعداد في قاعدة البيانات
      // يمكن إضافة منطق حفظ الإعداد هنا
    });

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          value
              ? 'تم تفعيل الحساب التلقائي للضرائب'
              : 'تم إلغاء تفعيل الحساب التلقائي للضرائب',
        ),
        backgroundColor: Colors.green,
      ),
    );
  }

  /// عرض تفاصيل التذكير
  void _showReminderDetails(String title, String subtitle) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(subtitle),
            const SizedBox(height: 16),
            const Text(
              'تفاصيل إضافية:',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            const Text('• يتم إرسال تذكير قبل الموعد بـ 7 أيام'),
            const Text('• يمكن تخصيص مواعيد التذكير'),
            const Text('• التذكيرات تظهر في قسم الإشعارات'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إغلاق'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _setupTaxReminders();
            },
            child: const Text('إعداد التذكيرات'),
          ),
        ],
      ),
    );
  }

  /// بناء صف معلومات
  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              label,
              style: const TextStyle(
                fontWeight: FontWeight.w500,
                color: Colors.grey,
                fontSize: 12,
              ),
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w400),
            ),
          ),
        ],
      ),
    );
  }
}
