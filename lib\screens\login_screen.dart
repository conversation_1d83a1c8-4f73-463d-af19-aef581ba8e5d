import 'package:flutter/material.dart';
import '../services/encryption_service.dart';
import '../database/database_helper.dart';
import '../services/logging_service.dart';
import '../constants/revolutionary_design_colors.dart';
import '../constants/app_constants.dart';
import '../widgets/app_logo.dart';
import 'revolutionary_home_screen.dart';
import 'password_setup_screen.dart';

/// شاشة تسجيل الدخول بكلمة مرور قاعدة البيانات مع تأثيرات بصرية جذابة
class LoginScreen extends StatefulWidget {
  const LoginScreen({super.key});

  @override
  State<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen>
    with TickerProviderStateMixin {
  final _formKey = GlobalKey<FormState>();
  final _passwordController = TextEditingController();

  bool _isPasswordVisible = false;
  bool _isLoading = false;
  String? _errorMessage;
  int _failedAttempts = 0;
  static const int _maxFailedAttempts = 5;

  late AnimationController _fadeController;
  late AnimationController _slideController;
  late AnimationController _scaleController;
  late AnimationController _shakeController;

  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _scaleAnimation;
  late Animation<double> _shakeAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _startAnimations();
    _checkEncryptionSetup();
  }

  void _initializeAnimations() {
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    _slideController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _shakeController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _fadeController, curve: Curves.easeInOut),
    );

    _slideAnimation =
        Tween<Offset>(begin: const Offset(0, 0.5), end: Offset.zero).animate(
          CurvedAnimation(parent: _slideController, curve: Curves.easeOutBack),
        );

    _scaleAnimation = Tween<double>(begin: 0.8, end: 1.0).animate(
      CurvedAnimation(parent: _scaleController, curve: Curves.elasticOut),
    );

    _shakeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _shakeController, curve: Curves.elasticInOut),
    );
  }

  void _startAnimations() {
    _fadeController.forward();
    Future.delayed(const Duration(milliseconds: 200), () {
      _slideController.forward();
    });
    Future.delayed(const Duration(milliseconds: 400), () {
      _scaleController.forward();
    });
  }

  @override
  void dispose() {
    _passwordController.dispose();
    _fadeController.dispose();
    _slideController.dispose();
    _scaleController.dispose();
    _shakeController.dispose();
    super.dispose();
  }

  Future<void> _checkEncryptionSetup() async {
    final isSetup = await EncryptionService.isEncryptionSetup();
    if (!isSetup && mounted) {
      Navigator.of(context).pushReplacement(
        MaterialPageRoute(
          builder: (context) => const PasswordSetupScreen(isFirstTime: true),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              RevolutionaryColors.damascusSky.withValues(alpha: 0.1),
              RevolutionaryColors.syrianGold.withValues(alpha: 0.05),
              RevolutionaryColors.damascusSkyLight.withValues(alpha: 0.08),
            ],
          ),
        ),
        child: SafeArea(
          child: AnimatedBuilder(
            animation: Listenable.merge([
              _fadeController,
              _slideController,
              _scaleController,
              _shakeController,
            ]),
            builder: (context, child) {
              return FadeTransition(
                opacity: _fadeAnimation,
                child: SingleChildScrollView(
                  padding: const EdgeInsets.all(24.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      const SizedBox(height: 40),

                      // الشعار والعنوان المتحرك
                      SlideTransition(
                        position: _slideAnimation,
                        child: ScaleTransition(
                          scale: _scaleAnimation,
                          child: _buildEnhancedHeader(),
                        ),
                      ),

                      const SizedBox(height: 50),

                      // النموذج المتحرك
                      SlideTransition(
                        position: _slideAnimation,
                        child: _buildEnhancedForm(),
                      ),

                      const SizedBox(height: 24),

                      // زر تسجيل الدخول المتحرك
                      ScaleTransition(
                        scale: _scaleAnimation,
                        child: _buildEnhancedLoginButton(),
                      ),

                      if (_errorMessage != null) ...[
                        const SizedBox(height: 16),
                        _buildEnhancedErrorMessage(),
                      ],

                      const SizedBox(height: 32),

                      // خيارات إضافية متحركة
                      FadeTransition(
                        opacity: _fadeAnimation,
                        child: _buildEnhancedAdditionalOptions(),
                      ),
                    ],
                  ),
                ),
              );
            },
          ),
        ),
      ),
    );
  }

  Widget _buildEnhancedHeader() {
    return Column(
      children: [
        Container(
          width: 140,
          height: 140,
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                RevolutionaryColors.damascusSky.withValues(alpha: 0.2),
                RevolutionaryColors.syrianGold.withValues(alpha: 0.1),
              ],
            ),
            shape: BoxShape.circle,
            boxShadow: [
              BoxShadow(
                color: RevolutionaryColors.damascusSky.withValues(alpha: 0.3),
                blurRadius: 20,
                spreadRadius: 5,
              ),
            ],
          ),
          child: AppLogo(size: 80),
        ),
        const SizedBox(height: 24),
        ShaderMask(
          shaderCallback: (bounds) => LinearGradient(
            colors: [
              RevolutionaryColors.damascusSky,
              RevolutionaryColors.syrianGold,
            ],
          ).createShader(bounds),
          child: Text(
            AppConstants.appNameArabic,
            style: const TextStyle(
              fontSize: 36,
              fontWeight: FontWeight.bold,
              color: Colors.white,
              letterSpacing: 1.5,
            ),
            textAlign: TextAlign.center,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          'نظام محاسبة متكامل للشركات السورية',
          style: TextStyle(
            fontSize: 18,
            color: RevolutionaryColors.textSecondary,
            fontWeight: FontWeight.w500,
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 20),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
          decoration: BoxDecoration(
            color: RevolutionaryColors.jasminePetal,
            borderRadius: BorderRadius.circular(25),
            border: Border.all(
              color: RevolutionaryColors.damascusSky.withValues(alpha: 0.3),
              width: 1,
            ),
          ),
          child: Text(
            'أدخل كلمة مرور قاعدة البيانات للمتابعة',
            style: TextStyle(
              fontSize: 16,
              color: RevolutionaryColors.damascusSky,
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),
        ),
      ],
    );
  }

  Widget _buildEnhancedForm() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.9),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: RevolutionaryColors.damascusSky.withValues(alpha: 0.1),
            blurRadius: 20,
            spreadRadius: 5,
          ),
        ],
      ),
      padding: const EdgeInsets.all(24),
      child: Form(
        key: _formKey,
        child: TextFormField(
          controller: _passwordController,
          obscureText: !_isPasswordVisible,
          enabled: !_isLoading,
          style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
          decoration: InputDecoration(
            labelText: 'كلمة مرور قاعدة البيانات',
            labelStyle: TextStyle(
              color: RevolutionaryColors.damascusSky,
              fontWeight: FontWeight.w500,
            ),
            prefixIcon: Icon(
              Icons.lock_outline,
              color: RevolutionaryColors.damascusSky,
            ),
            suffixIcon: IconButton(
              icon: Icon(
                _isPasswordVisible ? Icons.visibility_off : Icons.visibility,
                color: RevolutionaryColors.damascusSky,
              ),
              onPressed: () {
                setState(() {
                  _isPasswordVisible = !_isPasswordVisible;
                });
              },
            ),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(15),
              borderSide: BorderSide(
                color: RevolutionaryColors.damascusSky.withValues(alpha: 0.3),
              ),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(15),
              borderSide: BorderSide(
                color: RevolutionaryColors.damascusSky.withValues(alpha: 0.3),
              ),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(15),
              borderSide: BorderSide(
                color: RevolutionaryColors.damascusSky,
                width: 2,
              ),
            ),
            filled: true,
            fillColor: RevolutionaryColors.backgroundCard,
            contentPadding: const EdgeInsets.symmetric(
              horizontal: 20,
              vertical: 16,
            ),
          ),
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'كلمة المرور مطلوبة';
            }
            return null;
          },
          onFieldSubmitted: (_) => _handleLogin(),
        ),
      ),
    );
  }

  Widget _buildEnhancedLoginButton() {
    return Container(
      width: double.infinity,
      height: 56,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.centerLeft,
          end: Alignment.centerRight,
          colors: [
            RevolutionaryColors.damascusSky,
            RevolutionaryColors.syrianGold,
          ],
        ),
        borderRadius: BorderRadius.circular(28),
        boxShadow: [
          BoxShadow(
            color: RevolutionaryColors.damascusSky.withValues(alpha: 0.4),
            blurRadius: 15,
            spreadRadius: 2,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: ElevatedButton(
        onPressed: _isLoading || _failedAttempts >= _maxFailedAttempts
            ? null
            : _handleLogin,
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.transparent,
          shadowColor: Colors.transparent,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(28),
          ),
        ),
        child: _isLoading
            ? const SizedBox(
                height: 24,
                width: 24,
                child: CircularProgressIndicator(
                  strokeWidth: 3,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              )
            : Text(
                _failedAttempts >= _maxFailedAttempts
                    ? 'تم حظر المحاولات'
                    : 'تسجيل الدخول',
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                  letterSpacing: 1,
                ),
              ),
      ),
    );
  }

  Widget _buildEnhancedErrorMessage() {
    return AnimatedBuilder(
      animation: _shakeAnimation,
      builder: (context, child) {
        return Transform.translate(
          offset: Offset(
            _shakeAnimation.value * 10 * (1 - _shakeAnimation.value),
            0,
          ),
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  RevolutionaryColors.errorCoral.withValues(alpha: 0.1),
                  RevolutionaryColors.errorCoral.withValues(alpha: 0.05),
                ],
              ),
              borderRadius: BorderRadius.circular(15),
              border: Border.all(
                color: RevolutionaryColors.errorCoral.withValues(alpha: 0.3),
              ),
              boxShadow: [
                BoxShadow(
                  color: RevolutionaryColors.errorCoral.withValues(alpha: 0.2),
                  blurRadius: 10,
                  spreadRadius: 2,
                ),
              ],
            ),
            child: Row(
              children: [
                Icon(
                  Icons.error_outline,
                  color: RevolutionaryColors.errorCoral,
                  size: 24,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    _errorMessage!,
                    style: TextStyle(
                      color: RevolutionaryColors.errorCoral,
                      fontWeight: FontWeight.w500,
                      fontSize: 16,
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildEnhancedAdditionalOptions() {
    return Column(
      children: [
        if (_failedAttempts > 0) ...[
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              color: RevolutionaryColors.warningAmber,
              borderRadius: BorderRadius.circular(20),
              border: Border.all(
                color: RevolutionaryColors.warningAmber.withValues(alpha: 0.3),
              ),
            ),
            child: Text(
              'المحاولات المتبقية: ${_maxFailedAttempts - _failedAttempts}',
              style: TextStyle(
                color: RevolutionaryColors.warningAmber,
                fontWeight: FontWeight.bold,
                fontSize: 14,
              ),
            ),
          ),
          const SizedBox(height: 20),
        ],

        Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(25),
            border: Border.all(),
          ),
          child: TextButton(
            onPressed: _isLoading ? null : _showResetDialog,
            style: TextButton.styleFrom(
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(25),
              ),
            ),
            child: Text(
              'نسيت كلمة المرور؟',
              style: TextStyle(
                color: RevolutionaryColors.damascusSky,
                fontWeight: FontWeight.w600,
                fontSize: 16,
              ),
            ),
          ),
        ),
      ],
    );
  }

  Future<void> _handleLogin() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final password = _passwordController.text;

      // تهيئة قاعدة البيانات مع كلمة المرور
      final databaseHelper = DatabaseHelper();
      final success = await databaseHelper.initializeDatabase(password);

      if (success) {
        LoggingService.security('تم تسجيل الدخول بنجاح', category: 'Login');

        if (mounted) {
          Navigator.of(context).pushReplacement(
            MaterialPageRoute(
              builder: (context) => const RevolutionaryHomeScreen(),
            ),
          );
        }
      } else {
        _failedAttempts++;
        LoggingService.security(
          'فشل في تسجيل الدخول - المحاولة $_failedAttempts',
          category: 'Login',
        );

        setState(() {
          if (_failedAttempts >= _maxFailedAttempts) {
            _errorMessage = 'تم حظر المحاولات بسبب كثرة المحاولات الفاشلة';
          } else {
            _errorMessage =
                'كلمة مرور خاطئة. المحاولات المتبقية: ${_maxFailedAttempts - _failedAttempts}';
          }
        });

        // تأثير الاهتزاز عند الخطأ
        _shakeController.forward().then((_) {
          _shakeController.reset();
        });
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'حدث خطأ في تسجيل الدخول: ${e.toString()}';
      });

      // تأثير الاهتزاز عند الخطأ
      _shakeController.forward().then((_) {
        _shakeController.reset();
      });
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _showResetDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إعادة تعيين كلمة المرور'),
        content: const Text(
          'تحذير: إعادة تعيين كلمة المرور سيؤدي إلى فقدان جميع البيانات المشفرة.\n\nهل أنت متأكد من المتابعة؟',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.of(context).pop();
              await _resetEncryption();
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text(
              'إعادة تعيين',
              style: TextStyle(color: Colors.white),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _resetEncryption() async {
    final success = await EncryptionService.resetEncryption();
    if (success && mounted) {
      Navigator.of(context).pushReplacement(
        MaterialPageRoute(
          builder: (context) => const PasswordSetupScreen(isFirstTime: true),
        ),
      );
    }
  }
}
