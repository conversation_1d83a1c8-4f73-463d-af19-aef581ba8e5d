/// خدمة النظام الضريبي السوري
/// تدير حساب وإدارة جميع أنواع الضرائب السورية
library;

import 'dart:convert';
import 'package:sqflite/sqflite.dart';
import '../database/database_helper.dart';
import '../models/syrian_tax_models.dart';

class SyrianTaxService {
  static final SyrianTaxService _instance = SyrianTaxService._internal();
  factory SyrianTaxService() => _instance;
  SyrianTaxService._internal();

  final DatabaseHelper _databaseHelper = DatabaseHelper();

  /// إنشاء جداول النظام الضريبي
  Future<void> createTables(Database db) async {
    // جدول معلومات دافعي الضرائب
    await db.execute('''
      CREATE TABLE IF NOT EXISTS taxpayer_info (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        tax_number TEXT UNIQUE NOT NULL,
        category TEXT NOT NULL,
        address TEXT NOT NULL,
        phone TEXT NOT NULL,
        email TEXT NOT NULL,
        business_activity TEXT NOT NULL,
        registration_date TEXT NOT NULL,
        is_active INTEGER DEFAULT 1,
        additional_info TEXT
      )
    ''');

    // جدول حسابات الضرائب
    await db.execute('''
      CREATE TABLE IF NOT EXISTS tax_calculations (
        id TEXT PRIMARY KEY,
        tax_type TEXT NOT NULL,
        taxable_amount REAL NOT NULL,
        tax_rate REAL NOT NULL,
        tax_amount REAL NOT NULL,
        exempt_amount REAL DEFAULT 0,
        deductible_amount REAL DEFAULT 0,
        calculation_date TEXT NOT NULL,
        period TEXT NOT NULL,
        details TEXT
      )
    ''');

    // جدول الإقرارات الضريبية
    await db.execute('''
      CREATE TABLE IF NOT EXISTS tax_declarations (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        declaration_number TEXT UNIQUE NOT NULL,
        type TEXT NOT NULL,
        tax_type TEXT NOT NULL,
        taxpayer_id TEXT NOT NULL,
        period TEXT NOT NULL,
        from_date TEXT NOT NULL,
        to_date TEXT NOT NULL,
        submission_date TEXT NOT NULL,
        due_date TEXT,
        status TEXT NOT NULL,
        calculations TEXT NOT NULL,
        total_tax_amount REAL NOT NULL,
        total_paid_amount REAL DEFAULT 0,
        remaining_amount REAL NOT NULL,
        notes TEXT,
        attachments TEXT,
        FOREIGN KEY (taxpayer_id) REFERENCES taxpayer_info (id)
      )
    ''');

    // جدول التقارير الضريبية
    await db.execute('''
      CREATE TABLE IF NOT EXISTS tax_reports (
        id TEXT PRIMARY KEY,
        title TEXT NOT NULL,
        tax_type TEXT NOT NULL,
        period TEXT NOT NULL,
        from_date TEXT NOT NULL,
        to_date TEXT NOT NULL,
        generated_date TEXT NOT NULL,
        data TEXT NOT NULL,
        calculations TEXT NOT NULL,
        total_tax_amount REAL NOT NULL,
        generated_by TEXT NOT NULL
      )
    ''');

    // جدول إعدادات الضرائب
    await db.execute('''
      CREATE TABLE IF NOT EXISTS tax_settings (
        id INTEGER PRIMARY KEY,
        enable_automatic_calculation INTEGER DEFAULT 1,
        enable_tax_reminders INTEGER DEFAULT 1,
        reminder_days_before INTEGER DEFAULT 7,
        default_taxpayer_id TEXT,
        enabled_tax_types TEXT,
        custom_rates TEXT
      )
    ''');
  }

  /// حفظ معلومات دافع الضرائب
  Future<void> saveTaxpayerInfo(TaxpayerInfo taxpayer) async {
    final db = await _databaseHelper.database;

    await db.insert('taxpayer_info', {
      'id': taxpayer.id,
      'name': taxpayer.name,
      'tax_number': taxpayer.taxNumber,
      'category': taxpayer.category.name,
      'address': taxpayer.address,
      'phone': taxpayer.phone,
      'email': taxpayer.email,
      'business_activity': taxpayer.businessActivity,
      'registration_date': taxpayer.registrationDate.toIso8601String(),
      'is_active': taxpayer.isActive ? 1 : 0,
      'additional_info': jsonEncode(taxpayer.additionalInfo),
    }, conflictAlgorithm: ConflictAlgorithm.replace);
  }

  /// الحصول على معلومات دافع الضرائب
  Future<TaxpayerInfo?> getTaxpayerInfo(String id) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'taxpayer_info',
      where: 'id = ?',
      whereArgs: [id],
    );

    if (maps.isNotEmpty) {
      return _mapToTaxpayerInfo(maps.first);
    }
    return null;
  }

  /// حساب ضريبة الدخل للأفراد
  Future<TaxCalculation> calculateIncomeTax({
    required double grossIncome,
    required double deductions,
    required String period,
    TaxpayerCategory category = TaxpayerCategory.individual,
  }) async {
    final taxableIncome = grossIncome - deductions;
    double taxAmount = 0.0;

    if (category == TaxpayerCategory.individual) {
      // حساب ضريبة الدخل التصاعدية للأفراد
      taxAmount = _calculateProgressiveIncomeTax(taxableIncome);
    } else if (category == TaxpayerCategory.company) {
      // ضريبة ثابتة 25% للشركات
      taxAmount = taxableIncome * 0.25;
    }

    return TaxCalculation(
      id: 'income_tax_${DateTime.now().millisecondsSinceEpoch}',
      taxType: SyrianTaxType.incomeTax,
      taxableAmount: taxableIncome,
      taxRate: taxAmount / taxableIncome,
      taxAmount: taxAmount,
      deductibleAmount: deductions,
      calculationDate: DateTime.now(),
      period: period,
      details: {
        'grossIncome': grossIncome,
        'deductions': deductions,
        'category': category.name,
      },
    );
  }

  /// حساب ضريبة المبيعات
  Future<TaxCalculation> calculateSalesTax({
    required double salesAmount,
    required String itemCategory,
    required String period,
  }) async {
    double taxRate = 0.11; // المعدل الافتراضي 11%

    // تحديد المعدل حسب فئة السلعة
    switch (itemCategory.toLowerCase()) {
      case 'luxury':
        taxRate = 0.15; // 15% للسلع الكمالية
        break;
      case 'essential':
        taxRate = 0.0; // معفاة للسلع الأساسية
        break;
      default:
        taxRate = 0.11; // 11% للسلع العادية
    }

    final taxAmount = salesAmount * taxRate;

    return TaxCalculation(
      id: 'sales_tax_${DateTime.now().millisecondsSinceEpoch}',
      taxType: SyrianTaxType.salesTax,
      taxableAmount: salesAmount,
      taxRate: taxRate,
      taxAmount: taxAmount,
      calculationDate: DateTime.now(),
      period: period,
      details: {'itemCategory': itemCategory, 'appliedRate': taxRate},
    );
  }

  /// حساب رسم الخدمات
  Future<TaxCalculation> calculateServiceFeeTax({
    required double serviceAmount,
    required String period,
  }) async {
    const taxRate = 0.05; // 5% رسم خدمات
    final taxAmount = serviceAmount * taxRate;

    return TaxCalculation(
      id: 'service_fee_${DateTime.now().millisecondsSinceEpoch}',
      taxType: SyrianTaxType.servicesFeeTax,
      taxableAmount: serviceAmount,
      taxRate: taxRate,
      taxAmount: taxAmount,
      calculationDate: DateTime.now(),
      period: period,
    );
  }

  /// إنشاء إقرار ضريبي
  Future<int> createTaxDeclaration(TaxDeclaration declaration) async {
    final db = await _databaseHelper.database;

    return await db.insert('tax_declarations', {
      'declaration_number': declaration.declarationNumber,
      'type': declaration.type.name,
      'tax_type': declaration.taxType.name,
      'taxpayer_id': declaration.taxpayerId,
      'period': declaration.period,
      'from_date': declaration.fromDate.toIso8601String(),
      'to_date': declaration.toDate.toIso8601String(),
      'submission_date': declaration.submissionDate.toIso8601String(),
      'due_date': declaration.dueDate?.toIso8601String(),
      'status': declaration.status.name,
      'calculations': jsonEncode(
        declaration.calculations.map((c) => c.toJson()).toList(),
      ),
      'total_tax_amount': declaration.totalTaxAmount,
      'total_paid_amount': declaration.totalPaidAmount,
      'remaining_amount': declaration.remainingAmount,
      'notes': declaration.notes,
      'attachments': jsonEncode(declaration.attachments),
    });
  }

  /// الحصول على جميع الإقرارات الضريبية
  Future<List<TaxDeclaration>> getAllTaxDeclarations() async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'tax_declarations',
      orderBy: 'submission_date DESC',
    );

    return maps.map((map) => _mapToTaxDeclaration(map)).toList();
  }

  /// إنشاء تقرير ضريبة الدخل السنوي
  Future<TaxReport> generateAnnualIncomeTaxReport({
    required String taxpayerId,
    required int year,
  }) async {
    final fromDate = DateTime(year, 1, 1);
    final toDate = DateTime(year, 12, 31);

    // جمع البيانات المالية للسنة
    final revenues = await _getRevenueForPeriod(fromDate, toDate);
    final expenses = await _getExpensesForPeriod(fromDate, toDate);
    final grossIncome = revenues - expenses;

    // حساب الخصومات المسموحة
    final allowedDeductions = await _calculateAllowedDeductions(
      taxpayerId,
      fromDate,
      toDate,
    );

    // حساب ضريبة الدخل
    final taxCalculation = await calculateIncomeTax(
      grossIncome: grossIncome,
      deductions: allowedDeductions,
      period: year.toString(),
    );

    return TaxReport(
      id: 'income_tax_report_${year}_${DateTime.now().millisecondsSinceEpoch}',
      title: 'تقرير ضريبة الدخل السنوي - $year',
      taxType: SyrianTaxType.incomeTax,
      period: year.toString(),
      fromDate: fromDate,
      toDate: toDate,
      generatedDate: DateTime.now(),
      data: {
        'revenues': revenues,
        'expenses': expenses,
        'grossIncome': grossIncome,
        'allowedDeductions': allowedDeductions,
        'taxableIncome': grossIncome - allowedDeductions,
      },
      calculations: [taxCalculation],
      totalTaxAmount: taxCalculation.taxAmount,
      generatedBy: 'system',
    );
  }

  /// إنشاء تقرير ضريبة المبيعات الشهري
  Future<TaxReport> generateMonthlySalesTaxReport({
    required int year,
    required int month,
  }) async {
    final fromDate = DateTime(year, month, 1);
    final toDate = DateTime(year, month + 1, 0);

    // جمع بيانات المبيعات للشهر
    final salesData = await _getSalesDataForPeriod(fromDate, toDate);
    final calculations = <TaxCalculation>[];
    double totalTaxAmount = 0.0;

    // حساب ضريبة المبيعات لكل فئة
    for (final category in salesData.keys) {
      final salesAmount = salesData[category]!;
      final taxCalc = await calculateSalesTax(
        salesAmount: salesAmount,
        itemCategory: category,
        period: '$year-${month.toString().padLeft(2, '0')}',
      );
      calculations.add(taxCalc);
      totalTaxAmount += taxCalc.taxAmount;
    }

    return TaxReport(
      id: 'sales_tax_report_${year}_${month}_${DateTime.now().millisecondsSinceEpoch}',
      title: 'تقرير ضريبة المبيعات - $month/$year',
      taxType: SyrianTaxType.salesTax,
      period: '$year-${month.toString().padLeft(2, '0')}',
      fromDate: fromDate,
      toDate: toDate,
      generatedDate: DateTime.now(),
      data: {
        'salesByCategory': salesData,
        'totalSales': salesData.values.fold(0.0, (a, b) => a + b),
      },
      calculations: calculations,
      totalTaxAmount: totalTaxAmount,
      generatedBy: 'system',
    );
  }

  /// حساب ضريبة الدخل التصاعدية
  double _calculateProgressiveIncomeTax(double taxableIncome) {
    double tax = 0.0;

    // الشرائح الضريبية للأفراد في سوريا
    final brackets = [
      {'min': 0.0, 'max': 600000.0, 'rate': 0.0},
      {'min': 600001.0, 'max': 1200000.0, 'rate': 0.05},
      {'min': 1200001.0, 'max': 2400000.0, 'rate': 0.10},
      {'min': 2400001.0, 'max': 4800000.0, 'rate': 0.15},
      {'min': 4800001.0, 'max': double.infinity, 'rate': 0.20},
    ];

    for (final bracket in brackets) {
      final min = bracket['min'] as double;
      final max = bracket['max'] as double;
      final rate = bracket['rate'] as double;

      if (taxableIncome > min) {
        final taxableInBracket = (taxableIncome > max)
            ? max - min
            : taxableIncome - min;
        tax += taxableInBracket * rate;
      }
    }

    return tax;
  }

  /// الحصول على الإيرادات للفترة
  Future<double> _getRevenueForPeriod(
    DateTime fromDate,
    DateTime toDate,
  ) async {
    try {
      final db = await _databaseHelper.database;
      final result = await db.rawQuery(
        '''
        SELECT COALESCE(SUM(je.credit), 0) as total_revenue
        FROM journal_entries je
        JOIN accounts a ON je.account_id = a.id
        WHERE a.account_type = 'revenue'
        AND je.entry_date BETWEEN ? AND ?
        AND je.is_posted = 1
      ''',
        [fromDate.toIso8601String(), toDate.toIso8601String()],
      );

      return result.first['total_revenue'] as double? ?? 0.0;
    } catch (e) {
      return 0.0;
    }
  }

  /// الحصول على المصروفات للفترة
  Future<double> _getExpensesForPeriod(
    DateTime fromDate,
    DateTime toDate,
  ) async {
    try {
      final db = await _databaseHelper.database;
      final result = await db.rawQuery(
        '''
        SELECT COALESCE(SUM(je.debit), 0) as total_expenses
        FROM journal_entries je
        JOIN accounts a ON je.account_id = a.id
        WHERE a.account_type = 'expense'
        AND je.entry_date BETWEEN ? AND ?
        AND je.is_posted = 1
      ''',
        [fromDate.toIso8601String(), toDate.toIso8601String()],
      );

      return result.first['total_expenses'] as double? ?? 0.0;
    } catch (e) {
      return 0.0;
    }
  }

  /// حساب الخصومات المسموحة
  Future<double> _calculateAllowedDeductions(
    String taxpayerId,
    DateTime fromDate,
    DateTime toDate,
  ) async {
    try {
      final db = await _databaseHelper.database;
      // حساب الخصومات المسموحة مثل الاستهلاك والمصروفات المعتمدة
      final result = await db.rawQuery(
        '''
        SELECT COALESCE(SUM(je.debit), 0) as total_deductions
        FROM journal_entries je
        JOIN accounts a ON je.account_id = a.id
        WHERE a.account_name LIKE '%استهلاك%'
        OR a.account_name LIKE '%خصم%'
        OR a.account_name LIKE '%مصروف معتمد%'
        AND je.entry_date BETWEEN ? AND ?
        AND je.is_posted = 1
      ''',
        [fromDate.toIso8601String(), toDate.toIso8601String()],
      );

      return result.first['total_deductions'] as double? ?? 0.0;
    } catch (e) {
      return 0.0;
    }
  }

  /// الحصول على بيانات المبيعات للفترة
  Future<Map<String, double>> _getSalesDataForPeriod(
    DateTime fromDate,
    DateTime toDate,
  ) async {
    try {
      final db = await _databaseHelper.database;
      final result = await db.rawQuery(
        '''
        SELECT
          CASE
            WHEN i.category LIKE '%كمالي%' OR i.category LIKE '%luxury%' THEN 'luxury'
            WHEN i.category LIKE '%أساسي%' OR i.category LIKE '%essential%' THEN 'essential'
            ELSE 'standard'
          END as item_category,
          COALESCE(SUM(inv.total_amount), 0) as total_sales
        FROM invoices inv
        JOIN invoice_items ii ON inv.id = ii.invoice_id
        JOIN items i ON ii.item_id = i.id
        WHERE inv.invoice_date BETWEEN ? AND ?
        AND inv.status = 'confirmed'
        GROUP BY item_category
      ''',
        [fromDate.toIso8601String(), toDate.toIso8601String()],
      );

      final salesData = <String, double>{
        'standard': 0.0,
        'luxury': 0.0,
        'essential': 0.0,
      };

      for (final row in result) {
        final category = row['item_category'] as String;
        final amount = row['total_sales'] as double? ?? 0.0;
        salesData[category] = amount;
      }

      return salesData;
    } catch (e) {
      return {'standard': 0.0, 'luxury': 0.0, 'essential': 0.0};
    }
  }

  /// تحويل البيانات إلى معلومات دافع الضرائب
  TaxpayerInfo _mapToTaxpayerInfo(Map<String, dynamic> map) {
    return TaxpayerInfo(
      id: map['id'],
      name: map['name'],
      taxNumber: map['tax_number'],
      category: TaxpayerCategory.values.firstWhere(
        (e) => e.name == map['category'],
      ),
      address: map['address'],
      phone: map['phone'],
      email: map['email'],
      businessActivity: map['business_activity'],
      registrationDate: DateTime.parse(map['registration_date']),
      isActive: map['is_active'] == 1,
      additionalInfo: map['additional_info'] != null
          ? jsonDecode(map['additional_info'])
          : {},
    );
  }

  /// تحويل البيانات إلى إقرار ضريبي
  TaxDeclaration _mapToTaxDeclaration(Map<String, dynamic> map) {
    return TaxDeclaration(
      id: map['id'],
      declarationNumber: map['declaration_number'],
      type: TaxDeclarationType.values.firstWhere((e) => e.name == map['type']),
      taxType: SyrianTaxType.values.firstWhere(
        (e) => e.name == map['tax_type'],
      ),
      taxpayerId: map['taxpayer_id'],
      period: map['period'],
      fromDate: DateTime.parse(map['from_date']),
      toDate: DateTime.parse(map['to_date']),
      submissionDate: DateTime.parse(map['submission_date']),
      dueDate: map['due_date'] != null ? DateTime.parse(map['due_date']) : null,
      status: TaxDeclarationStatus.values.firstWhere(
        (e) => e.name == map['status'],
      ),
      calculations: (jsonDecode(map['calculations']) as List)
          .map((c) => TaxCalculation.fromJson(c))
          .toList(),
      totalTaxAmount: map['total_tax_amount'],
      totalPaidAmount: map['total_paid_amount'],
      remainingAmount: map['remaining_amount'],
      notes: map['notes'] ?? '',
      attachments: map['attachments'] != null
          ? jsonDecode(map['attachments'])
          : {},
    );
  }
}
