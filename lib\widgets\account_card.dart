import 'package:flutter/material.dart';
import '../constants/revolutionary_design_colors.dart';
import '../models/account.dart';

class AccountCard extends StatelessWidget {
  final Account account;
  final VoidCallback onTap;
  final VoidCallback onEdit;
  final VoidCallback onDelete;

  const AccountCard({
    super.key,
    required this.account,
    required this.onTap,
    required this.onEdit,
    required this.onDelete,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: InkWell(
        borderRadius: BorderRadius.circular(12),
        onTap: onTap,
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  // أيقونة نوع الحساب
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: _getAccountTypeColor().withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      account.accountIcon,
                      style: const TextStyle(fontSize: 20),
                    ),
                  ),
                  const SizedBox(width: 12),
                  // معلومات الحساب
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Expanded(
                              child: Text(
                                account.name,
                                style: Theme.of(context).textTheme.titleMedium
                                    ?.copyWith(
                                      fontWeight: FontWeight.w600,
                                      color: RevolutionaryColors.textPrimary,
                                    ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                            if (!account.isActive)
                              Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 8,
                                  vertical: 2,
                                ),
                                decoration: BoxDecoration(
                                  color: RevolutionaryColors.errorCoral.withValues(alpha: 0.1),
                                  borderRadius: BorderRadius.circular(4),
                                ),
                                child: Text(
                                  'غير نشط',
                                  style: Theme.of(context).textTheme.bodySmall
                                      ?.copyWith(
                                        color: RevolutionaryColors.errorCoral,
                                        fontSize: 10,
                                      ),
                                ),
                              ),
                          ],
                        ),
                        const SizedBox(height: 4),
                        Row(
                          children: [
                            Text(
                              'كود: ${account.code}',
                              style: Theme.of(context).textTheme.bodySmall
                                  ?.copyWith(color: RevolutionaryColors.textSecondary),
                            ),
                            const SizedBox(width: 16),
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 6,
                                vertical: 2,
                              ),
                              decoration: BoxDecoration(
                                color: _getAccountTypeColor().withValues(alpha: 0.1),
                                borderRadius: BorderRadius.circular(4),
                              ),
                              child: Text(
                                account.accountTypeArabic,
                                style: Theme.of(context).textTheme.bodySmall
                                    ?.copyWith(
                                      color: _getAccountTypeColor(),
                                      fontSize: 10,
                                    ),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                  // قائمة الخيارات
                  PopupMenuButton<String>(
                    onSelected: (value) {
                      switch (value) {
                        case 'edit':
                          onEdit();
                          break;
                        case 'delete':
                          onDelete();
                          break;
                      }
                    },
                    itemBuilder: (context) => [
                      const PopupMenuItem(
                        value: 'edit',
                        child: Row(
                          children: [
                            Icon(Icons.edit, size: 18),
                            SizedBox(width: 8),
                            Text('تعديل'),
                          ],
                        ),
                      ),
                      const PopupMenuItem(
                        value: 'delete',
                        child: Row(
                          children: [
                            Icon(Icons.delete, size: 18, color: Colors.red),
                            SizedBox(width: 8),
                            Text('حذف', style: TextStyle(color: Colors.red)),
                          ],
                        ),
                      ),
                    ],
                    child: const Icon(
                      Icons.more_vert,
                      color: RevolutionaryColors.textSecondary,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              // الرصيد
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'الرصيد:',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: RevolutionaryColors.textSecondary,
                    ),
                  ),
                  Text(
                    '${account.displayBalance} ل.س',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: account.balance >= 0
                          ? RevolutionaryColors.successGlow
                          : RevolutionaryColors.errorCoral,
                    ),
                  ),
                ],
              ),
              // الوصف (إذا كان موجوداً)
              if (account.description != null &&
                  account.description!.isNotEmpty) ...[
                const SizedBox(height: 8),
                Text(
                  account.description!,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: RevolutionaryColors.textSecondary,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Color _getAccountTypeColor() {
    switch (account.type) {
      case 'asset':
        return RevolutionaryColors.successGlow;
      case 'liability':
        return RevolutionaryColors.warningAmber;
      case 'equity':
        return RevolutionaryColors.infoTurquoise;
      case 'revenue':
        return RevolutionaryColors.successGlow;
      case 'expense':
        return RevolutionaryColors.errorCoral;
      case 'purchase':
        return RevolutionaryColors.oliveBranch;
      case 'sale':
        return RevolutionaryColors.damascusSky;
      case 'inventory':
        return RevolutionaryColors.infoTurquoise;
      default:
        return RevolutionaryColors.textSecondary;
    }
  }
}
