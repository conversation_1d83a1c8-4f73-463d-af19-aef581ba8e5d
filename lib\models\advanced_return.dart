/// نموذج إدارة المرتجعات المتقدمة
/// يوفر نظام شامل لإدارة مرتجعات المبيعات والمشتريات
library;

/// أسباب الإرجاع
enum ReturnReason {
  defective('معيب'),
  wrongItem('صنف خاطئ'),
  customerRequest('طلب العميل'),
  qualityIssue('مشكلة في الجودة'),
  damaged('تالف'),
  expired('منتهي الصلاحية'),
  other('أخرى');

  const ReturnReason(this.arabicName);
  final String arabicName;
}

/// حالة المرتجع
enum ReturnStatus {
  pending('في الانتظار'),
  approved('موافق عليه'),
  rejected('مرفوض'),
  processed('تم المعالجة'),
  refunded('تم الاسترداد'),
  exchanged('تم الاستبدال'),
  cancelled('ملغي');

  const ReturnStatus(this.arabicName);
  final String arabicName;
}

/// نوع المرتجع
enum ReturnType {
  salesReturn('مرتجع مبيعات'),
  purchaseReturn('مرتجع مشتريات');

  const ReturnType(this.arabicName);
  final String arabicName;
}

/// نوع المعالجة
enum ReturnProcessingType {
  refund('استرداد نقدي'),
  exchange('استبدال'),
  credit('رصيد دائن'),
  repair('إصلاح');

  const ReturnProcessingType(this.arabicName);
  final String arabicName;
}

/// نموذج المرتجع المتقدم
class AdvancedReturn {
  final int? id;
  final String returnNumber;
  final DateTime returnDate;
  final ReturnType type;
  final int originalInvoiceId;
  final String originalInvoiceNumber;
  final int? customerId;
  final int? supplierId;
  final String? customerName;
  final String? supplierName;
  final ReturnReason reason;
  final String? reasonDescription;
  final ReturnStatus status;
  final ReturnProcessingType? processingType;
  final double totalAmount;
  final double processedAmount;
  final double remainingAmount;
  final int currencyId;
  final String? notes;
  final String? internalNotes;
  final DateTime? approvalDate;
  final int? approvedBy;
  final DateTime? processedDate;
  final int? processedBy;
  final String? attachments; // JSON array of file paths
  final bool requiresApproval;
  final int priority; // 1-5, 5 being highest
  final DateTime createdAt;
  final DateTime updatedAt;
  final List<AdvancedReturnItem> items;

  AdvancedReturn({
    this.id,
    required this.returnNumber,
    required this.returnDate,
    required this.type,
    required this.originalInvoiceId,
    required this.originalInvoiceNumber,
    this.customerId,
    this.supplierId,
    this.customerName,
    this.supplierName,
    required this.reason,
    this.reasonDescription,
    this.status = ReturnStatus.pending,
    this.processingType,
    this.totalAmount = 0.0,
    this.processedAmount = 0.0,
    this.remainingAmount = 0.0,
    required this.currencyId,
    this.notes,
    this.internalNotes,
    this.approvalDate,
    this.approvedBy,
    this.processedDate,
    this.processedBy,
    this.attachments,
    this.requiresApproval = true,
    this.priority = 3,
    DateTime? createdAt,
    DateTime? updatedAt,
    this.items = const [],
  }) : createdAt = createdAt ?? DateTime.now(),
       updatedAt = updatedAt ?? DateTime.now();

  /// تحويل إلى Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'return_number': returnNumber,
      'return_date': returnDate.toIso8601String(),
      'type': type.name,
      'original_invoice_id': originalInvoiceId,
      'original_invoice_number': originalInvoiceNumber,
      'customer_id': customerId,
      'supplier_id': supplierId,
      'customer_name': customerName,
      'supplier_name': supplierName,
      'reason': reason.name,
      'reason_description': reasonDescription,
      'status': status.name,
      'processing_type': processingType?.name,
      'total_amount': totalAmount,
      'processed_amount': processedAmount,
      'remaining_amount': remainingAmount,
      'currency_id': currencyId,
      'notes': notes,
      'internal_notes': internalNotes,
      'approval_date': approvalDate?.toIso8601String(),
      'approved_by': approvedBy,
      'processed_date': processedDate?.toIso8601String(),
      'processed_by': processedBy,
      'attachments': attachments,
      'requires_approval': requiresApproval ? 1 : 0,
      'priority': priority,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  /// إنشاء من Map
  factory AdvancedReturn.fromMap(Map<String, dynamic> map) {
    return AdvancedReturn(
      id: map['id'],
      returnNumber: map['return_number'] ?? '',
      returnDate: DateTime.parse(map['return_date']),
      type: ReturnType.values.firstWhere((e) => e.name == map['type']),
      originalInvoiceId: map['original_invoice_id'],
      originalInvoiceNumber: map['original_invoice_number'] ?? '',
      customerId: map['customer_id'],
      supplierId: map['supplier_id'],
      customerName: map['customer_name'],
      supplierName: map['supplier_name'],
      reason: ReturnReason.values.firstWhere((e) => e.name == map['reason']),
      reasonDescription: map['reason_description'],
      status: ReturnStatus.values.firstWhere((e) => e.name == map['status']),
      processingType: map['processing_type'] != null
          ? ReturnProcessingType.values.firstWhere(
              (e) => e.name == map['processing_type'],
            )
          : null,
      totalAmount: (map['total_amount'] ?? 0.0).toDouble(),
      processedAmount: (map['processed_amount'] ?? 0.0).toDouble(),
      remainingAmount: (map['remaining_amount'] ?? 0.0).toDouble(),
      currencyId: map['currency_id'],
      notes: map['notes'],
      internalNotes: map['internal_notes'],
      approvalDate: map['approval_date'] != null
          ? DateTime.parse(map['approval_date'])
          : null,
      approvedBy: map['approved_by'],
      processedDate: map['processed_date'] != null
          ? DateTime.parse(map['processed_date'])
          : null,
      processedBy: map['processed_by'],
      attachments: map['attachments'],
      requiresApproval: (map['requires_approval'] ?? 1) == 1,
      priority: map['priority'] ?? 3,
      createdAt: DateTime.parse(map['created_at']),
      updatedAt: DateTime.parse(map['updated_at']),
    );
  }

  /// نسخ مع تعديل
  AdvancedReturn copyWith({
    int? id,
    String? returnNumber,
    DateTime? returnDate,
    ReturnType? type,
    int? originalInvoiceId,
    String? originalInvoiceNumber,
    int? customerId,
    int? supplierId,
    String? customerName,
    String? supplierName,
    ReturnReason? reason,
    String? reasonDescription,
    ReturnStatus? status,
    ReturnProcessingType? processingType,
    double? totalAmount,
    double? processedAmount,
    double? remainingAmount,
    int? currencyId,
    String? notes,
    String? internalNotes,
    DateTime? approvalDate,
    int? approvedBy,
    DateTime? processedDate,
    int? processedBy,
    String? attachments,
    bool? requiresApproval,
    int? priority,
    DateTime? createdAt,
    DateTime? updatedAt,
    List<AdvancedReturnItem>? items,
  }) {
    return AdvancedReturn(
      id: id ?? this.id,
      returnNumber: returnNumber ?? this.returnNumber,
      returnDate: returnDate ?? this.returnDate,
      type: type ?? this.type,
      originalInvoiceId: originalInvoiceId ?? this.originalInvoiceId,
      originalInvoiceNumber:
          originalInvoiceNumber ?? this.originalInvoiceNumber,
      customerId: customerId ?? this.customerId,
      supplierId: supplierId ?? this.supplierId,
      customerName: customerName ?? this.customerName,
      supplierName: supplierName ?? this.supplierName,
      reason: reason ?? this.reason,
      reasonDescription: reasonDescription ?? this.reasonDescription,
      status: status ?? this.status,
      processingType: processingType ?? this.processingType,
      totalAmount: totalAmount ?? this.totalAmount,
      processedAmount: processedAmount ?? this.processedAmount,
      remainingAmount: remainingAmount ?? this.remainingAmount,
      currencyId: currencyId ?? this.currencyId,
      notes: notes ?? this.notes,
      internalNotes: internalNotes ?? this.internalNotes,
      approvalDate: approvalDate ?? this.approvalDate,
      approvedBy: approvedBy ?? this.approvedBy,
      processedDate: processedDate ?? this.processedDate,
      processedBy: processedBy ?? this.processedBy,
      attachments: attachments ?? this.attachments,
      requiresApproval: requiresApproval ?? this.requiresApproval,
      priority: priority ?? this.priority,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      items: items ?? this.items,
    );
  }

  /// التحقق من إمكانية الموافقة
  bool get canBeApproved => status == ReturnStatus.pending && requiresApproval;

  /// التحقق من إمكانية المعالجة
  bool get canBeProcessed => status == ReturnStatus.approved;

  /// التحقق من اكتمال المعالجة
  bool get isFullyProcessed => processedAmount >= totalAmount;

  /// نسبة المعالجة
  double get processingPercentage {
    if (totalAmount == 0) return 0.0;
    return (processedAmount / totalAmount) * 100;
  }

  @override
  String toString() {
    return 'AdvancedReturn{id: $id, returnNumber: $returnNumber, status: ${status.arabicName}}';
  }
}

/// عنصر المرتجع
class AdvancedReturnItem {
  final int? id;
  final int returnId;
  final int originalInvoiceItemId;
  final int itemId;
  final String itemName;
  final String? itemCode;
  final double originalQuantity;
  final double returnQuantity;
  final double unitPrice;
  final double totalAmount;
  final String? unit;
  final String? condition; // حالة الصنف المرتجع
  final String? notes;
  final bool isProcessed;
  final DateTime? processedDate;
  final DateTime createdAt;

  AdvancedReturnItem({
    this.id,
    required this.returnId,
    required this.originalInvoiceItemId,
    required this.itemId,
    required this.itemName,
    this.itemCode,
    required this.originalQuantity,
    required this.returnQuantity,
    required this.unitPrice,
    required this.totalAmount,
    this.unit,
    this.condition,
    this.notes,
    this.isProcessed = false,
    this.processedDate,
    DateTime? createdAt,
  }) : createdAt = createdAt ?? DateTime.now();

  /// تحويل إلى Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'return_id': returnId,
      'original_invoice_item_id': originalInvoiceItemId,
      'item_id': itemId,
      'item_name': itemName,
      'item_code': itemCode,
      'original_quantity': originalQuantity,
      'return_quantity': returnQuantity,
      'unit_price': unitPrice,
      'total_amount': totalAmount,
      'unit': unit,
      'condition': condition,
      'notes': notes,
      'is_processed': isProcessed ? 1 : 0,
      'processed_date': processedDate?.toIso8601String(),
      'created_at': createdAt.toIso8601String(),
    };
  }

  /// إنشاء من Map
  factory AdvancedReturnItem.fromMap(Map<String, dynamic> map) {
    return AdvancedReturnItem(
      id: map['id'],
      returnId: map['return_id'],
      originalInvoiceItemId: map['original_invoice_item_id'],
      itemId: map['item_id'],
      itemName: map['item_name'] ?? '',
      itemCode: map['item_code'],
      originalQuantity: (map['original_quantity'] ?? 0.0).toDouble(),
      returnQuantity: (map['return_quantity'] ?? 0.0).toDouble(),
      unitPrice: (map['unit_price'] ?? 0.0).toDouble(),
      totalAmount: (map['total_amount'] ?? 0.0).toDouble(),
      unit: map['unit'],
      condition: map['condition'],
      notes: map['notes'],
      isProcessed: (map['is_processed'] ?? 0) == 1,
      processedDate: map['processed_date'] != null
          ? DateTime.parse(map['processed_date'])
          : null,
      createdAt: DateTime.parse(map['created_at']),
    );
  }

  /// نسخ مع تعديل
  AdvancedReturnItem copyWith({
    int? id,
    int? returnId,
    int? originalInvoiceItemId,
    int? itemId,
    String? itemName,
    String? itemCode,
    double? originalQuantity,
    double? returnQuantity,
    double? unitPrice,
    double? totalAmount,
    String? unit,
    String? condition,
    String? notes,
    bool? isProcessed,
    DateTime? processedDate,
    DateTime? createdAt,
  }) {
    return AdvancedReturnItem(
      id: id ?? this.id,
      returnId: returnId ?? this.returnId,
      originalInvoiceItemId:
          originalInvoiceItemId ?? this.originalInvoiceItemId,
      itemId: itemId ?? this.itemId,
      itemName: itemName ?? this.itemName,
      itemCode: itemCode ?? this.itemCode,
      originalQuantity: originalQuantity ?? this.originalQuantity,
      returnQuantity: returnQuantity ?? this.returnQuantity,
      unitPrice: unitPrice ?? this.unitPrice,
      totalAmount: totalAmount ?? this.totalAmount,
      unit: unit ?? this.unit,
      condition: condition ?? this.condition,
      notes: notes ?? this.notes,
      isProcessed: isProcessed ?? this.isProcessed,
      processedDate: processedDate ?? this.processedDate,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  @override
  String toString() {
    return 'AdvancedReturnItem{id: $id, itemName: $itemName, returnQuantity: $returnQuantity}';
  }
}
