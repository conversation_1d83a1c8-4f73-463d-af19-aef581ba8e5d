/// خدمة إدارة الباركود المتكاملة
/// تتعامل مع إنشاء وقراءة ومعالجة الباركود للأصناف والمواقع
library;

import 'dart:typed_data';
import 'package:barcode/barcode.dart';
import 'package:image/image.dart' as img;
import '../database/database_helper.dart';
import '../models/item.dart';
import '../models/warehouse_location.dart';
import '../services/logging_service.dart';
import '../services/audit_service.dart';
import '../exceptions/validation_exception.dart';
import '../constants/app_constants.dart';

/// أنواع الباركود المدعومة
enum BarcodeType {
  /// Code 128 - الأكثر شيوعاً
  code128('code128', 'Code 128'),

  /// Code 39 - بسيط وموثوق
  code39('code39', 'Code 39'),

  /// EAN-13 - للمنتجات التجارية
  ean13('ean13', 'EAN-13'),

  /// EAN-8 - نسخة مختصرة من EAN-13
  ean8('ean8', 'EAN-8'),

  /// UPC-A - أمريكي
  upcA('upca', 'UPC-A'),

  /// QR Code - للبيانات المعقدة
  qrCode('qrcode', 'QR Code');

  const BarcodeType(this.code, this.displayName);

  final String code;
  final String displayName;

  static BarcodeType fromCode(String code) {
    return BarcodeType.values.firstWhere(
      (type) => type.code == code,
      orElse: () => BarcodeType.code128,
    );
  }

  /// الحصول على نوع الباركود المناسب
  Barcode get barcodeGenerator {
    switch (this) {
      case BarcodeType.code128:
        return Barcode.code128();
      case BarcodeType.code39:
        return Barcode.code39();
      case BarcodeType.ean13:
        return Barcode.ean13();
      case BarcodeType.ean8:
        return Barcode.ean8();
      case BarcodeType.upcA:
        return Barcode.upcA();
      case BarcodeType.qrCode:
        return Barcode.qrCode();
    }
  }
}

/// نموذج بيانات الباركود
class BarcodeData {
  final String code;
  final BarcodeType type;
  final String? itemId;
  final String? locationId;
  final String? warehouseId;
  final Map<String, dynamic>? additionalData;
  final DateTime createdAt;

  BarcodeData({
    required this.code,
    required this.type,
    this.itemId,
    this.locationId,
    this.warehouseId,
    this.additionalData,
    DateTime? createdAt,
  }) : createdAt = createdAt ?? DateTime.now();

  Map<String, dynamic> toMap() {
    return {
      'code': code,
      'type': type.code,
      'item_id': itemId,
      'location_id': locationId,
      'warehouse_id': warehouseId,
      'additional_data': additionalData?.toString(),
      'created_at': createdAt.toIso8601String(),
    };
  }

  factory BarcodeData.fromMap(Map<String, dynamic> map) {
    return BarcodeData(
      code: map['code'] ?? '',
      type: BarcodeType.fromCode(map['type'] ?? 'code128'),
      itemId: map['item_id'],
      locationId: map['location_id'],
      warehouseId: map['warehouse_id'],
      additionalData: map['additional_data'] != null
          ? {'raw': map['additional_data']}
          : null,
      createdAt: DateTime.parse(
        map['created_at'] ?? DateTime.now().toIso8601String(),
      ),
    );
  }
}

class BarcodeService {
  final DatabaseHelper _databaseHelper = DatabaseHelper();

  /// إنشاء جداول الباركود
  Future<void> createTables() async {
    final db = await _databaseHelper.database;

    await db.transaction((txn) async {
      // جدول الباركود
      await txn.execute('''
        CREATE TABLE IF NOT EXISTS barcodes (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          code TEXT NOT NULL UNIQUE,
          type TEXT NOT NULL DEFAULT 'code128',
          entity_type TEXT NOT NULL,
          entity_id INTEGER NOT NULL,
          additional_data TEXT,
          is_active INTEGER NOT NULL DEFAULT 1,
          created_at TEXT NOT NULL,
          updated_at TEXT NOT NULL
        )
      ''');

      // جدول سجل مسح الباركود
      await txn.execute('''
        CREATE TABLE IF NOT EXISTS barcode_scan_history (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          barcode_code TEXT NOT NULL,
          scanned_by TEXT,
          scan_location TEXT,
          scan_purpose TEXT,
          scan_result TEXT,
          additional_info TEXT,
          scanned_at TEXT NOT NULL,
          FOREIGN KEY (barcode_code) REFERENCES barcodes (code)
        )
      ''');

      // إنشاء فهارس للأداء
      await txn.execute(
        'CREATE INDEX IF NOT EXISTS idx_barcodes_code ON barcodes(code)',
      );
      await txn.execute(
        'CREATE INDEX IF NOT EXISTS idx_barcodes_entity ON barcodes(entity_type, entity_id)',
      );
      await txn.execute(
        'CREATE INDEX IF NOT EXISTS idx_scan_history_code ON barcode_scan_history(barcode_code)',
      );
    });

    LoggingService.info('تم إنشاء جداول الباركود', category: 'BarcodeService');
  }

  /// إنشاء باركود للصنف
  Future<String> generateItemBarcode(
    int itemId, {
    BarcodeType type = BarcodeType.code128,
    String? customCode,
  }) async {
    try {
      final db = await _databaseHelper.database;

      // التحقق من وجود الصنف
      final itemResult = await db.query(
        AppConstants.itemsTable,
        where: 'id = ?',
        whereArgs: [itemId],
      );

      if (itemResult.isEmpty) {
        throw ValidationException('الصنف غير موجود');
      }

      // إنشاء كود الباركود
      final barcodeCode =
          customCode ?? await _generateBarcodeCode('ITEM', itemId);

      // التحقق من عدم تكرار الكود
      await _validateUniqueCode(barcodeCode);

      // إدراج الباركود
      await db.insert('barcodes', {
        'code': barcodeCode,
        'type': type.code,
        'entity_type': 'item',
        'entity_id': itemId,
        'created_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
      });

      // تحديث الصنف بكود الباركود
      await db.update(
        AppConstants.itemsTable,
        {'barcode': barcodeCode},
        where: 'id = ?',
        whereArgs: [itemId],
      );

      await AuditService.log(
        action: 'CREATE',
        entityType: 'barcode',
        entityId: itemId,
        entityName: barcodeCode,
        description: 'إنشاء باركود للصنف: $itemId',
        newValues: {'code': barcodeCode, 'type': type.code},
      );

      LoggingService.info(
        'تم إنشاء باركود للصنف',
        category: 'BarcodeService',
        data: {
          'item_id': itemId,
          'barcode_code': barcodeCode,
          'type': type.code,
        },
      );

      return barcodeCode;
    } catch (e) {
      LoggingService.error(
        'خطأ في إنشاء باركود الصنف',
        category: 'BarcodeService',
        data: {'item_id': itemId, 'error': e.toString()},
      );
      rethrow;
    }
  }

  /// إنشاء باركود للموقع
  Future<String> generateLocationBarcode(
    int locationId, {
    BarcodeType type = BarcodeType.qrCode,
    String? customCode,
  }) async {
    try {
      final db = await _databaseHelper.database;

      // التحقق من وجود الموقع
      final locationResult = await db.query(
        'warehouse_locations',
        where: 'id = ?',
        whereArgs: [locationId],
      );

      if (locationResult.isEmpty) {
        throw ValidationException('الموقع غير موجود');
      }

      // إنشاء كود الباركود
      final barcodeCode =
          customCode ?? await _generateBarcodeCode('LOC', locationId);

      // التحقق من عدم تكرار الكود
      await _validateUniqueCode(barcodeCode);

      // إدراج الباركود
      await db.insert('barcodes', {
        'code': barcodeCode,
        'type': type.code,
        'entity_type': 'location',
        'entity_id': locationId,
        'created_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
      });

      // تحديث الموقع بكود الباركود
      await db.update(
        'warehouse_locations',
        {'location_barcode': barcodeCode},
        where: 'id = ?',
        whereArgs: [locationId],
      );

      await AuditService.log(
        action: 'CREATE',
        entityType: 'barcode',
        entityId: locationId,
        entityName: barcodeCode,
        description: 'إنشاء باركود للموقع: $locationId',
        newValues: {'code': barcodeCode, 'type': type.code},
      );

      LoggingService.info(
        'تم إنشاء باركود للموقع',
        category: 'BarcodeService',
        data: {
          'location_id': locationId,
          'barcode_code': barcodeCode,
          'type': type.code,
        },
      );

      return barcodeCode;
    } catch (e) {
      LoggingService.error(
        'خطأ في إنشاء باركود الموقع',
        category: 'BarcodeService',
        data: {'location_id': locationId, 'error': e.toString()},
      );
      rethrow;
    }
  }

  /// إنشاء صورة الباركود
  Future<Uint8List> generateBarcodeImage(
    String code,
    BarcodeType type, {
    int width = 200,
    int height = 80,
    bool showText = true,
  }) async {
    try {
      final barcode = type.barcodeGenerator;

      // التحقق من صحة الكود
      if (!barcode.isValid(code)) {
        throw ValidationException('كود الباركود غير صالح للنوع المحدد');
      }

      // إنشاء صورة الباركود باستخدام مكتبة image
      final image = img.Image(width: width, height: height);
      img.fill(image, color: img.ColorRgb8(255, 255, 255)); // خلفية بيضاء

      // إنشاء SVG للباركود
      final svgString = barcode.toSvg(
        code,
        width: width.toDouble(),
        height: height.toDouble(),
        drawText: showText,
      );

      // تحويل SVG إلى صورة
      _drawBarcodeFromSvg(image, svgString, width, height);

      return Uint8List.fromList(img.encodePng(image));
    } catch (e) {
      LoggingService.error(
        'خطأ في إنشاء صورة الباركود',
        category: 'BarcodeService',
        data: {'code': code, 'type': type.code, 'error': e.toString()},
      );
      rethrow;
    }
  }

  /// البحث عن باركود
  Future<BarcodeData?> findBarcode(String code) async {
    try {
      final db = await _databaseHelper.database;

      final result = await db.query(
        'barcodes',
        where: 'code = ? AND is_active = 1',
        whereArgs: [code],
      );

      if (result.isEmpty) return null;

      return BarcodeData.fromMap(result.first);
    } catch (e) {
      LoggingService.error(
        'خطأ في البحث عن الباركود',
        category: 'BarcodeService',
        data: {'code': code, 'error': e.toString()},
      );
      return null;
    }
  }

  /// الحصول على معلومات الصنف من الباركود
  Future<Item?> getItemByBarcode(String code) async {
    try {
      final db = await _databaseHelper.database;

      final result = await db.rawQuery(
        '''
        SELECT i.* FROM ${AppConstants.itemsTable} i
        JOIN barcodes b ON i.id = b.entity_id
        WHERE b.code = ? AND b.entity_type = 'item' AND b.is_active = 1
      ''',
        [code],
      );

      if (result.isEmpty) {
        // البحث المباشر في جدول الأصناف
        final directResult = await db.query(
          AppConstants.itemsTable,
          where: 'barcode = ?',
          whereArgs: [code],
        );

        if (directResult.isEmpty) return null;
        return Item.fromMap(directResult.first);
      }

      return Item.fromMap(result.first);
    } catch (e) {
      LoggingService.error(
        'خطأ في الحصول على الصنف من الباركود',
        category: 'BarcodeService',
        data: {'code': code, 'error': e.toString()},
      );
      return null;
    }
  }

  /// الحصول على معلومات الموقع من الباركود
  Future<WarehouseLocation?> getLocationByBarcode(String code) async {
    try {
      final db = await _databaseHelper.database;

      final result = await db.rawQuery(
        '''
        SELECT wl.* FROM warehouse_locations wl
        JOIN barcodes b ON wl.id = b.entity_id
        WHERE b.code = ? AND b.entity_type = 'location' AND b.is_active = 1
      ''',
        [code],
      );

      if (result.isEmpty) {
        // البحث المباشر في جدول المواقع
        final directResult = await db.query(
          'warehouse_locations',
          where: 'location_barcode = ?',
          whereArgs: [code],
        );

        if (directResult.isEmpty) return null;
        return WarehouseLocation.fromMap(directResult.first);
      }

      return WarehouseLocation.fromMap(result.first);
    } catch (e) {
      LoggingService.error(
        'خطأ في الحصول على الموقع من الباركود',
        category: 'BarcodeService',
        data: {'code': code, 'error': e.toString()},
      );
      return null;
    }
  }

  /// تسجيل عملية مسح الباركود
  Future<void> recordScan({
    required String barcodeCode,
    String? scannedBy,
    String? scanLocation,
    String? scanPurpose,
    String? scanResult,
    Map<String, dynamic>? additionalInfo,
  }) async {
    try {
      final db = await _databaseHelper.database;

      await db.insert('barcode_scan_history', {
        'barcode_code': barcodeCode,
        'scanned_by': scannedBy,
        'scan_location': scanLocation,
        'scan_purpose': scanPurpose,
        'scan_result': scanResult,
        'additional_info': additionalInfo?.toString(),
        'scanned_at': DateTime.now().toIso8601String(),
      });

      LoggingService.info(
        'تم تسجيل عملية مسح الباركود',
        category: 'BarcodeService',
        data: {
          'barcode_code': barcodeCode,
          'scanned_by': scannedBy,
          'scan_purpose': scanPurpose,
        },
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في تسجيل عملية مسح الباركود',
        category: 'BarcodeService',
        data: {'barcode_code': barcodeCode, 'error': e.toString()},
      );
    }
  }

  /// طرق مساعدة خاصة

  /// إنشاء كود باركود فريد
  Future<String> _generateBarcodeCode(String prefix, int entityId) async {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final baseCode = '$prefix${entityId.toString().padLeft(6, '0')}';

    // إضافة جزء من الوقت لضمان الفرادة
    final timeCode = (timestamp % 10000).toString().padLeft(4, '0');

    return '$baseCode$timeCode';
  }

  /// التحقق من فرادة الكود
  Future<void> _validateUniqueCode(String code) async {
    final db = await _databaseHelper.database;

    final existing = await db.query(
      'barcodes',
      where: 'code = ?',
      whereArgs: [code],
    );

    if (existing.isNotEmpty) {
      throw ValidationException('كود الباركود موجود بالفعل');
    }

    // التحقق من الأصناف
    final itemExists = await db.query(
      AppConstants.itemsTable,
      where: 'barcode = ?',
      whereArgs: [code],
    );

    if (itemExists.isNotEmpty) {
      throw ValidationException('كود الباركود موجود في جدول الأصناف');
    }

    // التحقق من المواقع
    final locationExists = await db.query(
      'warehouse_locations',
      where: 'location_barcode = ?',
      whereArgs: [code],
    );

    if (locationExists.isNotEmpty) {
      throw ValidationException('كود الباركود موجود في جدول المواقع');
    }
  }

  /// تحويل SVG إلى صورة ورسم الباركود
  void _drawBarcodeFromSvg(
    img.Image image,
    String svgString,
    int width,
    int height,
  ) {
    // تحليل SVG وإستخراج عناصر المستطيلات
    final lines = svgString.split('\n');

    // البحث عن عناصر rect في SVG لرسم الخطوط
    for (final line in lines) {
      if (line.contains('<rect')) {
        final xMatch = RegExp(r'x="([^"]*)"').firstMatch(line);
        final yMatch = RegExp(r'y="([^"]*)"').firstMatch(line);
        final widthMatch = RegExp(r'width="([^"]*)"').firstMatch(line);
        final heightMatch = RegExp(r'height="([^"]*)"').firstMatch(line);

        if (xMatch != null &&
            yMatch != null &&
            widthMatch != null &&
            heightMatch != null) {
          final x = double.tryParse(xMatch.group(1) ?? '0') ?? 0;
          final y = double.tryParse(yMatch.group(1) ?? '0') ?? 0;
          final rectWidth = double.tryParse(widthMatch.group(1) ?? '0') ?? 0;
          final rectHeight = double.tryParse(heightMatch.group(1) ?? '0') ?? 0;

          // رسم المستطيل الأسود للباركود
          img.fillRect(
            image,
            x1: x.toInt(),
            y1: y.toInt(),
            x2: (x + rectWidth).toInt(),
            y2: (y + rectHeight).toInt(),
            color: img.ColorRgb8(0, 0, 0), // أسود
          );
        }
      }

      // رسم النص إذا كان مطلوباً
      if (line.contains('<text')) {
        final xMatch = RegExp(r'x="([^"]*)"').firstMatch(line);
        final yMatch = RegExp(r'y="([^"]*)"').firstMatch(line);
        final textMatch = RegExp(r'>([^<]*)<').firstMatch(line);

        if (xMatch != null && yMatch != null && textMatch != null) {
          final x = double.tryParse(xMatch.group(1) ?? '0') ?? 0;
          final y = double.tryParse(yMatch.group(1) ?? '0') ?? 0;
          final text = textMatch.group(1) ?? '';

          // رسم النص (تطبيق بسيط - يمكن تحسينه)
          if (text.isNotEmpty) {
            _drawSimpleText(image, text, x.toInt(), y.toInt());
          }
        }
      }
    }
  }

  /// رسم نص بسيط على الصورة
  void _drawSimpleText(img.Image image, String text, int x, int y) {
    // تطبيق بسيط لرسم النص - يمكن تحسينه باستخدام خط أفضل
    // هذا مجرد مثال بسيط لعرض النص
    final textWidth = text.length * 6; // تقدير عرض النص
    final startX = x - (textWidth ~/ 2); // توسيط النص

    // رسم خلفية بيضاء للنص
    img.fillRect(
      image,
      x1: startX - 2,
      y1: y - 8,
      x2: startX + textWidth + 2,
      y2: y + 2,
      color: img.ColorRgb8(255, 255, 255),
    );

    // رسم النص كنقاط بسيطة (يمكن تحسينه)
    for (int i = 0; i < text.length; i++) {
      final charX = startX + (i * 6);
      _drawSimpleChar(image, text[i], charX, y);
    }
  }

  /// رسم حرف بسيط
  void _drawSimpleChar(img.Image image, String char, int x, int y) {
    // رسم بسيط للأحرف كنقاط - يمكن تحسينه
    final pixels = _getCharPixels(char);
    for (int row = 0; row < pixels.length; row++) {
      for (int col = 0; col < pixels[row].length; col++) {
        if (pixels[row][col] == 1) {
          final pixelX = x + col;
          final pixelY = y - 6 + row;
          if (pixelX >= 0 &&
              pixelX < image.width &&
              pixelY >= 0 &&
              pixelY < image.height) {
            image.setPixel(pixelX, pixelY, img.ColorRgb8(0, 0, 0));
          }
        }
      }
    }
  }

  /// الحصول على نمط البكسل للحرف (تطبيق بسيط)
  List<List<int>> _getCharPixels(String char) {
    // أنماط بسيطة للأرقام - يمكن توسيعها
    switch (char) {
      case '0':
        return [
          [1, 1, 1],
          [1, 0, 1],
          [1, 0, 1],
          [1, 0, 1],
          [1, 1, 1],
        ];
      case '1':
        return [
          [0, 1, 0],
          [1, 1, 0],
          [0, 1, 0],
          [0, 1, 0],
          [1, 1, 1],
        ];
      case '2':
        return [
          [1, 1, 1],
          [0, 0, 1],
          [1, 1, 1],
          [1, 0, 0],
          [1, 1, 1],
        ];
      case '3':
        return [
          [1, 1, 1],
          [0, 0, 1],
          [1, 1, 1],
          [0, 0, 1],
          [1, 1, 1],
        ];
      case '4':
        return [
          [1, 0, 1],
          [1, 0, 1],
          [1, 1, 1],
          [0, 0, 1],
          [0, 0, 1],
        ];
      case '5':
        return [
          [1, 1, 1],
          [1, 0, 0],
          [1, 1, 1],
          [0, 0, 1],
          [1, 1, 1],
        ];
      case '6':
        return [
          [1, 1, 1],
          [1, 0, 0],
          [1, 1, 1],
          [1, 0, 1],
          [1, 1, 1],
        ];
      case '7':
        return [
          [1, 1, 1],
          [0, 0, 1],
          [0, 0, 1],
          [0, 0, 1],
          [0, 0, 1],
        ];
      case '8':
        return [
          [1, 1, 1],
          [1, 0, 1],
          [1, 1, 1],
          [1, 0, 1],
          [1, 1, 1],
        ];
      case '9':
        return [
          [1, 1, 1],
          [1, 0, 1],
          [1, 1, 1],
          [0, 0, 1],
          [1, 1, 1],
        ];
      default:
        // نمط افتراضي للأحرف غير المعرفة
        return [
          [1, 1, 1],
          [1, 0, 1],
          [1, 0, 1],
          [1, 0, 1],
          [1, 1, 1],
        ];
    }
  }
}
