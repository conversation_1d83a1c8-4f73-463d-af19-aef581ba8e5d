import 'dart:typed_data';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter/material.dart';
import 'package:smart_ledger/services/advanced_export_service.dart';
import 'package:smart_ledger/services/chart_pdf_export_service.dart';
import 'package:smart_ledger/widgets/advanced_export_dialog.dart';
import 'package:smart_ledger/widgets/export_options_widget.dart';
import 'package:smart_ledger/constants/revolutionary_design_colors.dart';

void main() {
  group('Advanced Export Service Tests', () {
    test('ExportCustomization should have correct default values', () {
      const customization = ExportCustomization(title: 'Test Report');

      expect(customization.title, equals('Test Report'));
      expect(
        customization.headerColor,
        equals(RevolutionaryColors.damascusSky),
      );
      expect(customization.includeCharts, isTrue);
      expect(customization.includeStatistics, isTrue);
      expect(customization.includeWatermark, isFalse);
      expect(customization.customFooter, isNull);
    });

    test('ExportCustomization should accept custom values', () {
      const customization = ExportCustomization(
        title: 'Custom Report',
        headerColor: RevolutionaryColors.successGlow,
        includeCharts: false,
        includeStatistics: false,
        includeWatermark: true,
        customFooter: 'Custom Footer',
      );

      expect(customization.title, equals('Custom Report'));
      expect(
        customization.headerColor,
        equals(RevolutionaryColors.successGlow),
      );
      expect(customization.includeCharts, isFalse);
      expect(customization.includeStatistics, isFalse);
      expect(customization.includeWatermark, isTrue);
      expect(customization.customFooter, equals('Custom Footer'));
    });

    group('PDF Export Tests', () {
      test('exportAdvancedPDF should handle null data gracefully', () async {
        final result = await AdvancedExportService.exportAdvancedPDF(
          reportType: 'test_report',
          reportData: null,
          filters: {},
        );

        // Should not throw an exception
        expect(result, isA<String?>());
      });

      test('exportAdvancedPDF should handle empty filters', () async {
        final result = await AdvancedExportService.exportAdvancedPDF(
          reportType: 'trial_balance',
          reportData: [],
          filters: {},
        );

        expect(result, isA<String?>());
      });
    });

    group('Excel Export Tests', () {
      test('exportAdvancedExcel should handle null data gracefully', () async {
        final result = await AdvancedExportService.exportAdvancedExcel(
          reportType: 'test_report',
          reportData: null,
          filters: {},
        );

        expect(result, isA<String?>());
      });

      test('exportAdvancedExcel should handle empty filters', () async {
        final result = await AdvancedExportService.exportAdvancedExcel(
          reportType: 'profit_loss',
          reportData: {},
          filters: {},
        );

        expect(result, isA<String?>());
      });
    });
  });

  group('Chart PDF Export Service Tests', () {
    test('ChartExportData should store data correctly', () {
      final testData = ChartExportData(
        title: 'Test Chart',
        imageData: Uint8List.fromList([1, 2, 3, 4]),
        description: 'Test Description',
        metadata: {'key': 'value'},
      );

      expect(testData.title, equals('Test Chart'));
      expect(testData.imageData, equals(Uint8List.fromList([1, 2, 3, 4])));
      expect(testData.description, equals('Test Description'));
      expect(testData.metadata, equals({'key': 'value'}));
    });

    test('ChartSummaryData should store data correctly', () {
      const summaryData = ChartSummaryData(
        indicator: 'Total Sales',
        value: '1000',
        description: 'Total sales for the period',
      );

      expect(summaryData.indicator, equals('Total Sales'));
      expect(summaryData.value, equals('1000'));
      expect(summaryData.description, equals('Total sales for the period'));
    });
  });

  group('Export Widgets Tests', () {
    testWidgets('AdvancedExportDialog should display correctly', (
      WidgetTester tester,
    ) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: AdvancedExportDialog(
              reportType: 'trial_balance',
              reportData: [],
              filters: {'من تاريخ': '2024-01-01', 'إلى تاريخ': '2024-12-31'},
            ),
          ),
        ),
      );

      // التحقق من وجود العناصر الأساسية
      expect(find.text('خيارات التصدير المتقدمة'), findsOneWidget);
      expect(find.text('تنسيق التصدير'), findsOneWidget);
      expect(find.text('عنوان التقرير'), findsOneWidget);
      expect(find.text('لون الرأس'), findsOneWidget);
      expect(find.text('محتوى التقرير'), findsOneWidget);
      expect(find.text('خيارات إضافية'), findsOneWidget);

      // التحقق من وجود أزرار التصدير
      expect(find.text('تصدير PDF'), findsOneWidget);
      expect(find.text('إلغاء'), findsOneWidget);
    });

    testWidgets('ExportOptionsWidget should display correctly', (
      WidgetTester tester,
    ) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ExportOptionsWidget(
              reportType: 'balance_sheet',
              reportData: {},
              filters: {},
            ),
          ),
        ),
      );

      // التحقق من وجود العناصر الأساسية
      expect(find.text('خيارات التصدير'), findsOneWidget);
      expect(find.text('PDF'), findsOneWidget);
      expect(find.text('Excel'), findsOneWidget);
      expect(find.text('خيارات متقدمة'), findsOneWidget);
    });

    testWidgets('ExportToolbarWidget should display correctly', (
      WidgetTester tester,
    ) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ExportToolbarWidget(
              reportType: 'inventory_report',
              reportData: [],
              filters: {},
            ),
          ),
        ),
      );

      // التحقق من وجود أيقونة التصدير
      expect(find.byIcon(Icons.file_download), findsOneWidget);

      // النقر على الأيقونة لفتح القائمة
      await tester.tap(find.byIcon(Icons.file_download));
      await tester.pumpAndSettle();

      // التحقق من وجود خيارات التصدير
      expect(find.text('تصدير PDF'), findsOneWidget);
      expect(find.text('تصدير Excel'), findsOneWidget);
      expect(find.text('خيارات متقدمة'), findsOneWidget);
    });

    testWidgets('Export buttons should be interactive', (
      WidgetTester tester,
    ) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ExportOptionsWidget(
              reportType: 'sales_analysis',
              reportData: [],
              filters: {},
            ),
          ),
        ),
      );

      // العثور على أزرار التصدير
      final pdfButton = find.text('PDF');
      final excelButton = find.text('Excel');
      final advancedButton = find.text('خيارات متقدمة');

      expect(pdfButton, findsOneWidget);
      expect(excelButton, findsOneWidget);
      expect(advancedButton, findsOneWidget);

      // التحقق من أن الأزرار قابلة للنقر
      await tester.tap(advancedButton);
      await tester.pumpAndSettle();

      // يجب أن يفتح حوار الخيارات المتقدمة
      expect(find.text('خيارات التصدير المتقدمة'), findsOneWidget);
    });
  });

  group('Export Integration Tests', () {
    testWidgets('Full export workflow should work', (
      WidgetTester tester,
    ) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ExportOptionsWidget(
              reportType: 'trial_balance',
              reportData: [
                {'account': 'حساب 1', 'debit': 1000.0, 'credit': 0.0},
                {'account': 'حساب 2', 'debit': 0.0, 'credit': 1000.0},
              ],
              filters: {
                'من تاريخ': '2024-01-01',
                'إلى تاريخ': '2024-12-31',
                'نوع الحساب': 'جميع الحسابات',
              },
            ),
          ),
        ),
      );

      // فتح الخيارات المتقدمة
      await tester.tap(find.text('خيارات متقدمة'));
      await tester.pumpAndSettle();

      // التحقق من فتح الحوار
      expect(find.text('خيارات التصدير المتقدمة'), findsOneWidget);

      // تغيير بعض الإعدادات
      await tester.tap(find.text('Excel'));
      await tester.pumpAndSettle();

      // إدخال عنوان مخصص
      final titleField = find.byType(TextField);
      await tester.enterText(titleField, 'ميزان المراجعة المخصص');
      await tester.pumpAndSettle();

      // تغيير إعدادات المحتوى
      final chartsCheckbox = find.text('تضمين الرسوم البيانية');
      await tester.tap(chartsCheckbox);
      await tester.pumpAndSettle();

      // محاولة التصدير (ستفشل في البيئة التجريبية لكن يجب ألا تتعطل)
      final exportButton = find.text('تصدير Excel');
      await tester.tap(exportButton);
      await tester.pumpAndSettle();

      // التحقق من عدم تعطل التطبيق
      expect(tester.takeException(), isNull);
    });
  });
}
