/// شاشة تفاصيل القرض
/// عرض شامل لتفاصيل القرض والأقساط مع إمكانية الإدارة
library;

import 'package:flutter/material.dart';
import '../constants/app_constants.dart';
import '../constants/revolutionary_design_colors.dart';
import '../models/hr_models.dart';
import '../services/loan_service.dart';
import '../services/employee_service.dart';
import '../services/logging_service.dart';
import '../widgets/loading_widget.dart';
import 'loan_installments_screen.dart';

class LoanDetailsScreen extends StatefulWidget {
  final Loan loan;

  const LoanDetailsScreen({super.key, required this.loan});

  @override
  State<LoanDetailsScreen> createState() => _LoanDetailsScreenState();
}

class _LoanDetailsScreenState extends State<LoanDetailsScreen> {
  final LoanService _loanService = LoanService();
  final EmployeeService _employeeService = EmployeeService();

  late Loan _loan;
  Employee? _employee;
  List<LoanInstallment> _installments = [];
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _loan = widget.loan;
    _loadLoanDetails();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('تفاصيل القرض #${_loan.id}'),
        backgroundColor: RevolutionaryColors.damascusSky,
        foregroundColor: Colors.white,
        actions: [
          PopupMenuButton<String>(
            onSelected: _handleMenuAction,
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'installments',
                child: Row(
                  children: [
                    Icon(Icons.list),
                    SizedBox(width: 8),
                    Text('عرض الأقساط'),
                  ],
                ),
              ),
              if (_loan.status == AppConstants.loanStatusActive)
                const PopupMenuItem(
                  value: 'pay_installment',
                  child: Row(
                    children: [
                      Icon(Icons.payment),
                      SizedBox(width: 8),
                      Text('دفع قسط'),
                    ],
                  ),
                ),
              if (_loan.status == AppConstants.loanStatusActive)
                const PopupMenuItem(
                  value: 'close_loan',
                  child: Row(
                    children: [
                      Icon(Icons.close),
                      SizedBox(width: 8),
                      Text('إغلاق القرض'),
                    ],
                  ),
                ),
            ],
          ),
        ],
      ),
      body: _isLoading
          ? const LoadingWidget(message: 'جاري تحميل التفاصيل...')
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildLoanHeader(),
                  const SizedBox(height: 16),
                  _buildEmployeeInfoCard(),
                  const SizedBox(height: 16),
                  _buildLoanDetailsCard(),
                  const SizedBox(height: 16),
                  _buildInstallmentSummaryCard(),
                  const SizedBox(height: 16),
                  _buildProgressCard(),
                  const SizedBox(height: 24),
                  _buildActionButtons(),
                ],
              ),
            ),
    );
  }

  Widget _buildLoanHeader() {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              _getStatusColor(_loan.status),
              _getStatusColor(_loan.status).withValues(alpha: 0.8),
            ],
          ),
          borderRadius: BorderRadius.circular(16),
        ),
        child: Column(
          children: [
            Icon(_getStatusIcon(_loan.status), size: 48, color: Colors.white),
            const SizedBox(height: 12),
            Text(
              _getStatusText(_loan.status),
              style: const TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              '${_loan.amount.toStringAsFixed(0)} ل.س',
              style: const TextStyle(fontSize: 20, color: Colors.white70),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmployeeInfoCard() {
    if (_employee == null) return const SizedBox();

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            CircleAvatar(
              radius: 30,
              backgroundColor: RevolutionaryColors.damascusSky.withValues(
                alpha: 0.1,
              ),
              backgroundImage: _employee!.photoPath != null
                  ? AssetImage(_employee!.photoPath!)
                  : null,
              child: _employee!.photoPath == null
                  ? Icon(
                      Icons.person,
                      size: 30,
                      color: RevolutionaryColors.damascusSky,
                    )
                  : null,
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    _employee!.displayName,
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'رقم الموظف: ${_employee!.employeeNumber}',
                    style: TextStyle(fontSize: 14, color: Colors.grey[600]),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'الراتب الأساسي: ${_employee!.basicSalary.toStringAsFixed(0)} ل.س',
                    style: TextStyle(fontSize: 14, color: Colors.grey[600]),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLoanDetailsCard() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.info, color: RevolutionaryColors.damascusSky),
                const SizedBox(width: 8),
                const Text(
                  'تفاصيل القرض',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildDetailRow('رقم القرض', _loan.id.toString()),
            _buildDetailRow(
              'المبلغ الأصلي',
              '${_loan.amount.toStringAsFixed(0)} ل.س',
            ),
            _buildDetailRow(
              'المبلغ الإجمالي',
              '${_loan.totalAmount.toStringAsFixed(0)} ل.س',
            ),
            _buildDetailRow(
              'المبلغ المتبقي',
              '${_loan.remainingAmount.toStringAsFixed(0)} ل.س',
            ),
            _buildDetailRow('معدل الفائدة', '${_loan.interestRate}%'),
            _buildDetailRow(
              'القسط الشهري',
              '${_loan.monthlyInstallment.toStringAsFixed(0)} ل.س',
            ),
            _buildDetailRow('عدد الأقساط', '${_loan.installments}'),
            _buildDetailRow('تاريخ القرض', _formatDate(_loan.loanDate)),
            if (_loan.firstInstallmentDate != null)
              _buildDetailRow(
                'تاريخ أول قسط',
                _formatDate(_loan.firstInstallmentDate!),
              ),
            if (_loan.purpose != null && _loan.purpose!.isNotEmpty)
              _buildDetailRow('الغرض', _loan.purpose!),
            if (_loan.notes != null && _loan.notes!.isNotEmpty)
              _buildDetailRow('ملاحظات', _loan.notes!),
          ],
        ),
      ),
    );
  }

  Widget _buildInstallmentSummaryCard() {
    final paidInstallments = _installments.where((i) => i.isPaid).length;
    final overdueInstallments = _installments.where((i) => i.isOverdue).length;
    final upcomingInstallments = _installments.where((i) => i.isDueSoon).length;

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.schedule, color: RevolutionaryColors.damascusSky),
                const SizedBox(width: 8),
                const Text(
                  'ملخص الأقساط',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildSummaryItem(
                    'مدفوعة',
                    paidInstallments.toString(),
                    RevolutionaryColors.successGlow,
                    Icons.check_circle,
                  ),
                ),
                Expanded(
                  child: _buildSummaryItem(
                    'متأخرة',
                    overdueInstallments.toString(),
                    RevolutionaryColors.errorCoral,
                    Icons.warning,
                  ),
                ),
                Expanded(
                  child: _buildSummaryItem(
                    'قريبة',
                    upcomingInstallments.toString(),
                    RevolutionaryColors.warningAmber,
                    Icons.schedule,
                  ),
                ),
                Expanded(
                  child: _buildSummaryItem(
                    'الإجمالي',
                    _installments.length.toString(),
                    RevolutionaryColors.infoTurquoise,
                    Icons.list,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProgressCard() {
    final paidInstallments = _installments.where((i) => i.isPaid).length;
    final progress = _loan.installments > 0
        ? (paidInstallments / _loan.installments)
        : 0.0;

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.trending_up, color: RevolutionaryColors.damascusSky),
                const SizedBox(width: 8),
                const Text(
                  'تقدم السداد',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            LinearProgressIndicator(
              value: progress,
              backgroundColor: Colors.grey[300],
              valueColor: AlwaysStoppedAnimation<Color>(
                progress >= 1.0
                    ? RevolutionaryColors.successGlow
                    : RevolutionaryColors.damascusSky,
              ),
              minHeight: 8,
            ),
            const SizedBox(height: 8),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  '$paidInstallments من ${_loan.installments} أقساط',
                  style: const TextStyle(fontSize: 14),
                ),
                Text(
                  '${(progress * 100).toStringAsFixed(1)}%',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: RevolutionaryColors.damascusSky,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButtons() {
    return Row(
      children: [
        Expanded(
          child: ElevatedButton.icon(
            onPressed: () => _navigateToInstallments(),
            icon: const Icon(Icons.list),
            label: const Text('عرض الأقساط'),
            style: ElevatedButton.styleFrom(
              backgroundColor: RevolutionaryColors.damascusSky,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.all(16),
            ),
          ),
        ),
        if (_loan.status == AppConstants.loanStatusActive) ...[
          const SizedBox(width: 16),
          Expanded(
            child: ElevatedButton.icon(
              onPressed: () => _payNextInstallment(),
              icon: const Icon(Icons.payment),
              label: const Text('دفع قسط'),
              style: ElevatedButton.styleFrom(
                backgroundColor: RevolutionaryColors.successGlow,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.all(16),
              ),
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: TextStyle(
                fontWeight: FontWeight.w500,
                color: Colors.grey[600],
              ),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(child: Text(value, style: const TextStyle(fontSize: 16))),
        ],
      ),
    );
  }

  Widget _buildSummaryItem(
    String title,
    String value,
    Color color,
    IconData icon,
  ) {
    return Container(
      padding: const EdgeInsets.all(12),
      margin: const EdgeInsets.symmetric(horizontal: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: const TextStyle(fontSize: 12),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Future<void> _loadLoanDetails() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // تحميل بيانات الموظف
      final employee = await _employeeService.getEmployeeById(_loan.employeeId);

      // تحميل أقساط القرض
      final installments = await _loanService.getLoanInstallments(_loan.id!);

      setState(() {
        _employee = employee;
        _installments = installments;
      });
    } catch (e) {
      LoggingService.error(
        'خطأ في تحميل تفاصيل القرض',
        category: 'LoanDetailsScreen',
        data: {'loanId': _loan.id, 'error': e.toString()},
      );
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Color _getStatusColor(String status) {
    switch (status) {
      case AppConstants.loanStatusActive:
        return RevolutionaryColors.successGlow;
      case AppConstants.loanStatusCompleted:
        return RevolutionaryColors.infoTurquoise;
      case AppConstants.loanStatusCancelled:
        return RevolutionaryColors.errorCoral;
      case AppConstants.loanStatusOverdue:
        return RevolutionaryColors.warningAmber;
      default:
        return Colors.grey;
    }
  }

  IconData _getStatusIcon(String status) {
    switch (status) {
      case AppConstants.loanStatusActive:
        return Icons.trending_up;
      case AppConstants.loanStatusCompleted:
        return Icons.check_circle;
      case AppConstants.loanStatusCancelled:
        return Icons.cancel;
      case AppConstants.loanStatusOverdue:
        return Icons.warning;
      default:
        return Icons.help;
    }
  }

  String _getStatusText(String status) {
    switch (status) {
      case AppConstants.loanStatusActive:
        return 'قرض نشط';
      case AppConstants.loanStatusCompleted:
        return 'قرض مكتمل';
      case AppConstants.loanStatusCancelled:
        return 'قرض ملغي';
      case AppConstants.loanStatusOverdue:
        return 'قرض متأخر';
      default:
        return 'غير محدد';
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  void _handleMenuAction(String action) {
    switch (action) {
      case 'installments':
        _navigateToInstallments();
        break;
      case 'pay_installment':
        _payNextInstallment();
        break;
      case 'close_loan':
        _closeLoan();
        break;
    }
  }

  void _navigateToInstallments() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => LoanInstallmentsScreen(loan: _loan),
      ),
    ).then((result) {
      if (result == true) {
        _loadLoanDetails(); // إعادة تحميل البيانات
      }
    });
  }

  Future<void> _payNextInstallment() async {
    final nextInstallment = _installments.where((i) => !i.isPaid).toList()
      ..sort((a, b) => a.dueDate.compareTo(b.dueDate));

    if (nextInstallment.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('لا توجد أقساط متبقية للدفع'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    final installment = nextInstallment.first;

    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد دفع القسط'),
        content: Text(
          'هل تريد تأكيد دفع القسط بمبلغ ${installment.amount.toStringAsFixed(0)} ل.س؟',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            style: ElevatedButton.styleFrom(
              backgroundColor: RevolutionaryColors.successGlow,
            ),
            child: const Text('تأكيد الدفع'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await _loanService.payInstallment(
          installmentId: installment.id!,
          paidDate: DateTime.now(),
        );

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('تم دفع القسط بنجاح'),
              backgroundColor: RevolutionaryColors.successGlow,
            ),
          );
          _loadLoanDetails(); // إعادة تحميل البيانات
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('خطأ في دفع القسط: ${e.toString()}'),
              backgroundColor: RevolutionaryColors.errorCoral,
            ),
          );
        }
      }
    }
  }

  Future<void> _closeLoan() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد إغلاق القرض'),
        content: const Text(
          'هل تريد إغلاق هذا القرض؟ سيتم إلغاء جميع الأقساط المتبقية.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            style: ElevatedButton.styleFrom(
              backgroundColor: RevolutionaryColors.errorCoral,
            ),
            child: const Text('إغلاق القرض'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        // هنا يمكن إضافة منطق إغلاق القرض
        // await _loanService.closeLoan(_loan.id!);

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('تم إغلاق القرض بنجاح'),
              backgroundColor: RevolutionaryColors.successGlow,
            ),
          );
          Navigator.pop(context, true);
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('خطأ في إغلاق القرض: ${e.toString()}'),
              backgroundColor: RevolutionaryColors.errorCoral,
            ),
          );
        }
      }
    }
  }
}
