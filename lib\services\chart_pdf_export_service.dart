import 'dart:typed_data';
import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import '../services/logging_service.dart';
import '../constants/revolutionary_design_colors.dart';

/// خدمة تصدير الرسوم البيانية إلى PDF
class ChartPdfExportService {
  /// التقاط الرسم البياني وتحويله إلى صورة
  static Future<Uint8List?> captureChartAsImage(GlobalKey chartKey) async {
    try {
      LoggingService.info(
        'بدء التقاط الرسم البياني',
        category: 'ChartPdfExport',
      );

      final RenderRepaintBoundary? boundary =
          chartKey.currentContext?.findRenderObject() as RenderRepaintBoundary?;

      if (boundary == null) {
        LoggingService.error(
          'لم يتم العثور على RenderRepaintBoundary',
          category: 'ChartPdfExport',
        );
        return null;
      }

      final ui.Image image = await boundary.toImage(pixelRatio: 2.0);
      final ByteData? byteData = await image.toByteData(
        format: ui.ImageByteFormat.png,
      );

      if (byteData == null) {
        LoggingService.error(
          'فشل في تحويل الصورة إلى ByteData',
          category: 'ChartPdfExport',
        );
        return null;
      }

      LoggingService.info(
        'تم التقاط الرسم البياني بنجاح',
        category: 'ChartPdfExport',
      );

      return byteData.buffer.asUint8List();
    } catch (e) {
      LoggingService.error(
        'خطأ في التقاط الرسم البياني',
        category: 'ChartPdfExport',
        data: {'error': e.toString()},
      );
      return null;
    }
  }

  /// إنشاء صفحة PDF تحتوي على الرسم البياني
  static pw.Widget buildChartPage({
    required Uint8List chartImage,
    required String chartTitle,
    required String reportType,
    Map<String, dynamic>? chartData,
    Color headerColor = RevolutionaryColors.damascusSky,
  }) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        // عنوان الرسم البياني
        _buildChartHeader(chartTitle, reportType, headerColor),
        pw.SizedBox(height: 20),

        // الرسم البياني
        _buildChartImage(chartImage),
        pw.SizedBox(height: 20),

        // معلومات إضافية عن الرسم البياني
        if (chartData != null) _buildChartInfo(chartData),
      ],
    );
  }

  /// بناء رأس الرسم البياني
  static pw.Widget _buildChartHeader(
    String title,
    String reportType,
    Color headerColor,
  ) {
    return pw.Container(
      width: double.infinity,
      padding: const pw.EdgeInsets.all(16),
      decoration: pw.BoxDecoration(
        color: PdfColor.fromInt(headerColor.toARGB32() & 0xFFFFFF).shade(0.1),
        borderRadius: pw.BorderRadius.circular(8),
        border: pw.Border.all(
          color: PdfColor.fromInt(headerColor.toARGB32() & 0xFFFFFF),
          width: 1,
        ),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Text(
            title,
            style: pw.TextStyle(
              fontSize: 18,
              fontWeight: pw.FontWeight.bold,
              color: PdfColor.fromInt(headerColor.toARGB32() & 0xFFFFFF),
            ),
          ),
          pw.SizedBox(height: 4),
          pw.Text(
            'نوع التقرير: $reportType',
            style: pw.TextStyle(fontSize: 12, color: PdfColors.grey700),
          ),
        ],
      ),
    );
  }

  /// بناء صورة الرسم البياني
  static pw.Widget _buildChartImage(Uint8List chartImage) {
    return pw.Container(
      width: double.infinity,
      child: pw.Center(
        child: pw.Container(
          decoration: pw.BoxDecoration(
            border: pw.Border.all(color: PdfColors.grey300, width: 1),
            borderRadius: pw.BorderRadius.circular(8),
          ),
          child: pw.ClipRRect(
            horizontalRadius: 8,
            verticalRadius: 8,
            child: pw.Image(
              pw.MemoryImage(chartImage),
              fit: pw.BoxFit.contain,
              height: 400,
            ),
          ),
        ),
      ),
    );
  }

  /// بناء معلومات الرسم البياني
  static pw.Widget _buildChartInfo(Map<String, dynamic> chartData) {
    return pw.Container(
      width: double.infinity,
      padding: const pw.EdgeInsets.all(16),
      decoration: pw.BoxDecoration(
        color: PdfColors.grey100,
        borderRadius: pw.BorderRadius.circular(8),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Text(
            'معلومات الرسم البياني',
            style: pw.TextStyle(fontSize: 14, fontWeight: pw.FontWeight.bold),
          ),
          pw.SizedBox(height: 10),
          ...chartData.entries.map(
            (entry) => pw.Padding(
              padding: const pw.EdgeInsets.only(bottom: 4),
              child: pw.Row(
                children: [
                  pw.Text(
                    '${entry.key}: ',
                    style: pw.TextStyle(
                      fontSize: 12,
                      fontWeight: pw.FontWeight.bold,
                    ),
                  ),
                  pw.Text('${entry.value}', style: pw.TextStyle(fontSize: 12)),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// إنشاء صفحة مقارنة للرسوم البيانية
  static pw.Widget buildChartComparisonPage({
    required List<ChartExportData> charts,
    required String comparisonTitle,
    Color headerColor = RevolutionaryColors.damascusSky,
  }) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        // عنوان المقارنة
        pw.Container(
          width: double.infinity,
          padding: const pw.EdgeInsets.all(16),
          decoration: pw.BoxDecoration(
            color: PdfColor.fromInt(
              headerColor.toARGB32() & 0xFFFFFF,
            ).shade(0.1),
            borderRadius: pw.BorderRadius.circular(8),
          ),
          child: pw.Text(
            comparisonTitle,
            style: pw.TextStyle(
              fontSize: 18,
              fontWeight: pw.FontWeight.bold,
              color: PdfColor.fromInt(headerColor.toARGB32() & 0xFFFFFF),
            ),
          ),
        ),
        pw.SizedBox(height: 20),

        // الرسوم البيانية
        ...charts.asMap().entries.map((entry) {
          final index = entry.key;
          final chart = entry.value;

          return pw.Column(
            children: [
              if (index > 0) pw.SizedBox(height: 20),
              _buildComparisonChartSection(chart, index + 1),
            ],
          );
        }),
      ],
    );
  }

  /// بناء قسم رسم بياني في صفحة المقارنة
  static pw.Widget _buildComparisonChartSection(
    ChartExportData chart,
    int index,
  ) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Text(
          '$index. ${chart.title}',
          style: pw.TextStyle(fontSize: 14, fontWeight: pw.FontWeight.bold),
        ),
        pw.SizedBox(height: 10),
        pw.Container(
          height: 250,
          child: pw.Center(
            child: pw.Image(
              pw.MemoryImage(chart.imageData),
              fit: pw.BoxFit.contain,
            ),
          ),
        ),
        if (chart.description != null) ...[
          pw.SizedBox(height: 8),
          pw.Text(
            chart.description!,
            style: pw.TextStyle(fontSize: 10, color: PdfColors.grey600),
          ),
        ],
      ],
    );
  }

  /// إنشاء ملخص إحصائي للرسوم البيانية
  static pw.Widget buildChartSummary({
    required List<ChartSummaryData> summaryData,
    required String summaryTitle,
    Color headerColor = RevolutionaryColors.damascusSky,
  }) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        // عنوان الملخص
        pw.Container(
          width: double.infinity,
          padding: const pw.EdgeInsets.all(16),
          decoration: pw.BoxDecoration(
            color: PdfColor.fromInt(
              headerColor.toARGB32() & 0xFFFFFF,
            ).shade(0.1),
            borderRadius: pw.BorderRadius.circular(8),
          ),
          child: pw.Text(
            summaryTitle,
            style: pw.TextStyle(
              fontSize: 16,
              fontWeight: pw.FontWeight.bold,
              color: PdfColor.fromInt(headerColor.toARGB32() & 0xFFFFFF),
            ),
          ),
        ),
        pw.SizedBox(height: 16),

        // جدول الملخص
        pw.Table(
          border: pw.TableBorder.all(color: PdfColors.grey300),
          children: [
            // رأس الجدول
            pw.TableRow(
              decoration: pw.BoxDecoration(color: PdfColors.grey100),
              children: [
                _buildTableCell('المؤشر', isHeader: true),
                _buildTableCell('القيمة', isHeader: true),
                _buildTableCell('الوصف', isHeader: true),
              ],
            ),
            // بيانات الجدول
            ...summaryData.map(
              (data) => pw.TableRow(
                children: [
                  _buildTableCell(data.indicator),
                  _buildTableCell(data.value),
                  _buildTableCell(data.description),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// بناء خلية جدول
  static pw.Widget _buildTableCell(String text, {bool isHeader = false}) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(8),
      child: pw.Text(
        text,
        style: pw.TextStyle(
          fontSize: isHeader ? 12 : 10,
          fontWeight: isHeader ? pw.FontWeight.bold : pw.FontWeight.normal,
        ),
      ),
    );
  }
}

/// بيانات تصدير الرسم البياني
class ChartExportData {
  final String title;
  final Uint8List imageData;
  final String? description;
  final Map<String, dynamic>? metadata;

  const ChartExportData({
    required this.title,
    required this.imageData,
    this.description,
    this.metadata,
  });
}

/// بيانات ملخص الرسم البياني
class ChartSummaryData {
  final String indicator;
  final String value;
  final String description;

  const ChartSummaryData({
    required this.indicator,
    required this.value,
    required this.description,
  });
}
