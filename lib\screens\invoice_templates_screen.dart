/// شاشة إدارة قوالب طباعة الفواتير
/// توفر إدارة شاملة لقوالب الطباعة مع إمكانية الإنشاء والتعديل والحذف
library;

import 'package:flutter/material.dart';
import '../models/invoice_template.dart';
import '../services/invoice_template_service.dart';
import '../constants/revolutionary_design_colors.dart';
import '../widgets/loading_widget.dart';

class InvoiceTemplatesScreen extends StatefulWidget {
  const InvoiceTemplatesScreen({super.key});

  @override
  State<InvoiceTemplatesScreen> createState() => _InvoiceTemplatesScreenState();
}

class _InvoiceTemplatesScreenState extends State<InvoiceTemplatesScreen> {
  final InvoiceTemplateService _templateService = InvoiceTemplateService();
  final TextEditingController _searchController = TextEditingController();

  List<InvoiceTemplate> _templates = [];
  List<InvoiceTemplate> _filteredTemplates = [];
  bool _isLoading = true;
  Map<String, dynamic> _statistics = {};

  @override
  void initState() {
    super.initState();
    _loadTemplates();
    _loadStatistics();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _loadTemplates() async {
    setState(() => _isLoading = true);

    try {
      final templates = await _templateService.getAllTemplates();
      setState(() {
        _templates = templates;
        _filteredTemplates = templates;
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحميل القوالب: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _loadStatistics() async {
    try {
      final stats = await _templateService.getTemplateStatistics();
      setState(() {
        _statistics = stats;
      });
    } catch (e) {
      // تجاهل الأخطاء في الإحصائيات
    }
  }

  void _filterTemplates(String query) {
    setState(() {
      if (query.isEmpty) {
        _filteredTemplates = _templates;
      } else {
        _filteredTemplates = _templates
            .where((template) =>
                template.name.toLowerCase().contains(query.toLowerCase()) ||
                template.type.displayName.toLowerCase().contains(query.toLowerCase()))
            .toList();
      }
    });
  }

  Future<void> _createDefaultTemplates() async {
    try {
      await _templateService.createDefaultTemplates();
      await _loadTemplates();
      await _loadStatistics();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم إنشاء القوالب الافتراضية بنجاح'),
            backgroundColor: RevolutionaryColors.successGlow,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في إنشاء القوالب الافتراضية: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _setAsDefault(InvoiceTemplate template) async {
    try {
      await _templateService.setAsDefault(template.id!);
      await _loadTemplates();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('تم تعيين ${template.name} كقالب افتراضي'),
            backgroundColor: RevolutionaryColors.successGlow,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تعيين القالب الافتراضي: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _toggleTemplateStatus(InvoiceTemplate template) async {
    try {
      await _templateService.toggleTemplateStatus(template.id!, !template.isActive);
      await _loadTemplates();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              template.isActive
                  ? 'تم إلغاء تفعيل ${template.name}'
                  : 'تم تفعيل ${template.name}',
            ),
            backgroundColor: RevolutionaryColors.successGlow,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تغيير حالة القالب: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _duplicateTemplate(InvoiceTemplate template) async {
    final nameController = TextEditingController(text: '${template.name} - نسخة');

    final result = await showDialog<String>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('نسخ القالب'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('سيتم إنشاء نسخة من القالب "${template.name}"'),
            const SizedBox(height: 16),
            TextField(
              controller: nameController,
              decoration: const InputDecoration(
                labelText: 'اسم القالب الجديد',
                border: OutlineInputBorder(),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(nameController.text),
            child: const Text('نسخ'),
          ),
        ],
      ),
    );

    if (result != null && result.isNotEmpty) {
      try {
        await _templateService.duplicateTemplate(template.id!, result);
        await _loadTemplates();

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('تم نسخ القالب باسم "$result"'),
              backgroundColor: RevolutionaryColors.successGlow,
            ),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('خطأ في نسخ القالب: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  Future<void> _deleteTemplate(InvoiceTemplate template) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text('هل أنت متأكد من حذف القالب "${template.name}"؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('حذف'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await _templateService.deleteTemplate(template.id!);
        await _loadTemplates();
        await _loadStatistics();

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('تم حذف القالب "${template.name}"'),
              backgroundColor: RevolutionaryColors.successGlow,
            ),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('خطأ في حذف القالب: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('قوالب طباعة الفواتير'),
        backgroundColor: RevolutionaryColors.damascusSky,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              _loadTemplates();
              _loadStatistics();
            },
            tooltip: 'تحديث',
          ),
          PopupMenuButton<String>(
            onSelected: (value) {
              switch (value) {
                case 'create_defaults':
                  _createDefaultTemplates();
                  break;
                case 'statistics':
                  _showStatistics();
                  break;
              }
            },
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'create_defaults',
                child: ListTile(
                  leading: Icon(Icons.auto_fix_high),
                  title: Text('إنشاء القوالب الافتراضية'),
                ),
              ),
              const PopupMenuItem(
                value: 'statistics',
                child: ListTile(
                  leading: Icon(Icons.analytics),
                  title: Text('الإحصائيات'),
                ),
              ),
            ],
          ),
        ],
      ),
      body: Column(
        children: [
          // شريط البحث
          Padding(
            padding: const EdgeInsets.all(16),
            child: TextField(
              controller: _searchController,
              decoration: const InputDecoration(
                labelText: 'البحث في القوالب',
                prefixIcon: Icon(Icons.search),
                border: OutlineInputBorder(),
              ),
              onChanged: _filterTemplates,
            ),
          ),

          // قائمة القوالب
          Expanded(
            child: _isLoading
                ? const Center(child: LoadingWidget())
                : _filteredTemplates.isEmpty
                    ? _buildEmptyState()
                    : ListView.builder(
                        padding: const EdgeInsets.symmetric(horizontal: 16),
                        itemCount: _filteredTemplates.length,
                        itemBuilder: (context, index) {
                          final template = _filteredTemplates[index];
                          return _buildTemplateCard(template);
                        },
                      ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          // فتح شاشة إنشاء قالب جديد
          _showCreateTemplateDialog();
        },
        backgroundColor: RevolutionaryColors.damascusSky,
        child: const Icon(Icons.add, color: Colors.white),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.description_outlined,
            size: 64,
            color: Colors.grey,
          ),
          const SizedBox(height: 16),
          const Text(
            'لا توجد قوالب',
            style: TextStyle(fontSize: 18, color: Colors.grey),
          ),
          const SizedBox(height: 8),
          const Text(
            'ابدأ بإنشاء القوالب الافتراضية',
            style: TextStyle(color: Colors.grey),
          ),
          const SizedBox(height: 16),
          ElevatedButton.icon(
            onPressed: _createDefaultTemplates,
            icon: const Icon(Icons.auto_fix_high),
            label: const Text('إنشاء القوالب الافتراضية'),
            style: ElevatedButton.styleFrom(
              backgroundColor: RevolutionaryColors.damascusSky,
              foregroundColor: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTemplateCard(InvoiceTemplate template) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                // أيقونة القالب
                Container(
                  width: 50,
                  height: 50,
                  decoration: BoxDecoration(
                    color: _getTemplateColor(template.type),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    _getTemplateIcon(template.type),
                    color: Colors.white,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),

                // معلومات القالب
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Text(
                            template.name,
                            style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          if (template.isDefault) ...[
                            const SizedBox(width: 8),
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 8,
                                vertical: 2,
                              ),
                              decoration: BoxDecoration(
                                color: RevolutionaryColors.successGlow,
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: const Text(
                                'افتراضي',
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 10,
                                ),
                              ),
                            ),
                          ],
                          if (!template.isActive) ...[
                            const SizedBox(width: 8),
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 8,
                                vertical: 2,
                              ),
                              decoration: BoxDecoration(
                                color: Colors.grey,
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: const Text(
                                'غير نشط',
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 10,
                                ),
                              ),
                            ),
                          ],
                        ],
                      ),
                      const SizedBox(height: 4),
                      Text(
                        template.type.description,
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ),

                // قائمة الإجراءات
                PopupMenuButton<String>(
                  onSelected: (value) {
                    switch (value) {
                      case 'set_default':
                        _setAsDefault(template);
                        break;
                      case 'toggle_status':
                        _toggleTemplateStatus(template);
                        break;
                      case 'duplicate':
                        _duplicateTemplate(template);
                        break;
                      case 'delete':
                        _deleteTemplate(template);
                        break;
                    }
                  },
                  itemBuilder: (context) => [
                    if (!template.isDefault)
                      const PopupMenuItem(
                        value: 'set_default',
                        child: ListTile(
                          leading: Icon(Icons.star),
                          title: Text('تعيين كافتراضي'),
                        ),
                      ),
                    PopupMenuItem(
                      value: 'toggle_status',
                      child: ListTile(
                        leading: Icon(
                          template.isActive ? Icons.visibility_off : Icons.visibility,
                        ),
                        title: Text(template.isActive ? 'إلغاء التفعيل' : 'تفعيل'),
                      ),
                    ),
                    const PopupMenuItem(
                      value: 'duplicate',
                      child: ListTile(
                        leading: Icon(Icons.copy),
                        title: Text('نسخ'),
                      ),
                    ),
                    const PopupMenuItem(
                      value: 'delete',
                      child: ListTile(
                        leading: Icon(Icons.delete, color: Colors.red),
                        title: Text('حذف', style: TextStyle(color: Colors.red)),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  void _showStatistics() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إحصائيات القوالب'),
        content: SizedBox(
          width: 300,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildStatItem('إجمالي القوالب', _statistics['total_templates']?.toString() ?? '0'),
              _buildStatItem('القوالب النشطة', _statistics['active_templates']?.toString() ?? '0'),
              _buildStatItem('القوالب الكلاسيكية', _statistics['classic_templates']?.toString() ?? '0'),
              _buildStatItem('القوالب الحديثة', _statistics['modern_templates']?.toString() ?? '0'),
              _buildStatItem('القوالب المبسطة', _statistics['minimal_templates']?.toString() ?? '0'),
              _buildStatItem('القوالب الفاخرة', _statistics['luxury_templates']?.toString() ?? '0'),
              _buildStatItem('القوالب المخصصة', _statistics['custom_templates']?.toString() ?? '0'),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label),
          Text(
            value,
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
        ],
      ),
    );
  }

  void _showCreateTemplateDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إنشاء قالب جديد'),
        content: const Text('هذه الميزة قيد التطوير'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  Color _getTemplateColor(InvoiceTemplateType type) {
    switch (type) {
      case InvoiceTemplateType.classic:
        return Colors.blue;
      case InvoiceTemplateType.modern:
        return Colors.purple;
      case InvoiceTemplateType.minimal:
        return Colors.grey;
      case InvoiceTemplateType.luxury:
        return Colors.amber;
      case InvoiceTemplateType.custom:
        return Colors.green;
    }
  }

  IconData _getTemplateIcon(InvoiceTemplateType type) {
    switch (type) {
      case InvoiceTemplateType.classic:
        return Icons.article;
      case InvoiceTemplateType.modern:
        return Icons.auto_awesome;
      case InvoiceTemplateType.minimal:
        return Icons.minimize;
      case InvoiceTemplateType.luxury:
        return Icons.diamond;
      case InvoiceTemplateType.custom:
        return Icons.tune;
    }
  }
}
