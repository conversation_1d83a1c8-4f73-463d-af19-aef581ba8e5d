import 'package:flutter/material.dart';
import '../constants/revolutionary_design_colors.dart';
import '../widgets/revolutionary_ui_components.dart';
import '../services/advanced_dashboard_service.dart';
import '../widgets/revolutionary_interactive_components.dart';
import '../constants/revolutionary_animations.dart';

/// شاشة عرض توضيحي للتصميم الثوري
class RevolutionaryDemoScreen extends StatefulWidget {
  const RevolutionaryDemoScreen({super.key});

  @override
  State<RevolutionaryDemoScreen> createState() =>
      _RevolutionaryDemoScreenState();
}

class _RevolutionaryDemoScreenState extends State<RevolutionaryDemoScreen>
    with TickerProviderStateMixin {
  late AnimationController _mainAnimationController;
  late AnimationController _statsAnimationController;

  final AdvancedDashboardService _dashboardService = AdvancedDashboardService();

  bool _isToggled = false;
  bool _isCardSelected = false;
  double _progressValue = 0.0;
  String _searchQuery = '';
  Map<String, dynamic> _realStats = {};
  bool _isLoadingStats = true;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _startDemo();
    _loadRealData();
  }

  void _setupAnimations() {
    _mainAnimationController = AnimationController(
      duration: RevolutionaryAnimations.epic,
      vsync: this,
    );

    _statsAnimationController = AnimationController(
      duration: RevolutionaryAnimations.majestic,
      vsync: this,
    );
  }

  void _startDemo() {
    _mainAnimationController.forward();

    Future.delayed(const Duration(milliseconds: 500), () {
      _statsAnimationController.forward();
    });

    // محاكاة تقدم
    Future.delayed(const Duration(seconds: 1), () {
      _animateProgress();
    });
  }

  void _animateProgress() {
    const duration = Duration(milliseconds: 2000);
    const steps = 100;
    final stepDuration = Duration(
      milliseconds: duration.inMilliseconds ~/ steps,
    );

    for (int i = 0; i <= steps; i++) {
      Future.delayed(stepDuration * i, () {
        if (mounted) {
          setState(() {
            _progressValue = i / steps;
          });
        }
      });
    }
  }

  Future<void> _loadRealData() async {
    try {
      setState(() => _isLoadingStats = true);

      // تحميل البيانات الحقيقية من الخدمة
      final dashboardData = await _dashboardService.getDashboardData();

      setState(() {
        _realStats = {
          'totalInvoices': dashboardData.financialSummary.totalInvoices,
          'totalRevenue': dashboardData.financialSummary.totalRevenue,
          'totalCustomers': dashboardData.financialSummary.totalCustomers,
          'totalItems': dashboardData.kpis.length, // عدد مؤشرات الأداء كبديل
          'pendingInvoices':
              (dashboardData.financialSummary.totalInvoices * 0.1)
                  .round(), // تقدير
          'monthlyGrowth': dashboardData.financialSummary.profitMargin,
        };
        _isLoadingStats = false;
      });
    } catch (e) {
      // في حالة الخطأ، استخدم البيانات الافتراضية
      setState(() {
        _realStats = {
          'totalInvoices': 1250,
          'totalRevenue': 2500000.0,
          'totalCustomers': 340,
          'totalItems': 890,
          'pendingInvoices': 45,
          'monthlyGrowth': 12.5,
        };
        _isLoadingStats = false;
      });
    }
  }

  @override
  void dispose() {
    _mainAnimationController.dispose();
    _statsAnimationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: RevolutionaryColors.jasmineGradient,
        ),
        child: SafeArea(
          child: SingleChildScrollView(
            child: Column(
              children: [
                _buildHeader(),
                _buildSearchSection(),
                _buildStatsSection(),
                _buildInteractiveSection(),
                _buildComponentsShowcase(),
                _buildFooter(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return AnimatedBuilder(
      animation: _mainAnimationController,
      builder: (context, child) {
        return RevolutionaryAnimations.floating3DCardAnimation(
          animation: _mainAnimationController,
          child: Container(
            width: double.infinity,
            margin: const EdgeInsets.all(16),
            padding: const EdgeInsets.all(24),
            decoration: const BoxDecoration(
              gradient: RevolutionaryColors.damascusGradient,
              borderRadius: BorderRadius.all(Radius.circular(25)),
              boxShadow: [
                BoxShadow(
                  color: RevolutionaryColors.shadowBlue,
                  blurRadius: 20,
                  offset: Offset(0, 10),
                ),
              ],
            ),
            child: Column(
              children: [
                const Icon(
                  Icons.auto_awesome,
                  size: 48,
                  color: RevolutionaryColors.syrianGold,
                ),
                const SizedBox(height: 16),
                RevolutionaryAnimations.typewriterAnimation(
                  text: 'التصميم الثوري لـ Smart Ledger',
                  animation: _mainAnimationController,
                  style: const TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: RevolutionaryColors.textOnDark,
                    fontFamily: 'Cairo',
                  ),
                ),
                const SizedBox(height: 8),
                const Text(
                  'تجربة فريدة تجمع بين الجمال والوظائف المتقدمة',
                  style: TextStyle(
                    fontSize: 16,
                    color: RevolutionaryColors.textOnDark,
                    fontFamily: 'Cairo',
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildSearchSection() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: RevolutionaryInteractive.interactiveSearchBar(
        onSearch: (query) {
          setState(() {
            _searchQuery = query;
          });
        },
        hint: 'ابحث في المكونات الثورية...',
        prefixIcon: Icons.search,
        actions: [
          IconButton(
            onPressed: () {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('تم البحث!'),
                  backgroundColor: RevolutionaryColors.damascusSky,
                ),
              );
            },
            icon: const Icon(
              Icons.filter_list,
              color: RevolutionaryColors.damascusSky,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatsSection() {
    return AnimatedBuilder(
      animation: _statsAnimationController,
      builder: (context, child) {
        return Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                _isLoadingStats
                    ? 'جاري تحميل الإحصائيات...'
                    : 'إحصائيات حقيقية من النظام',
                style: const TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: RevolutionaryColors.textPrimary,
                  fontFamily: 'Cairo',
                ),
              ),
              const SizedBox(height: 16),
              if (_isLoadingStats)
                const Center(
                  child: CircularProgressIndicator(
                    valueColor: AlwaysStoppedAnimation<Color>(
                      RevolutionaryColors.syrianGold,
                    ),
                  ),
                )
              else
                Column(
                  children: [
                    Row(
                      children: [
                        Expanded(
                          child: RevolutionaryAnimations.floating3DCardAnimation(
                            animation: _statsAnimationController,
                            delay: const Duration(milliseconds: 100),
                            child: RevolutionaryUI.animatedStatsCard(
                              title: 'إجمالي الإيرادات',
                              value:
                                  '${(_realStats['totalRevenue'] ?? 0).toStringAsFixed(0)} ل.س',
                              icon: Icons.trending_up,
                              color: RevolutionaryColors.successGlow,
                              percentage: _realStats['monthlyGrowth'] ?? 0.0,
                              isIncreasing:
                                  (_realStats['monthlyGrowth'] ?? 0.0) > 0,
                            ),
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child:
                              RevolutionaryAnimations.floating3DCardAnimation(
                                animation: _statsAnimationController,
                                delay: const Duration(milliseconds: 200),
                                child: RevolutionaryUI.animatedStatsCard(
                                  title: 'إجمالي العملاء',
                                  value: '${_realStats['totalCustomers'] ?? 0}',
                                  icon: Icons.people_alt,
                                  color: RevolutionaryColors.infoTurquoise,
                                  percentage: 8.7,
                                  isIncreasing: true,
                                ),
                              ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 12),
                    Row(
                      children: [
                        Expanded(
                          child:
                              RevolutionaryAnimations.floating3DCardAnimation(
                                animation: _statsAnimationController,
                                delay: const Duration(milliseconds: 300),
                                child: RevolutionaryUI.animatedStatsCard(
                                  title: 'إجمالي الفواتير',
                                  value: '${_realStats['totalInvoices'] ?? 0}',
                                  icon: Icons.receipt_long,
                                  color: RevolutionaryColors.damascusSky,
                                  percentage: 12.3,
                                  isIncreasing: true,
                                ),
                              ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child:
                              RevolutionaryAnimations.floating3DCardAnimation(
                                animation: _statsAnimationController,
                                delay: const Duration(milliseconds: 400),
                                child: RevolutionaryUI.animatedStatsCard(
                                  title: 'الفواتير المعلقة',
                                  value:
                                      '${_realStats['pendingInvoices'] ?? 0}',
                                  icon: Icons.pending_actions,
                                  color: RevolutionaryColors.warningAmber,
                                  percentage: -2.1,
                                  isIncreasing: false,
                                ),
                              ),
                        ),
                      ],
                    ),
                  ],
                ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildInteractiveSection() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'مكونات تفاعلية',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: RevolutionaryColors.textPrimary,
              fontFamily: 'Cairo',
            ),
          ),
          const SizedBox(height: 16),

          // مؤشر التقدم
          RevolutionaryInteractive.revolutionaryProgressIndicator(
            value: _progressValue,
            label: 'تقدم المعالجة',
            color: RevolutionaryColors.damascusSky,
          ),

          const SizedBox(height: 16),

          // مفتاح التبديل
          Center(
            child: RevolutionaryInteractive.revolutionaryToggle(
              value: _isToggled,
              onChanged: (value) {
                setState(() {
                  _isToggled = value;
                });
              },
              label: 'تفعيل الوضع المتقدم',
              activeIcon: Icons.auto_awesome,
              inactiveIcon: Icons.auto_awesome_outlined,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildComponentsShowcase() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'معرض المكونات',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: RevolutionaryColors.textPrimary,
              fontFamily: 'Cairo',
            ),
          ),
          const SizedBox(height: 16),

          // عرض نتيجة البحث
          if (_searchQuery.isNotEmpty)
            Container(
              width: double.infinity,
              margin: const EdgeInsets.only(bottom: 16),
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: RevolutionaryColors.damascusSky.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: RevolutionaryColors.damascusSky.withValues(alpha: 0.3),
                  width: 1,
                ),
              ),
              child: Row(
                children: [
                  const Icon(
                    Icons.search,
                    color: RevolutionaryColors.damascusSky,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'البحث عن: "$_searchQuery"',
                      style: const TextStyle(
                        fontSize: 14,
                        color: RevolutionaryColors.textPrimary,
                        fontFamily: 'Cairo',
                      ),
                    ),
                  ),
                  IconButton(
                    onPressed: () {
                      setState(() {
                        _searchQuery = '';
                      });
                    },
                    icon: const Icon(
                      Icons.clear,
                      color: RevolutionaryColors.textSecondary,
                      size: 18,
                    ),
                  ),
                ],
              ),
            ),

          // بطاقة تفاعلية
          RevolutionaryInteractive.interactiveCard(
            title: 'بطاقة تفاعلية',
            subtitle: 'اضغط للتفاعل معها',
            icon: Icons.touch_app,
            accentColor: RevolutionaryColors.syrianGold,
            isSelected: _isCardSelected,
            hasNotification: true,
            onTap: () {
              setState(() {
                _isCardSelected = !_isCardSelected;
              });
            },
            onLongPress: () {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('ضغطة طويلة!'),
                  backgroundColor: RevolutionaryColors.syrianGold,
                ),
              );
            },
            child: const Text(
              'هذه بطاقة تفاعلية مع تأثيرات بصرية متقدمة',
              style: TextStyle(
                fontSize: 14,
                color: RevolutionaryColors.textSecondary,
                fontFamily: 'Cairo',
              ),
            ),
          ),

          const SizedBox(height: 16),

          // أزرار متوهجة
          Row(
            children: [
              Expanded(
                child: RevolutionaryUI.glowingButton(
                  text: 'حفظ',
                  icon: Icons.save,
                  onPressed: () {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('تم الحفظ بنجاح!'),
                        backgroundColor: RevolutionaryColors.successGlow,
                      ),
                    );
                  },
                  isPrimary: true,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: RevolutionaryUI.glowingButton(
                  text: 'إلغاء',
                  icon: Icons.cancel,
                  onPressed: () {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('تم الإلغاء'),
                        backgroundColor: RevolutionaryColors.errorCoral,
                      ),
                    );
                  },
                  isPrimary: false,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildFooter() {
    return Container(
      width: double.infinity,
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: RevolutionaryColors.heritageGradient,
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: RevolutionaryColors.borderGold, width: 2),
      ),
      child: Column(
        children: [
          const Icon(
            Icons.star,
            size: 32,
            color: RevolutionaryColors.syrianGold,
          ),
          const SizedBox(height: 12),
          const Text(
            'تصميم ثوري يجمع بين الأصالة والحداثة',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: RevolutionaryColors.textPrimary,
              fontFamily: 'Cairo',
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          Text(
            'مطور بحب وإتقان لخدمة المجتمع السوري',
            style: TextStyle(
              fontSize: 14,
              color: RevolutionaryColors.textSecondary,
              fontFamily: 'Cairo',
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}
