/// خدمة إدارة الحضور والانصراف
/// توفر عمليات تسجيل وإدارة الحضور والانصراف للموظفين
library;

import '../database/database_helper.dart';
import '../constants/app_constants.dart';
import '../services/logging_service.dart';
import '../services/audit_service.dart';
import '../exceptions/validation_exception.dart';
import '../models/hr_models.dart';

class AttendanceService {
  final DatabaseHelper _databaseHelper = DatabaseHelper();

  /// تسجيل حضور موظف
  Future<Attendance> checkIn({
    required int employeeId,
    DateTime? checkInTime,
    String? notes,
  }) async {
    try {
      final db = await _databaseHelper.database;
      final now = DateTime.now();
      final today = DateTime(now.year, now.month, now.day);
      final actualCheckInTime = checkInTime ?? now;

      // التحقق من عدم وجود تسجيل حضور لنفس اليوم
      final existingAttendance = await getAttendanceByDate(employeeId, today);
      if (existingAttendance != null) {
        throw ValidationException('تم تسجيل الحضور لهذا اليوم مسبقاً');
      }

      // حساب التأخير (افتراض بداية العمل 8:00 صباحاً)
      final workStartTime = DateTime(today.year, today.month, today.day, 8, 0);
      final lateMinutes = actualCheckInTime.isAfter(workStartTime)
          ? actualCheckInTime.difference(workStartTime).inMinutes
          : 0;

      final attendance = Attendance(
        employeeId: employeeId,
        attendanceDate: today,
        checkInTime: actualCheckInTime,
        lateMinutes: lateMinutes,
        status: 'present',
        notes: notes,
        createdAt: now,
        updatedAt: now,
      );

      final id = await db.insert(
        AppConstants.attendanceTable,
        attendance.toMap(),
      );

      final newAttendance = attendance.copyWith(id: id);

      // تسجيل في سجل المراجعة
      await AuditService.log(
        action: 'CHECK_IN',
        entityType: 'Attendance',
        entityId: id,
        description: 'تسجيل حضور الموظف',
        newValues: newAttendance.toMap(),
      );

      LoggingService.info(
        'تم تسجيل حضور الموظف بنجاح',
        category: 'AttendanceService',
        data: {
          'employeeId': employeeId,
          'checkInTime': actualCheckInTime.toIso8601String(),
        },
      );

      return newAttendance;
    } catch (e) {
      LoggingService.error(
        'خطأ في تسجيل الحضور',
        category: 'AttendanceService',
        data: {'employeeId': employeeId, 'error': e.toString()},
      );
      rethrow;
    }
  }

  /// تسجيل انصراف موظف
  Future<Attendance> checkOut({
    required int employeeId,
    DateTime? checkOutTime,
    String? notes,
  }) async {
    try {
      final db = await _databaseHelper.database;
      final now = DateTime.now();
      final today = DateTime(now.year, now.month, now.day);
      final actualCheckOutTime = checkOutTime ?? now;

      // الحصول على تسجيل الحضور لليوم
      final attendance = await getAttendanceByDate(employeeId, today);
      if (attendance == null) {
        throw ValidationException('لم يتم تسجيل الحضور لهذا اليوم');
      }

      if (attendance.hasCheckOut) {
        throw ValidationException('تم تسجيل الانصراف لهذا اليوم مسبقاً');
      }

      // حساب الانصراف المبكر (افتراض نهاية العمل 4:00 مساءً)
      final workEndTime = DateTime(today.year, today.month, today.day, 16, 0);
      final earlyLeaveMinutes = actualCheckOutTime.isBefore(workEndTime)
          ? workEndTime.difference(actualCheckOutTime).inMinutes
          : 0;

      // حساب ساعات العمل
      final workDuration = actualCheckOutTime.difference(
        attendance.checkInTime!,
      );
      double totalHours = workDuration.inMinutes / 60.0;

      // خصم فترة الاستراحة (افتراض ساعة واحدة)
      if (totalHours > 4) {
        totalHours -= 1; // خصم ساعة استراحة
      }

      // حساب الساعات الإضافية (أكثر من 8 ساعات)
      final regularHours = totalHours > 8 ? 8.0 : totalHours;
      final overtimeHours = totalHours > 8 ? totalHours - 8 : 0.0;

      final updatedAttendance = Attendance(
        id: attendance.id,
        employeeId: employeeId,
        attendanceDate: today,
        checkInTime: attendance.checkInTime,
        checkOutTime: actualCheckOutTime,
        breakStartTime: attendance.breakStartTime,
        breakEndTime: attendance.breakEndTime,
        totalHours: totalHours,
        regularHours: regularHours,
        overtimeHours: overtimeHours,
        lateMinutes: attendance.lateMinutes,
        earlyLeaveMinutes: earlyLeaveMinutes,
        status: 'present',
        notes: notes ?? attendance.notes,
        createdAt: attendance.createdAt,
        updatedAt: now,
      );

      await db.update(
        AppConstants.attendanceTable,
        updatedAttendance.toMap(),
        where: 'id = ?',
        whereArgs: [attendance.id],
      );

      // تسجيل في سجل المراجعة
      await AuditService.log(
        action: 'CHECK_OUT',
        entityType: 'Attendance',
        entityId: attendance.id,
        description: 'تسجيل انصراف الموظف',
        oldValues: attendance.toMap(),
        newValues: updatedAttendance.toMap(),
      );

      LoggingService.info(
        'تم تسجيل انصراف الموظف بنجاح',
        category: 'AttendanceService',
        data: {
          'employeeId': employeeId,
          'checkOutTime': actualCheckOutTime.toIso8601String(),
        },
      );

      return updatedAttendance;
    } catch (e) {
      LoggingService.error(
        'خطأ في تسجيل الانصراف',
        category: 'AttendanceService',
        data: {'employeeId': employeeId, 'error': e.toString()},
      );
      rethrow;
    }
  }

  /// الحصول على حضور موظف في تاريخ معين
  Future<Attendance?> getAttendanceByDate(int employeeId, DateTime date) async {
    try {
      final db = await _databaseHelper.database;

      final result = await db.query(
        AppConstants.attendanceTable,
        where: 'employee_id = ? AND attendance_date = ?',
        whereArgs: [employeeId, date.toIso8601String().split('T')[0]],
        limit: 1,
      );

      if (result.isEmpty) return null;
      return Attendance.fromMap(result.first);
    } catch (e) {
      LoggingService.error(
        'خطأ في الحصول على الحضور',
        category: 'AttendanceService',
        data: {
          'employeeId': employeeId,
          'date': date.toIso8601String(),
          'error': e.toString(),
        },
      );
      rethrow;
    }
  }

  /// الحصول على حضور موظف لفترة معينة
  Future<List<Attendance>> getAttendanceByPeriod({
    required int employeeId,
    required DateTime fromDate,
    required DateTime toDate,
  }) async {
    try {
      final db = await _databaseHelper.database;

      final result = await db.query(
        AppConstants.attendanceTable,
        where: 'employee_id = ? AND attendance_date BETWEEN ? AND ?',
        whereArgs: [
          employeeId,
          fromDate.toIso8601String().split('T')[0],
          toDate.toIso8601String().split('T')[0],
        ],
        orderBy: 'attendance_date DESC',
      );

      return result.map((map) => Attendance.fromMap(map)).toList();
    } catch (e) {
      LoggingService.error(
        'خطأ في الحصول على حضور الفترة',
        category: 'AttendanceService',
        data: {
          'employeeId': employeeId,
          'fromDate': fromDate.toIso8601String(),
          'toDate': toDate.toIso8601String(),
          'error': e.toString(),
        },
      );
      rethrow;
    }
  }
}
