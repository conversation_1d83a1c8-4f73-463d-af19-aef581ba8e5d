/// شاشة إدارة الرواتب
/// واجهة لحساب وإدارة رواتب الموظفين
library;

import 'package:flutter/material.dart';
import '../constants/revolutionary_design_colors.dart';
import '../models/hr_models.dart';
import '../services/payroll_service.dart';
import '../services/employee_service.dart';
import '../services/logging_service.dart';
import '../widgets/loading_widget.dart';
import 'employee_salary_calculation_screen.dart';

class PayrollScreen extends StatefulWidget {
  const PayrollScreen({super.key});

  @override
  State<PayrollScreen> createState() => _PayrollScreenState();
}

class _PayrollScreenState extends State<PayrollScreen> {
  final PayrollService _payrollService = PayrollService();
  final EmployeeService _employeeService = EmployeeService();

  List<Employee> _employees = [];
  List<PayrollRecord> _payrollRecords = [];
  bool _isLoading = true;
  String? _error;
  int _selectedMonth = DateTime.now().month;
  int _selectedYear = DateTime.now().year;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    try {
      setState(() {
        _isLoading = true;
        _error = null;
      });

      // تحميل قائمة الموظفين النشطين
      final employees = await _employeeService.getAllEmployees(
        activeOnly: true,
      );

      // تحميل كشوف الرواتب للشهر المحدد
      final payrollRecords = <PayrollRecord>[];
      for (final employee in employees) {
        final record = await _payrollService.getPayrollRecord(
          employee.id!,
          _selectedMonth,
          _selectedYear,
        );
        if (record != null) {
          payrollRecords.add(record);
        }
      }

      setState(() {
        _employees = employees;
        _payrollRecords = payrollRecords;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });

      LoggingService.error(
        'خطأ في تحميل بيانات الرواتب',
        category: 'PayrollScreen',
        data: {'error': e.toString()},
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إدارة الرواتب'),
        backgroundColor: RevolutionaryColors.syrianGold,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.date_range),
            onPressed: _selectMonthYear,
          ),
          IconButton(icon: const Icon(Icons.refresh), onPressed: _loadData),
        ],
      ),
      body: Column(
        children: [
          _buildMonthYearHeader(),
          _buildPayrollSummary(),
          Expanded(child: _buildPayrollList()),
        ],
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _showBulkPayrollDialog,
        backgroundColor: RevolutionaryColors.syrianGold,
        icon: const Icon(Icons.calculate, color: Colors.white),
        label: const Text(
          'حساب الرواتب',
          style: TextStyle(color: Colors.white),
        ),
      ),
    );
  }

  Widget _buildMonthYearHeader() {
    final monthNames = [
      'يناير',
      'فبراير',
      'مارس',
      'أبريل',
      'مايو',
      'يونيو',
      'يوليو',
      'أغسطس',
      'سبتمبر',
      'أكتوبر',
      'نوفمبر',
      'ديسمبر',
    ];

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: RevolutionaryColors.syrianGold.withValues(alpha: 0.1),
        border: Border(bottom: BorderSide(color: Colors.grey[300]!)),
      ),
      child: Row(
        children: [
          Icon(Icons.calendar_month, color: RevolutionaryColors.syrianGold),
          const SizedBox(width: 8),
          Text(
            'رواتب ${monthNames[_selectedMonth - 1]} $_selectedYear',
            style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const Spacer(),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: RevolutionaryColors.syrianGold,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              '${_payrollRecords.length} كشف راتب',
              style: const TextStyle(
                color: Colors.white,
                fontSize: 12,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPayrollSummary() {
    final totalGrossSalary = _payrollRecords.fold<double>(
      0,
      (sum, record) => sum + record.grossSalary,
    );
    final totalDeductions = _payrollRecords.fold<double>(
      0,
      (sum, record) => sum + record.totalDeductions,
    );
    final totalNetSalary = _payrollRecords.fold<double>(
      0,
      (sum, record) => sum + record.netSalary,
    );

    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          Expanded(
            child: _buildSummaryCard(
              'الراتب الإجمالي',
              '${totalGrossSalary.toStringAsFixed(0)} ل.س',
              Icons.account_balance_wallet,
              RevolutionaryColors.successGlow,
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: _buildSummaryCard(
              'الاستقطاعات',
              '${totalDeductions.toStringAsFixed(0)} ل.س',
              Icons.remove_circle,
              RevolutionaryColors.errorCoral,
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: _buildSummaryCard(
              'صافي الراتب',
              '${totalNetSalary.toStringAsFixed(0)} ل.س',
              Icons.payments,
              RevolutionaryColors.syrianGold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryCard(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          children: [
            Icon(icon, color: color, size: 24),
            const SizedBox(height: 4),
            Text(
              title,
              style: const TextStyle(fontSize: 12, color: Colors.grey),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 4),
            Text(
              value,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: color,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPayrollList() {
    if (_isLoading) {
      return const LoadingWidget(message: 'جاري تحميل بيانات الرواتب...');
    }

    if (_error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 64, color: Colors.red[400]),
            const SizedBox(height: 16),
            Text(
              'حدث خطأ',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.red[600],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              _error!,
              style: TextStyle(fontSize: 14, color: Colors.grey[600]),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadData,
              child: const Text('إعادة المحاولة'),
            ),
          ],
        ),
      );
    }

    if (_employees.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.people_outline, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text(
              'لا يوجد موظفين',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 8),
            Text(
              'يرجى إضافة موظفين أولاً',
              style: TextStyle(color: Colors.grey),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _employees.length,
      itemBuilder: (context, index) {
        final employee = _employees[index];
        final payrollRecord = _payrollRecords.firstWhere(
          (record) => record.employeeId == employee.id,
          orElse: () => PayrollRecord(
            employeeId: employee.id!,
            month: _selectedMonth,
            year: _selectedYear,
            basicSalary: employee.basicSalary,
            grossSalary: 0,
            totalDeductions: 0,
            netSalary: 0,
            workingDays: 0,
            actualWorkingDays: 0,
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          ),
        );

        return _buildEmployeePayrollCard(employee, payrollRecord);
      },
    );
  }

  Widget _buildEmployeePayrollCard(
    Employee employee,
    PayrollRecord payrollRecord,
  ) {
    final hasPayroll = payrollRecord.id != null;
    final isCalculated = hasPayroll && payrollRecord.netSalary > 0;

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Row(
              children: [
                // صورة الموظف
                CircleAvatar(
                  radius: 25,
                  backgroundColor: RevolutionaryColors.syrianGold.withValues(
                    alpha: 0.1,
                  ),
                  backgroundImage: employee.photoPath != null
                      ? AssetImage(employee.photoPath!)
                      : null,
                  child: employee.photoPath == null
                      ? Icon(
                          Icons.person,
                          size: 25,
                          color: RevolutionaryColors.syrianGold,
                        )
                      : null,
                ),
                const SizedBox(width: 12),
                // معلومات الموظف
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        employee.displayName,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'الراتب الأساسي: ${employee.basicSalary.toStringAsFixed(0)} ل.س',
                        style: TextStyle(fontSize: 14, color: Colors.grey[600]),
                      ),
                    ],
                  ),
                ),
                // حالة الراتب
                _buildPayrollStatus(isCalculated, payrollRecord.status),
              ],
            ),
            if (hasPayroll && isCalculated) ...[
              const SizedBox(height: 12),
              const Divider(),
              const SizedBox(height: 8),
              _buildPayrollDetails(payrollRecord),
            ],
            const SizedBox(height: 12),
            _buildPayrollActions(employee, payrollRecord),
          ],
        ),
      ),
    );
  }

  Widget _buildPayrollStatus(bool isCalculated, String status) {
    if (!isCalculated) {
      return Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: Colors.grey.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.grey),
        ),
        child: const Text(
          'غير محسوب',
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey,
            fontWeight: FontWeight.bold,
          ),
        ),
      );
    }

    Color statusColor;
    String statusText;

    switch (status) {
      case 'draft':
        statusColor = RevolutionaryColors.warningAmber;
        statusText = 'مسودة';
        break;
      case 'approved':
        statusColor = RevolutionaryColors.successGlow;
        statusText = 'معتمد';
        break;
      case 'paid':
        statusColor = RevolutionaryColors.damascusSky;
        statusText = 'مدفوع';
        break;
      default:
        statusColor = Colors.grey;
        statusText = 'غير محدد';
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: statusColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: statusColor),
      ),
      child: Text(
        statusText,
        style: TextStyle(
          fontSize: 12,
          color: statusColor,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  Widget _buildPayrollDetails(PayrollRecord payrollRecord) {
    return Column(
      children: [
        Row(
          children: [
            Expanded(
              child: _buildDetailItem(
                'الراتب الإجمالي',
                '${payrollRecord.grossSalary.toStringAsFixed(0)} ل.س',
                RevolutionaryColors.successGlow,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildDetailItem(
                'الاستقطاعات',
                '${payrollRecord.totalDeductions.toStringAsFixed(0)} ل.س',
                RevolutionaryColors.errorCoral,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            Expanded(
              child: _buildDetailItem(
                'صافي الراتب',
                '${payrollRecord.netSalary.toStringAsFixed(0)} ل.س',
                RevolutionaryColors.syrianGold,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildDetailItem(
                'أيام العمل',
                '${payrollRecord.actualWorkingDays}/${payrollRecord.workingDays}',
                RevolutionaryColors.damascusSky,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildDetailItem(String label, String value, Color color) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(label, style: const TextStyle(fontSize: 12, color: Colors.grey)),
        const SizedBox(height: 2),
        Text(
          value,
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
      ],
    );
  }

  Widget _buildPayrollActions(Employee employee, PayrollRecord payrollRecord) {
    final hasPayroll = payrollRecord.id != null;
    final isCalculated = hasPayroll && payrollRecord.netSalary > 0;

    return Row(
      children: [
        if (!isCalculated)
          Expanded(
            child: ElevatedButton.icon(
              onPressed: () => _calculatePayroll(employee),
              icon: const Icon(Icons.calculate, size: 18),
              label: const Text('حساب الراتب'),
              style: ElevatedButton.styleFrom(
                backgroundColor: RevolutionaryColors.syrianGold,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 8),
              ),
            ),
          ),
        if (isCalculated && payrollRecord.canEdit) ...[
          Expanded(
            child: ElevatedButton.icon(
              onPressed: () => _editPayroll(employee, payrollRecord),
              icon: const Icon(Icons.edit, size: 18),
              label: const Text('تعديل'),
              style: ElevatedButton.styleFrom(
                backgroundColor: RevolutionaryColors.warningAmber,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 8),
              ),
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: ElevatedButton.icon(
              onPressed: () => _approvePayroll(payrollRecord),
              icon: const Icon(Icons.check, size: 18),
              label: const Text('اعتماد'),
              style: ElevatedButton.styleFrom(
                backgroundColor: RevolutionaryColors.successGlow,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 8),
              ),
            ),
          ),
        ],
      ],
    );
  }

  Future<void> _selectMonthYear() async {
    final result = await showDialog<Map<String, int>>(
      context: context,
      builder: (context) => _MonthYearPickerDialog(
        selectedMonth: _selectedMonth,
        selectedYear: _selectedYear,
      ),
    );

    if (result != null) {
      setState(() {
        _selectedMonth = result['month']!;
        _selectedYear = result['year']!;
      });
      _loadData(); // إعادة تحميل البيانات للشهر الجديد
    }
  }

  Future<void> _calculatePayroll(Employee employee) async {
    try {
      await _payrollService.calculatePayroll(
        employeeId: employee.id!,
        month: _selectedMonth,
        year: _selectedYear,
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('تم حساب راتب "${employee.displayName}" بنجاح'),
            backgroundColor: RevolutionaryColors.successGlow,
          ),
        );
      }

      _loadData();
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في حساب الراتب: ${e.toString()}'),
            backgroundColor: RevolutionaryColors.errorCoral,
          ),
        );
      }
    }
  }

  void _editPayroll(Employee employee, PayrollRecord payrollRecord) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => EmployeeSalaryCalculationScreen(
          employee: employee,
          month: _selectedMonth,
          year: _selectedYear,
        ),
      ),
    ).then((result) {
      if (result == true) {
        _loadData(); // إعادة تحميل البيانات
      }
    });
  }

  Future<void> _approvePayroll(PayrollRecord payrollRecord) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد اعتماد الراتب'),
        content: Text(
          'هل تريد اعتماد راتب الموظف بمبلغ ${payrollRecord.netSalary.toStringAsFixed(0)} ل.س؟',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            style: ElevatedButton.styleFrom(
              backgroundColor: RevolutionaryColors.successGlow,
            ),
            child: const Text('اعتماد'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        // هنا يمكن إضافة منطق اعتماد الراتب
        // مثل تغيير حالة الراتب إلى "معتمد"

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم اعتماد الراتب بنجاح'),
              backgroundColor: RevolutionaryColors.successGlow,
            ),
          );
          _loadData(); // إعادة تحميل البيانات
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('خطأ في اعتماد الراتب: ${e.toString()}'),
              backgroundColor: RevolutionaryColors.errorCoral,
            ),
          );
        }
      }
    }
  }

  void _showBulkPayrollDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('حساب الرواتب'),
        content: const Text('اختر نوع العملية'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _calculateAllPayrolls();
            },
            child: const Text('حساب جميع الرواتب'),
          ),
        ],
      ),
    );
  }

  Future<void> _calculateAllPayrolls() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد حساب جميع الرواتب'),
        content: Text(
          'هل تريد حساب رواتب جميع الموظفين النشطين للشهر ${_getMonthName(_selectedMonth)} $_selectedYear؟\n\n'
          'عدد الموظفين: ${_employees.length}',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            style: ElevatedButton.styleFrom(
              backgroundColor: RevolutionaryColors.successGlow,
            ),
            child: const Text('حساب الرواتب'),
          ),
        ],
      ),
    );

    if (confirmed == true && mounted) {
      try {
        // إظهار مؤشر التقدم
        showDialog(
          context: context,
          barrierDismissible: false,
          builder: (context) => const AlertDialog(
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                CircularProgressIndicator(),
                SizedBox(height: 16),
                Text('جاري حساب الرواتب...'),
              ],
            ),
          ),
        );

        int successCount = 0;
        int errorCount = 0;

        // حساب راتب كل موظف
        for (final employee in _employees) {
          try {
            await _payrollService.calculatePayroll(
              employeeId: employee.id!,
              month: _selectedMonth,
              year: _selectedYear,
            );
            successCount++;
          } catch (e) {
            errorCount++;
            LoggingService.error(
              'خطأ في حساب راتب الموظف',
              category: 'PayrollScreen',
              data: {
                'employeeId': employee.id,
                'employeeName': employee.displayName,
                'error': e.toString(),
              },
            );
          }
        }

        // إغلاق مؤشر التقدم
        if (mounted) {
          Navigator.pop(context);
        }

        // عرض النتيجة
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'تم حساب $successCount راتب بنجاح${errorCount > 0 ? ' مع $errorCount أخطاء' : ''}',
              ),
              backgroundColor: errorCount > 0
                  ? RevolutionaryColors.warningAmber
                  : RevolutionaryColors.successGlow,
            ),
          );
          _loadData(); // إعادة تحميل البيانات
        }
      } catch (e) {
        // إغلاق مؤشر التقدم في حالة الخطأ
        if (mounted) {
          Navigator.pop(context);
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('خطأ في حساب الرواتب: ${e.toString()}'),
              backgroundColor: RevolutionaryColors.errorCoral,
            ),
          );
        }
      }
    }
  }

  String _getMonthName(int month) {
    const months = [
      'يناير',
      'فبراير',
      'مارس',
      'أبريل',
      'مايو',
      'يونيو',
      'يوليو',
      'أغسطس',
      'سبتمبر',
      'أكتوبر',
      'نوفمبر',
      'ديسمبر',
    ];
    return months[month - 1];
  }
}

/// حوار منتقي الشهر والسنة
class _MonthYearPickerDialog extends StatefulWidget {
  final int selectedMonth;
  final int selectedYear;

  const _MonthYearPickerDialog({
    required this.selectedMonth,
    required this.selectedYear,
  });

  @override
  State<_MonthYearPickerDialog> createState() => _MonthYearPickerDialogState();
}

class _MonthYearPickerDialogState extends State<_MonthYearPickerDialog> {
  late int _selectedMonth;
  late int _selectedYear;

  @override
  void initState() {
    super.initState();
    _selectedMonth = widget.selectedMonth;
    _selectedYear = widget.selectedYear;
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('اختيار الشهر والسنة'),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          DropdownButtonFormField<int>(
            value: _selectedMonth,
            decoration: const InputDecoration(
              labelText: 'الشهر',
              border: OutlineInputBorder(),
            ),
            items: List.generate(12, (index) {
              final month = index + 1;
              return DropdownMenuItem(
                value: month,
                child: Text(_getMonthName(month)),
              );
            }),
            onChanged: (value) {
              setState(() {
                _selectedMonth = value!;
              });
            },
          ),
          const SizedBox(height: 16),
          DropdownButtonFormField<int>(
            value: _selectedYear,
            decoration: const InputDecoration(
              labelText: 'السنة',
              border: OutlineInputBorder(),
            ),
            items: List.generate(5, (index) {
              final year = DateTime.now().year - 2 + index;
              return DropdownMenuItem(
                value: year,
                child: Text(year.toString()),
              );
            }),
            onChanged: (value) {
              setState(() {
                _selectedYear = value!;
              });
            },
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('إلغاء'),
        ),
        ElevatedButton(
          onPressed: () => Navigator.pop(context, {
            'month': _selectedMonth,
            'year': _selectedYear,
          }),
          style: ElevatedButton.styleFrom(
            backgroundColor: RevolutionaryColors.successGlow,
          ),
          child: const Text('تأكيد'),
        ),
      ],
    );
  }

  String _getMonthName(int month) {
    const months = [
      'يناير',
      'فبراير',
      'مارس',
      'أبريل',
      'مايو',
      'يونيو',
      'يوليو',
      'أغسطس',
      'سبتمبر',
      'أكتوبر',
      'نوفمبر',
      'ديسمبر',
    ];
    return months[month - 1];
  }
}
