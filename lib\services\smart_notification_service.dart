/// خدمة نظام التنبيهات الذكية
/// تدير إنشاء وإرسال ومتابعة جميع أنواع التنبيهات
library;

import 'dart:async';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:sqflite/sqflite.dart';
import '../database/database_helper.dart';
import '../models/smart_notification_models.dart';
import '../services/invoice_service.dart';
import '../services/item_service.dart';
import '../services/payment_service.dart';
import '../services/logging_service.dart';
import '../services/invoice_status_service.dart';
import '../models/invoice_status.dart';
import '../models/payment.dart';

class SmartNotificationService {
  static final SmartNotificationService _instance =
      SmartNotificationService._internal();
  factory SmartNotificationService() => _instance;
  SmartNotificationService._internal();

  final DatabaseHelper _databaseHelper = DatabaseHelper();
  final InvoiceService _invoiceService = InvoiceService();
  final ItemService _itemService = ItemService();
  final PaymentService _paymentService = PaymentService();
  final InvoiceStatusService _invoiceStatusService = InvoiceStatusService();

  Timer? _notificationTimer;
  NotificationSettings _settings = const NotificationSettings();

  /// إنشاء جداول التنبيهات
  Future<void> createTables(Database db) async {
    // جدول التنبيهات الذكية
    await db.execute('''
      CREATE TABLE IF NOT EXISTS smart_notifications (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        title TEXT NOT NULL,
        message TEXT NOT NULL,
        type TEXT NOT NULL,
        priority TEXT NOT NULL,
        status TEXT NOT NULL,
        scheduled_time TEXT NOT NULL,
        sent_time TEXT,
        read_time TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT,
        related_entity_id TEXT,
        related_entity_type TEXT,
        metadata TEXT,
        methods TEXT NOT NULL,
        recurrence TEXT NOT NULL,
        expiry_time TEXT,
        snooze_count INTEGER DEFAULT 0,
        snooze_until TEXT,
        is_active INTEGER DEFAULT 1
      )
    ''');

    // جدول قوالب التنبيهات
    await db.execute('''
      CREATE TABLE IF NOT EXISTS notification_templates (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        title_template TEXT NOT NULL,
        message_template TEXT NOT NULL,
        type TEXT NOT NULL,
        default_priority TEXT NOT NULL,
        default_methods TEXT NOT NULL,
        default_metadata TEXT,
        is_active INTEGER DEFAULT 1,
        created_at TEXT NOT NULL,
        updated_at TEXT
      )
    ''');

    // جدول إعدادات التنبيهات
    await db.execute('''
      CREATE TABLE IF NOT EXISTS notification_settings (
        id INTEGER PRIMARY KEY,
        settings_json TEXT NOT NULL,
        updated_at TEXT NOT NULL
      )
    ''');

    // إنشاء فهارس للأداء
    await db.execute('''
      CREATE INDEX IF NOT EXISTS idx_notifications_scheduled_time 
      ON smart_notifications(scheduled_time)
    ''');

    await db.execute('''
      CREATE INDEX IF NOT EXISTS idx_notifications_status 
      ON smart_notifications(status)
    ''');

    await db.execute('''
      CREATE INDEX IF NOT EXISTS idx_notifications_type 
      ON smart_notifications(type)
    ''');
  }

  /// بدء خدمة التنبيهات
  Future<void> startNotificationService() async {
    await _loadSettings();
    await _createDefaultTemplates();
    _startNotificationTimer();
  }

  /// إيقاف خدمة التنبيهات
  void stopNotificationService() {
    _notificationTimer?.cancel();
    _notificationTimer = null;
  }

  /// إنشاء تنبيه جديد
  Future<int> createNotification(SmartNotification notification) async {
    final db = await _databaseHelper.database;

    return await db.insert('smart_notifications', {
      'title': notification.title,
      'message': notification.message,
      'type': notification.type.name,
      'priority': notification.priority.name,
      'status': notification.status.name,
      'scheduled_time': notification.scheduledTime.toIso8601String(),
      'sent_time': notification.sentTime?.toIso8601String(),
      'read_time': notification.readTime?.toIso8601String(),
      'created_at': notification.createdAt.toIso8601String(),
      'updated_at': notification.updatedAt?.toIso8601String(),
      'related_entity_id': notification.relatedEntityId,
      'related_entity_type': notification.relatedEntityType,
      'metadata': jsonEncode(notification.metadata),
      'methods': jsonEncode(notification.methods.map((m) => m.name).toList()),
      'recurrence': notification.recurrence.name,
      'expiry_time': notification.expiryTime?.toIso8601String(),
      'snooze_count': notification.snoozeCount,
      'snooze_until': notification.snoozeUntil?.toIso8601String(),
      'is_active': notification.isActive ? 1 : 0,
    });
  }

  /// الحصول على جميع التنبيهات
  Future<List<SmartNotification>> getAllNotifications({
    NotificationStatus? status,
    NotificationType? type,
    int? limit,
  }) async {
    final db = await _databaseHelper.database;

    String whereClause = 'is_active = 1';
    List<dynamic> whereArgs = [];

    if (status != null) {
      whereClause += ' AND status = ?';
      whereArgs.add(status.name);
    }

    if (type != null) {
      whereClause += ' AND type = ?';
      whereArgs.add(type.name);
    }

    final List<Map<String, dynamic>> maps = await db.query(
      'smart_notifications',
      where: whereClause,
      whereArgs: whereArgs,
      orderBy: 'scheduled_time DESC',
      limit: limit,
    );

    return maps.map((map) => _mapToNotification(map)).toList();
  }

  /// الحصول على التنبيهات المعلقة
  Future<List<SmartNotification>> getPendingNotifications() async {
    final now = DateTime.now();
    final db = await _databaseHelper.database;

    final List<Map<String, dynamic>> maps = await db.query(
      'smart_notifications',
      where: '''
        status = ? AND 
        is_active = 1 AND 
        scheduled_time <= ? AND 
        (expiry_time IS NULL OR expiry_time > ?) AND
        (snooze_until IS NULL OR snooze_until <= ?)
      ''',
      whereArgs: [
        NotificationStatus.pending.name,
        now.toIso8601String(),
        now.toIso8601String(),
        now.toIso8601String(),
      ],
      orderBy: 'priority DESC, scheduled_time ASC',
    );

    return maps.map((map) => _mapToNotification(map)).toList();
  }

  /// تحديث حالة التنبيه
  Future<void> updateNotificationStatus(
    int notificationId,
    NotificationStatus status, {
    DateTime? timestamp,
  }) async {
    final db = await _databaseHelper.database;
    final now = timestamp ?? DateTime.now();

    final updateData = <String, dynamic>{
      'status': status.name,
      'updated_at': now.toIso8601String(),
    };

    switch (status) {
      case NotificationStatus.sent:
        updateData['sent_time'] = now.toIso8601String();
        break;
      case NotificationStatus.read:
        updateData['read_time'] = now.toIso8601String();
        break;
      default:
        break;
    }

    await db.update(
      'smart_notifications',
      updateData,
      where: 'id = ?',
      whereArgs: [notificationId],
    );
  }

  /// تأجيل التنبيه
  Future<void> snoozeNotification(int notificationId, Duration duration) async {
    final db = await _databaseHelper.database;
    final snoozeUntil = DateTime.now().add(duration);

    await db.update(
      'smart_notifications',
      {
        'snooze_until': snoozeUntil.toIso8601String(),
        'snooze_count': 'snooze_count + 1',
        'updated_at': DateTime.now().toIso8601String(),
      },
      where: 'id = ?',
      whereArgs: [notificationId],
    );
  }

  /// إنشاء تنبيهات الدفعات المستحقة
  Future<void> createPaymentDueNotifications() async {
    try {
      // الحصول على الفواتير غير المدفوعة
      final unpaidInvoices = await _invoiceService.getUnpaidInvoices();

      for (final invoice in unpaidInvoices) {
        if (invoice.dueDate != null) {
          // الحصول على ملخص الدفعات للفاتورة
          final paymentSummary = await _paymentService.getPaymentSummary(
            invoice.id!,
          );

          final daysUntilDue = invoice.dueDate!
              .difference(DateTime.now())
              .inDays;

          // تنبيه قبل 3 أيام من الاستحقاق
          if (daysUntilDue == 3) {
            await createNotification(
              SmartNotification(
                title: 'دفعة مستحقة قريباً',
                message:
                    'الفاتورة ${invoice.invoiceNumber} مستحقة خلال 3 أيام - المبلغ المتبقي: ${paymentSummary.remainingAmount.toStringAsFixed(2)}',
                type: NotificationType.payment,
                priority: NotificationPriority.medium,
                scheduledTime: DateTime.now(),
                createdAt: DateTime.now(),
                relatedEntityId: invoice.id.toString(),
                relatedEntityType: 'invoice',
                metadata: {
                  'invoiceNumber': invoice.invoiceNumber,
                  'totalAmount': invoice.totalAmount,
                  'paidAmount': paymentSummary.paidAmount,
                  'remainingAmount': paymentSummary.remainingAmount,
                  'dueDate': invoice.dueDate!.toIso8601String(),
                  'paymentCount': paymentSummary.payments.length,
                },
              ),
            );
          }

          // تنبيه في يوم الاستحقاق
          if (daysUntilDue == 0) {
            await createNotification(
              SmartNotification(
                title: 'دفعة مستحقة اليوم',
                message:
                    'الفاتورة ${invoice.invoiceNumber} مستحقة اليوم - المبلغ المتبقي: ${paymentSummary.remainingAmount.toStringAsFixed(2)}',
                type: NotificationType.payment,
                priority: NotificationPriority.high,
                scheduledTime: DateTime.now(),
                createdAt: DateTime.now(),
                relatedEntityId: invoice.id.toString(),
                relatedEntityType: 'invoice',
                metadata: {
                  'invoiceNumber': invoice.invoiceNumber,
                  'totalAmount': invoice.totalAmount,
                  'paidAmount': paymentSummary.paidAmount,
                  'remainingAmount': paymentSummary.remainingAmount,
                  'dueDate': invoice.dueDate!.toIso8601String(),
                  'paymentCount': paymentSummary.payments.length,
                },
              ),
            );
          }

          // تنبيه للدفعات المتأخرة
          if (daysUntilDue < 0) {
            await createNotification(
              SmartNotification(
                title: 'دفعة متأخرة',
                message:
                    'الفاتورة ${invoice.invoiceNumber} متأخرة ${-daysUntilDue} يوم - المبلغ المتبقي: ${paymentSummary.remainingAmount.toStringAsFixed(2)}',
                type: NotificationType.warning,
                priority: NotificationPriority.urgent,
                scheduledTime: DateTime.now(),
                createdAt: DateTime.now(),
                relatedEntityId: invoice.id.toString(),
                relatedEntityType: 'invoice',
                metadata: {
                  'invoiceNumber': invoice.invoiceNumber,
                  'totalAmount': invoice.totalAmount,
                  'paidAmount': paymentSummary.paidAmount,
                  'remainingAmount': paymentSummary.remainingAmount,
                  'dueDate': invoice.dueDate!.toIso8601String(),
                  'daysOverdue': -daysUntilDue,
                  'paymentCount': paymentSummary.payments.length,
                },
              ),
            );
          }
        }
      }
    } catch (e) {
      // تسجيل الخطأ
      LoggingService.error(
        'خطأ في إنشاء تنبيهات الدفعات',
        category: 'SmartNotifications',
        data: {'error': e.toString()},
      );
    }
  }

  /// إنشاء تنبيهات المخزون المنخفض
  Future<void> createLowStockNotifications() async {
    try {
      final lowStockItems = await _itemService.getLowStockItems();

      for (final item in lowStockItems) {
        await createNotification(
          SmartNotification(
            title: 'مخزون منخفض',
            message: 'الصنف ${item.name} وصل إلى الحد الأدنى للمخزون',
            type: NotificationType.inventory,
            priority: NotificationPriority.medium,
            scheduledTime: DateTime.now(),
            createdAt: DateTime.now(),
            relatedEntityId: item.id.toString(),
            relatedEntityType: 'item',
            metadata: {
              'itemName': item.name,
              'currentStock': item.quantity,
              'minimumStock': item.minQuantity,
            },
          ),
        );
      }
    } catch (e) {
      LoggingService.error(
        'خطأ في إنشاء تنبيهات المخزون',
        category: 'SmartNotifications',
        data: {'error': e.toString()},
      );
    }
  }

  /// إنشاء تنبيهات الدفعات الجزئية
  Future<void> createPartialPaymentNotifications() async {
    try {
      // الحصول على الفواتير المدفوعة جزئياً
      final partiallyPaidInvoices = await _invoiceStatusService
          .getInvoicesByStatus(InvoiceStatus.partiallyPaid);

      for (final invoice in partiallyPaidInvoices) {
        final paymentSummary = await _paymentService.getPaymentSummary(
          invoice.id!,
        );

        // إنشاء تنبيه للدفعات الجزئية
        await createNotification(
          SmartNotification(
            title: 'دفعة جزئية مسجلة',
            message:
                'تم دفع ${paymentSummary.paidAmount.toStringAsFixed(2)} من أصل ${paymentSummary.totalAmount.toStringAsFixed(2)} للفاتورة ${invoice.invoiceNumber}',
            type: NotificationType.payment,
            priority: NotificationPriority.low,
            scheduledTime: DateTime.now(),
            createdAt: DateTime.now(),
            relatedEntityId: invoice.id.toString(),
            relatedEntityType: 'invoice',
            metadata: {
              'invoiceNumber': invoice.invoiceNumber,
              'totalAmount': paymentSummary.totalAmount,
              'paidAmount': paymentSummary.paidAmount,
              'remainingAmount': paymentSummary.remainingAmount,
              'paymentPercentage':
                  (paymentSummary.paidAmount / paymentSummary.totalAmount * 100)
                      .toStringAsFixed(1),
              'paymentCount': paymentSummary.payments.length,
            },
          ),
        );
      }
    } catch (e) {
      LoggingService.error(
        'خطأ في إنشاء تنبيهات الدفعات الجزئية',
        category: 'SmartNotifications',
        data: {'error': e.toString()},
      );
    }
  }

  /// إنشاء تنبيهات تأكيد الدفعات
  Future<void> createPaymentConfirmationNotifications() async {
    try {
      // الحصول على الدفعات المعلقة التي تحتاج تأكيد
      final pendingPayments = await _paymentService.searchPayments(
        status: PaymentStatus.pending,
      );

      for (final payment in pendingPayments) {
        // فحص إذا كانت الدفعة معلقة لأكثر من 24 ساعة
        final hoursSinceCreation = DateTime.now()
            .difference(payment.createdAt)
            .inHours;

        if (hoursSinceCreation >= 24) {
          final invoice = await _invoiceService.getInvoiceById(
            payment.invoiceId,
          );

          await createNotification(
            SmartNotification(
              title: 'دفعة تحتاج تأكيد',
              message:
                  'الدفعة بقيمة ${payment.amount.toStringAsFixed(2)} للفاتورة ${invoice?.invoiceNumber} معلقة منذ $hoursSinceCreation ساعة',
              type: NotificationType.warning,
              priority: NotificationPriority.medium,
              scheduledTime: DateTime.now(),
              createdAt: DateTime.now(),
              relatedEntityId: payment.id.toString(),
              relatedEntityType: 'payment',
              metadata: {
                'paymentId': payment.id,
                'invoiceNumber': invoice?.invoiceNumber,
                'amount': payment.amount,
                'method': payment.method.displayName,
                'hoursPending': hoursSinceCreation,
                'reference': payment.reference,
              },
            ),
          );
        }
      }
    } catch (e) {
      LoggingService.error(
        'خطأ في إنشاء تنبيهات تأكيد الدفعات',
        category: 'SmartNotifications',
        data: {'error': e.toString()},
      );
    }
  }

  /// إنشاء تنبيهات الدفعات الناجحة
  Future<void> createSuccessfulPaymentNotifications() async {
    try {
      // الحصول على الدفعات المؤكدة حديثاً (آخر 24 ساعة)
      final yesterday = DateTime.now().subtract(const Duration(days: 1));
      final recentPayments = await _paymentService.searchPayments(
        status: PaymentStatus.confirmed,
        fromDate: yesterday,
      );

      for (final payment in recentPayments) {
        final invoice = await _invoiceService.getInvoiceById(payment.invoiceId);
        final paymentSummary = await _paymentService.getPaymentSummary(
          payment.invoiceId,
        );

        // إنشاء تنبيه للدفعة الناجحة
        await createNotification(
          SmartNotification(
            title: 'تم تأكيد الدفعة',
            message:
                'تم تأكيد دفعة بقيمة ${payment.amount.toStringAsFixed(2)} للفاتورة ${invoice?.invoiceNumber}',
            type: NotificationType.payment,
            priority: NotificationPriority.low,
            scheduledTime: DateTime.now(),
            createdAt: DateTime.now(),
            relatedEntityId: payment.id.toString(),
            relatedEntityType: 'payment',
            metadata: {
              'paymentId': payment.id,
              'invoiceNumber': invoice?.invoiceNumber,
              'amount': payment.amount,
              'method': payment.method.displayName,
              'reference': payment.reference,
              'remainingAmount': paymentSummary.remainingAmount,
              'isFullyPaid': paymentSummary.remainingAmount <= 0.01,
            },
          ),
        );
      }
    } catch (e) {
      LoggingService.error(
        'خطأ في إنشاء تنبيهات الدفعات الناجحة',
        category: 'SmartNotifications',
        data: {'error': e.toString()},
      );
    }
  }

  /// بدء مؤقت التنبيهات
  void _startNotificationTimer() {
    _notificationTimer = Timer.periodic(
      const Duration(minutes: 5), // فحص كل 5 دقائق
      (timer) async {
        await _processNotifications();
      },
    );
  }

  /// معالجة التنبيهات المعلقة
  Future<void> _processNotifications() async {
    if (!_settings.enableNotifications) return;

    final pendingNotifications = await getPendingNotifications();

    for (final notification in pendingNotifications) {
      if (_shouldSendNotification(notification)) {
        await _sendNotification(notification);
      }
    }
  }

  /// فحص ما إذا كان يجب إرسال التنبيه
  bool _shouldSendNotification(SmartNotification notification) {
    // فحص الساعات الهادئة
    if (_settings.enableQuietHours && _isQuietHours()) {
      return false;
    }

    // فحص إعدادات النوع
    if (_settings.typeSettings[notification.type] == false) {
      return false;
    }

    // فحص إعدادات الأولوية
    if (_settings.prioritySettings[notification.priority] == false) {
      return false;
    }

    return true;
  }

  /// إرسال التنبيه
  Future<void> _sendNotification(SmartNotification notification) async {
    try {
      // إرسال التنبيه حسب الطرق المحددة
      for (final method in notification.methods) {
        if (_settings.methodSettings[method] != false) {
          await _sendByMethod(notification, method);
        }
      }

      // تحديث حالة التنبيه
      await updateNotificationStatus(notification.id!, NotificationStatus.sent);

      // إنشاء التنبيه التالي إذا كان متكرراً
      if (notification.recurrence != NotificationRecurrence.once) {
        await _createRecurringNotification(notification);
      }
    } catch (e) {
      LoggingService.error(
        'خطأ في إرسال التنبيه',
        category: 'SmartNotifications',
        data: {'error': e.toString()},
      );
    }
  }

  /// إرسال التنبيه بطريقة محددة
  Future<void> _sendByMethod(
    SmartNotification notification,
    NotificationMethod method,
  ) async {
    switch (method) {
      case NotificationMethod.inApp:
        // التنبيه داخل التطبيق - سيتم عرضه في الواجهة
        break;
      case NotificationMethod.push:
        // إشعار فوري - يحتاج إلى تكامل مع خدمة الإشعارات
        break;
      case NotificationMethod.email:
        // بريد إلكتروني - يحتاج إلى خدمة البريد
        break;
      case NotificationMethod.sms:
        // رسالة نصية - يحتاج إلى خدمة الرسائل
        break;
      case NotificationMethod.desktop:
        // إشعار سطح المكتب
        break;
      case NotificationMethod.sound:
        // تشغيل صوت
        break;
    }
  }

  /// فحص الساعات الهادئة
  bool _isQuietHours() {
    if (!_settings.enableQuietHours) return false;

    final now = TimeOfDay.now();
    final start = _settings.quietHoursStart;
    final end = _settings.quietHoursEnd;

    if (start == null || end == null) return false;

    // تحويل الأوقات إلى دقائق للمقارنة
    final nowMinutes = now.hour * 60 + now.minute;
    final startMinutes = start.hour * 60 + start.minute;
    final endMinutes = end.hour * 60 + end.minute;

    // إذا كان وقت البداية أقل من وقت النهاية (نفس اليوم)
    if (startMinutes < endMinutes) {
      return nowMinutes >= startMinutes && nowMinutes <= endMinutes;
    }
    // إذا كان وقت البداية أكبر من وقت النهاية (عبر منتصف الليل)
    else {
      return nowMinutes >= startMinutes || nowMinutes <= endMinutes;
    }
  }

  /// تحميل الإعدادات
  Future<void> _loadSettings() async {
    try {
      final db = await _databaseHelper.database;
      final List<Map<String, dynamic>> maps = await db.query(
        'notification_settings',
        where: 'id = ?',
        whereArgs: [1],
      );

      if (maps.isNotEmpty) {
        final settingsJson = maps.first['settings_json'] as String;
        final settingsMap = jsonDecode(settingsJson);
        _settings = NotificationSettings.fromJson(settingsMap);
      }
    } catch (e) {
      LoggingService.error(
        'خطأ في تحميل إعدادات التنبيهات',
        category: 'SmartNotifications',
        data: {'error': e.toString()},
      );
    }
  }

  /// إنشاء القوالب الافتراضية
  Future<void> _createDefaultTemplates() async {
    try {
      final db = await _databaseHelper.database;

      // قالب تنبيه الدفعات المستحقة
      await db.insert('notification_templates', {
        'name': 'payment_due',
        'title': 'دفعة مستحقة',
        'message': 'لديك دفعة مستحقة بقيمة {amount} {currency}',
        'type': 'payment',
        'priority': 'high',
        'created_at': DateTime.now().toIso8601String(),
      });

      // قالب تنبيه المخزون المنخفض
      await db.insert('notification_templates', {
        'name': 'low_stock',
        'title': 'مخزون منخفض',
        'message': 'الصنف {itemName} وصل إلى الحد الأدنى للمخزون',
        'type': 'inventory',
        'priority': 'medium',
        'created_at': DateTime.now().toIso8601String(),
      });

      // قالب تنبيه الضرائب
      await db.insert('notification_templates', {
        'name': 'tax_due',
        'title': 'موعد ضريبي',
        'message': 'موعد تقديم الإقرار الضريبي {taxType} في {dueDate}',
        'type': 'tax',
        'priority': 'high',
        'created_at': DateTime.now().toIso8601String(),
      });

      LoggingService.info(
        'تم إنشاء القوالب الافتراضية للتنبيهات',
        category: 'SmartNotifications',
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في إنشاء القوالب الافتراضية',
        category: 'SmartNotifications',
        data: {'error': e.toString()},
      );
    }
  }

  /// إنشاء تنبيه متكرر
  Future<void> _createRecurringNotification(
    SmartNotification notification,
  ) async {
    try {
      if (notification.recurrence == NotificationRecurrence.once) {
        return;
      }

      DateTime nextDate = notification.scheduledTime;

      // حساب التاريخ التالي حسب نمط التكرار
      switch (notification.recurrence) {
        case NotificationRecurrence.daily:
          nextDate = nextDate.add(const Duration(days: 1));
          break;
        case NotificationRecurrence.weekly:
          nextDate = nextDate.add(const Duration(days: 7));
          break;
        case NotificationRecurrence.monthly:
          nextDate = DateTime(
            nextDate.year,
            nextDate.month + 1,
            nextDate.day,
            nextDate.hour,
            nextDate.minute,
          );
          break;
        case NotificationRecurrence.yearly:
          nextDate = DateTime(
            nextDate.year + 1,
            nextDate.month,
            nextDate.day,
            nextDate.hour,
            nextDate.minute,
          );
          break;
        case NotificationRecurrence.quarterly:
          nextDate = DateTime(
            nextDate.year,
            nextDate.month + 3,
            nextDate.day,
            nextDate.hour,
            nextDate.minute,
          );
          break;
        case NotificationRecurrence.custom:
          // للتكرار المخصص، نحتاج لمنطق إضافي
          return;
        case NotificationRecurrence.once:
          return; // لا نحتاج لإنشاء تكرار
      }

      // إنشاء التنبيه التالي
      final nextNotification = SmartNotification(
        title: notification.title,
        message: notification.message,
        type: notification.type,
        priority: notification.priority,
        scheduledTime: nextDate,
        createdAt: DateTime.now(),
        recurrence: notification.recurrence,
        relatedEntityId: notification.relatedEntityId,
        relatedEntityType: notification.relatedEntityType,
        metadata: notification.metadata,
        methods: notification.methods,
      );

      await createNotification(nextNotification);

      LoggingService.info(
        'تم إنشاء تنبيه متكرر للتاريخ: ${nextDate.toIso8601String()}',
        category: 'SmartNotifications',
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في إنشاء التنبيه المتكرر',
        category: 'SmartNotifications',
        data: {'error': e.toString()},
      );
    }
  }

  /// تحويل البيانات إلى تنبيه
  SmartNotification _mapToNotification(Map<String, dynamic> map) {
    return SmartNotification(
      id: map['id'],
      title: map['title'],
      message: map['message'],
      type: NotificationType.values.firstWhere((e) => e.name == map['type']),
      priority: NotificationPriority.values.firstWhere(
        (e) => e.name == map['priority'],
      ),
      status: NotificationStatus.values.firstWhere(
        (e) => e.name == map['status'],
      ),
      scheduledTime: DateTime.parse(map['scheduled_time']),
      sentTime: map['sent_time'] != null
          ? DateTime.parse(map['sent_time'])
          : null,
      readTime: map['read_time'] != null
          ? DateTime.parse(map['read_time'])
          : null,
      createdAt: DateTime.parse(map['created_at']),
      updatedAt: map['updated_at'] != null
          ? DateTime.parse(map['updated_at'])
          : null,
      relatedEntityId: map['related_entity_id'],
      relatedEntityType: map['related_entity_type'],
      metadata: map['metadata'] != null ? jsonDecode(map['metadata']) : {},
      methods: (jsonDecode(map['methods']) as List)
          .map((m) => NotificationMethod.values.firstWhere((e) => e.name == m))
          .toList(),
      recurrence: NotificationRecurrence.values.firstWhere(
        (e) => e.name == map['recurrence'],
      ),
      expiryTime: map['expiry_time'] != null
          ? DateTime.parse(map['expiry_time'])
          : null,
      snoozeCount: map['snooze_count'] ?? 0,
      snoozeUntil: map['snooze_until'] != null
          ? DateTime.parse(map['snooze_until'])
          : null,
      isActive: map['is_active'] == 1,
    );
  }
}
