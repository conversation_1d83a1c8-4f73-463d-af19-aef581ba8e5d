/// شاشة أداء النظام والإحصائيات المتقدمة
/// تعرض إحصائيات شاملة وأداء النظام مع رسوم بيانية تفاعلية
library;

import 'dart:convert';
import 'package:flutter/material.dart';
import '../services/performance_optimization_service.dart';
import '../services/memory_management_service.dart';
import '../services/cache_service.dart';
import '../services/report_cache_service.dart';
import '../constants/revolutionary_design_colors.dart';
import '../widgets/loading_widget.dart';
import '../widgets/interactive_chart_widget.dart';
import '../responsive/responsive.dart';
import '../services/chart_service.dart';

class SystemPerformanceScreen extends StatefulWidget {
  const SystemPerformanceScreen({super.key});

  @override
  State<SystemPerformanceScreen> createState() =>
      _SystemPerformanceScreenState();
}

class _SystemPerformanceScreenState extends State<SystemPerformanceScreen>
    with TickerProviderStateMixin {
  final PerformanceOptimizationService _performanceService =
      PerformanceOptimizationService();
  final MemoryManagementService _memoryService = MemoryManagementService();

  late TabController _tabController;
  bool _isLoading = true;
  Map<String, dynamic> _systemMetrics = {};
  Map<String, dynamic> _memoryStats = {};
  List<Map<String, dynamic>> _performanceHistory = [];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _loadPerformanceData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadPerformanceData() async {
    setState(() => _isLoading = true);
    try {
      final metrics = await _performanceService.getSystemMetrics();
      final memory = _memoryService.getStatistics();
      final history = await _performanceService.getPerformanceHistory();

      setState(() {
        _systemMetrics = metrics;
        _memoryStats = _convertMemoryStatsToMap(memory);
        _performanceHistory = history;
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      _showErrorSnackBar('خطأ في تحميل البيانات: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const ResponsiveText.h2('أداء النظام والإحصائيات'),
        backgroundColor: RevolutionaryColors.damascusSky,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            onPressed: _optimizeSystem,
            icon: const Icon(Icons.tune),
            tooltip: 'تحسين النظام',
          ),
          IconButton(
            onPressed: _clearCache,
            icon: const Icon(Icons.clear_all),
            tooltip: 'مسح الذاكرة المؤقتة',
          ),
          IconButton(
            onPressed: _exportReport,
            icon: const Icon(Icons.file_download),
            tooltip: 'تصدير التقرير',
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          isScrollable: true,
          tabs: const [
            Tab(icon: Icon(Icons.dashboard), text: 'لوحة التحكم'),
            Tab(icon: Icon(Icons.memory), text: 'الذاكرة'),
            Tab(icon: Icon(Icons.speed), text: 'الأداء'),
            Tab(icon: Icon(Icons.analytics), text: 'التحليلات'),
          ],
        ),
      ),
      body: _isLoading
          ? const LoadingWidget()
          : TabBarView(
              controller: _tabController,
              children: [
                _buildDashboardTab(),
                _buildMemoryTab(),
                _buildPerformanceTab(),
                _buildAnalyticsTab(),
              ],
            ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _refreshData,
        backgroundColor: RevolutionaryColors.damascusSky,
        icon: const Icon(Icons.refresh, color: Colors.white),
        label: const Text('تحديث', style: TextStyle(color: Colors.white)),
      ),
    );
  }

  Widget _buildDashboardTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // مؤشرات الأداء الرئيسية
          const ResponsiveText.h3('مؤشرات الأداء الرئيسية'),
          const SizedBox(height: 16),

          _buildSystemMetricsGrid(),

          const SizedBox(height: 24),

          // رسم بياني للأداء
          const ResponsiveText.h4('أداء النظام خلال الساعة الماضية'),
          const SizedBox(height: 16),

          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: SizedBox(
                height: 200,
                child: InteractiveChartWidget(
                  reportType: 'performance_metrics',
                  data: _getPerformanceChartData(),
                  initialChartType: ChartType.line,
                ),
              ),
            ),
          ),

          const SizedBox(height: 24),

          // حالة الخدمات
          const ResponsiveText.h4('حالة الخدمات'),
          const SizedBox(height: 16),

          _buildServicesStatus(),
        ],
      ),
    );
  }

  Widget _buildMemoryTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // إحصائيات الذاكرة
          const ResponsiveText.h3('إحصائيات الذاكرة'),
          const SizedBox(height: 16),

          _buildMemoryStatsGrid(),

          const SizedBox(height: 24),

          // توزيع استخدام الذاكرة
          const ResponsiveText.h4('توزيع استخدام الذاكرة'),
          const SizedBox(height: 16),

          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: SizedBox(
                height: 250,
                child: InteractiveChartWidget(
                  reportType: 'memory_distribution',
                  data: _getMemoryDistributionData(),
                  initialChartType: ChartType.pie,
                ),
              ),
            ),
          ),

          const SizedBox(height: 24),

          // إجراءات إدارة الذاكرة
          const ResponsiveText.h4('إدارة الذاكرة'),
          const SizedBox(height: 16),

          _buildMemoryActions(),
        ],
      ),
    );
  }

  Widget _buildPerformanceTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // مؤشرات الأداء المفصلة
          const ResponsiveText.h3('مؤشرات الأداء المفصلة'),
          const SizedBox(height: 16),

          _buildDetailedPerformanceGrid(),

          const SizedBox(height: 24),

          // تحليل الاختناقات
          const ResponsiveText.h4('تحليل الاختناقات'),
          const SizedBox(height: 16),

          _buildBottleneckAnalysis(),

          const SizedBox(height: 24),

          // توصيات التحسين
          const ResponsiveText.h4('توصيات التحسين'),
          const SizedBox(height: 16),

          _buildOptimizationRecommendations(),
        ],
      ),
    );
  }

  Widget _buildAnalyticsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // تحليل الاتجاهات
          const ResponsiveText.h3('تحليل الاتجاهات'),
          const SizedBox(height: 16),

          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: SizedBox(
                height: 200,
                child: InteractiveChartWidget(
                  reportType: 'performance_trends',
                  data: _getTrendsData(),
                  initialChartType: ChartType.line,
                ),
              ),
            ),
          ),

          const SizedBox(height: 24),

          // إحصائيات الاستخدام
          const ResponsiveText.h4('إحصائيات الاستخدام'),
          const SizedBox(height: 16),

          _buildUsageStatistics(),

          const SizedBox(height: 24),

          // تقرير الأداء
          const ResponsiveText.h4('تقرير الأداء'),
          const SizedBox(height: 16),

          _buildPerformanceReport(),
        ],
      ),
    );
  }

  Widget _buildSystemMetricsGrid() {
    return GridView.count(
      crossAxisCount: context.isTablet ? 4 : 2,
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisSpacing: 16,
      mainAxisSpacing: 16,
      children: [
        _buildMetricCard(
          'استخدام المعالج',
          '${_systemMetrics['cpu_usage'] ?? 0}%',
          Icons.memory,
          _getUsageColor(_systemMetrics['cpu_usage'] ?? 0),
        ),
        _buildMetricCard(
          'استخدام الذاكرة',
          '${_systemMetrics['memory_usage'] ?? 0}%',
          Icons.storage,
          _getUsageColor(_systemMetrics['memory_usage'] ?? 0),
        ),
        _buildMetricCard(
          'مساحة القرص',
          '${_systemMetrics['disk_usage'] ?? 0}%',
          Icons.storage,
          _getUsageColor(_systemMetrics['disk_usage'] ?? 0),
        ),
        _buildMetricCard(
          'سرعة الاستجابة',
          '${_systemMetrics['response_time'] ?? 0} مللي ثانية',
          Icons.speed,
          RevolutionaryColors.damascusSky,
        ),
      ],
    );
  }

  Widget _buildMemoryStatsGrid() {
    return GridView.count(
      crossAxisCount: context.isTablet ? 3 : 2,
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisSpacing: 16,
      mainAxisSpacing: 16,
      children: [
        _buildMetricCard(
          'الذاكرة المستخدمة',
          '${_memoryStats['used_memory'] ?? 0} MB',
          Icons.memory,
          RevolutionaryColors.warningAmber,
        ),
        _buildMetricCard(
          'الذاكرة المتاحة',
          '${_memoryStats['available_memory'] ?? 0} MB',
          Icons.memory,
          RevolutionaryColors.successGlow,
        ),
        _buildMetricCard(
          'الذاكرة المؤقتة',
          '${_memoryStats['cache_memory'] ?? 0} MB',
          Icons.cached,
          RevolutionaryColors.damascusSky,
        ),
      ],
    );
  }

  Widget _buildDetailedPerformanceGrid() {
    return GridView.count(
      crossAxisCount: context.isTablet ? 4 : 2,
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisSpacing: 16,
      mainAxisSpacing: 16,
      children: [
        _buildMetricCard(
          'عدد العمليات النشطة',
          '${_systemMetrics['active_processes'] ?? 0}',
          Icons.play_circle,
          RevolutionaryColors.damascusSky,
        ),
        _buildMetricCard(
          'معدل الإدخال/الإخراج',
          '${_systemMetrics['io_rate'] ?? 0} MB/s',
          Icons.swap_horiz,
          RevolutionaryColors.successGlow,
        ),
        _buildMetricCard(
          'استخدام الشبكة',
          '${_systemMetrics['network_usage'] ?? 0} KB/s',
          Icons.network_check,
          RevolutionaryColors.warningAmber,
        ),
        _buildMetricCard(
          'درجة حرارة المعالج',
          '${_systemMetrics['cpu_temperature'] ?? 0}°C',
          Icons.thermostat,
          _getTemperatureColor(_systemMetrics['cpu_temperature'] ?? 0),
        ),
      ],
    );
  }

  Widget _buildMetricCard(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, color: color, size: 32),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: color,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: TextStyle(fontSize: 11, color: color.withValues(alpha: 0.8)),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildServicesStatus() {
    final services = [
      {
        'name': 'خدمة قاعدة البيانات',
        'status': 'نشطة',
        'color': RevolutionaryColors.successGlow,
      },
      {
        'name': 'خدمة النسخ الاحتياطية',
        'status': 'نشطة',
        'color': RevolutionaryColors.successGlow,
      },
      {
        'name': 'خدمة التقارير',
        'status': 'نشطة',
        'color': RevolutionaryColors.successGlow,
      },
      {
        'name': 'خدمة التحديثات',
        'status': 'متوقفة',
        'color': RevolutionaryColors.errorCoral,
      },
    ];

    return Column(
      children: services.map((service) {
        return Card(
          margin: const EdgeInsets.only(bottom: 8),
          child: ListTile(
            leading: CircleAvatar(
              backgroundColor: service['color'] as Color,
              child: Icon(
                service['status'] == 'نشطة' ? Icons.check_circle : Icons.error,
                color: service['color'] as Color,
              ),
            ),
            title: Text(service['name'] as String),
            subtitle: Text(service['status'] as String),
            trailing: IconButton(
              onPressed: () => _toggleService(service['name'] as String),
              icon: Icon(
                service['status'] == 'نشطة' ? Icons.stop : Icons.play_arrow,
              ),
            ),
          ),
        );
      }).toList(),
    );
  }

  Widget _buildMemoryActions() {
    return Column(
      children: [
        Card(
          child: ListTile(
            leading: const Icon(Icons.cleaning_services),
            title: const Text('تنظيف الذاكرة'),
            subtitle: const Text('تحرير الذاكرة غير المستخدمة'),
            trailing: ElevatedButton(
              onPressed: _cleanMemory,
              child: const Text('تنظيف'),
            ),
          ),
        ),
        Card(
          child: ListTile(
            leading: const Icon(Icons.cached),
            title: const Text('مسح الذاكرة المؤقتة'),
            subtitle: const Text('حذف الملفات المؤقتة'),
            trailing: ElevatedButton(
              onPressed: _clearCache,
              child: const Text('مسح'),
            ),
          ),
        ),
        Card(
          child: ListTile(
            leading: const Icon(Icons.compress),
            title: const Text('ضغط الذاكرة'),
            subtitle: const Text('تحسين استخدام الذاكرة'),
            trailing: ElevatedButton(
              onPressed: _compressMemory,
              child: const Text('ضغط'),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildBottleneckAnalysis() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'تحليل الاختناقات المكتشفة:',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            _buildBottleneckItem(
              'استخدام عالي للمعالج',
              'المعالج يعمل بنسبة 85%',
              RevolutionaryColors.warningAmber,
            ),
            _buildBottleneckItem(
              'ذاكرة منخفضة',
              'الذاكرة المتاحة أقل من 20%',
              RevolutionaryColors.errorCoral,
            ),
            _buildBottleneckItem(
              'عمليات إدخال/إخراج بطيئة',
              'سرعة القرص أقل من المتوقع',
              RevolutionaryColors.warningAmber,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBottleneckItem(String title, String description, Color color) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          Icon(Icons.warning, color: color, size: 20),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(fontWeight: FontWeight.w500),
                ),
                Text(
                  description,
                  style: TextStyle(color: Colors.grey[600], fontSize: 12),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOptimizationRecommendations() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'توصيات التحسين:',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            _buildRecommendationItem(
              'إغلاق التطبيقات غير المستخدمة',
              'لتوفير ذاكرة إضافية',
              Icons.close,
            ),
            _buildRecommendationItem(
              'تنظيف الملفات المؤقتة',
              'لتحرير مساحة القرص',
              Icons.cleaning_services,
            ),
            _buildRecommendationItem(
              'إعادة تشغيل الخدمات',
              'لتحسين الأداء العام',
              Icons.refresh,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRecommendationItem(
    String title,
    String description,
    IconData icon,
  ) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          Icon(icon, color: RevolutionaryColors.damascusSky, size: 20),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(fontWeight: FontWeight.w500),
                ),
                Text(
                  description,
                  style: TextStyle(color: Colors.grey[600], fontSize: 12),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildUsageStatistics() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'إحصائيات الاستخدام اليومي:',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            _buildUsageItem('متوسط استخدام المعالج', '65%'),
            _buildUsageItem('متوسط استخدام الذاكرة', '72%'),
            _buildUsageItem('عدد العمليات المنجزة', '1,247'),
            _buildUsageItem('وقت التشغيل', '18 ساعة 32 دقيقة'),
          ],
        ),
      ),
    );
  }

  Widget _buildUsageItem(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label),
          Text(
            value,
            style: TextStyle(
              fontWeight: FontWeight.bold,
              color: RevolutionaryColors.damascusSky,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPerformanceReport() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'تقرير الأداء العام:',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            Text(
              'النظام يعمل بكفاءة جيدة مع بعض المجالات التي تحتاج تحسين. '
              'استخدام المعالج ضمن المعدل الطبيعي، لكن الذاكرة تحتاج إلى تحسين. '
              'يُنصح بتنظيف الملفات المؤقتة وإعادة تشغيل بعض الخدمات.',
              style: TextStyle(color: Colors.grey[700]),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _generateDetailedReport,
                    icon: const Icon(Icons.assessment),
                    label: const Text('تقرير مفصل'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: RevolutionaryColors.damascusSky,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _scheduleOptimization,
                    icon: const Icon(Icons.schedule),
                    label: const Text('جدولة التحسين'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: RevolutionaryColors.successGlow,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  // دالة تحويل إحصائيات الذاكرة إلى خريطة
  Map<String, dynamic> _convertMemoryStatsToMap(dynamic memoryStats) {
    if (memoryStats == null) {
      return {'used_memory': 0, 'available_memory': 0, 'cache_memory': 0};
    }

    // إذا كان memoryStats من نوع MemoryStatistics
    return {
      'used_memory': memoryStats.currentUsageMB?.round() ?? 0,
      'available_memory':
          ((memoryStats.totalMemoryMB ?? 0) - (memoryStats.currentUsageMB ?? 0))
              .round(),
      'cache_memory':
          (memoryStats.cacheSize ?? 0) ~/
          (1024 * 1024), // تحويل من بايت إلى ميجابايت
    };
  }

  // دوال مساعدة لإنشاء البيانات
  List<Map<String, dynamic>> _getPerformanceChartData() {
    // استخدام البيانات الفعلية من تاريخ الأداء إذا كانت متوفرة
    if (_performanceHistory.isNotEmpty) {
      return _performanceHistory.take(10).map((entry) {
        return {
          'time': entry['timestamp'] ?? DateTime.now().toString(),
          'cpu': entry['cpu_usage'] ?? 0,
          'memory': entry['memory_usage'] ?? 0,
        };
      }).toList();
    }

    // البيانات الافتراضية إذا لم تكن البيانات الفعلية متوفرة
    return [
      {'time': '00:00', 'cpu': 45, 'memory': 60},
      {'time': '06:00', 'cpu': 30, 'memory': 55},
      {'time': '12:00', 'cpu': 75, 'memory': 80},
      {'time': '18:00', 'cpu': 85, 'memory': 75},
      {'time': '24:00', 'cpu': 40, 'memory': 65},
    ];
  }

  List<Map<String, dynamic>> _getMemoryDistributionData() {
    return [
      {'type': 'مستخدمة', 'value': 60},
      {'type': 'مؤقتة', 'value': 25},
      {'type': 'متاحة', 'value': 15},
    ];
  }

  List<Map<String, dynamic>> _getTrendsData() {
    return [
      {'day': 'السبت', 'performance': 85},
      {'day': 'الأحد', 'performance': 78},
      {'day': 'الاثنين', 'performance': 92},
      {'day': 'الثلاثاء', 'performance': 88},
      {'day': 'الأربعاء', 'performance': 95},
      {'day': 'الخميس', 'performance': 82},
      {'day': 'الجمعة', 'performance': 90},
    ];
  }

  Color _getUsageColor(int usage) {
    if (usage < 50) return RevolutionaryColors.successGlow;
    if (usage < 80) return RevolutionaryColors.warningAmber;
    return RevolutionaryColors.errorCoral;
  }

  Color _getTemperatureColor(int temperature) {
    if (temperature < 60) return RevolutionaryColors.successGlow;
    if (temperature < 80) return RevolutionaryColors.warningAmber;
    return RevolutionaryColors.errorCoral;
  }

  // دوال الإجراءات
  Future<void> _optimizeSystem() async {
    _showInfoSnackBar('جاري تحسين النظام...');
    try {
      // تنفيذ تحسين شامل للنظام
      final result = await _performanceService
          .performComprehensiveOptimization();

      if (result.success) {
        _showSuccessSnackBar(
          'تم تحسين النظام بنجاح في ${result.totalTimeMs} مللي ثانية',
        );
        await _loadPerformanceData(); // إعادة تحميل البيانات
      } else {
        _showErrorSnackBar('فشل في تحسين النظام');
      }
    } catch (e) {
      _showErrorSnackBar('خطأ في تحسين النظام: $e');
    }
  }

  Future<void> _clearCache() async {
    _showInfoSnackBar('جاري مسح الذاكرة المؤقتة...');
    try {
      // مسح جميع أنواع التخزين المؤقت
      final cacheService = CacheService();
      final reportCacheService = ReportCacheService();

      cacheService.clear();
      await reportCacheService.clearAllReports();

      _showSuccessSnackBar('تم مسح الذاكرة المؤقتة بنجاح');
      await _loadPerformanceData(); // إعادة تحميل البيانات
    } catch (e) {
      _showErrorSnackBar('خطأ في مسح الذاكرة المؤقتة: $e');
    }
  }

  Future<void> _exportReport() async {
    _showInfoSnackBar('جاري تصدير التقرير...');
    try {
      // إنشاء تقرير شامل للأداء
      final memoryStats = _memoryService.getStatistics();
      final reportData = {
        'timestamp': DateTime.now().toIso8601String(),
        'systemMetrics': _systemMetrics,
        'memoryStats': _memoryStats,
        'performanceHistory': _performanceHistory,
        'memoryManagement': {
          'currentUsageMB': memoryStats.currentUsageMB,
          'totalMemoryMB': memoryStats.totalMemoryMB,
          'pressureLevel': memoryStats.pressureLevel.name,
          'cacheSize': memoryStats.cacheSize,
          'averageUsageMB': memoryStats.averageUsageMB,
          'peakUsageMB': memoryStats.peakUsageMB,
        },
      };

      // تصدير التقرير كـ JSON (يمكن تطويرها لاحقاً لحفظ ملف فعلي)
      final reportJson = jsonEncode(reportData);

      // في المستقبل يمكن حفظ التقرير في ملف أو مشاركته
      // await _saveReportToFile(reportJson);

      // عرض معلومات التقرير للمستخدم
      _showSuccessSnackBar(
        'تم إنشاء تقرير الأداء بنجاح (${reportJson.length} حرف)',
      );
    } catch (e) {
      _showErrorSnackBar('خطأ في تصدير التقرير: $e');
    }
  }

  Future<void> _refreshData() async {
    await _loadPerformanceData();
    _showSuccessSnackBar('تم تحديث البيانات');
  }

  Future<void> _toggleService(String serviceName) async {
    _showInfoSnackBar('جاري تغيير حالة الخدمة...');
    try {
      // تغيير حالة الخدمات المختلفة
      switch (serviceName.toLowerCase()) {
        case 'performance':
          // تبديل حالة خدمة تحسين الأداء
          await _performanceService.startPerformanceOptimization();
          _showSuccessSnackBar('تم تفعيل خدمة تحسين الأداء');
          break;
        case 'memory':
          // تبديل حالة خدمة إدارة الذاكرة
          _memoryService.startMonitoring();
          _showSuccessSnackBar('تم تفعيل خدمة مراقبة الذاكرة');
          break;
        default:
          _showInfoSnackBar('خدمة غير معروفة: $serviceName');
      }
    } catch (e) {
      _showErrorSnackBar('خطأ في تغيير حالة الخدمة: $e');
    }
  }

  Future<void> _cleanMemory() async {
    _showInfoSnackBar('جاري تنظيف الذاكرة...');
    try {
      // تنظيف الذاكرة باستخدام خدمة إدارة الذاكرة
      // استخدام التحسين الداخلي المتاح
      final cacheService = CacheService();
      cacheService.clear();

      // محاكاة تنظيف الذاكرة
      _showSuccessSnackBar('تم تنظيف الذاكرة بنجاح');
      await _loadPerformanceData(); // إعادة تحميل البيانات
    } catch (e) {
      _showErrorSnackBar('خطأ في تنظيف الذاكرة: $e');
    }
  }

  Future<void> _compressMemory() async {
    _showInfoSnackBar('جاري ضغط الذاكرة...');
    try {
      // ضغط الذاكرة عبر تنظيف التخزين المؤقت
      final cacheService = CacheService();
      cacheService.removeExpired();

      _showSuccessSnackBar('تم ضغط الذاكرة بنجاح');
      await _loadPerformanceData(); // إعادة تحميل البيانات
    } catch (e) {
      _showErrorSnackBar('خطأ في ضغط الذاكرة: $e');
    }
  }

  Future<void> _generateDetailedReport() async {
    _showInfoSnackBar('جاري إنشاء التقرير المفصل...');
    try {
      // إنشاء تقرير مفصل للأداء
      final performanceMetrics = await _performanceService
          .getPerformanceMetrics();
      final memoryStats = _memoryService.getStatistics();

      final detailedReport = {
        'generatedAt': DateTime.now().toIso8601String(),
        'systemOverview': _systemMetrics,
        'performanceMetrics': {
          'databaseResponseTime': performanceMetrics.databaseResponseTimeMs,
          'memoryUsage': performanceMetrics.memoryUsageMB,
          'databaseSize': performanceMetrics.databaseSizeMB,
          'totalRecords': performanceMetrics.totalRecords,
          'indexesHealthy': performanceMetrics.indexesHealthy,
        },
        'memoryAnalysis': {
          'currentUsageMB': memoryStats.currentUsageMB,
          'totalMemoryMB': memoryStats.totalMemoryMB,
          'pressureLevel': memoryStats.pressureLevel.name,
          'cacheSize': memoryStats.cacheSize,
          'averageUsageMB': memoryStats.averageUsageMB,
          'peakUsageMB': memoryStats.peakUsageMB,
        },
        'recommendations': _generateRecommendations(
          performanceMetrics,
          memoryStats,
        ),
      };

      // تحويل التقرير إلى JSON وعرض معلومات عنه
      final detailedReportJson = jsonEncode(detailedReport);
      _showSuccessSnackBar(
        'تم إنشاء التقرير المفصل بنجاح (${detailedReportJson.length} حرف)',
      );
    } catch (e) {
      _showErrorSnackBar('خطأ في إنشاء التقرير المفصل: $e');
    }
  }

  Future<void> _scheduleOptimization() async {
    _showInfoSnackBar('جاري جدولة التحسين...');
    try {
      // بدء خدمة التحسين التلقائي
      await _performanceService.startPerformanceOptimization();
      _memoryService.startMonitoring();

      _showSuccessSnackBar('تم جدولة التحسين التلقائي بنجاح');
    } catch (e) {
      _showErrorSnackBar('خطأ في جدولة التحسين: $e');
    }
  }

  List<String> _generateRecommendations(
    dynamic performanceMetrics,
    dynamic memoryStats,
  ) {
    final recommendations = <String>[];

    // توصيات بناءً على أداء قاعدة البيانات
    if (performanceMetrics.databaseResponseTimeMs > 1000) {
      recommendations.add('يُنصح بتحسين استعلامات قاعدة البيانات');
    }

    // توصيات بناءً على استخدام الذاكرة
    if (memoryStats.currentUsageRatio > 0.8) {
      recommendations.add(
        'استخدام الذاكرة مرتفع - يُنصح بتنظيف التخزين المؤقت',
      );
    }

    // توصيات بناءً على حجم قاعدة البيانات
    if (performanceMetrics.databaseSizeMB > 100) {
      recommendations.add('حجم قاعدة البيانات كبير - يُنصح بضغط البيانات');
    }

    // توصيات عامة
    if (recommendations.isEmpty) {
      recommendations.add('الأداء جيد - لا توجد توصيات خاصة');
    }

    return recommendations;
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: RevolutionaryColors.successGlow,
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message), backgroundColor: Colors.red),
    );
  }

  void _showInfoSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: RevolutionaryColors.damascusSky,
      ),
    );
  }
}
