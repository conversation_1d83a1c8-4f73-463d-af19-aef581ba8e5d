# 📡 توثيق APIs والخدمات - Smart Ledger

**الإصدار:** 1.0.0  
**التاريخ:** 15 يوليو 2025  
**المطور:** مجد محمد زياد يسير  

---

## 🎯 نظرة عامة

يحتوي Smart Ledger على مجموعة شاملة من الخدمات والواجهات البرمجية التي تدير جميع العمليات المحاسبية والمالية. هذا التوثيق يوفر دليلاً مفصلاً لجميع الخدمات المتاحة.

---

## 🏗️ الخدمات الأساسية

### 1. **AccountService - خدمة الحسابات**

#### الوصف
تدير جميع العمليات المتعلقة بالحسابات المحاسبية.

#### الطرق المتاحة

##### `insertAccount(Account account)`
```dart
Future<int> insertAccount(Account account)
```
**الوصف:** إدراج حساب جديد في النظام  
**المعاملات:**
- `account`: كائن الحساب المراد إدراجه

**القيمة المرجعة:** `int` - معرف الحساب الجديد  
**الاستثناءات:** `DatabaseException`, `ValidationException`

**مثال:**
```dart
final account = Account(
  code: '1001',
  name: 'الصندوق',
  type: AppConstants.accountTypeAsset,
  level: 1,
  isActive: true,
  balance: 0.0,
  currencyId: 1,
  createdAt: DateTime.now(),
  updatedAt: DateTime.now(),
);

final accountId = await accountService.insertAccount(account);
print('تم إنشاء الحساب برقم: $accountId');
```

##### `getAllAccounts()`
```dart
Future<List<Account>> getAllAccounts()
```
**الوصف:** الحصول على جميع الحسابات  
**القيمة المرجعة:** `List<Account>` - قائمة بجميع الحسابات

**مثال:**
```dart
final accounts = await accountService.getAllAccounts();
for (final account in accounts) {
  print('${account.code} - ${account.name}');
}
```

##### `getAccountById(int id)`
```dart
Future<Account?> getAccountById(int id)
```
**الوصف:** البحث عن حساب بالمعرف  
**المعاملات:**
- `id`: معرف الحساب

**القيمة المرجعة:** `Account?` - الحساب أو null إذا لم يوجد

##### `updateAccount(Account account)`
```dart
Future<int> updateAccount(Account account)
```
**الوصف:** تحديث بيانات حساب موجود  
**المعاملات:**
- `account`: كائن الحساب المحدث

**القيمة المرجعة:** `int` - عدد الصفوف المتأثرة

##### `deleteAccount(int id)`
```dart
Future<int> deleteAccount(int id)
```
**الوصف:** حذف حساب من النظام  
**المعاملات:**
- `id`: معرف الحساب المراد حذفه

**القيمة المرجعة:** `int` - عدد الصفوف المتأثرة

##### `getAccountsByType(String type)`
```dart
Future<List<Account>> getAccountsByType(String type)
```
**الوصف:** الحصول على الحسابات حسب النوع  
**المعاملات:**
- `type`: نوع الحساب (asset, liability, equity, revenue, expense)

**القيمة المرجعة:** `List<Account>` - قائمة الحسابات من النوع المحدد

##### `generateAccountCode(String accountType)`
```dart
Future<String> generateAccountCode(String accountType)
```
**الوصف:** توليد رمز حساب تلقائي  
**المعاملات:**
- `accountType`: نوع الحساب

**القيمة المرجعة:** `String` - رمز الحساب الجديد

---

### 2. **InvoiceService - خدمة الفواتير**

#### الوصف
تدير جميع العمليات المتعلقة بالفواتير والمبيعات والمشتريات.

#### الطرق المتاحة

##### `insertInvoice(Invoice invoice)`
```dart
Future<int> insertInvoice(Invoice invoice)
```
**الوصف:** إنشاء فاتورة جديدة  
**المعاملات:**
- `invoice`: كائن الفاتورة

**القيمة المرجعة:** `int` - معرف الفاتورة الجديدة

**مثال:**
```dart
final invoice = Invoice(
  invoiceNumber: 'INV-2025-001',
  customerId: 1,
  invoiceDate: DateTime.now(),
  dueDate: DateTime.now().add(Duration(days: 30)),
  subtotal: 1000.0,
  taxAmount: 110.0,
  totalAmount: 1110.0,
  status: 'draft',
  createdAt: DateTime.now(),
  updatedAt: DateTime.now(),
);

final invoiceId = await invoiceService.insertInvoice(invoice);
```

##### `getAllInvoices()`
```dart
Future<List<Invoice>> getAllInvoices()
```
**الوصف:** الحصول على جميع الفواتير  
**القيمة المرجعة:** `List<Invoice>` - قائمة بجميع الفواتير

##### `getInvoiceById(int id)`
```dart
Future<Invoice?> getInvoiceById(int id)
```
**الوصف:** البحث عن فاتورة بالمعرف  
**المعاملات:**
- `id`: معرف الفاتورة

**القيمة المرجعة:** `Invoice?` - الفاتورة أو null

##### `updateInvoice(Invoice invoice)`
```dart
Future<int> updateInvoice(Invoice invoice)
```
**الوصف:** تحديث بيانات فاتورة  
**المعاملات:**
- `invoice`: كائن الفاتورة المحدث

**القيمة المرجعة:** `int` - عدد الصفوف المتأثرة

##### `generateInvoiceNumber(String type)`
```dart
Future<String> generateInvoiceNumber(String type)
```
**الوصف:** توليد رقم فاتورة تلقائي  
**المعاملات:**
- `type`: نوع الفاتورة (sales, purchase, service)

**القيمة المرجعة:** `String` - رقم الفاتورة الجديد

---

### 3. **ItemService - خدمة الأصناف**

#### الوصف
تدير جميع العمليات المتعلقة بأصناف المخزون والمنتجات.

#### الطرق المتاحة

##### `insertItem(Item item)`
```dart
Future<int> insertItem(Item item)
```
**الوصف:** إضافة صنف جديد  
**المعاملات:**
- `item`: كائن الصنف

**القيمة المرجعة:** `int` - معرف الصنف الجديد

**مثال:**
```dart
final item = Item(
  code: 'ITEM-001',
  name: 'لابتوب Dell',
  category: 'إلكترونيات',
  unit: 'قطعة',
  purchasePrice: 800.0,
  salePrice: 1000.0,
  currentStock: 10,
  minStock: 2,
  isActive: true,
  createdAt: DateTime.now(),
  updatedAt: DateTime.now(),
);

final itemId = await itemService.insertItem(item);
```

##### `getAllItems()`
```dart
Future<List<Item>> getAllItems()
```
**الوصف:** الحصول على جميع الأصناف  
**القيمة المرجعة:** `List<Item>` - قائمة بجميع الأصناف

##### `getItemById(int id)`
```dart
Future<Item?> getItemById(int id)
```
**الوصف:** البحث عن صنف بالمعرف  
**المعاملات:**
- `id`: معرف الصنف

**القيمة المرجعة:** `Item?` - الصنف أو null

##### `updateItemStock(int itemId, double quantity, String operation)`
```dart
Future<void> updateItemStock(int itemId, double quantity, String operation)
```
**الوصف:** تحديث مخزون صنف  
**المعاملات:**
- `itemId`: معرف الصنف
- `quantity`: الكمية
- `operation`: نوع العملية (add, subtract)

##### `getLowStockItems()`
```dart
Future<List<Item>> getLowStockItems()
```
**الوصف:** الحصول على الأصناف منخفضة المخزون  
**القيمة المرجعة:** `List<Item>` - قائمة الأصناف تحت الحد الأدنى

---

### 4. **JournalEntryService - خدمة القيود**

#### الوصف
تدير جميع العمليات المتعلقة بالقيود المحاسبية.

#### الطرق المتاحة

##### `insertJournalEntry(JournalEntry entry)`
```dart
Future<int> insertJournalEntry(JournalEntry entry)
```
**الوصف:** إدراج قيد محاسبي جديد  
**المعاملات:**
- `entry`: كائن القيد

**القيمة المرجعة:** `int` - معرف القيد الجديد

**مثال:**
```dart
final entry = JournalEntry(
  entryNumber: 'JE-2025-001',
  date: DateTime.now(),
  description: 'قيد افتتاحي',
  reference: 'REF-001',
  totalDebit: 1000.0,
  totalCredit: 1000.0,
  isPosted: false,
  createdAt: DateTime.now(),
  updatedAt: DateTime.now(),
);

final entryId = await journalService.insertJournalEntry(entry);
```

##### `postJournalEntry(int entryId)`
```dart
Future<void> postJournalEntry(int entryId)
```
**الوصف:** ترحيل قيد محاسبي  
**المعاملات:**
- `entryId`: معرف القيد

##### `getTrialBalance(DateTime? fromDate, DateTime? toDate)`
```dart
Future<List<TrialBalanceItem>> getTrialBalance(DateTime? fromDate, DateTime? toDate)
```
**الوصف:** إنشاء ميزان المراجعة  
**المعاملات:**
- `fromDate`: تاريخ البداية (اختياري)
- `toDate`: تاريخ النهاية (اختياري)

**القيمة المرجعة:** `List<TrialBalanceItem>` - عناصر ميزان المراجعة

---

## 🔧 الخدمات المتقدمة

### 1. **SyrianTaxService - النظام الضريبي السوري**

#### الوصف
يدير جميع العمليات المتعلقة بالضرائب السورية.

#### الطرق المتاحة

##### `calculateIncomeTax(double income, TaxpayerCategory category)`
```dart
Future<TaxCalculation> calculateIncomeTax(double income, TaxpayerCategory category)
```
**الوصف:** حساب ضريبة الدخل  
**المعاملات:**
- `income`: الدخل الخاضع للضريبة
- `category`: فئة دافع الضريبة

**القيمة المرجعة:** `TaxCalculation` - تفاصيل حساب الضريبة

##### `generateTaxReport(int year)`
```dart
Future<TaxReport> generateTaxReport(int year)
```
**الوصف:** إنشاء تقرير ضريبي سنوي  
**المعاملات:**
- `year`: السنة المالية

**القيمة المرجعة:** `TaxReport` - التقرير الضريبي

### 2. **SmartNotificationService - التنبيهات الذكية**

#### الوصف
يدير نظام التنبيهات والإشعارات الذكية.

#### الطرق المتاحة

##### `createPaymentDueNotifications()`
```dart
Future<void> createPaymentDueNotifications()
```
**الوصف:** إنشاء تنبيهات الدفعات المستحقة

##### `createLowStockNotifications()`
```dart
Future<void> createLowStockNotifications()
```
**الوصف:** إنشاء تنبيهات المخزون المنخفض

##### `getAllNotifications()`
```dart
Future<List<SmartNotification>> getAllNotifications()
```
**الوصف:** الحصول على جميع التنبيهات  
**القيمة المرجعة:** `List<SmartNotification>` - قائمة التنبيهات

### 3. **EncryptionService - خدمة التشفير**

#### الوصف
يدير عمليات التشفير وحماية البيانات.

#### الطرق المتاحة

##### `getDatabasePassword(String userPassword)`
```dart
static Future<String?> getDatabasePassword(String userPassword)
```
**الوصف:** الحصول على مفتاح قاعدة البيانات  
**المعاملات:**
- `userPassword`: كلمة مرور المستخدم

**القيمة المرجعة:** `String?` - مفتاح قاعدة البيانات أو null

##### `setupEncryption(String userPassword)`
```dart
static Future<bool> setupEncryption(String userPassword)
```
**الوصف:** إعداد التشفير لأول مرة  
**المعاملات:**
- `userPassword`: كلمة مرور المستخدم

**القيمة المرجعة:** `bool` - نجح الإعداد أم لا

---

## 📊 نماذج البيانات

### Account - نموذج الحساب
```dart
class Account {
  final int? id;
  final String code;
  final String name;
  final String type;
  final int? parentId;
  final int level;
  final bool isActive;
  final double balance;
  final int currencyId;
  final DateTime createdAt;
  final DateTime updatedAt;
  
  // Constructor, toMap, fromMap methods...
}
```

### Invoice - نموذج الفاتورة
```dart
class Invoice {
  final int? id;
  final String invoiceNumber;
  final int customerId;
  final DateTime invoiceDate;
  final DateTime? dueDate;
  final double subtotal;
  final double taxAmount;
  final double discountAmount;
  final double totalAmount;
  final String status;
  final String? notes;
  final DateTime createdAt;
  final DateTime updatedAt;
  
  // Constructor, toMap, fromMap methods...
}
```

### Item - نموذج الصنف
```dart
class Item {
  final int? id;
  final String code;
  final String name;
  final String? description;
  final String category;
  final String unit;
  final double purchasePrice;
  final double salePrice;
  final double currentStock;
  final double minStock;
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;
  
  // Constructor, toMap, fromMap methods...
}
```

---

## 🔍 أمثلة الاستخدام

### مثال شامل: إنشاء فاتورة مبيعات كاملة

```dart
Future<void> createSalesInvoiceExample() async {
  try {
    // 1. إنشاء الفاتورة
    final invoice = Invoice(
      invoiceNumber: await invoiceService.generateInvoiceNumber('sales'),
      customerId: 1,
      invoiceDate: DateTime.now(),
      dueDate: DateTime.now().add(Duration(days: 30)),
      subtotal: 0, // سيتم حسابه
      taxAmount: 0, // سيتم حسابه
      discountAmount: 0,
      totalAmount: 0, // سيتم حسابه
      status: 'draft',
      notes: 'فاتورة مبيعات تجريبية',
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );
    
    final invoiceId = await invoiceService.insertInvoice(invoice);
    
    // 2. إضافة عناصر الفاتورة
    final items = [
      InvoiceItem(
        invoiceId: invoiceId,
        itemId: 1,
        quantity: 2,
        unitPrice: 500.0,
        discountRate: 0,
        taxRate: 11.0,
        lineTotal: 1000.0,
      ),
      InvoiceItem(
        invoiceId: invoiceId,
        itemId: 2,
        quantity: 1,
        unitPrice: 300.0,
        discountRate: 5.0,
        taxRate: 11.0,
        lineTotal: 285.0,
      ),
    ];
    
    for (final item in items) {
      await invoiceService.insertInvoiceItem(item);
    }
    
    // 3. تحديث إجماليات الفاتورة
    await invoiceService.updateInvoiceTotals(invoiceId);
    
    // 4. إنشاء القيود المحاسبية التلقائية
    await invoiceService.createAutomaticJournalEntries(invoiceId);
    
    print('تم إنشاء الفاتورة بنجاح: $invoiceId');
    
  } catch (e) {
    print('خطأ في إنشاء الفاتورة: $e');
  }
}
```

---

## ⚠️ معالجة الأخطاء

### أنواع الاستثناءات الشائعة

```dart
// استثناء قاعدة البيانات
try {
  await accountService.insertAccount(account);
} on DatabaseException catch (e) {
  print('خطأ في قاعدة البيانات: ${e.message}');
} on ValidationException catch (e) {
  print('خطأ في التحقق: ${e.message}');
} catch (e) {
  print('خطأ عام: $e');
}
```

### رموز الأخطاء الشائعة

| الرمز | الوصف | الحل |
|-------|--------|------|
| DB001 | خطأ في الاتصال بقاعدة البيانات | تحقق من كلمة المرور |
| VAL001 | بيانات غير صحيحة | راجع البيانات المدخلة |
| AUTH001 | خطأ في التحقق | تحقق من الصلاحيات |
| ENC001 | خطأ في التشفير | راجع مفتاح التشفير |

---

## 📈 مراقبة الأداء

### مقاييس الأداء المهمة

```dart
// قياس وقت تنفيذ العمليات
final stopwatch = Stopwatch()..start();
await accountService.getAllAccounts();
stopwatch.stop();
print('وقت التنفيذ: ${stopwatch.elapsedMilliseconds}ms');

// مراقبة استهلاك الذاكرة
final memoryUsage = ProcessInfo.currentRss;
print('استهلاك الذاكرة: ${memoryUsage / 1024 / 1024}MB');
```

---

## ✅ الخلاصة

هذا التوثيق يوفر دليلاً شاملاً لجميع APIs والخدمات في Smart Ledger. استخدم هذا المرجع لـ:

- **فهم الخدمات المتاحة** ووظائفها
- **تطوير ميزات جديدة** باستخدام الخدمات الموجودة
- **حل المشاكل** ومعالجة الأخطاء
- **تحسين الأداء** ومراقبة النظام

**نصائح للاستخدام الأمثل:**
- اقرأ التوثيق بعناية قبل الاستخدام
- استخدم معالجة الأخطاء المناسبة
- راقب الأداء بانتظام
- اتبع أفضل الممارسات

**للمساعدة الإضافية:** راجع الدليل التقني أو اتصل بفريق الدعم.
