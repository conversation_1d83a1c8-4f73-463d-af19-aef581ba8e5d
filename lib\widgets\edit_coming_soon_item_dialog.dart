/// حوار تعديل الصنف القريب
/// يسمح للمستخدم بتعديل جميع معلومات الصنف الموجود
library;

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../models/coming_soon_item.dart';
import '../services/coming_soon_service.dart';
import '../constants/revolutionary_design_colors.dart';
import '../services/logging_service.dart';
import '../utils/form_validators.dart';

class EditComingSoonItemDialog extends StatefulWidget {
  final ComingSoonItem item;

  const EditComingSoonItemDialog({
    super.key,
    required this.item,
  });

  @override
  State<EditComingSoonItemDialog> createState() =>
      _EditComingSoonItemDialogState();
}

class _EditComingSoonItemDialogState extends State<EditComingSoonItemDialog> {
  final _formKey = GlobalKey<FormState>();
  final ComingSoonService _comingSoonService = ComingSoonService();

  // Controllers
  late final TextEditingController _nameController;
  late final TextEditingController _descriptionController;
  late final TextEditingController _priceController;
  late final TextEditingController _unitController;
  late final TextEditingController _supplierController;
  late final TextEditingController _notesController;

  // Form data
  late String _selectedCategory;
  late DateTime _expectedDate;
  late bool _isActive;
  bool _isLoading = false;

  final List<String> _categories = [
    'إلكترونيات',
    'ملابس',
    'أغذية',
    'أدوات',
    'مواد بناء',
    'أثاث',
    'كتب',
    'رياضة',
    'أخرى',
  ];

  @override
  void initState() {
    super.initState();
    _initializeControllers();
  }

  void _initializeControllers() {
    _nameController = TextEditingController(text: widget.item.name);
    _descriptionController = TextEditingController(text: widget.item.description);
    _priceController = TextEditingController(
      text: widget.item.estimatedPrice.toString(),
    );
    _unitController = TextEditingController(text: widget.item.unit ?? '');
    _supplierController = TextEditingController(
      text: widget.item.supplierInfo ?? '',
    );
    _notesController = TextEditingController(text: widget.item.notes ?? '');

    _selectedCategory = widget.item.category ?? 'إلكترونيات';
    _expectedDate = widget.item.expectedAvailabilityDate;
    _isActive = widget.item.isActive;
  }

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    _priceController.dispose();
    _unitController.dispose();
    _supplierController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        constraints: const BoxConstraints(maxWidth: 500, maxHeight: 700),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildHeader(),
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(24),
                child: _buildForm(),
              ),
            ),
            _buildActions(),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: RevolutionaryColors.damascusSky,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(16),
          topRight: Radius.circular(16),
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(
              Icons.edit,
              color: Colors.white,
              size: 24,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'تعديل الصنف القريب',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    fontFamily: 'Cairo',
                  ),
                ),
                Text(
                  widget.item.name,
                  style: TextStyle(
                    color: Colors.white.withValues(alpha: 0.8),
                    fontSize: 14,
                    fontFamily: 'Cairo',
                  ),
                ),
              ],
            ),
          ),
          IconButton(
            onPressed: () => Navigator.of(context).pop(),
            icon: const Icon(Icons.close, color: Colors.white),
          ),
        ],
      ),
    );
  }

  Widget _buildForm() {
    return Form(
      key: _formKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildBasicInfoSection(),
          const SizedBox(height: 24),
          _buildPriceSection(),
          const SizedBox(height: 24),
          _buildDateSection(),
          const SizedBox(height: 24),
          _buildSupplierSection(),
          const SizedBox(height: 24),
          _buildNotesSection(),
          const SizedBox(height: 24),
          _buildStatusSection(),
        ],
      ),
    );
  }

  Widget _buildBasicInfoSection() {
    return _buildSection(
      title: 'المعلومات الأساسية',
      icon: Icons.info_outline,
      children: [
        TextFormField(
          controller: _nameController,
          decoration: InputDecoration(
            labelText: 'اسم الصنف *',
            prefixIcon: const Icon(Icons.inventory),
            border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
          ),
          validator: FormValidators.required,
          textInputAction: TextInputAction.next,
        ),
        const SizedBox(height: 16),
        TextFormField(
          controller: _descriptionController,
          decoration: InputDecoration(
            labelText: 'الوصف *',
            prefixIcon: const Icon(Icons.description),
            border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
          ),
          validator: FormValidators.required,
          maxLines: 3,
          textInputAction: TextInputAction.next,
        ),
        const SizedBox(height: 16),
        DropdownButtonFormField<String>(
          value: _selectedCategory,
          decoration: InputDecoration(
            labelText: 'الفئة',
            prefixIcon: const Icon(Icons.category),
            border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
          ),
          items: _categories.map((category) {
            return DropdownMenuItem(
              value: category,
              child: Text(category),
            );
          }).toList(),
          onChanged: (value) {
            setState(() {
              _selectedCategory = value!;
            });
          },
        ),
        const SizedBox(height: 16),
        TextFormField(
          controller: _unitController,
          decoration: InputDecoration(
            labelText: 'الوحدة',
            hintText: 'مثل: قطعة، كيلو، متر',
            prefixIcon: const Icon(Icons.straighten),
            border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
          ),
          textInputAction: TextInputAction.next,
        ),
      ],
    );
  }

  Widget _buildPriceSection() {
    return _buildSection(
      title: 'معلومات السعر',
      icon: Icons.attach_money,
      children: [
        TextFormField(
          controller: _priceController,
          decoration: InputDecoration(
            labelText: 'السعر المتوقع',
            prefixIcon: const Icon(Icons.monetization_on),
            suffixText: 'ل.س',
            border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
          ),
          keyboardType: TextInputType.number,
          inputFormatters: [
            FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d{0,2}')),
          ],
          validator: (value) {
            if (value?.isEmpty == true) return null;
            final price = double.tryParse(value!);
            if (price == null || price < 0) {
              return 'يرجى إدخال سعر صحيح';
            }
            return null;
          },
          textInputAction: TextInputAction.next,
        ),
      ],
    );
  }

  Widget _buildDateSection() {
    return _buildSection(
      title: 'تاريخ التوفر المتوقع',
      icon: Icons.schedule,
      children: [
        InkWell(
          onTap: _selectDate,
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey.shade300),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.calendar_today,
                  color: RevolutionaryColors.damascusSky,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    _formatDate(_expectedDate),
                    style: const TextStyle(
                      fontSize: 16,
                      fontFamily: 'Cairo',
                    ),
                  ),
                ),
                Icon(
                  Icons.arrow_drop_down,
                  color: Colors.grey.shade600,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildSupplierSection() {
    return _buildSection(
      title: 'معلومات المورد',
      icon: Icons.business,
      children: [
        TextFormField(
          controller: _supplierController,
          decoration: InputDecoration(
            labelText: 'اسم المورد',
            hintText: 'أدخل اسم المورد أو معلومات الاتصال',
            prefixIcon: const Icon(Icons.person_outline),
            border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
          ),
          textInputAction: TextInputAction.next,
        ),
      ],
    );
  }

  Widget _buildNotesSection() {
    return _buildSection(
      title: 'ملاحظات إضافية',
      icon: Icons.note,
      children: [
        TextFormField(
          controller: _notesController,
          decoration: InputDecoration(
            labelText: 'ملاحظات',
            hintText: 'أي ملاحظات إضافية حول الصنف',
            prefixIcon: const Icon(Icons.note_outlined),
            border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
          ),
          maxLines: 3,
          textInputAction: TextInputAction.done,
        ),
      ],
    );
  }

  Widget _buildStatusSection() {
    return _buildSection(
      title: 'حالة الصنف',
      icon: Icons.toggle_on,
      children: [
        SwitchListTile(
          title: const Text('نشط'),
          subtitle: Text(_isActive ? 'الصنف نشط ومتاح' : 'الصنف غير نشط'),
          value: _isActive,
          onChanged: (value) {
            setState(() {
              _isActive = value;
            });
          },
          activeColor: RevolutionaryColors.successGlow,
        ),
      ],
    );
  }

  Widget _buildSection({
    required String title,
    required IconData icon,
    required List<Widget> children,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              icon,
              color: RevolutionaryColors.damascusSky,
              size: 20,
            ),
            const SizedBox(width: 8),
            Text(
              title,
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: RevolutionaryColors.damascusSky,
                fontFamily: 'Cairo',
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        ...children,
      ],
    );
  }

  Widget _buildActions() {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(16),
          bottomRight: Radius.circular(16),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: OutlinedButton.icon(
              onPressed: _isLoading ? null : () => Navigator.of(context).pop(),
              icon: const Icon(Icons.cancel),
              label: const Text('إلغاء'),
              style: OutlinedButton.styleFrom(
                foregroundColor: Colors.grey.shade600,
                side: BorderSide(color: Colors.grey.shade300),
                padding: const EdgeInsets.symmetric(vertical: 12),
              ),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            flex: 2,
            child: ElevatedButton.icon(
              onPressed: _isLoading ? null : _updateItem,
              icon: _isLoading
                  ? const SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                  : const Icon(Icons.save),
              label: Text(_isLoading ? 'جاري الحفظ...' : 'حفظ التغييرات'),
              style: ElevatedButton.styleFrom(
                backgroundColor: RevolutionaryColors.damascusSky,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 12),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _selectDate() async {
    final selectedDate = await showDatePicker(
      context: context,
      initialDate: _expectedDate,
      firstDate: DateTime.now().subtract(const Duration(days: 365)),
      lastDate: DateTime.now().add(const Duration(days: 365 * 2)),
      locale: const Locale('ar'),
    );

    if (selectedDate != null) {
      setState(() {
        _expectedDate = selectedDate;
      });
    }
  }

  Future<void> _updateItem() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isLoading = true);

    try {
      final updatedItem = widget.item.copyWith(
        name: _nameController.text.trim(),
        description: _descriptionController.text.trim(),
        category: _selectedCategory,
        estimatedPrice: double.tryParse(_priceController.text) ?? 0.0,
        unit: _unitController.text.trim().isEmpty
            ? null
            : _unitController.text.trim(),
        expectedAvailabilityDate: _expectedDate,
        supplierInfo: _supplierController.text.trim().isEmpty
            ? null
            : _supplierController.text.trim(),
        notes: _notesController.text.trim().isEmpty
            ? null
            : _notesController.text.trim(),
        isActive: _isActive,
        updatedAt: DateTime.now(),
      );

      await _comingSoonService.updateComingSoonItem(updatedItem);

      if (mounted) {
        Navigator.of(context).pop(true);
        _showSuccessSnackBar('تم تحديث الصنف بنجاح');
      }
    } catch (e) {
      LoggingService.error(
        'خطأ في تحديث الصنف القريب',
        category: 'EditComingSoonItemDialog',
        data: {'error': e.toString()},
      );

      if (mounted) {
        _showErrorSnackBar('خطأ في تحديث الصنف: $e');
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: RevolutionaryColors.successGlow,
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: RevolutionaryColors.errorCoral,
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}
