import 'package:flutter/material.dart';
import '../constants/revolutionary_design_colors.dart';
import '../constants/app_constants.dart';
import '../models/account.dart';
import '../services/account_service.dart';
import '../services/performance_service.dart';
import '../services/progressive_loading_service.dart';
import '../services/screen_reader_service.dart';
import '../widgets/account_card.dart';
import '../widgets/loading_widget.dart';
import '../widgets/progressive_list_view.dart';
import '../widgets/keyboard_shortcuts_wrapper.dart';
import '../widgets/undo_redo_status_widget.dart';
import '../services/undo_redo_helpers.dart';
import 'add_account_screen.dart';
import 'account_details_screen.dart';
import 'shortcuts_guide_screen.dart';

class AccountsScreen extends StatefulWidget {
  const AccountsScreen({super.key});

  @override
  State<AccountsScreen> createState() => _AccountsScreenState();
}

class _AccountsScreenState extends State<AccountsScreen>
    with TickerProviderStateMixin {
  final AccountService _accountService = AccountService();
  final PerformanceService _performanceService = PerformanceService();
  final ProgressiveLoadingService _progressiveLoadingService =
      ProgressiveLoadingService();
  final TextEditingController _searchController = TextEditingController();

  List<Account> _accounts = [];
  List<Account> _filteredAccounts = [];
  bool _isLoading = true;
  String _selectedType = 'all';
  bool _useProgressiveLoading = false;

  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    _loadAccounts();
  }

  @override
  void dispose() {
    _animationController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _loadAccounts() async {
    setState(() => _isLoading = true);
    ScreenReaderService.announceLoadingStart('الحسابات');

    try {
      // استخدام خدمة الأداء لقياس وقت التحميل
      final accounts = await _performanceService.measureOperation(
        'load_all_accounts',
        () => _accountService.getAllAccounts(),
        category: 'accounts',
        metadata: {'screen': 'accounts_screen'},
      );

      setState(() {
        _accounts = accounts;
        _filteredAccounts = accounts;
        _isLoading = false;
      });
      _animationController.forward();
      ScreenReaderService.announceLoadingComplete('الحسابات', accounts.length);

      // تسجيل إحصائيات التحميل
      _performanceService.updateMetric(
        'accounts_loaded_count',
        accounts.length.toDouble(),
      );

      // تسجيل إحصائيات أنواع الحسابات
      final accountsByType = <String, int>{};
      for (final account in accounts) {
        accountsByType[account.type] = (accountsByType[account.type] ?? 0) + 1;
      }

      for (final entry in accountsByType.entries) {
        _performanceService.updateMetric(
          'accounts_${entry.key}_count',
          entry.value.toDouble(),
        );
      }
    } catch (e) {
      setState(() => _isLoading = false);
      ScreenReaderService.announceError('تحميل الحسابات', e.toString());
      _showErrorSnackBar('حدث خطأ في تحميل الحسابات: $e');
    }
  }

  void _filterAccounts() {
    setState(() {
      _filteredAccounts = _accounts.where((account) {
        final matchesSearch =
            account.name.toLowerCase().contains(
              _searchController.text.toLowerCase(),
            ) ||
            account.code.contains(_searchController.text);

        final matchesType =
            _selectedType == 'all' || account.type == _selectedType;

        return matchesSearch && matchesType;
      }).toList();
    });

    // إعلان نتائج الفلترة
    if (_searchController.text.isNotEmpty) {
      ScreenReaderService.announceSearchResults(
        _filteredAccounts.length,
        _searchController.text,
      );
    } else if (_selectedType != 'all') {
      final filterName = _getFilterDisplayName(_selectedType);
      ScreenReaderService.announceFilterApplied(
        filterName,
        _filteredAccounts.length,
      );
    }
  }

  String _getFilterDisplayName(String filterType) {
    switch (filterType) {
      case AppConstants.accountTypeAsset:
        return 'الأصول';
      case AppConstants.accountTypeLiability:
        return 'الخصوم';
      case AppConstants.accountTypeRevenue:
        return 'الإيرادات';
      case AppConstants.accountTypeExpense:
        return 'المصروفات';
      case AppConstants.accountTypePurchase:
        return 'المشتريات';
      case AppConstants.accountTypeSale:
        return 'المبيعات';
      case AppConstants.accountTypeInventory:
        return 'المخزون';
      default:
        return 'الكل';
    }
  }

  @override
  Widget build(BuildContext context) {
    return KeyboardShortcutsWrapper(
      screenType: 'account',
      onNew: _addNewAccount,
      onRefresh: _loadAccounts,
      onSearch: _focusSearchField,
      onHelp: _showShortcutsHelp,
      child: Scaffold(
        appBar: AppBar(
          title: Semantics(
            header: true,
            label: 'شاشة دليل الحسابات',
            child: const Text('دليل الحسابات'),
          ),
          actions: [
            // إضافة widget حالة Undo/Redo
            const UndoRedoStatusWidget(showLabels: false, showCounts: false),
            const SizedBox(width: 8),
            IconButton(
              icon: Icon(
                _useProgressiveLoading ? Icons.view_stream : Icons.view_list,
              ),
              onPressed: _toggleLoadingMode,
              tooltip: _useProgressiveLoading
                  ? 'التحميل العادي'
                  : 'التحميل التدريجي',
            ),
            Semantics(
              button: true,
              label: 'إضافة حساب جديد',
              hint: 'اضغط لإضافة حساب محاسبي جديد',
              child: IconButton(
                icon: const Icon(Icons.add),
                onPressed: _addNewAccount,
                tooltip: 'إضافة حساب جديد (Ctrl+N)',
              ),
            ),
            Semantics(
              button: true,
              label: 'تحديث قائمة الحسابات',
              hint: 'اضغط لإعادة تحميل الحسابات',
              child: IconButton(
                icon: const Icon(Icons.refresh),
                onPressed: _loadAccounts,
                tooltip: 'تحديث (F5)',
              ),
            ),
            Semantics(
              button: true,
              label: 'دليل الاختصارات',
              hint: 'اضغط لعرض دليل اختصارات لوحة المفاتيح',
              child: IconButton(
                icon: const Icon(Icons.help_outline),
                onPressed: _showShortcutsHelp,
                tooltip: 'مساعدة (F1)',
              ),
            ),
          ],
        ),
        body: Column(
          children: [
            _buildSearchAndFilter(),
            Expanded(
              child: _isLoading ? const LoadingWidget() : _buildAccountsList(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSearchAndFilter() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: RevolutionaryColors.backgroundCard,
        boxShadow: [
          BoxShadow(
            color: RevolutionaryColors.shadowMedium,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // شريط البحث
          Semantics(
            textField: true,
            label: 'حقل البحث في الحسابات',
            hint: 'اكتب اسم أو رمز الحساب للبحث',
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'البحث في الحسابات...',
                prefixIcon: Semantics(
                  label: 'أيقونة البحث',
                  child: const Icon(Icons.search),
                ),
                suffixIcon: _searchController.text.isNotEmpty
                    ? Semantics(
                        button: true,
                        label: 'مسح نص البحث',
                        hint: 'اضغط لمسح النص المكتوب',
                        child: IconButton(
                          icon: const Icon(Icons.clear),
                          onPressed: () {
                            _searchController.clear();
                            _filterAccounts();
                          },
                          tooltip: 'مسح',
                        ),
                      )
                    : null,
              ),
              onChanged: (_) => _filterAccounts(),
            ),
          ),
          const SizedBox(height: 12),
          // فلتر نوع الحساب
          Semantics(
            label: 'فلاتر أنواع الحسابات',
            hint: 'اختر نوع الحساب للفلترة',
            child: SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Row(
                children: [
                  _buildFilterChip('all', 'الكل'),
                  _buildFilterChip(AppConstants.accountTypeAsset, 'أصول'),
                  _buildFilterChip(AppConstants.accountTypeLiability, 'خصوم'),
                  _buildFilterChip(AppConstants.accountTypeRevenue, 'إيرادات'),
                  _buildFilterChip(AppConstants.accountTypeExpense, 'مصروفات'),
                  _buildFilterChip(AppConstants.accountTypePurchase, 'مشتريات'),
                  _buildFilterChip(AppConstants.accountTypeSale, 'مبيعات'),
                  _buildFilterChip(AppConstants.accountTypeInventory, 'مخزون'),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterChip(String type, String label) {
    final isSelected = _selectedType == type;
    return Padding(
      padding: const EdgeInsets.only(left: 8),
      child: Semantics(
        button: true,
        selected: isSelected,
        label: 'فلتر $label',
        hint: isSelected ? 'مُحدد حالياً' : 'اضغط للتصفية حسب $label',
        child: FilterChip(
          label: Text(label),
          selected: isSelected,
          onSelected: (selected) {
            setState(() {
              _selectedType = type;
              _filterAccounts();
            });
          },
          selectedColor: RevolutionaryColors.damascusSky.withValues(alpha: 0.2),
          checkmarkColor: RevolutionaryColors.damascusSky,
        ),
      ),
    );
  }

  Widget _buildAccountsList() {
    // استخدام التحميل التدريجي إذا كان مفعلاً
    if (_useProgressiveLoading) {
      return _buildProgressiveAccountsList();
    }

    // التحميل العادي
    if (_filteredAccounts.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.account_tree_outlined,
              size: 64,
              color: RevolutionaryColors.textHint,
            ),
            const SizedBox(height: 16),
            Text(
              'لا توجد حسابات',
              style: Theme.of(
                context,
              ).textTheme.titleLarge?.copyWith(color: RevolutionaryColors.textHint),
            ),
            const SizedBox(height: 8),
            Text(
              'اضغط على + لإضافة حساب جديد',
              style: Theme.of(
                context,
              ).textTheme.bodyMedium?.copyWith(color: RevolutionaryColors.textHint),
            ),
          ],
        ),
      );
    }

    return FadeTransition(
      opacity: _fadeAnimation,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: _filteredAccounts.length,
        itemBuilder: (context, index) {
          final account = _filteredAccounts[index];
          return AccountCard(
            account: account,
            onTap: () => _viewAccountDetails(account),
            onEdit: () => _editAccount(account),
            onDelete: () => _deleteAccount(account),
          );
        },
      ),
    );
  }

  Future<void> _addNewAccount() async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const AddAccountScreen()),
    );

    if (result == true) {
      _loadAccounts();
    }
  }

  void _viewAccountDetails(Account account) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => AccountDetailsScreen(account: account),
      ),
    );
  }

  Future<void> _editAccount(Account account) async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => AddAccountScreen(accountToEdit: account),
      ),
    );

    if (result == true) {
      _loadAccounts();
    }
  }

  void _deleteAccount(Account account) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text('هل أنت متأكد من حذف الحساب "${account.name}"؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.pop(context);
              await _performDeleteAccount(account);
            },
            style: ElevatedButton.styleFrom(backgroundColor: RevolutionaryColors.errorCoral),
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }

  Future<void> _performDeleteAccount(Account account) async {
    try {
      // استخدام UndoRedoHelpers لحذف الحساب مع دعم التراجع
      final success = await UndoRedoHelpers.deleteAccountWithUndo(
        accountId: account.id!,
        accountData: account.toMap(),
        description: 'حذف الحساب: ${account.name}',
      );

      if (success) {
        _showSuccessSnackBar('تم حذف الحساب بنجاح');
        _loadAccounts();
      } else {
        _showErrorSnackBar('فشل في حذف الحساب');
      }
    } catch (e) {
      _showErrorSnackBar('حدث خطأ في حذف الحساب: $e');
    }
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message), backgroundColor: RevolutionaryColors.successGlow),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message), backgroundColor: RevolutionaryColors.errorCoral),
    );
  }

  // ===============================
  // دوال اختصارات لوحة المفاتيح
  // ===============================

  void _focusSearchField() {
    FocusScope.of(context).requestFocus(FocusNode());
    // التركيز على حقل البحث
    if (_searchController.text.isEmpty) {
      // إذا كان حقل البحث فارغ، ركز عليه
      WidgetsBinding.instance.addPostFrameCallback((_) {
        FocusScope.of(context).requestFocus(FocusNode());
      });
    }
  }

  void _showShortcutsHelp() {
    Navigator.of(context).push(
      MaterialPageRoute(builder: (context) => const ShortcutsGuideScreen()),
    );
  }

  /// التبديل بين التحميل العادي والتدريجي
  void _toggleLoadingMode() {
    setState(() {
      _useProgressiveLoading = !_useProgressiveLoading;
    });

    // إعادة تحميل البيانات بالطريقة الجديدة
    _loadAccounts();

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          _useProgressiveLoading
              ? 'تم تفعيل التحميل التدريجي'
              : 'تم تفعيل التحميل العادي',
        ),
        backgroundColor: RevolutionaryColors.successGlow,
      ),
    );
  }

  /// بناء واجهة التحميل التدريجي للحسابات
  Widget _buildProgressiveAccountsList() {
    return SearchableProgressiveListView<Map<String, dynamic>>(
      loadStreamBuilder: (searchTerm) =>
          _progressiveLoadingService.loadAccountsProgressive(
            searchTerm: searchTerm,
            accountType: _selectedType,
          ),
      itemBuilder: (context, accountData, index) {
        final account = Account.fromMap(accountData);
        return AccountCard(
          account: account,
          onTap: () => _viewAccountDetails(account),
          onEdit: () => _editAccount(account),
          onDelete: () => _deleteAccount(account),
        );
      },
      searchHint: 'البحث في الحسابات...',
      emptyMessage: 'لا توجد حسابات',
      padding: const EdgeInsets.all(16),
    );
  }
}
