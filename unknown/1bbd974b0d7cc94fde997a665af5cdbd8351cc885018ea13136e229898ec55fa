import 'package:flutter/material.dart';
import 'app_dimensions.dart';

/// حاوية متجاوبة تتكيف مع حجم الشاشة
class ResponsiveContainer extends StatelessWidget {
  final Widget child;
  final EdgeInsets? padding;
  final double? maxWidth;
  final bool centerContent;
  final Color? backgroundColor;

  const ResponsiveContainer({
    super.key,
    required this.child,
    this.padding,
    this.maxWidth,
    this.centerContent = true,
    this.backgroundColor,
  });

  @override
  Widget build(BuildContext context) {
    final dimensions = AppDimensions.of(context);
    final effectiveMaxWidth = maxWidth ?? dimensions.maxContentWidth;
    final effectivePadding =
        padding ?? EdgeInsets.all(dimensions.paddingS); // تقليل الحشو الافتراضي

    Widget content = Container(
      width: double.infinity,
      constraints: BoxConstraints(maxWidth: effectiveMaxWidth),
      padding: effectivePadding,
      decoration: backgroundColor != null
          ? BoxDecoration(color: backgroundColor)
          : null,
      child: child,
    );

    if (centerContent && effectiveMaxWidth != double.infinity) {
      content = Center(child: content);
    }

    return content;
  }
}

/// نص متجاوب يتكيف حجم الخط مع الشاشة
class ResponsiveText extends StatelessWidget {
  final String text;
  final TextStyle? style;
  final TextAlign? textAlign;
  final int? maxLines;
  final TextOverflow? overflow;
  final ResponsiveTextType type;

  const ResponsiveText(
    this.text, {
    super.key,
    this.style,
    this.textAlign,
    this.maxLines,
    this.overflow,
    this.type = ResponsiveTextType.body,
  });

  /// إنشاء نص عنوان رئيسي
  const ResponsiveText.h1(
    this.text, {
    super.key,
    this.style,
    this.textAlign,
    this.maxLines,
    this.overflow,
  }) : type = ResponsiveTextType.h1;

  /// إنشاء نص عنوان ثانوي
  const ResponsiveText.h2(
    this.text, {
    super.key,
    this.style,
    this.textAlign,
    this.maxLines,
    this.overflow,
  }) : type = ResponsiveTextType.h2;

  /// إنشاء نص عنوان فرعي
  const ResponsiveText.h3(
    this.text, {
    super.key,
    this.style,
    this.textAlign,
    this.maxLines,
    this.overflow,
  }) : type = ResponsiveTextType.h3;

  /// إنشاء نص عنوان صغير
  const ResponsiveText.h4(
    this.text, {
    super.key,
    this.style,
    this.textAlign,
    this.maxLines,
    this.overflow,
  }) : type = ResponsiveTextType.h4;

  /// إنشاء نص عادي
  const ResponsiveText.body(
    this.text, {
    super.key,
    this.style,
    this.textAlign,
    this.maxLines,
    this.overflow,
  }) : type = ResponsiveTextType.body;

  /// إنشاء نص صغير
  const ResponsiveText.caption(
    this.text, {
    super.key,
    this.style,
    this.textAlign,
    this.maxLines,
    this.overflow,
  }) : type = ResponsiveTextType.caption;

  @override
  Widget build(BuildContext context) {
    final dimensions = AppDimensions.of(context);

    double fontSize;
    FontWeight fontWeight;

    switch (type) {
      case ResponsiveTextType.h1:
        fontSize = dimensions.fontSizeH1;
        fontWeight = FontWeight.bold;
        break;
      case ResponsiveTextType.h2:
        fontSize = dimensions.fontSizeH2;
        fontWeight = FontWeight.w600;
        break;
      case ResponsiveTextType.h3:
        fontSize = dimensions.fontSizeH3;
        fontWeight = FontWeight.w500;
        break;
      case ResponsiveTextType.h4:
        fontSize = dimensions.fontSizeH4;
        fontWeight = FontWeight.w500;
        break;
      case ResponsiveTextType.body:
        fontSize = dimensions.fontSizeBody;
        fontWeight = FontWeight.normal;
        break;
      case ResponsiveTextType.caption:
        fontSize = dimensions.fontSizeCaption;
        fontWeight = FontWeight.normal;
        break;
    }

    return Text(
      text,
      style: TextStyle(fontSize: fontSize, fontWeight: fontWeight).merge(style),
      textAlign: textAlign,
      maxLines: maxLines,
      overflow: overflow,
    );
  }
}

/// أنواع النصوص المتجاوبة
enum ResponsiveTextType { h1, h2, h3, h4, body, caption }

/// زر متجاوب يتكيف مع حجم الشاشة
class ResponsiveButton extends StatelessWidget {
  final String text;
  final VoidCallback? onPressed;
  final ButtonStyle? style;
  final Widget? icon;
  final bool isExpanded;
  final ResponsiveButtonType type;

  const ResponsiveButton({
    super.key,
    required this.text,
    this.onPressed,
    this.style,
    this.icon,
    this.isExpanded = false,
    this.type = ResponsiveButtonType.elevated,
  });

  /// إنشاء زر مرفوع
  const ResponsiveButton.elevated({
    super.key,
    required this.text,
    this.onPressed,
    this.style,
    this.icon,
    this.isExpanded = false,
  }) : type = ResponsiveButtonType.elevated;

  /// إنشاء زر مخطط
  const ResponsiveButton.outlined({
    super.key,
    required this.text,
    this.onPressed,
    this.style,
    this.icon,
    this.isExpanded = false,
  }) : type = ResponsiveButtonType.outlined;

  /// إنشاء زر نصي
  const ResponsiveButton.text({
    super.key,
    required this.text,
    this.onPressed,
    this.style,
    this.icon,
    this.isExpanded = false,
  }) : type = ResponsiveButtonType.text;

  @override
  Widget build(BuildContext context) {
    final dimensions = AppDimensions.of(context);

    final buttonStyle = ButtonStyle(
      minimumSize: WidgetStateProperty.all(
        Size(isExpanded ? double.infinity : 0, dimensions.buttonHeight),
      ),
      padding: WidgetStateProperty.all(dimensions.buttonPadding),
      textStyle: WidgetStateProperty.all(
        TextStyle(fontSize: dimensions.fontSizeButton),
      ),
      shape: WidgetStateProperty.all(
        RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(dimensions.buttonBorderRadius),
        ),
      ),
    ).merge(style);

    Widget button;

    if (icon != null) {
      switch (type) {
        case ResponsiveButtonType.elevated:
          button = ElevatedButton.icon(
            onPressed: onPressed,
            icon: icon!,
            label: Text(text),
            style: buttonStyle,
          );
          break;
        case ResponsiveButtonType.outlined:
          button = OutlinedButton.icon(
            onPressed: onPressed,
            icon: icon!,
            label: Text(text),
            style: buttonStyle,
          );
          break;
        case ResponsiveButtonType.text:
          button = TextButton.icon(
            onPressed: onPressed,
            icon: icon!,
            label: Text(text),
            style: buttonStyle,
          );
          break;
      }
    } else {
      switch (type) {
        case ResponsiveButtonType.elevated:
          button = ElevatedButton(
            onPressed: onPressed,
            style: buttonStyle,
            child: Text(text),
          );
          break;
        case ResponsiveButtonType.outlined:
          button = OutlinedButton(
            onPressed: onPressed,
            style: buttonStyle,
            child: Text(text),
          );
          break;
        case ResponsiveButtonType.text:
          button = TextButton(
            onPressed: onPressed,
            style: buttonStyle,
            child: Text(text),
          );
          break;
      }
    }

    return button;
  }
}

/// أنواع الأزرار المتجاوبة
enum ResponsiveButtonType { elevated, outlined, text }

/// بطاقة متجاوبة تتكيف مع حجم الشاشة
class ResponsiveCard extends StatelessWidget {
  final Widget child;
  final EdgeInsets? padding;
  final EdgeInsets? margin;
  final Color? color;
  final double? elevation;
  final VoidCallback? onTap;

  const ResponsiveCard({
    super.key,
    required this.child,
    this.padding,
    this.margin,
    this.color,
    this.elevation,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final dimensions = AppDimensions.of(context);

    final card = Card(
      color: color,
      elevation: elevation ?? 2.0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(dimensions.cardBorderRadius),
      ),
      margin: margin ?? EdgeInsets.all(dimensions.spacingS),
      child: Padding(
        padding: padding ?? EdgeInsets.all(dimensions.cardPadding),
        child: child,
      ),
    );

    if (onTap != null) {
      return InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(dimensions.cardBorderRadius),
        child: card,
      );
    }

    return card;
  }
}

/// أيقونة متجاوبة تتكيف مع حجم الشاشة
class ResponsiveIcon extends StatelessWidget {
  final IconData icon;
  final Color? color;
  final ResponsiveIconSize size;

  const ResponsiveIcon(
    this.icon, {
    super.key,
    this.color,
    this.size = ResponsiveIconSize.medium,
  });

  /// إنشاء أيقونة صغيرة
  const ResponsiveIcon.small(this.icon, {super.key, this.color})
    : size = ResponsiveIconSize.small;

  /// إنشاء أيقونة متوسطة
  const ResponsiveIcon.medium(this.icon, {super.key, this.color})
    : size = ResponsiveIconSize.medium;

  /// إنشاء أيقونة كبيرة
  const ResponsiveIcon.large(this.icon, {super.key, this.color})
    : size = ResponsiveIconSize.large;

  /// إنشاء أيقونة كبيرة جداً
  const ResponsiveIcon.extraLarge(this.icon, {super.key, this.color})
    : size = ResponsiveIconSize.extraLarge;

  @override
  Widget build(BuildContext context) {
    final dimensions = AppDimensions.of(context);

    double iconSize;
    switch (size) {
      case ResponsiveIconSize.small:
        iconSize = dimensions.iconSizeS;
        break;
      case ResponsiveIconSize.medium:
        iconSize = dimensions.iconSizeM;
        break;
      case ResponsiveIconSize.large:
        iconSize = dimensions.iconSizeL;
        break;
      case ResponsiveIconSize.extraLarge:
        iconSize = dimensions.iconSizeXL;
        break;
    }

    return Icon(icon, size: iconSize, color: color);
  }
}

/// أحجام الأيقونات المتجاوبة
enum ResponsiveIconSize { small, medium, large, extraLarge }

/// مساحة فارغة متجاوبة
class ResponsiveSpacing extends StatelessWidget {
  final ResponsiveSpacingSize size;
  final bool isVertical;

  const ResponsiveSpacing({
    super.key,
    this.size = ResponsiveSpacingSize.medium,
    this.isVertical = true,
  });

  /// إنشاء مساحة صغيرة
  const ResponsiveSpacing.small({super.key, this.isVertical = true})
    : size = ResponsiveSpacingSize.small;

  /// إنشاء مساحة متوسطة
  const ResponsiveSpacing.medium({super.key, this.isVertical = true})
    : size = ResponsiveSpacingSize.medium;

  /// إنشاء مساحة كبيرة
  const ResponsiveSpacing.large({super.key, this.isVertical = true})
    : size = ResponsiveSpacingSize.large;

  @override
  Widget build(BuildContext context) {
    final dimensions = AppDimensions.of(context);

    double spacing;
    switch (size) {
      case ResponsiveSpacingSize.small:
        spacing = dimensions.spacingS;
        break;
      case ResponsiveSpacingSize.medium:
        spacing = dimensions.spacingM;
        break;
      case ResponsiveSpacingSize.large:
        spacing = dimensions.spacingL;
        break;
    }

    return SizedBox(
      width: isVertical ? null : spacing,
      height: isVertical ? spacing : null,
    );
  }
}

/// أحجام المساحات المتجاوبة
enum ResponsiveSpacingSize { small, medium, large }
