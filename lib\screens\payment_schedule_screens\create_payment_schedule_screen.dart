/// شاشة إنشاء جدولة دفعات جديدة
/// تسمح للمستخدم بإنشاء جدولة دفعات جديدة مع تحديد جميع التفاصيل
library;

import 'package:flutter/material.dart';
import '../../models/payment_schedule.dart';
import '../../models/invoice.dart';
import '../../services/payment_schedule_service.dart';
import '../../services/invoice_service.dart';
import '../../constants/revolutionary_design_colors.dart';
import '../../widgets/loading_widget.dart';

class CreatePaymentScheduleScreen extends StatefulWidget {
  final VoidCallback onScheduleCreated;

  const CreatePaymentScheduleScreen({
    super.key,
    required this.onScheduleCreated,
  });

  @override
  State<CreatePaymentScheduleScreen> createState() =>
      _CreatePaymentScheduleScreenState();
}

class _CreatePaymentScheduleScreenState
    extends State<CreatePaymentScheduleScreen> {
  final _formKey = GlobalKey<FormState>();
  final PaymentScheduleService _scheduleService = PaymentScheduleService();
  final InvoiceService _invoiceService = InvoiceService();

  // Controllers
  final _scheduleNameController = TextEditingController();
  final _installmentAmountController = TextEditingController();
  final _totalInstallmentsController = TextEditingController();
  final _notesController = TextEditingController();

  // State variables
  bool _isLoading = false;
  bool _isSubmitting = false;
  List<Invoice> _invoices = [];
  Invoice? _selectedInvoice;
  PaymentFrequency _selectedFrequency = PaymentFrequency.monthly;
  DateTime _startDate = DateTime.now();
  DateTime? _endDate;
  bool _autoPayment = false;
  final List<PaymentReminder> _reminders = [];

  @override
  void initState() {
    super.initState();
    _loadInvoices();
  }

  @override
  void dispose() {
    _scheduleNameController.dispose();
    _installmentAmountController.dispose();
    _totalInstallmentsController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  Future<void> _loadInvoices() async {
    setState(() => _isLoading = true);
    try {
      final invoices = await _invoiceService.getAllInvoices();
      // فلترة الفواتير التي لا تحتوي على جدولة نشطة
      final filteredInvoices = <Invoice>[];
      for (final invoice in invoices) {
        final existingSchedule = await _scheduleService
            .getActiveScheduleByInvoiceId(invoice.id!);
        if (existingSchedule == null && invoice.remainingAmount > 0) {
          filteredInvoices.add(invoice);
        }
      }
      setState(() {
        _invoices = filteredInvoices;
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      _showError('خطأ في تحميل الفواتير: $e');
    }
  }

  Future<void> _createSchedule() async {
    if (!_formKey.currentState!.validate()) return;
    if (_selectedInvoice == null) {
      _showError('يرجى اختيار فاتورة');
      return;
    }

    setState(() => _isSubmitting = true);

    try {
      final installmentAmount = double.parse(_installmentAmountController.text);
      final totalInstallments = int.parse(_totalInstallmentsController.text);

      final schedule = PaymentSchedule(
        invoiceId: _selectedInvoice!.id!,
        scheduleName: _scheduleNameController.text,
        totalAmount: _selectedInvoice!.remainingAmount,
        frequency: _selectedFrequency,
        startDate: _startDate,
        endDate: _endDate,
        nextPaymentDate: _calculateNextPaymentDate(),
        installmentAmount: installmentAmount,
        totalInstallments: totalInstallments,
        autoPayment: _autoPayment,
        notes: _notesController.text.isNotEmpty ? _notesController.text : null,
        reminders: _reminders,
      );

      await _scheduleService.createPaymentSchedule(schedule);

      widget.onScheduleCreated();
      if (mounted) {
        Navigator.of(context).pop();
      }
    } catch (e) {
      setState(() => _isSubmitting = false);
      _showError('خطأ في إنشاء الجدولة: $e');
    }
  }

  DateTime _calculateNextPaymentDate() {
    switch (_selectedFrequency) {
      case PaymentFrequency.daily:
        return _startDate.add(const Duration(days: 1));
      case PaymentFrequency.weekly:
        return _startDate.add(const Duration(days: 7));
      case PaymentFrequency.biweekly:
        return _startDate.add(const Duration(days: 14));
      case PaymentFrequency.monthly:
        return DateTime(_startDate.year, _startDate.month + 1, _startDate.day);
      case PaymentFrequency.quarterly:
        return DateTime(_startDate.year, _startDate.month + 3, _startDate.day);
      case PaymentFrequency.semiAnnual:
        return DateTime(_startDate.year, _startDate.month + 6, _startDate.day);
      case PaymentFrequency.annual:
        return DateTime(_startDate.year + 1, _startDate.month, _startDate.day);
      case PaymentFrequency.once:
        return _startDate;
    }
  }

  void _showError(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: RevolutionaryColors.errorCoral,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: RevolutionaryColors.backgroundPrimary,
      appBar: AppBar(
        title: const Text('إنشاء جدولة دفعات جديدة'),
        backgroundColor: RevolutionaryColors.damascusSky,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: _isLoading
          ? const LoadingWidget()
          : Form(
              key: _formKey,
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildBasicInfo(),
                    const SizedBox(height: 24),
                    _buildInvoiceSelection(),
                    const SizedBox(height: 24),
                    _buildScheduleSettings(),
                    const SizedBox(height: 24),
                    _buildRemindersSection(),
                    const SizedBox(height: 24),
                    _buildNotesSection(),
                    const SizedBox(height: 32),
                    _buildSubmitButton(),
                  ],
                ),
              ),
            ),
    );
  }

  Widget _buildBasicInfo() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'المعلومات الأساسية',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: RevolutionaryColors.damascusSky,
              ),
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _scheduleNameController,
              decoration: InputDecoration(
                labelText: 'اسم الجدولة *',
                hintText: 'مثال: جدولة دفعات فاتورة رقم 001',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'يرجى إدخال اسم الجدولة';
                }
                return null;
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInvoiceSelection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'اختيار الفاتورة',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: RevolutionaryColors.damascusSky,
              ),
            ),
            const SizedBox(height: 16),
            DropdownButtonFormField<Invoice>(
              value: _selectedInvoice,
              decoration: InputDecoration(
                labelText: 'الفاتورة *',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              items: _invoices.map((invoice) {
                return DropdownMenuItem(
                  value: invoice,
                  child: Text(
                    '${invoice.invoiceNumber} - ${invoice.remainingAmount.toStringAsFixed(2)} ل.س',
                  ),
                );
              }).toList(),
              onChanged: (value) {
                setState(() {
                  _selectedInvoice = value;
                  if (value != null) {
                    // تحديث المبلغ الافتراضي للقسط
                    final suggestedAmount =
                        value.remainingAmount / 12; // 12 شهر افتراضي
                    _installmentAmountController.text = suggestedAmount
                        .toStringAsFixed(2);
                    _totalInstallmentsController.text = '12';
                  }
                });
              },
              validator: (value) {
                if (value == null) {
                  return 'يرجى اختيار فاتورة';
                }
                return null;
              },
            ),
            if (_selectedInvoice != null) ...[
              const SizedBox(height: 12),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: RevolutionaryColors.backgroundSecondary,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'تفاصيل الفاتورة:',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: RevolutionaryColors.textPrimary,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'المبلغ الإجمالي: ${_selectedInvoice!.totalAmount.toStringAsFixed(2)} ل.س',
                    ),
                    Text(
                      'المبلغ المدفوع: ${_selectedInvoice!.paidAmount.toStringAsFixed(2)} ل.س',
                    ),
                    Text(
                      'المبلغ المتبقي: ${_selectedInvoice!.remainingAmount.toStringAsFixed(2)} ل.س',
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildScheduleSettings() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'إعدادات الجدولة',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: RevolutionaryColors.damascusSky,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: TextFormField(
                    controller: _installmentAmountController,
                    keyboardType: TextInputType.number,
                    decoration: InputDecoration(
                      labelText: 'مبلغ القسط *',
                      suffixText: 'ل.س',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'يرجى إدخال مبلغ القسط';
                      }
                      final amount = double.tryParse(value);
                      if (amount == null || amount <= 0) {
                        return 'يرجى إدخال مبلغ صحيح';
                      }
                      return null;
                    },
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: TextFormField(
                    controller: _totalInstallmentsController,
                    keyboardType: TextInputType.number,
                    decoration: InputDecoration(
                      labelText: 'عدد الأقساط *',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'يرجى إدخال عدد الأقساط';
                      }
                      final count = int.tryParse(value);
                      if (count == null || count <= 0) {
                        return 'يرجى إدخال عدد صحيح';
                      }
                      return null;
                    },
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            DropdownButtonFormField<PaymentFrequency>(
              value: _selectedFrequency,
              decoration: InputDecoration(
                labelText: 'تكرار الدفع',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              items: PaymentFrequency.values.map((frequency) {
                return DropdownMenuItem(
                  value: frequency,
                  child: Text(frequency.displayName),
                );
              }).toList(),
              onChanged: (value) {
                setState(() {
                  _selectedFrequency = value!;
                });
              },
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: InkWell(
                    onTap: () => _selectStartDate(),
                    child: InputDecorator(
                      decoration: InputDecoration(
                        labelText: 'تاريخ البداية',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: Text(_formatDate(_startDate)),
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: InkWell(
                    onTap: () => _selectEndDate(),
                    child: InputDecorator(
                      decoration: InputDecoration(
                        labelText: 'تاريخ النهاية (اختياري)',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: Text(
                        _endDate != null ? _formatDate(_endDate!) : 'غير محدد',
                      ),
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            SwitchListTile(
              title: const Text('الدفع التلقائي'),
              subtitle: const Text('تفعيل الدفع التلقائي عند استحقاق القسط'),
              value: _autoPayment,
              onChanged: (value) {
                setState(() {
                  _autoPayment = value;
                });
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRemindersSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Text(
                  'التذكيرات',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: RevolutionaryColors.damascusSky,
                  ),
                ),
                const Spacer(),
                TextButton.icon(
                  onPressed: _addReminder,
                  icon: const Icon(Icons.add),
                  label: const Text('إضافة تذكير'),
                ),
              ],
            ),
            const SizedBox(height: 16),
            if (_reminders.isEmpty)
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: RevolutionaryColors.backgroundSecondary,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Center(child: Text('لا توجد تذكيرات مضافة')),
              )
            else
              ListView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: _reminders.length,
                itemBuilder: (context, index) {
                  final reminder = _reminders[index];
                  return Card(
                    child: ListTile(
                      title: Text(reminder.title),
                      subtitle: Text(
                        '${reminder.daysBefore} أيام قبل الاستحقاق',
                      ),
                      trailing: IconButton(
                        icon: const Icon(Icons.delete),
                        onPressed: () => _removeReminder(index),
                      ),
                    ),
                  );
                },
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildNotesSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'ملاحظات',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: RevolutionaryColors.damascusSky,
              ),
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _notesController,
              maxLines: 3,
              decoration: InputDecoration(
                labelText: 'ملاحظات إضافية',
                hintText: 'أدخل أي ملاحظات حول الجدولة...',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSubmitButton() {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: _isSubmitting ? null : _createSchedule,
        style: ElevatedButton.styleFrom(
          backgroundColor: RevolutionaryColors.damascusSky,
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(vertical: 16),
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        ),
        child: _isSubmitting
            ? const SizedBox(
                height: 20,
                width: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              )
            : const Text(
                'إنشاء الجدولة',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
              ),
      ),
    );
  }

  Future<void> _selectStartDate() async {
    final date = await showDatePicker(
      context: context,
      initialDate: _startDate,
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365 * 5)),
    );
    if (date != null) {
      setState(() {
        _startDate = date;
      });
    }
  }

  Future<void> _selectEndDate() async {
    final date = await showDatePicker(
      context: context,
      initialDate: _endDate ?? _startDate.add(const Duration(days: 365)),
      firstDate: _startDate,
      lastDate: DateTime.now().add(const Duration(days: 365 * 10)),
    );
    if (date != null) {
      setState(() {
        _endDate = date;
      });
    }
  }

  void _addReminder() {
    showDialog(
      context: context,
      builder: (context) => _ReminderDialog(
        onReminderAdded: (reminder) {
          setState(() {
            _reminders.add(reminder);
          });
        },
      ),
    );
  }

  void _removeReminder(int index) {
    setState(() {
      _reminders.removeAt(index);
    });
  }

  String _formatDate(DateTime date) {
    return '${date.day.toString().padLeft(2, '0')}/'
        '${date.month.toString().padLeft(2, '0')}/'
        '${date.year}';
  }
}

class _ReminderDialog extends StatefulWidget {
  final Function(PaymentReminder) onReminderAdded;

  const _ReminderDialog({required this.onReminderAdded});

  @override
  State<_ReminderDialog> createState() => _ReminderDialogState();
}

class _ReminderDialogState extends State<_ReminderDialog> {
  final _titleController = TextEditingController();
  final _messageController = TextEditingController();
  final _daysController = TextEditingController(text: '3');
  ReminderType _selectedType = ReminderType.notification;

  @override
  void dispose() {
    _titleController.dispose();
    _messageController.dispose();
    _daysController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('إضافة تذكير'),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          TextFormField(
            controller: _titleController,
            decoration: const InputDecoration(
              labelText: 'عنوان التذكير',
              border: OutlineInputBorder(),
            ),
          ),
          const SizedBox(height: 16),
          TextFormField(
            controller: _messageController,
            decoration: const InputDecoration(
              labelText: 'رسالة التذكير',
              border: OutlineInputBorder(),
            ),
            maxLines: 2,
          ),
          const SizedBox(height: 16),
          TextFormField(
            controller: _daysController,
            keyboardType: TextInputType.number,
            decoration: const InputDecoration(
              labelText: 'عدد الأيام قبل الاستحقاق',
              border: OutlineInputBorder(),
            ),
          ),
          const SizedBox(height: 16),
          DropdownButtonFormField<ReminderType>(
            value: _selectedType,
            decoration: const InputDecoration(
              labelText: 'نوع التذكير',
              border: OutlineInputBorder(),
            ),
            items: ReminderType.values.map((type) {
              return DropdownMenuItem(
                value: type,
                child: Text(type.displayName),
              );
            }).toList(),
            onChanged: (value) {
              setState(() {
                _selectedType = value!;
              });
            },
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('إلغاء'),
        ),
        ElevatedButton(
          onPressed: () {
            if (_titleController.text.isNotEmpty &&
                _messageController.text.isNotEmpty &&
                _daysController.text.isNotEmpty) {
              final reminder = PaymentReminder(
                scheduleId: 0, // سيتم تحديثه عند الحفظ
                type: _selectedType,
                daysBefore: int.parse(_daysController.text),
                title: _titleController.text,
                message: _messageController.text,
              );
              widget.onReminderAdded(reminder);
              Navigator.of(context).pop();
            }
          },
          child: const Text('إضافة'),
        ),
      ],
    );
  }
}
