/// نماذج نظام التنبيهات الذكية
/// يدعم جميع أنواع التنبيهات والإشعارات الذكية
library;

import 'package:flutter/material.dart';

/// نوع التنبيه
enum NotificationType {
  payment,           // دفعة مستحقة
  invoice,           // فاتورة
  inventory,         // مخزون
  tax,               // ضريبة
  reminder,          // تذكير عام
  warning,           // تحذير
  error,             // خطأ
  info,              // معلومات
  success,           // نجاح
  backup,            // نسخ احتياطي
  maintenance,       // صيانة
  security,          // أمان
}

/// أولوية التنبيه
enum NotificationPriority {
  low,               // منخفضة
  medium,            // متوسطة
  high,              // عالية
  urgent,            // عاجلة
  critical,          // حرجة
}

/// حالة التنبيه
enum NotificationStatus {
  pending,           // معلق
  sent,              // مرسل
  read,              // مقروء
  dismissed,         // مرفوض
  snoozed,           // مؤجل
  expired,           // منتهي الصلاحية
}

/// طريقة التنبيه
enum NotificationMethod {
  inApp,             // داخل التطبيق
  email,             // بريد إلكتروني
  sms,               // رسالة نصية
  push,              // إشعار فوري
  desktop,           // سطح المكتب
  sound,             // صوت
}

/// تكرار التنبيه
enum NotificationRecurrence {
  once,              // مرة واحدة
  daily,             // يومي
  weekly,            // أسبوعي
  monthly,           // شهري
  quarterly,         // ربع سنوي
  yearly,            // سنوي
  custom,            // مخصص
}

/// التنبيه الذكي
class SmartNotification {
  final int? id;
  final String title;
  final String message;
  final NotificationType type;
  final NotificationPriority priority;
  final NotificationStatus status;
  final DateTime scheduledTime;
  final DateTime? sentTime;
  final DateTime? readTime;
  final DateTime createdAt;
  final DateTime? updatedAt;
  final String? relatedEntityId;
  final String? relatedEntityType;
  final Map<String, dynamic> metadata;
  final List<NotificationMethod> methods;
  final NotificationRecurrence recurrence;
  final DateTime? expiryTime;
  final int snoozeCount;
  final DateTime? snoozeUntil;
  final bool isActive;

  const SmartNotification({
    this.id,
    required this.title,
    required this.message,
    required this.type,
    this.priority = NotificationPriority.medium,
    this.status = NotificationStatus.pending,
    required this.scheduledTime,
    this.sentTime,
    this.readTime,
    required this.createdAt,
    this.updatedAt,
    this.relatedEntityId,
    this.relatedEntityType,
    this.metadata = const {},
    this.methods = const [NotificationMethod.inApp],
    this.recurrence = NotificationRecurrence.once,
    this.expiryTime,
    this.snoozeCount = 0,
    this.snoozeUntil,
    this.isActive = true,
  });

  SmartNotification copyWith({
    int? id,
    String? title,
    String? message,
    NotificationType? type,
    NotificationPriority? priority,
    NotificationStatus? status,
    DateTime? scheduledTime,
    DateTime? sentTime,
    DateTime? readTime,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? relatedEntityId,
    String? relatedEntityType,
    Map<String, dynamic>? metadata,
    List<NotificationMethod>? methods,
    NotificationRecurrence? recurrence,
    DateTime? expiryTime,
    int? snoozeCount,
    DateTime? snoozeUntil,
    bool? isActive,
  }) {
    return SmartNotification(
      id: id ?? this.id,
      title: title ?? this.title,
      message: message ?? this.message,
      type: type ?? this.type,
      priority: priority ?? this.priority,
      status: status ?? this.status,
      scheduledTime: scheduledTime ?? this.scheduledTime,
      sentTime: sentTime ?? this.sentTime,
      readTime: readTime ?? this.readTime,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      relatedEntityId: relatedEntityId ?? this.relatedEntityId,
      relatedEntityType: relatedEntityType ?? this.relatedEntityType,
      metadata: metadata ?? this.metadata,
      methods: methods ?? this.methods,
      recurrence: recurrence ?? this.recurrence,
      expiryTime: expiryTime ?? this.expiryTime,
      snoozeCount: snoozeCount ?? this.snoozeCount,
      snoozeUntil: snoozeUntil ?? this.snoozeUntil,
      isActive: isActive ?? this.isActive,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'message': message,
      'type': type.name,
      'priority': priority.name,
      'status': status.name,
      'scheduledTime': scheduledTime.toIso8601String(),
      'sentTime': sentTime?.toIso8601String(),
      'readTime': readTime?.toIso8601String(),
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
      'relatedEntityId': relatedEntityId,
      'relatedEntityType': relatedEntityType,
      'metadata': metadata,
      'methods': methods.map((m) => m.name).toList(),
      'recurrence': recurrence.name,
      'expiryTime': expiryTime?.toIso8601String(),
      'snoozeCount': snoozeCount,
      'snoozeUntil': snoozeUntil?.toIso8601String(),
      'isActive': isActive,
    };
  }

  factory SmartNotification.fromJson(Map<String, dynamic> json) {
    return SmartNotification(
      id: json['id'],
      title: json['title'],
      message: json['message'],
      type: NotificationType.values.firstWhere(
        (e) => e.name == json['type'],
      ),
      priority: NotificationPriority.values.firstWhere(
        (e) => e.name == json['priority'],
        orElse: () => NotificationPriority.medium,
      ),
      status: NotificationStatus.values.firstWhere(
        (e) => e.name == json['status'],
        orElse: () => NotificationStatus.pending,
      ),
      scheduledTime: DateTime.parse(json['scheduledTime']),
      sentTime: json['sentTime'] != null 
          ? DateTime.parse(json['sentTime']) 
          : null,
      readTime: json['readTime'] != null 
          ? DateTime.parse(json['readTime']) 
          : null,
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: json['updatedAt'] != null 
          ? DateTime.parse(json['updatedAt']) 
          : null,
      relatedEntityId: json['relatedEntityId'],
      relatedEntityType: json['relatedEntityType'],
      metadata: Map<String, dynamic>.from(json['metadata'] ?? {}),
      methods: (json['methods'] as List?)
          ?.map((m) => NotificationMethod.values.firstWhere(
                (e) => e.name == m,
                orElse: () => NotificationMethod.inApp,
              ))
          .toList() ?? [NotificationMethod.inApp],
      recurrence: NotificationRecurrence.values.firstWhere(
        (e) => e.name == json['recurrence'],
        orElse: () => NotificationRecurrence.once,
      ),
      expiryTime: json['expiryTime'] != null 
          ? DateTime.parse(json['expiryTime']) 
          : null,
      snoozeCount: json['snoozeCount'] ?? 0,
      snoozeUntil: json['snoozeUntil'] != null 
          ? DateTime.parse(json['snoozeUntil']) 
          : null,
      isActive: json['isActive'] ?? true,
    );
  }
}

/// قالب التنبيه
class NotificationTemplate {
  final int? id;
  final String name;
  final String titleTemplate;
  final String messageTemplate;
  final NotificationType type;
  final NotificationPriority defaultPriority;
  final List<NotificationMethod> defaultMethods;
  final Map<String, dynamic> defaultMetadata;
  final bool isActive;
  final DateTime createdAt;
  final DateTime? updatedAt;

  const NotificationTemplate({
    this.id,
    required this.name,
    required this.titleTemplate,
    required this.messageTemplate,
    required this.type,
    this.defaultPriority = NotificationPriority.medium,
    this.defaultMethods = const [NotificationMethod.inApp],
    this.defaultMetadata = const {},
    this.isActive = true,
    required this.createdAt,
    this.updatedAt,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'titleTemplate': titleTemplate,
      'messageTemplate': messageTemplate,
      'type': type.name,
      'defaultPriority': defaultPriority.name,
      'defaultMethods': defaultMethods.map((m) => m.name).toList(),
      'defaultMetadata': defaultMetadata,
      'isActive': isActive,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
    };
  }

  factory NotificationTemplate.fromJson(Map<String, dynamic> json) {
    return NotificationTemplate(
      id: json['id'],
      name: json['name'],
      titleTemplate: json['titleTemplate'],
      messageTemplate: json['messageTemplate'],
      type: NotificationType.values.firstWhere(
        (e) => e.name == json['type'],
      ),
      defaultPriority: NotificationPriority.values.firstWhere(
        (e) => e.name == json['defaultPriority'],
        orElse: () => NotificationPriority.medium,
      ),
      defaultMethods: (json['defaultMethods'] as List?)
          ?.map((m) => NotificationMethod.values.firstWhere(
                (e) => e.name == m,
                orElse: () => NotificationMethod.inApp,
              ))
          .toList() ?? [NotificationMethod.inApp],
      defaultMetadata: Map<String, dynamic>.from(json['defaultMetadata'] ?? {}),
      isActive: json['isActive'] ?? true,
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: json['updatedAt'] != null 
          ? DateTime.parse(json['updatedAt']) 
          : null,
    );
  }
}

/// إعدادات التنبيهات
class NotificationSettings {
  final bool enableNotifications;
  final bool enableSounds;
  final bool enableVibration;
  final Map<NotificationType, bool> typeSettings;
  final Map<NotificationPriority, bool> prioritySettings;
  final Map<NotificationMethod, bool> methodSettings;
  final int defaultSnoozeMinutes;
  final bool enableQuietHours;
  final TimeOfDay? quietHoursStart;
  final TimeOfDay? quietHoursEnd;
  final bool enableWeekendQuietHours;
  final int maxNotificationsPerDay;
  final bool groupSimilarNotifications;

  const NotificationSettings({
    this.enableNotifications = true,
    this.enableSounds = true,
    this.enableVibration = true,
    this.typeSettings = const {},
    this.prioritySettings = const {},
    this.methodSettings = const {},
    this.defaultSnoozeMinutes = 15,
    this.enableQuietHours = false,
    this.quietHoursStart,
    this.quietHoursEnd,
    this.enableWeekendQuietHours = false,
    this.maxNotificationsPerDay = 50,
    this.groupSimilarNotifications = true,
  });

  Map<String, dynamic> toJson() {
    return {
      'enableNotifications': enableNotifications,
      'enableSounds': enableSounds,
      'enableVibration': enableVibration,
      'typeSettings': typeSettings.map(
        (key, value) => MapEntry(key.name, value),
      ),
      'prioritySettings': prioritySettings.map(
        (key, value) => MapEntry(key.name, value),
      ),
      'methodSettings': methodSettings.map(
        (key, value) => MapEntry(key.name, value),
      ),
      'defaultSnoozeMinutes': defaultSnoozeMinutes,
      'enableQuietHours': enableQuietHours,
      'quietHoursStart': quietHoursStart != null 
          ? '${quietHoursStart!.hour}:${quietHoursStart!.minute}' 
          : null,
      'quietHoursEnd': quietHoursEnd != null 
          ? '${quietHoursEnd!.hour}:${quietHoursEnd!.minute}' 
          : null,
      'enableWeekendQuietHours': enableWeekendQuietHours,
      'maxNotificationsPerDay': maxNotificationsPerDay,
      'groupSimilarNotifications': groupSimilarNotifications,
    };
  }

  factory NotificationSettings.fromJson(Map<String, dynamic> json) {
    return NotificationSettings(
      enableNotifications: json['enableNotifications'] ?? true,
      enableSounds: json['enableSounds'] ?? true,
      enableVibration: json['enableVibration'] ?? true,
      typeSettings: (json['typeSettings'] as Map<String, dynamic>?)
          ?.map((key, value) => MapEntry(
                NotificationType.values.firstWhere((e) => e.name == key),
                value as bool,
              )) ?? {},
      prioritySettings: (json['prioritySettings'] as Map<String, dynamic>?)
          ?.map((key, value) => MapEntry(
                NotificationPriority.values.firstWhere((e) => e.name == key),
                value as bool,
              )) ?? {},
      methodSettings: (json['methodSettings'] as Map<String, dynamic>?)
          ?.map((key, value) => MapEntry(
                NotificationMethod.values.firstWhere((e) => e.name == key),
                value as bool,
              )) ?? {},
      defaultSnoozeMinutes: json['defaultSnoozeMinutes'] ?? 15,
      enableQuietHours: json['enableQuietHours'] ?? false,
      quietHoursStart: json['quietHoursStart'] != null 
          ? _parseTimeOfDay(json['quietHoursStart']) 
          : null,
      quietHoursEnd: json['quietHoursEnd'] != null 
          ? _parseTimeOfDay(json['quietHoursEnd']) 
          : null,
      enableWeekendQuietHours: json['enableWeekendQuietHours'] ?? false,
      maxNotificationsPerDay: json['maxNotificationsPerDay'] ?? 50,
      groupSimilarNotifications: json['groupSimilarNotifications'] ?? true,
    );
  }

  static TimeOfDay _parseTimeOfDay(String timeString) {
    final parts = timeString.split(':');
    return TimeOfDay(
      hour: int.parse(parts[0]),
      minute: int.parse(parts[1]),
    );
  }
}
