/// خدمة التخطيط الوظيفي
/// توفر جميع العمليات المتعلقة بالتخطيط الوظيفي والتطوير المهني
library;

import '../database/database_helper.dart';
import '../services/logging_service.dart';
import '../services/audit_service.dart';
import '../exceptions/validation_exception.dart';
import '../models/career_planning_models.dart';

class CareerPlanningService {
  final DatabaseHelper _databaseHelper = DatabaseHelper();

  /// إنشاء جداول التخطيط الوظيفي
  Future<void> _createCareerPlanningTables() async {
    final db = await _databaseHelper.database;

    // جدول المسارات الوظيفية
    await db.execute('''
      CREATE TABLE IF NOT EXISTS career_paths (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        description TEXT NOT NULL,
        department TEXT NOT NULL,
        level TEXT NOT NULL,
        required_skills TEXT,
        preferred_qualifications TEXT,
        min_salary REAL DEFAULT 0,
        max_salary REAL DEFAULT 0,
        experience_years INTEGER DEFAULT 0,
        next_level_path TEXT,
        is_active BOOLEAN DEFAULT 1,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL
      )
    ''');

    // جدول خطط التطوير الوظيفي
    await db.execute('''
      CREATE TABLE IF NOT EXISTS career_development_plans (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        employee_id INTEGER NOT NULL,
        current_path_id INTEGER NOT NULL,
        target_path_id INTEGER,
        status TEXT DEFAULT 'draft',
        start_date TEXT NOT NULL,
        target_date TEXT NOT NULL,
        goals TEXT NOT NULL,
        current_skills TEXT,
        skill_gaps TEXT,
        development_actions TEXT,
        milestones TEXT,
        mentor_id INTEGER,
        manager_notes TEXT,
        employee_notes TEXT,
        progress_percentage REAL DEFAULT 0,
        last_review_date TEXT,
        next_review_date TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        FOREIGN KEY (employee_id) REFERENCES employees (id),
        FOREIGN KEY (current_path_id) REFERENCES career_paths (id),
        FOREIGN KEY (target_path_id) REFERENCES career_paths (id),
        FOREIGN KEY (mentor_id) REFERENCES employees (id)
      )
    ''');

    // جدول مراجعات التطوير الوظيفي
    await db.execute('''
      CREATE TABLE IF NOT EXISTS career_reviews (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        plan_id INTEGER NOT NULL,
        reviewer_id INTEGER NOT NULL,
        review_date TEXT NOT NULL,
        review_type TEXT DEFAULT 'quarterly',
        progress_rating REAL DEFAULT 3.0,
        achievements TEXT,
        challenges TEXT,
        feedback TEXT,
        recommendations TEXT,
        next_steps TEXT,
        is_approved BOOLEAN DEFAULT 0,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        FOREIGN KEY (plan_id) REFERENCES career_development_plans (id),
        FOREIGN KEY (reviewer_id) REFERENCES employees (id)
      )
    ''');
  }

  /// إضافة مسار وظيفي جديد
  Future<CareerPath> addCareerPath(CareerPath path) async {
    try {
      await _createCareerPlanningTables();

      // التحقق من عدم تكرار اسم المسار في نفس القسم
      final existing = await _getCareerPathByNameAndDepartment(
        path.name,
        path.department,
      );
      if (existing != null) {
        throw ValidationException('يوجد مسار وظيفي بنفس الاسم في هذا القسم');
      }

      final db = await _databaseHelper.database;
      final id = await db.insert('career_paths', path.toMap());

      final savedPath = path.copyWith(id: id);

      // تسجيل في سجل المراجعة
      await AuditService.log(
        action: 'CREATE',
        entityType: 'CareerPath',
        entityId: id,
        description: 'إضافة مسار وظيفي جديد: ${path.name}',
        newValues: savedPath.toMap(),
      );

      LoggingService.info(
        'تم إضافة مسار وظيفي جديد',
        category: 'CareerPlanningService',
        data: {'pathId': id, 'name': path.name, 'department': path.department},
      );

      return savedPath;
    } catch (e) {
      LoggingService.error(
        'خطأ في إضافة المسار الوظيفي',
        category: 'CareerPlanningService',
        data: {'error': e.toString()},
      );
      rethrow;
    }
  }

  /// الحصول على جميع المسارات الوظيفية
  Future<List<CareerPath>> getAllCareerPaths({
    String? department,
    String? level,
    bool? isActive,
  }) async {
    try {
      await _createCareerPlanningTables();
      final db = await _databaseHelper.database;

      String whereClause = '1=1';
      List<dynamic> whereArgs = [];

      if (department != null) {
        whereClause += ' AND department = ?';
        whereArgs.add(department);
      }

      if (level != null) {
        whereClause += ' AND level = ?';
        whereArgs.add(level);
      }

      if (isActive != null) {
        whereClause += ' AND is_active = ?';
        whereArgs.add(isActive ? 1 : 0);
      }

      final result = await db.query(
        'career_paths',
        where: whereClause,
        whereArgs: whereArgs.isNotEmpty ? whereArgs : null,
        orderBy: 'department, level, name',
      );

      return result.map((map) => CareerPath.fromMap(map)).toList();
    } catch (e) {
      LoggingService.error(
        'خطأ في جلب المسارات الوظيفية',
        category: 'CareerPlanningService',
        data: {'error': e.toString()},
      );
      return [];
    }
  }

  /// إنشاء خطة تطوير وظيفي
  Future<CareerDevelopmentPlan> createDevelopmentPlan(
    CareerDevelopmentPlan plan,
  ) async {
    try {
      await _createCareerPlanningTables();

      // التحقق من صحة التواريخ
      if (plan.startDate.isAfter(plan.targetDate)) {
        throw ValidationException(
          'تاريخ البداية لا يمكن أن يكون بعد التاريخ المستهدف',
        );
      }

      // التحقق من عدم وجود خطة نشطة للموظف
      final existingPlan = await _getActiveDevelopmentPlan(plan.employeeId);
      if (existingPlan != null) {
        throw ValidationException('يوجد خطة تطوير نشطة للموظف');
      }

      final db = await _databaseHelper.database;
      final id = await db.insert('career_development_plans', plan.toMap());

      final savedPlan = plan.copyWith(id: id);

      // تسجيل في سجل المراجعة
      await AuditService.log(
        action: 'CREATE',
        entityType: 'CareerDevelopmentPlan',
        entityId: id,
        description: 'إنشاء خطة تطوير وظيفي جديدة',
        newValues: savedPlan.toMap(),
      );

      LoggingService.info(
        'تم إنشاء خطة تطوير وظيفي جديدة',
        category: 'CareerPlanningService',
        data: {'planId': id, 'employeeId': plan.employeeId},
      );

      return savedPlan;
    } catch (e) {
      LoggingService.error(
        'خطأ في إنشاء خطة التطوير الوظيفي',
        category: 'CareerPlanningService',
        data: {'employeeId': plan.employeeId, 'error': e.toString()},
      );
      rethrow;
    }
  }

  /// الحصول على خطط التطوير الوظيفي
  Future<List<CareerDevelopmentPlan>> getDevelopmentPlans({
    int? employeeId,
    String? status,
    int? mentorId,
  }) async {
    try {
      await _createCareerPlanningTables();
      final db = await _databaseHelper.database;

      String whereClause = '1=1';
      List<dynamic> whereArgs = [];

      if (employeeId != null) {
        whereClause += ' AND employee_id = ?';
        whereArgs.add(employeeId);
      }

      if (status != null) {
        whereClause += ' AND status = ?';
        whereArgs.add(status);
      }

      if (mentorId != null) {
        whereClause += ' AND mentor_id = ?';
        whereArgs.add(mentorId);
      }

      final result = await db.query(
        'career_development_plans',
        where: whereClause,
        whereArgs: whereArgs.isNotEmpty ? whereArgs : null,
        orderBy: 'target_date',
      );

      return result.map((map) => CareerDevelopmentPlan.fromMap(map)).toList();
    } catch (e) {
      LoggingService.error(
        'خطأ في جلب خطط التطوير الوظيفي',
        category: 'CareerPlanningService',
        data: {'error': e.toString()},
      );
      return [];
    }
  }

  /// تحديث تقدم خطة التطوير
  Future<CareerDevelopmentPlan> updatePlanProgress({
    required int planId,
    required double progressPercentage,
    String? employeeNotes,
    String? managerNotes,
  }) async {
    try {
      await _createCareerPlanningTables();
      final db = await _databaseHelper.database;

      final updateData = <String, dynamic>{
        'progress_percentage': progressPercentage,
        'updated_at': DateTime.now().toIso8601String(),
      };

      if (employeeNotes != null) updateData['employee_notes'] = employeeNotes;
      if (managerNotes != null) updateData['manager_notes'] = managerNotes;

      // تحديث حالة الخطة إذا اكتملت
      if (progressPercentage >= 100) {
        updateData['status'] = 'completed';
      } else if (progressPercentage > 0) {
        updateData['status'] = 'active';
      }

      await db.update(
        'career_development_plans',
        updateData,
        where: 'id = ?',
        whereArgs: [planId],
      );

      // الحصول على الخطة المحدثة
      final result = await db.query(
        'career_development_plans',
        where: 'id = ?',
        whereArgs: [planId],
        limit: 1,
      );

      if (result.isEmpty) {
        throw ValidationException('خطة التطوير غير موجودة');
      }

      final updatedPlan = CareerDevelopmentPlan.fromMap(result.first);

      // تسجيل في سجل المراجعة
      await AuditService.log(
        action: 'UPDATE',
        entityType: 'CareerDevelopmentPlan',
        entityId: planId,
        description:
            'تحديث تقدم خطة التطوير: ${progressPercentage.toStringAsFixed(1)}%',
        newValues: updatedPlan.toMap(),
      );

      LoggingService.info(
        'تم تحديث تقدم خطة التطوير',
        category: 'CareerPlanningService',
        data: {'planId': planId, 'progress': progressPercentage},
      );

      return updatedPlan;
    } catch (e) {
      LoggingService.error(
        'خطأ في تحديث تقدم خطة التطوير',
        category: 'CareerPlanningService',
        data: {'planId': planId, 'error': e.toString()},
      );
      rethrow;
    }
  }

  /// إضافة مراجعة للتطوير الوظيفي
  Future<CareerReview> addCareerReview(CareerReview review) async {
    try {
      await _createCareerPlanningTables();

      final db = await _databaseHelper.database;
      final id = await db.insert('career_reviews', review.toMap());

      final savedReview = review.copyWith(id: id);

      // تحديث تاريخ آخر مراجعة في الخطة
      await db.update(
        'career_development_plans',
        {
          'last_review_date': review.reviewDate.toIso8601String().split('T')[0],
          'updated_at': DateTime.now().toIso8601String(),
        },
        where: 'id = ?',
        whereArgs: [review.planId],
      );

      // تسجيل في سجل المراجعة
      await AuditService.log(
        action: 'CREATE',
        entityType: 'CareerReview',
        entityId: id,
        description: 'إضافة مراجعة تطوير وظيفي',
        newValues: savedReview.toMap(),
      );

      LoggingService.info(
        'تم إضافة مراجعة تطوير وظيفي',
        category: 'CareerPlanningService',
        data: {'reviewId': id, 'planId': review.planId},
      );

      return savedReview;
    } catch (e) {
      LoggingService.error(
        'خطأ في إضافة مراجعة التطوير الوظيفي',
        category: 'CareerPlanningService',
        data: {'error': e.toString()},
      );
      rethrow;
    }
  }

  // دوال مساعدة خاصة
  Future<CareerPath?> _getCareerPathByNameAndDepartment(
    String name,
    String department,
  ) async {
    final db = await _databaseHelper.database;
    final result = await db.query(
      'career_paths',
      where: 'name = ? AND department = ?',
      whereArgs: [name, department],
      limit: 1,
    );
    return result.isNotEmpty ? CareerPath.fromMap(result.first) : null;
  }

  Future<CareerDevelopmentPlan?> _getActiveDevelopmentPlan(
    int employeeId,
  ) async {
    final db = await _databaseHelper.database;
    final result = await db.query(
      'career_development_plans',
      where: 'employee_id = ? AND status IN (?, ?)',
      whereArgs: [employeeId, 'active', 'draft'],
      limit: 1,
    );
    return result.isNotEmpty
        ? CareerDevelopmentPlan.fromMap(result.first)
        : null;
  }

  /// الحصول على مراجعات التطوير الوظيفي
  Future<List<CareerReview>> getCareerReviews({
    int? planId,
    int? reviewerId,
    String? reviewType,
  }) async {
    try {
      await _createCareerPlanningTables();
      final db = await _databaseHelper.database;

      String whereClause = '1=1';
      List<dynamic> whereArgs = [];

      if (planId != null) {
        whereClause += ' AND plan_id = ?';
        whereArgs.add(planId);
      }

      if (reviewerId != null) {
        whereClause += ' AND reviewer_id = ?';
        whereArgs.add(reviewerId);
      }

      if (reviewType != null) {
        whereClause += ' AND review_type = ?';
        whereArgs.add(reviewType);
      }

      final result = await db.query(
        'career_reviews',
        where: whereClause,
        whereArgs: whereArgs.isNotEmpty ? whereArgs : null,
        orderBy: 'review_date DESC',
      );

      return result.map((map) => CareerReview.fromMap(map)).toList();
    } catch (e) {
      LoggingService.error(
        'خطأ في جلب مراجعات التطوير الوظيفي',
        category: 'CareerPlanningService',
        data: {'error': e.toString()},
      );
      return [];
    }
  }

  /// الحصول على إحصائيات التخطيط الوظيفي
  Future<Map<String, dynamic>> getCareerPlanningStatistics() async {
    try {
      await _createCareerPlanningTables();
      final db = await _databaseHelper.database;

      // إحصائيات عامة
      final totalPaths = await db.rawQuery(
        'SELECT COUNT(*) as count FROM career_paths WHERE is_active = 1',
      );
      final totalPlans = await db.rawQuery(
        'SELECT COUNT(*) as count FROM career_development_plans',
      );
      final activePlans = await db.rawQuery(
        'SELECT COUNT(*) as count FROM career_development_plans WHERE status = "active"',
      );
      final completedPlans = await db.rawQuery(
        'SELECT COUNT(*) as count FROM career_development_plans WHERE status = "completed"',
      );

      // توزيع الخطط حسب الحالة
      final statusDistribution = await db.rawQuery('''
        SELECT status, COUNT(*) as count
        FROM career_development_plans
        GROUP BY status
        ORDER BY count DESC
      ''');

      // توزيع المسارات حسب القسم
      final departmentDistribution = await db.rawQuery('''
        SELECT department, COUNT(*) as count
        FROM career_paths
        WHERE is_active = 1
        GROUP BY department
        ORDER BY count DESC
      ''');

      // متوسط التقدم
      final averageProgress = await db.rawQuery('''
        SELECT AVG(progress_percentage) as average_progress
        FROM career_development_plans
        WHERE status IN ("active", "completed")
      ''');

      // الخطط المتأخرة
      final overduePlans = await db.rawQuery('''
        SELECT COUNT(*) as count
        FROM career_development_plans
        WHERE target_date < date('now') AND status NOT IN ("completed", "cancelled")
      ''');

      // أحدث المراجعات
      final recentReviews = await db.rawQuery('''
        SELECT COUNT(*) as count
        FROM career_reviews
        WHERE review_date >= date('now', '-30 days')
      ''');

      return {
        'totalPaths': totalPaths.first['count'] ?? 0,
        'totalPlans': totalPlans.first['count'] ?? 0,
        'activePlans': activePlans.first['count'] ?? 0,
        'completedPlans': completedPlans.first['count'] ?? 0,
        'completionRate': totalPlans.first['count'] != 0
            ? ((completedPlans.first['count'] as int) /
                      (totalPlans.first['count'] as int) *
                      100)
                  .toStringAsFixed(1)
            : '0.0',
        'statusDistribution': statusDistribution,
        'departmentDistribution': departmentDistribution,
        'averageProgress':
            (averageProgress.first['average_progress'] as double?) ?? 0.0,
        'overduePlans': overduePlans.first['count'] ?? 0,
        'recentReviews': recentReviews.first['count'] ?? 0,
      };
    } catch (e) {
      LoggingService.error(
        'خطأ في جلب إحصائيات التخطيط الوظيفي',
        category: 'CareerPlanningService',
        data: {'error': e.toString()},
      );
      return {};
    }
  }

  /// إنشاء المسارات الوظيفية الافتراضية
  Future<void> createDefaultCareerPaths() async {
    try {
      final defaultPaths = [
        {
          'name': 'محاسب مبتدئ',
          'description': 'موظف محاسبة في بداية مسيرته المهنية',
          'department': 'المحاسبة',
          'level': 'entry',
          'requiredSkills': ['أساسيات المحاسبة', 'Excel', 'إدخال البيانات'],
          'preferredQualifications': ['بكالوريوس محاسبة', 'دورات محاسبة'],
          'minSalary': 800000.0,
          'maxSalary': 1200000.0,
          'experienceYears': 0,
          'nextLevelPath': 'محاسب',
        },
        {
          'name': 'محاسب',
          'description': 'محاسب بخبرة متوسطة',
          'department': 'المحاسبة',
          'level': 'mid',
          'requiredSkills': [
            'المحاسبة المتقدمة',
            'التقارير المالية',
            'التحليل المالي',
          ],
          'preferredQualifications': ['بكالوريوس محاسبة', 'خبرة 2-5 سنوات'],
          'minSalary': 1200000.0,
          'maxSalary': 1800000.0,
          'experienceYears': 2,
          'nextLevelPath': 'محاسب أول',
        },
        {
          'name': 'محاسب أول',
          'description': 'محاسب كبير بخبرة واسعة',
          'department': 'المحاسبة',
          'level': 'senior',
          'requiredSkills': ['المحاسبة المتقدمة', 'الإشراف', 'التدقيق الداخلي'],
          'preferredQualifications': [
            'بكالوريوس محاسبة',
            'خبرة 5+ سنوات',
            'شهادات مهنية',
          ],
          'minSalary': 1800000.0,
          'maxSalary': 2500000.0,
          'experienceYears': 5,
          'nextLevelPath': 'مدير محاسبة',
        },
        {
          'name': 'مدير محاسبة',
          'description': 'مدير قسم المحاسبة',
          'department': 'المحاسبة',
          'level': 'manager',
          'requiredSkills': ['الإدارة', 'التخطيط المالي', 'القيادة'],
          'preferredQualifications': [
            'بكالوريوس محاسبة',
            'خبرة إدارية 3+ سنوات',
          ],
          'minSalary': 2500000.0,
          'maxSalary': 3500000.0,
          'experienceYears': 8,
          'nextLevelPath': 'مدير مالي',
        },
        {
          'name': 'مطور برمجيات مبتدئ',
          'description': 'مطور في بداية مسيرته المهنية',
          'department': 'تقنية المعلومات',
          'level': 'entry',
          'requiredSkills': ['البرمجة الأساسية', 'قواعد البيانات', 'Git'],
          'preferredQualifications': [
            'بكالوريوس هندسة معلوماتية',
            'مشاريع شخصية',
          ],
          'minSalary': 1000000.0,
          'maxSalary': 1500000.0,
          'experienceYears': 0,
          'nextLevelPath': 'مطور برمجيات',
        },
        {
          'name': 'مطور برمجيات',
          'description': 'مطور بخبرة متوسطة',
          'department': 'تقنية المعلومات',
          'level': 'mid',
          'requiredSkills': [
            'البرمجة المتقدمة',
            'تصميم الأنظمة',
            'اختبار البرمجيات',
          ],
          'preferredQualifications': [
            'بكالوريوس هندسة معلوماتية',
            'خبرة 2-5 سنوات',
          ],
          'minSalary': 1500000.0,
          'maxSalary': 2200000.0,
          'experienceYears': 2,
          'nextLevelPath': 'مطور أول',
        },
      ];

      for (final pathData in defaultPaths) {
        final existing = await _getCareerPathByNameAndDepartment(
          pathData['name'] as String,
          pathData['department'] as String,
        );

        if (existing == null) {
          final path = CareerPath(
            name: pathData['name'] as String,
            description: pathData['description'] as String,
            department: pathData['department'] as String,
            level: pathData['level'] as String,
            requiredSkills: pathData['requiredSkills'] as List<String>,
            preferredQualifications:
                pathData['preferredQualifications'] as List<String>,
            minSalary: pathData['minSalary'] as double,
            maxSalary: pathData['maxSalary'] as double,
            experienceYears: pathData['experienceYears'] as int,
            nextLevelPath: pathData['nextLevelPath'] as String?,
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          );

          await addCareerPath(path);
        }
      }

      LoggingService.info(
        'تم إنشاء المسارات الوظيفية الافتراضية',
        category: 'CareerPlanningService',
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في إنشاء المسارات الوظيفية الافتراضية',
        category: 'CareerPlanningService',
        data: {'error': e.toString()},
      );
    }
  }

  /// تحديث حالة خطة التطوير
  Future<CareerDevelopmentPlan> updatePlanStatus({
    required int planId,
    required String status,
  }) async {
    try {
      await _createCareerPlanningTables();
      final db = await _databaseHelper.database;

      await db.update(
        'career_development_plans',
        {'status': status, 'updated_at': DateTime.now().toIso8601String()},
        where: 'id = ?',
        whereArgs: [planId],
      );

      // الحصول على الخطة المحدثة
      final result = await db.query(
        'career_development_plans',
        where: 'id = ?',
        whereArgs: [planId],
        limit: 1,
      );

      if (result.isEmpty) {
        throw ValidationException('خطة التطوير غير موجودة');
      }

      final updatedPlan = CareerDevelopmentPlan.fromMap(result.first);

      // تسجيل في سجل المراجعة
      await AuditService.log(
        action: 'UPDATE',
        entityType: 'CareerDevelopmentPlan',
        entityId: planId,
        description: 'تحديث حالة خطة التطوير إلى: $status',
        newValues: updatedPlan.toMap(),
      );

      LoggingService.info(
        'تم تحديث حالة خطة التطوير',
        category: 'CareerPlanningService',
        data: {'planId': planId, 'status': status},
      );

      return updatedPlan;
    } catch (e) {
      LoggingService.error(
        'خطأ في تحديث حالة خطة التطوير',
        category: 'CareerPlanningService',
        data: {'planId': planId, 'error': e.toString()},
      );
      rethrow;
    }
  }

  /// البحث عن المسارات الوظيفية المناسبة للموظف
  Future<List<CareerPath>> suggestCareerPaths({
    required String currentDepartment,
    required String currentLevel,
    required List<String> currentSkills,
  }) async {
    try {
      await _createCareerPlanningTables();
      final db = await _databaseHelper.database;

      // البحث عن المسارات في نفس القسم أو أقسام مشابهة
      final result = await db.query(
        'career_paths',
        where: 'is_active = 1 AND (department = ? OR level = ?)',
        whereArgs: [currentDepartment, _getNextLevel(currentLevel)],
        orderBy: 'department, level',
      );

      final paths = result.map((map) => CareerPath.fromMap(map)).toList();

      // ترتيب المسارات حسب مدى التطابق مع المهارات الحالية
      paths.sort((a, b) {
        final aMatch = _calculateSkillMatch(currentSkills, a.requiredSkills);
        final bMatch = _calculateSkillMatch(currentSkills, b.requiredSkills);
        return bMatch.compareTo(aMatch);
      });

      return paths;
    } catch (e) {
      LoggingService.error(
        'خطأ في اقتراح المسارات الوظيفية',
        category: 'CareerPlanningService',
        data: {'error': e.toString()},
      );
      return [];
    }
  }

  // دوال مساعدة إضافية
  String _getNextLevel(String currentLevel) {
    switch (currentLevel) {
      case 'entry':
        return 'junior';
      case 'junior':
        return 'mid';
      case 'mid':
        return 'senior';
      case 'senior':
        return 'lead';
      case 'lead':
        return 'manager';
      case 'manager':
        return 'director';
      default:
        return currentLevel;
    }
  }

  double _calculateSkillMatch(
    List<String> currentSkills,
    List<String> requiredSkills,
  ) {
    if (requiredSkills.isEmpty) return 0.0;

    int matchCount = 0;
    for (final skill in requiredSkills) {
      if (currentSkills.any(
        (current) =>
            current.toLowerCase().contains(skill.toLowerCase()) ||
            skill.toLowerCase().contains(current.toLowerCase()),
      )) {
        matchCount++;
      }
    }

    return matchCount / requiredSkills.length;
  }
}
