# 🛠️ دليل المطور التقني - Smart Ledger

**الإصدار:** 1.0.0  
**التاريخ:** 15 يوليو 2025  
**المطور:** مجد محمد زياد يسير  

---

## 🏗️ معمارية التطبيق

### 📁 هيكل المشروع

```
smart_ledger/
├── 📁 lib/                     # الكود الرئيسي
│   ├── 📁 constants/           # الثوابت والإعدادات
│   │   ├── app_colors.dart     # ألوان التطبيق
│   │   ├── app_constants.dart  # الثوابت العامة
│   │   └── accessibility_constants.dart
│   ├── 📁 database/            # إعداد قاعدة البيانات
│   │   └── database_helper.dart
│   ├── 📁 models/              # نماذج البيانات
│   │   ├── account.dart        # نموذج الحساب
│   │   ├── invoice.dart        # نموذج الفاتورة
│   │   ├── item.dart          # نموذج الصنف
│   │   └── journal_entry.dart  # نموذج القيد
│   ├── 📁 services/            # خدمات المنطق
│   │   ├── account_service.dart
│   │   ├── invoice_service.dart
│   │   ├── encryption_service.dart
│   │   └── syrian_tax_service.dart
│   ├── 📁 screens/             # شاشات التطبيق
│   │   ├── home_screen.dart
│   │   ├── accounts_screen.dart
│   │   └── invoices_screen.dart
│   ├── 📁 widgets/             # المكونات المخصصة
│   │   ├── custom_app_bar.dart
│   │   └── data_table_widget.dart
│   └── 📁 responsive/          # التصميم المتجاوب
│       ├── breakpoints.dart
│       └── responsive_layout.dart
├── 📁 test/                    # الاختبارات
├── 📁 docs/                    # الوثائق
└── 📁 assets/                  # الموارد (صور، خطوط)
```

### 🔧 الأنماط المعمارية المستخدمة

#### 1. **Service Layer Pattern**
```dart
// مثال على خدمة الحسابات
class AccountService {
  final DatabaseHelper _databaseHelper = DatabaseHelper();
  
  Future<int> insertAccount(Account account) async {
    final db = await _databaseHelper.database;
    return await db.insert('accounts', account.toMap());
  }
  
  Future<List<Account>> getAllAccounts() async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query('accounts');
    return List.generate(maps.length, (i) => Account.fromMap(maps[i]));
  }
}
```

#### 2. **Repository Pattern**
```dart
// مثال على مستودع البيانات
abstract class AccountRepository {
  Future<List<Account>> getAccounts();
  Future<Account?> getAccountById(int id);
  Future<int> insertAccount(Account account);
  Future<int> updateAccount(Account account);
  Future<int> deleteAccount(int id);
}
```

#### 3. **Singleton Pattern**
```dart
// مثال على نمط المفرد
class DatabaseHelper {
  static final DatabaseHelper _instance = DatabaseHelper._internal();
  static Database? _database;
  
  DatabaseHelper._internal();
  factory DatabaseHelper() => _instance;
  
  Future<Database> get database async {
    _database ??= await _initDatabase();
    return _database!;
  }
}
```

---

## 🗄️ قاعدة البيانات

### 📊 مخطط قاعدة البيانات

#### الجداول الأساسية

```sql
-- جدول الحسابات
CREATE TABLE accounts (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  code TEXT NOT NULL UNIQUE,
  name TEXT NOT NULL,
  type TEXT NOT NULL,
  parent_id INTEGER,
  level INTEGER NOT NULL,
  is_active INTEGER DEFAULT 1,
  balance REAL DEFAULT 0,
  currency_id INTEGER DEFAULT 1,
  created_at TEXT NOT NULL,
  updated_at TEXT NOT NULL,
  FOREIGN KEY (parent_id) REFERENCES accounts (id),
  FOREIGN KEY (currency_id) REFERENCES currencies (id)
);

-- جدول الفواتير
CREATE TABLE invoices (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  invoice_number TEXT NOT NULL UNIQUE,
  customer_id INTEGER NOT NULL,
  invoice_date TEXT NOT NULL,
  due_date TEXT,
  subtotal REAL NOT NULL,
  tax_amount REAL DEFAULT 0,
  discount_amount REAL DEFAULT 0,
  total_amount REAL NOT NULL,
  status TEXT NOT NULL,
  notes TEXT,
  created_at TEXT NOT NULL,
  updated_at TEXT NOT NULL,
  FOREIGN KEY (customer_id) REFERENCES customers (id)
);

-- جدول عناصر الفاتورة
CREATE TABLE invoice_items (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  invoice_id INTEGER NOT NULL,
  item_id INTEGER NOT NULL,
  quantity REAL NOT NULL,
  unit_price REAL NOT NULL,
  discount_rate REAL DEFAULT 0,
  tax_rate REAL DEFAULT 0,
  line_total REAL NOT NULL,
  FOREIGN KEY (invoice_id) REFERENCES invoices (id),
  FOREIGN KEY (item_id) REFERENCES items (id)
);
```

### 🔐 تشفير قاعدة البيانات

```dart
// إعداد التشفير باستخدام SQLCipher
Future<Database> _initDatabase() async {
  String path = join(await getDatabasesPath(), 'smart_ledger.db');
  
  return await openDatabase(
    path,
    version: 1,
    onCreate: _onCreate,
    password: _encryptionKey, // مفتاح التشفير
  );
}

// إدارة مفاتيح التشفير
class EncryptionService {
  static String? _encryptionKey;
  
  static Future<String> generateKey() async {
    final random = Random.secure();
    final bytes = List<int>.generate(32, (i) => random.nextInt(256));
    return base64Encode(bytes);
  }
  
  static Future<bool> validateKey(String key) async {
    // التحقق من صحة المفتاح
    return key.length >= 32;
  }
}
```

---

## 🔧 الخدمات الأساسية

### 1. **خدمة الحسابات (AccountService)**

```dart
class AccountService {
  final DatabaseHelper _databaseHelper = DatabaseHelper();
  
  // إدراج حساب جديد
  Future<int> insertAccount(Account account) async {
    try {
      final db = await _databaseHelper.database;
      
      // التحقق من عدم تكرار الرمز
      final existing = await getAccountByCode(account.code);
      if (existing != null) {
        throw Exception('رمز الحساب موجود مسبقاً');
      }
      
      // إدراج الحساب
      final id = await db.insert('accounts', account.toMap());
      
      // تسجيل العملية في سجل المراجعة
      await _logAccountOperation('INSERT', id, account.toMap());
      
      return id;
    } catch (e) {
      LoggingService.error('خطأ في إدراج الحساب', data: {'error': e.toString()});
      rethrow;
    }
  }
  
  // توليد رمز حساب تلقائي
  Future<String> generateAccountCode(String accountType) async {
    final prefix = _getAccountTypePrefix(accountType);
    final lastCode = await _getLastAccountCode(prefix);
    final nextNumber = _extractNumber(lastCode) + 1;
    return '$prefix${nextNumber.toString().padLeft(3, '0')}';
  }
  
  // البحث المتقدم
  Future<List<Account>> searchAccounts({
    String? query,
    String? type,
    bool? isActive,
    int? parentId,
  }) async {
    final db = await _databaseHelper.database;
    
    String whereClause = '1=1';
    List<dynamic> whereArgs = [];
    
    if (query != null && query.isNotEmpty) {
      whereClause += ' AND (name LIKE ? OR code LIKE ?)';
      whereArgs.addAll(['%$query%', '%$query%']);
    }
    
    if (type != null) {
      whereClause += ' AND type = ?';
      whereArgs.add(type);
    }
    
    if (isActive != null) {
      whereClause += ' AND is_active = ?';
      whereArgs.add(isActive ? 1 : 0);
    }
    
    final maps = await db.query(
      'accounts',
      where: whereClause,
      whereArgs: whereArgs,
      orderBy: 'code ASC',
    );
    
    return maps.map((map) => Account.fromMap(map)).toList();
  }
}
```

### 2. **خدمة الفواتير (InvoiceService)**

```dart
class InvoiceService {
  final DatabaseHelper _databaseHelper = DatabaseHelper();
  final AccountService _accountService = AccountService();
  
  // إنشاء فاتورة مع القيود المحاسبية
  Future<int> createInvoiceWithEntries(Invoice invoice, List<InvoiceItem> items) async {
    final db = await _databaseHelper.database;
    
    return await db.transaction((txn) async {
      // 1. إدراج الفاتورة
      final invoiceId = await txn.insert('invoices', invoice.toMap());
      
      // 2. إدراج عناصر الفاتورة
      for (final item in items) {
        final itemWithInvoiceId = item.copyWith(invoiceId: invoiceId);
        await txn.insert('invoice_items', itemWithInvoiceId.toMap());
      }
      
      // 3. إنشاء القيود المحاسبية التلقائية
      await _createAutomaticJournalEntries(txn, invoiceId, invoice, items);
      
      // 4. تحديث المخزون
      await _updateInventory(txn, items, invoice.type);
      
      return invoiceId;
    });
  }
  
  // إنشاء القيود المحاسبية التلقائية
  Future<void> _createAutomaticJournalEntries(
    Transaction txn,
    int invoiceId,
    Invoice invoice,
    List<InvoiceItem> items,
  ) async {
    if (invoice.type == InvoiceType.sales) {
      // قيد المبيعات
      await _createSalesJournalEntry(txn, invoiceId, invoice, items);
    } else if (invoice.type == InvoiceType.purchase) {
      // قيد المشتريات
      await _createPurchaseJournalEntry(txn, invoiceId, invoice, items);
    }
  }
}
```

### 3. **خدمة التشفير (EncryptionService)**

```dart
class EncryptionService {
  static const String _algorithm = 'AES-256-GCM';
  
  // تشفير النصوص
  static Future<String> encryptText(String plainText, String key) async {
    try {
      final keyBytes = utf8.encode(key);
      final plainBytes = utf8.encode(plainText);
      
      // توليد IV عشوائي
      final iv = _generateRandomBytes(16);
      
      // تشفير البيانات
      final cipher = GCMBlockCipher(AESEngine());
      final params = AEADParameters(KeyParameter(keyBytes), 128, iv);
      cipher.init(true, params);
      
      final encryptedBytes = cipher.process(plainBytes);
      
      // دمج IV مع البيانات المشفرة
      final result = Uint8List.fromList([...iv, ...encryptedBytes]);
      
      return base64Encode(result);
    } catch (e) {
      throw EncryptionException('فشل في تشفير البيانات: $e');
    }
  }
  
  // فك التشفير
  static Future<String> decryptText(String encryptedText, String key) async {
    try {
      final encryptedBytes = base64Decode(encryptedText);
      final keyBytes = utf8.encode(key);
      
      // استخراج IV
      final iv = encryptedBytes.sublist(0, 16);
      final cipherText = encryptedBytes.sublist(16);
      
      // فك التشفير
      final cipher = GCMBlockCipher(AESEngine());
      final params = AEADParameters(KeyParameter(keyBytes), 128, iv);
      cipher.init(false, params);
      
      final decryptedBytes = cipher.process(cipherText);
      
      return utf8.decode(decryptedBytes);
    } catch (e) {
      throw EncryptionException('فشل في فك التشفير: $e');
    }
  }
  
  // توليد مفتاح آمن
  static String generateSecureKey() {
    final random = Random.secure();
    final bytes = List<int>.generate(32, (i) => random.nextInt(256));
    return base64Encode(bytes);
  }
}
```

---

## 🎨 واجهة المستخدم

### 🎯 نظام التصميم

#### الألوان الأساسية
```dart
class AppColors {
  // الألوان الأساسية
  static const Color primary = Color(0xFF2E7A5A);      // أخضر داكن
  static const Color primaryLight = Color(0xFF4CAF50);  // أخضر فاتح
  static const Color secondary = Color(0xFF1976D2);     // أزرق
  static const Color accent = Color(0xFFFF9800);        // برتقالي
  
  // ألوان الخلفية
  static const Color background = Color(0xFFF5F5F5);    // رمادي فاتح
  static const Color surface = Color(0xFFFFFFFF);       // أبيض
  static const Color card = Color(0xFFFAFAFA);          // رمادي فاتح جداً
  
  // ألوان النصوص
  static const Color textPrimary = Color(0xFF212121);   // أسود
  static const Color textSecondary = Color(0xFF757575); // رمادي
  static const Color textHint = Color(0xFFBDBDBD);      // رمادي فاتح
  
  // ألوان الحالة
  static const Color success = Color(0xFF4CAF50);       // أخضر
  static const Color warning = Color(0xFFFF9800);       // برتقالي
  static const Color error = Color(0xFFF44336);         // أحمر
  static const Color info = Color(0xFF2196F3);          // أزرق
}
```

#### المكونات المخصصة
```dart
// مكون جدول البيانات المخصص
class CustomDataTable extends StatelessWidget {
  final List<String> headers;
  final List<List<String>> rows;
  final Function(int)? onRowTap;
  final bool isLoading;
  
  const CustomDataTable({
    Key? key,
    required this.headers,
    required this.rows,
    this.onRowTap,
    this.isLoading = false,
  }) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    if (isLoading) {
      return const Center(child: CircularProgressIndicator());
    }
    
    return Card(
      elevation: 2,
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: DataTable(
          headingRowColor: MaterialStateProperty.all(AppColors.primaryLight),
          columns: headers.map((header) => DataColumn(
            label: Text(
              header,
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
          )).toList(),
          rows: rows.asMap().entries.map((entry) {
            final index = entry.key;
            final row = entry.value;
            
            return DataRow(
              onSelectChanged: onRowTap != null ? (_) => onRowTap!(index) : null,
              cells: row.map((cell) => DataCell(Text(cell))).toList(),
            );
          }).toList(),
        ),
      ),
    );
  }
}
```

### 📱 التصميم المتجاوب

```dart
// نقاط التوقف للتصميم المتجاوب
class Breakpoints {
  static const double mobile = 600;
  static const double tablet = 1024;
  static const double desktop = 1440;
  
  static bool isMobile(BuildContext context) {
    return MediaQuery.of(context).size.width < mobile;
  }
  
  static bool isTablet(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    return width >= mobile && width < desktop;
  }
  
  static bool isDesktop(BuildContext context) {
    return MediaQuery.of(context).size.width >= desktop;
  }
}

// تخطيط متجاوب
class ResponsiveLayout extends StatelessWidget {
  final Widget mobile;
  final Widget? tablet;
  final Widget desktop;
  
  const ResponsiveLayout({
    Key? key,
    required this.mobile,
    this.tablet,
    required this.desktop,
  }) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        if (constraints.maxWidth >= Breakpoints.desktop) {
          return desktop;
        } else if (constraints.maxWidth >= Breakpoints.mobile) {
          return tablet ?? mobile;
        } else {
          return mobile;
        }
      },
    );
  }
}
```

---

## 🔍 أفضل الممارسات

### 1. **إدارة الحالة**
```dart
// استخدام Provider لإدارة الحالة
class AccountProvider extends ChangeNotifier {
  final AccountService _accountService = AccountService();
  
  List<Account> _accounts = [];
  bool _isLoading = false;
  String? _error;
  
  List<Account> get accounts => _accounts;
  bool get isLoading => _isLoading;
  String? get error => _error;
  
  Future<void> loadAccounts() async {
    _setLoading(true);
    try {
      _accounts = await _accountService.getAllAccounts();
      _error = null;
    } catch (e) {
      _error = e.toString();
    } finally {
      _setLoading(false);
    }
  }
  
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }
}
```

### 2. **معالجة الأخطاء**
```dart
// فئة مخصصة للأخطاء
class SmartLedgerException implements Exception {
  final String message;
  final String? code;
  final dynamic originalError;
  
  const SmartLedgerException(
    this.message, {
    this.code,
    this.originalError,
  });
  
  @override
  String toString() => 'SmartLedgerException: $message';
}

// معالج الأخطاء العام
class ErrorHandler {
  static void handleError(dynamic error, {String? context}) {
    String message = 'حدث خطأ غير متوقع';
    
    if (error is SmartLedgerException) {
      message = error.message;
    } else if (error is DatabaseException) {
      message = 'خطأ في قاعدة البيانات';
    } else if (error is FormatException) {
      message = 'خطأ في تنسيق البيانات';
    }
    
    // تسجيل الخطأ
    LoggingService.error(
      message,
      category: context ?? 'General',
      data: {'error': error.toString()},
    );
    
    // عرض رسالة للمستخدم
    _showErrorToUser(message);
  }
  
  static void _showErrorToUser(String message) {
    // عرض SnackBar أو Dialog
  }
}
```

### 3. **التحقق من صحة البيانات**
```dart
// فئة التحقق من صحة البيانات
class Validators {
  static String? validateAccountName(String? value) {
    if (value == null || value.isEmpty) {
      return 'اسم الحساب مطلوب';
    }
    if (value.length < 3) {
      return 'اسم الحساب يجب أن يكون 3 أحرف على الأقل';
    }
    if (value.length > 100) {
      return 'اسم الحساب طويل جداً';
    }
    return null;
  }
  
  static String? validateAmount(String? value) {
    if (value == null || value.isEmpty) {
      return 'المبلغ مطلوب';
    }
    
    final amount = double.tryParse(value);
    if (amount == null) {
      return 'المبلغ غير صحيح';
    }
    
    if (amount < 0) {
      return 'المبلغ لا يمكن أن يكون سالباً';
    }
    
    return null;
  }
  
  static String? validateEmail(String? value) {
    if (value == null || value.isEmpty) {
      return null; // البريد الإلكتروني اختياري
    }
    
    final emailRegex = RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$');
    if (!emailRegex.hasMatch(value)) {
      return 'البريد الإلكتروني غير صحيح';
    }
    
    return null;
  }
}
```

---

## 🚀 نشر التطبيق

### 1. **إعداد الإنتاج**
```dart
// إعدادات الإنتاج
class ProductionConfig {
  static const bool isDebugMode = false;
  static const String apiBaseUrl = 'https://api.smartledger.sy';
  static const String appVersion = '1.0.0';
  static const int databaseVersion = 1;
  
  // إعدادات الأمان
  static const bool enableEncryption = true;
  static const bool enableAuditLog = true;
  static const bool enableBackup = true;
  
  // إعدادات الأداء
  static const int maxCacheSize = 100; // MB
  static const int maxBackupFiles = 10;
  static const Duration sessionTimeout = Duration(hours: 8);
}
```

### 2. **بناء التطبيق**
```bash
# بناء للويندوز
flutter build windows --release

# بناء للأندرويد
flutter build apk --release
flutter build appbundle --release

# بناء للويب
flutter build web --release
```

### 3. **اختبار ما قبل النشر**
```dart
// اختبارات التكامل
void main() {
  group('Integration Tests', () {
    testWidgets('Complete workflow test', (WidgetTester tester) async {
      // 1. تشغيل التطبيق
      await tester.pumpWidget(MyApp());
      
      // 2. تسجيل الدخول
      await tester.enterText(find.byKey(Key('password')), 'test123');
      await tester.tap(find.byKey(Key('login_button')));
      await tester.pumpAndSettle();
      
      // 3. إنشاء حساب جديد
      await tester.tap(find.byKey(Key('add_account')));
      await tester.enterText(find.byKey(Key('account_name')), 'حساب اختبار');
      await tester.tap(find.byKey(Key('save_account')));
      await tester.pumpAndSettle();
      
      // 4. التحقق من النتيجة
      expect(find.text('حساب اختبار'), findsOneWidget);
    });
  });
}
```

---

## 📚 الموارد الإضافية

### 🔗 روابط مفيدة
- **Flutter Documentation:** https://flutter.dev/docs
- **SQLite Documentation:** https://sqlite.org/docs.html
- **Dart Language:** https://dart.dev/guides

### 📖 مراجع إضافية
- **Clean Architecture:** Robert C. Martin
- **Design Patterns:** Gang of Four
- **Flutter Best Practices:** Community Guidelines

### 🛠️ أدوات التطوير المقترحة
- **IDE:** Visual Studio Code / Android Studio
- **Database Browser:** DB Browser for SQLite
- **Version Control:** Git
- **Testing:** Flutter Test Framework
- **Performance:** Flutter DevTools

---

## ✅ الخلاصة

هذا الدليل يوفر نظرة شاملة على البنية التقنية لـ Smart Ledger. اتبع هذه الإرشادات لضمان:

- **كود عالي الجودة** ومنظم
- **أداء ممتاز** وموثوقية عالية
- **أمان قوي** وحماية البيانات
- **سهولة الصيانة** والتطوير المستقبلي

**نصائح للنجاح:**
- اتبع أفضل الممارسات دائماً
- اكتب اختبارات شاملة
- وثق الكود بوضوح
- راجع الكود بانتظام

**مرحباً بك في فريق تطوير Smart Ledger! 🎉**
