@echo off
REM ===================================================================
REM سكريبت بناء الإنتاج - Smart Ledger
REM يقوم ببناء التطبيق لجميع المنصات المدعومة
REM ===================================================================

echo.
echo ========================================
echo    Smart Ledger - Production Build
echo ========================================
echo.

REM التحقق من وجود Flutter
flutter --version >nul 2>&1
if %errorlevel% neq 0 (
    echo خطأ: Flutter غير مثبت أو غير موجود في PATH
    pause
    exit /b 1
)

REM التحقق من وجود ملف pubspec.yaml
if not exist "pubspec.yaml" (
    echo خطأ: يجب تشغيل هذا السكريبت من مجلد المشروع الرئيسي
    pause
    exit /b 1
)

echo [1/8] تنظيف المشروع...
flutter clean
if %errorlevel% neq 0 (
    echo خطأ في تنظيف المشروع
    pause
    exit /b 1
)

echo [2/8] تحديث التبعيات...
flutter pub get
if %errorlevel% neq 0 (
    echo خطأ في تحديث التبعيات
    pause
    exit /b 1
)

echo [3/8] تشغيل الاختبارات...
flutter test
if %errorlevel% neq 0 (
    echo تحذير: بعض الاختبارات فشلت
    set /p continue="هل تريد المتابعة؟ (y/n): "
    if /i not "%continue%"=="y" (
        echo تم إلغاء البناء
        pause
        exit /b 1
    )
)

echo [4/8] تحليل الكود...
flutter analyze
if %errorlevel% neq 0 (
    echo تحذير: توجد مشاكل في تحليل الكود
    set /p continue="هل تريد المتابعة؟ (y/n): "
    if /i not "%continue%"=="y" (
        echo تم إلغاء البناء
        pause
        exit /b 1
    )
)

REM إنشاء مجلد البناء
if not exist "build\production" mkdir "build\production"

echo [5/8] بناء تطبيق Windows...
flutter build windows --release
if %errorlevel% neq 0 (
    echo خطأ في بناء تطبيق Windows
    pause
    exit /b 1
)

REM نسخ ملفات Windows
echo نسخ ملفات Windows...
xcopy "build\windows\x64\runner\Release\*" "build\production\windows\" /E /I /Y
if %errorlevel% neq 0 (
    echo خطأ في نسخ ملفات Windows
    pause
    exit /b 1
)

echo [6/8] بناء تطبيق Android APK...
flutter build apk --release --split-per-abi
if %errorlevel% neq 0 (
    echo خطأ في بناء Android APK
    pause
    exit /b 1
)

REM نسخ ملفات Android
echo نسخ ملفات Android...
if not exist "build\production\android" mkdir "build\production\android"
copy "build\app\outputs\flutter-apk\*.apk" "build\production\android\"
if %errorlevel% neq 0 (
    echo خطأ في نسخ ملفات Android
    pause
    exit /b 1
)

echo [7/8] بناء Android App Bundle...
flutter build appbundle --release
if %errorlevel% neq 0 (
    echo خطأ في بناء Android App Bundle
    pause
    exit /b 1
)

REM نسخ App Bundle
copy "build\app\outputs\bundle\release\*.aab" "build\production\android\"
if %errorlevel% neq 0 (
    echo خطأ في نسخ App Bundle
    pause
    exit /b 1
)

echo [8/8] إنشاء ملفات التوزيع...

REM إنشاء ملف معلومات البناء
echo إنشاء ملف معلومات البناء...
(
echo Smart Ledger - Production Build
echo ================================
echo.
echo Build Date: %date% %time%
echo Flutter Version: 
flutter --version | findstr "Flutter"
echo.
echo Build Information:
echo - Windows: build\production\windows\
echo - Android APK: build\production\android\*.apk
echo - Android Bundle: build\production\android\*.aab
echo.
echo Installation Instructions:
echo.
echo Windows:
echo 1. Extract the windows folder
echo 2. Run smart_ledger.exe
echo.
echo Android:
echo 1. Install the APK file on your device
echo 2. Or upload the AAB file to Google Play Store
echo.
echo Support: <EMAIL>
echo Website: www.smartledger.sy
) > "build\production\BUILD_INFO.txt"

REM إنشاء سكريبت تثبيت Windows
echo إنشاء سكريبت تثبيت Windows...
(
echo @echo off
echo.
echo ========================================
echo    Smart Ledger - Windows Installer
echo ========================================
echo.
echo Installing Smart Ledger...
echo.
echo Please choose installation directory:
echo 1. Program Files ^(recommended^)
echo 2. Current user directory
echo 3. Custom directory
echo.
set /p choice="Enter your choice (1-3): "
echo.
if "%%choice%%"=="1" (
    set "install_dir=%%ProgramFiles%%\Smart Ledger"
) else if "%%choice%%"=="2" (
    set "install_dir=%%USERPROFILE%%\Smart Ledger"
) else (
    set /p install_dir="Enter custom directory: "
)
echo.
echo Installing to: %%install_dir%%
echo.
if not exist "%%install_dir%%" mkdir "%%install_dir%%"
xcopy "windows\*" "%%install_dir%%\" /E /I /Y
echo.
echo Creating desktop shortcut...
echo Set oWS = WScript.CreateObject^("WScript.Shell"^) > CreateShortcut.vbs
echo sLinkFile = "%%USERPROFILE%%\Desktop\Smart Ledger.lnk" >> CreateShortcut.vbs
echo Set oLink = oWS.CreateShortcut^(sLinkFile^) >> CreateShortcut.vbs
echo oLink.TargetPath = "%%install_dir%%\smart_ledger.exe" >> CreateShortcut.vbs
echo oLink.WorkingDirectory = "%%install_dir%%" >> CreateShortcut.vbs
echo oLink.Description = "Smart Ledger - Accounting Software" >> CreateShortcut.vbs
echo oLink.Save >> CreateShortcut.vbs
cscript CreateShortcut.vbs
del CreateShortcut.vbs
echo.
echo Installation completed successfully!
echo You can now run Smart Ledger from:
echo - Desktop shortcut
echo - Start menu
echo - %%install_dir%%\smart_ledger.exe
echo.
pause
) > "build\production\windows_installer.bat"

REM إنشاء ملف README
echo إنشاء ملف README...
(
echo # Smart Ledger - Production Release
echo.
echo ## نظرة عامة
echo.
echo Smart Ledger هو تطبيق محاسبة شامل ومتطور مصمم خصيصاً للشركات والمؤسسات في سوريا.
echo يعمل التطبيق بشكل كامل أوفلاين ويوفر جميع الأدوات المحاسبية التي تحتاجها لإدارة أعمالك.
echo.
echo ## الميزات الرئيسية
echo.
echo - 💰 إدارة شاملة للحسابات مع دليل حسابات متكامل
echo - 📄 نظام فواتير متقدم مع قوالب احترافية  
echo - 📊 تقارير مالية تفصيلية مع رسوم بيانية تفاعلية
echo - 🏪 إدارة المخازن مع تتبع المخزون
echo - 💳 نظام دفعات متطور مع جدولة تلقائية
echo - 🔒 أمان عالي مع تشفير قاعدة البيانات
echo - 🇸🇾 متوافق مع القوانين السورية والنظام الضريبي
echo.
echo ## متطلبات النظام
echo.
echo ### Windows
echo - Windows 10 أو أحدث
echo - 4 GB RAM على الأقل
echo - 500 MB مساحة تخزين
echo.
echo ### Android  
echo - Android 7.0 أو أحدث
echo - 2 GB RAM على الأقل
echo - 200 MB مساحة تخزين
echo.
echo ## التثبيت
echo.
echo ### Windows
echo 1. شغل ملف `windows_installer.bat`
echo 2. اتبع التعليمات على الشاشة
echo 3. شغل التطبيق من اختصار سطح المكتب
echo.
echo ### Android
echo 1. ثبت ملف APK المناسب لجهازك
echo 2. اسمح بالتثبيت من مصادر غير معروفة إذا لزم الأمر
echo 3. شغل التطبيق من قائمة التطبيقات
echo.
echo ## الدعم
echo.
echo - البريد الإلكتروني: <EMAIL>
echo - الموقع الإلكتروني: www.smartledger.sy
echo - الهاتف: +963-11-1234567
echo.
echo ## الترخيص
echo.
echo جميع الحقوق محفوظة © 2025 مجد محمد زياد يسير
echo.
) > "build\production\README.md"

REM ضغط الملفات
echo ضغط ملفات التوزيع...
if exist "build\production\smart_ledger_windows.zip" del "build\production\smart_ledger_windows.zip"
powershell -command "Compress-Archive -Path 'build\production\windows\*' -DestinationPath 'build\production\smart_ledger_windows.zip'"

echo.
echo ========================================
echo       تم البناء بنجاح!
echo ========================================
echo.
echo ملفات التوزيع متوفرة في:
echo - build\production\
echo.
echo الملفات المتوفرة:
echo - smart_ledger_windows.zip (تطبيق Windows)
echo - android\*.apk (تطبيقات Android)
echo - android\*.aab (Android App Bundle)
echo - windows_installer.bat (مثبت Windows)
echo - README.md (دليل التثبيت)
echo - BUILD_INFO.txt (معلومات البناء)
echo.
echo يمكنك الآن توزيع هذه الملفات للمستخدمين.
echo.
pause
