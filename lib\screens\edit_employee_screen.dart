/// شاشة تعديل الموظف
/// تعديل جميع معلومات الموظف مع التحقق من صحة البيانات
library;

import 'package:flutter/material.dart';
import '../constants/app_constants.dart';
import '../constants/revolutionary_design_colors.dart';
import '../models/hr_models.dart';
import '../services/employee_service.dart';
import '../services/logging_service.dart';
import '../widgets/loading_widget.dart';

class EditEmployeeScreen extends StatefulWidget {
  final Employee employee;

  const EditEmployeeScreen({super.key, required this.employee});

  @override
  State<EditEmployeeScreen> createState() => _EditEmployeeScreenState();
}

class _EditEmployeeScreenState extends State<EditEmployeeScreen> {
  final _formKey = GlobalKey<FormState>();
  final EmployeeService _employeeService = EmployeeService();

  // Controllers
  late TextEditingController _employeeNumberController;
  late TextEditingController _nationalIdController;
  late TextEditingController _firstNameController;
  late TextEditingController _lastNameController;
  late TextEditingController _phoneController;
  late TextEditingController _emailController;
  late TextEditingController _addressController;
  late TextEditingController _emergencyContactNameController;
  late TextEditingController _emergencyContactPhoneController;
  late TextEditingController _positionController;
  late TextEditingController _basicSalaryController;
  late TextEditingController _allowancesController;
  late TextEditingController _bankAccountNumberController;
  late TextEditingController _bankNameController;
  late TextEditingController _notesController;

  // Form data
  DateTime? _dateOfBirth;
  DateTime? _hireDate;
  String _gender = AppConstants.genderMale;
  String _maritalStatus = AppConstants.maritalStatusSingle;
  String _employmentType = AppConstants.employmentTypeFullTime;
  String _status = AppConstants.employeeStatusActive;
  int? _departmentId;

  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _initializeControllers();
    _loadEmployeeData();
  }

  void _initializeControllers() {
    _employeeNumberController = TextEditingController();
    _nationalIdController = TextEditingController();
    _firstNameController = TextEditingController();
    _lastNameController = TextEditingController();
    _phoneController = TextEditingController();
    _emailController = TextEditingController();
    _addressController = TextEditingController();
    _emergencyContactNameController = TextEditingController();
    _emergencyContactPhoneController = TextEditingController();
    _positionController = TextEditingController();
    _basicSalaryController = TextEditingController();
    _allowancesController = TextEditingController();
    _bankAccountNumberController = TextEditingController();
    _bankNameController = TextEditingController();
    _notesController = TextEditingController();
  }

  void _loadEmployeeData() {
    final employee = widget.employee;

    _employeeNumberController.text = employee.employeeNumber;
    _nationalIdController.text = employee.nationalId;
    _firstNameController.text = employee.firstName;
    _lastNameController.text = employee.lastName;
    _phoneController.text = employee.phone ?? '';
    _emailController.text = employee.email ?? '';
    _addressController.text = employee.address ?? '';
    _emergencyContactNameController.text = employee.emergencyContactName ?? '';
    _emergencyContactPhoneController.text =
        employee.emergencyContactPhone ?? '';
    _positionController.text = ''; // سيتم ربطه بجدول المناصب لاحقاً
    _basicSalaryController.text = employee.basicSalary.toString();
    _allowancesController.text = '0'; // سيتم إضافة البدلات لاحقاً
    _bankAccountNumberController.text = employee.bankAccountNumber ?? '';
    _bankNameController.text = employee.bankName ?? '';
    _notesController.text = employee.notes ?? '';

    _dateOfBirth = employee.dateOfBirth;
    _hireDate = employee.hireDate;
    _gender = employee.gender ?? AppConstants.genderMale;
    _maritalStatus = employee.maritalStatus ?? AppConstants.maritalStatusSingle;
    _employmentType = AppConstants.employmentTypeFullTime; // قيمة افتراضية
    _status = employee.status;
    _departmentId = employee.departmentId;
  }

  @override
  void dispose() {
    _employeeNumberController.dispose();
    _nationalIdController.dispose();
    _firstNameController.dispose();
    _lastNameController.dispose();
    _phoneController.dispose();
    _emailController.dispose();
    _addressController.dispose();
    _emergencyContactNameController.dispose();
    _emergencyContactPhoneController.dispose();
    _positionController.dispose();
    _basicSalaryController.dispose();
    _allowancesController.dispose();
    _bankAccountNumberController.dispose();
    _bankNameController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('تعديل الموظف: ${widget.employee.displayName}'),
        backgroundColor: RevolutionaryColors.damascusSky,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.save),
            onPressed: _saveEmployee,
            tooltip: 'حفظ التغييرات',
          ),
        ],
      ),
      body: _isLoading
          ? const LoadingWidget(message: 'جاري حفظ التغييرات...')
          : Form(
              key: _formKey,
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildBasicInfoSection(),
                    const SizedBox(height: 24),
                    _buildContactInfoSection(),
                    const SizedBox(height: 24),
                    _buildEmploymentInfoSection(),
                    const SizedBox(height: 24),
                    _buildFinancialInfoSection(),
                    const SizedBox(height: 24),
                    _buildAdditionalInfoSection(),
                    const SizedBox(height: 32),
                    _buildActionButtons(),
                  ],
                ),
              ),
            ),
    );
  }

  Widget _buildBasicInfoSection() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.person, color: RevolutionaryColors.damascusSky),
                const SizedBox(width: 8),
                const Text(
                  'المعلومات الأساسية',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _employeeNumberController,
              decoration: const InputDecoration(
                labelText: 'رقم الموظف *',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.badge),
              ),
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'رقم الموظف مطلوب';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _nationalIdController,
              decoration: const InputDecoration(
                labelText: 'الرقم الوطني *',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.credit_card),
              ),
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'الرقم الوطني مطلوب';
                }
                if (value.trim().length != 11) {
                  return 'الرقم الوطني يجب أن يكون 11 رقم';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: TextFormField(
                    controller: _firstNameController,
                    decoration: const InputDecoration(
                      labelText: 'الاسم الأول *',
                      border: OutlineInputBorder(),
                    ),
                    validator: (value) {
                      if (value == null || value.trim().isEmpty) {
                        return 'الاسم الأول مطلوب';
                      }
                      return null;
                    },
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: TextFormField(
                    controller: _lastNameController,
                    decoration: const InputDecoration(
                      labelText: 'الاسم الأخير *',
                      border: OutlineInputBorder(),
                    ),
                    validator: (value) {
                      if (value == null || value.trim().isEmpty) {
                        return 'الاسم الأخير مطلوب';
                      }
                      return null;
                    },
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            InkWell(
              onTap: () => _selectDate(context, true),
              child: InputDecorator(
                decoration: const InputDecoration(
                  labelText: 'تاريخ الميلاد',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.calendar_today),
                ),
                child: Text(
                  _dateOfBirth != null
                      ? '${_dateOfBirth!.day}/${_dateOfBirth!.month}/${_dateOfBirth!.year}'
                      : 'اختر تاريخ الميلاد',
                ),
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: DropdownButtonFormField<String>(
                    value: _gender,
                    decoration: const InputDecoration(
                      labelText: 'الجنس',
                      border: OutlineInputBorder(),
                    ),
                    items: const [
                      DropdownMenuItem(
                        value: AppConstants.genderMale,
                        child: Text('ذكر'),
                      ),
                      DropdownMenuItem(
                        value: AppConstants.genderFemale,
                        child: Text('أنثى'),
                      ),
                    ],
                    onChanged: (value) {
                      setState(() {
                        _gender = value!;
                      });
                    },
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: DropdownButtonFormField<String>(
                    value: _maritalStatus,
                    decoration: const InputDecoration(
                      labelText: 'الحالة الاجتماعية',
                      border: OutlineInputBorder(),
                    ),
                    items: const [
                      DropdownMenuItem(
                        value: AppConstants.maritalStatusSingle,
                        child: Text('أعزب'),
                      ),
                      DropdownMenuItem(
                        value: AppConstants.maritalStatusMarried,
                        child: Text('متزوج'),
                      ),
                      DropdownMenuItem(
                        value: AppConstants.maritalStatusDivorced,
                        child: Text('مطلق'),
                      ),
                      DropdownMenuItem(
                        value: AppConstants.maritalStatusWidowed,
                        child: Text('أرمل'),
                      ),
                    ],
                    onChanged: (value) {
                      setState(() {
                        _maritalStatus = value!;
                      });
                    },
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildContactInfoSection() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.contact_phone,
                  color: RevolutionaryColors.damascusSky,
                ),
                const SizedBox(width: 8),
                const Text(
                  'معلومات الاتصال',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _phoneController,
              decoration: const InputDecoration(
                labelText: 'رقم الهاتف',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.phone),
              ),
              keyboardType: TextInputType.phone,
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _emailController,
              decoration: const InputDecoration(
                labelText: 'البريد الإلكتروني',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.email),
              ),
              keyboardType: TextInputType.emailAddress,
              validator: (value) {
                if (value != null && value.isNotEmpty) {
                  if (!RegExp(
                    r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$',
                  ).hasMatch(value)) {
                    return 'البريد الإلكتروني غير صحيح';
                  }
                }
                return null;
              },
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _addressController,
              decoration: const InputDecoration(
                labelText: 'العنوان',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.location_on),
              ),
              maxLines: 2,
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _emergencyContactNameController,
              decoration: const InputDecoration(
                labelText: 'اسم جهة اتصال الطوارئ',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.emergency),
              ),
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _emergencyContactPhoneController,
              decoration: const InputDecoration(
                labelText: 'هاتف جهة اتصال الطوارئ',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.phone_in_talk),
              ),
              keyboardType: TextInputType.phone,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmploymentInfoSection() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.work, color: RevolutionaryColors.damascusSky),
                const SizedBox(width: 8),
                const Text(
                  'معلومات التوظيف',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            InkWell(
              onTap: () => _selectDate(context, false),
              child: InputDecorator(
                decoration: const InputDecoration(
                  labelText: 'تاريخ التوظيف *',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.work_history),
                ),
                child: Text(
                  _hireDate != null
                      ? '${_hireDate!.day}/${_hireDate!.month}/${_hireDate!.year}'
                      : 'اختر تاريخ التوظيف',
                ),
              ),
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _positionController,
              decoration: const InputDecoration(
                labelText: 'المنصب',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.business_center),
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: DropdownButtonFormField<String>(
                    value: _employmentType,
                    decoration: const InputDecoration(
                      labelText: 'نوع التوظيف',
                      border: OutlineInputBorder(),
                    ),
                    items: const [
                      DropdownMenuItem(
                        value: AppConstants.employmentTypeFullTime,
                        child: Text('دوام كامل'),
                      ),
                      DropdownMenuItem(
                        value: AppConstants.employmentTypePartTime,
                        child: Text('دوام جزئي'),
                      ),
                      DropdownMenuItem(
                        value: AppConstants.employmentTypeContract,
                        child: Text('عقد'),
                      ),
                      DropdownMenuItem(
                        value: AppConstants.employmentTypeInternship,
                        child: Text('تدريب'),
                      ),
                    ],
                    onChanged: (value) {
                      setState(() {
                        _employmentType = value!;
                      });
                    },
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: DropdownButtonFormField<String>(
                    value: _status,
                    decoration: const InputDecoration(
                      labelText: 'الحالة',
                      border: OutlineInputBorder(),
                    ),
                    items: const [
                      DropdownMenuItem(
                        value: AppConstants.employeeStatusActive,
                        child: Text('نشط'),
                      ),
                      DropdownMenuItem(
                        value: AppConstants.employeeStatusInactive,
                        child: Text('غير نشط'),
                      ),
                      DropdownMenuItem(
                        value: AppConstants.employeeStatusTerminated,
                        child: Text('منتهي الخدمة'),
                      ),
                      DropdownMenuItem(
                        value: AppConstants.employeeStatusSuspended,
                        child: Text('موقوف'),
                      ),
                    ],
                    onChanged: (value) {
                      setState(() {
                        _status = value!;
                      });
                    },
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFinancialInfoSection() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.account_balance_wallet,
                  color: RevolutionaryColors.damascusSky,
                ),
                const SizedBox(width: 8),
                const Text(
                  'المعلومات المالية',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: TextFormField(
                    controller: _basicSalaryController,
                    decoration: const InputDecoration(
                      labelText: 'الراتب الأساسي *',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.money),
                      suffixText: 'ل.س',
                    ),
                    keyboardType: TextInputType.number,
                    validator: (value) {
                      if (value == null || value.trim().isEmpty) {
                        return 'الراتب الأساسي مطلوب';
                      }
                      if (double.tryParse(value) == null) {
                        return 'يجب أن يكون رقم صحيح';
                      }
                      if (double.parse(value) < 0) {
                        return 'يجب أن يكون أكبر من الصفر';
                      }
                      return null;
                    },
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: TextFormField(
                    controller: _allowancesController,
                    decoration: const InputDecoration(
                      labelText: 'البدلات',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.add_circle),
                      suffixText: 'ل.س',
                    ),
                    keyboardType: TextInputType.number,
                    validator: (value) {
                      if (value != null && value.isNotEmpty) {
                        if (double.tryParse(value) == null) {
                          return 'يجب أن يكون رقم صحيح';
                        }
                        if (double.parse(value) < 0) {
                          return 'يجب أن يكون أكبر من الصفر';
                        }
                      }
                      return null;
                    },
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _bankAccountNumberController,
              decoration: const InputDecoration(
                labelText: 'رقم الحساب البنكي',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.account_balance),
              ),
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _bankNameController,
              decoration: const InputDecoration(
                labelText: 'اسم البنك',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.business),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAdditionalInfoSection() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.info, color: RevolutionaryColors.damascusSky),
                const SizedBox(width: 8),
                const Text(
                  'معلومات إضافية',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _notesController,
              decoration: const InputDecoration(
                labelText: 'ملاحظات',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.note),
              ),
              maxLines: 3,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButtons() {
    return Row(
      children: [
        Expanded(
          child: OutlinedButton(
            onPressed: () => Navigator.pop(context),
            style: OutlinedButton.styleFrom(
              padding: const EdgeInsets.all(16),
              side: BorderSide(color: RevolutionaryColors.errorCoral),
            ),
            child: Text(
              'إلغاء',
              style: TextStyle(color: RevolutionaryColors.errorCoral),
            ),
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: ElevatedButton(
            onPressed: _saveEmployee,
            style: ElevatedButton.styleFrom(
              backgroundColor: RevolutionaryColors.successGlow,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.all(16),
            ),
            child: const Text('حفظ التغييرات'),
          ),
        ),
      ],
    );
  }

  Future<void> _selectDate(BuildContext context, bool isBirthDate) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: isBirthDate
          ? (_dateOfBirth ??
                DateTime.now().subtract(const Duration(days: 365 * 25)))
          : (_hireDate ?? DateTime.now()),
      firstDate: DateTime(1950),
      lastDate: DateTime.now(),
    );

    if (picked != null) {
      setState(() {
        if (isBirthDate) {
          _dateOfBirth = picked;
        } else {
          _hireDate = picked;
        }
      });
    }
  }

  Future<void> _saveEmployee() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (_hireDate == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('تاريخ التوظيف مطلوب'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final updatedEmployee = widget.employee.copyWith(
        employeeNumber: _employeeNumberController.text.trim(),
        nationalId: _nationalIdController.text.trim(),
        firstName: _firstNameController.text.trim(),
        lastName: _lastNameController.text.trim(),
        fullName:
            '${_firstNameController.text.trim()} ${_lastNameController.text.trim()}',
        dateOfBirth: _dateOfBirth,
        gender: _gender,
        maritalStatus: _maritalStatus,
        phone: _phoneController.text.trim().isNotEmpty
            ? _phoneController.text.trim()
            : null,
        email: _emailController.text.trim().isNotEmpty
            ? _emailController.text.trim()
            : null,
        address: _addressController.text.trim().isNotEmpty
            ? _addressController.text.trim()
            : null,
        emergencyContactName:
            _emergencyContactNameController.text.trim().isNotEmpty
            ? _emergencyContactNameController.text.trim()
            : null,
        emergencyContactPhone:
            _emergencyContactPhoneController.text.trim().isNotEmpty
            ? _emergencyContactPhoneController.text.trim()
            : null,
        hireDate: _hireDate!,
        departmentId: _departmentId,
        status: _status,
        basicSalary: double.parse(_basicSalaryController.text.trim()),
        bankAccountNumber: _bankAccountNumberController.text.trim().isNotEmpty
            ? _bankAccountNumberController.text.trim()
            : null,
        bankName: _bankNameController.text.trim().isNotEmpty
            ? _bankNameController.text.trim()
            : null,
        notes: _notesController.text.trim().isNotEmpty
            ? _notesController.text.trim()
            : null,
        updatedAt: DateTime.now(),
      );

      await _employeeService.updateEmployee(updatedEmployee);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'تم تحديث بيانات الموظف "${updatedEmployee.fullName}" بنجاح',
            ),
            backgroundColor: RevolutionaryColors.successGlow,
          ),
        );
        Navigator.pop(context, true);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحديث بيانات الموظف: ${e.toString()}'),
            backgroundColor: RevolutionaryColors.errorCoral,
          ),
        );
      }

      LoggingService.error(
        'خطأ في تحديث بيانات الموظف',
        category: 'EditEmployeeScreen',
        data: {'employeeId': widget.employee.id, 'error': e.toString()},
      );
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }
}
