import 'package:flutter/material.dart';
import '../services/undo_redo_service.dart';
import '../constants/revolutionary_design_colors.dart';

/// Widget لعرض حالة Undo/Redo
class UndoRedoStatusWidget extends StatefulWidget {
  final bool showLabels;
  final bool showCounts;
  final MainAxisAlignment alignment;

  const UndoRedoStatusWidget({
    super.key,
    this.showLabels = true,
    this.showCounts = false,
    this.alignment = MainAxisAlignment.center,
  });

  @override
  State<UndoRedoStatusWidget> createState() => _UndoRedoStatusWidgetState();
}

class _UndoRedoStatusWidgetState extends State<UndoRedoStatusWidget> {
  late UndoRedoService _undoRedoService;

  @override
  void initState() {
    super.initState();
    _undoRedoService = UndoRedoService();
    _undoRedoService.addListener(_onUndoRedoChanged);
  }

  @override
  void dispose() {
    _undoRedoService.removeListener(_onUndoRedoChanged);
    super.dispose();
  }

  void _onUndoRedoChanged() {
    if (mounted) {
      setState(() {});
    }
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: widget.alignment,
      mainAxisSize: MainAxisSize.min,
      children: [
        _buildUndoButton(),
        const SizedBox(width: 8),
        _buildRedoButton(),
        if (widget.showCounts) ...[
          const SizedBox(width: 16),
          _buildCountsDisplay(),
        ],
      ],
    );
  }

  Widget _buildUndoButton() {
    final canUndo = _undoRedoService.canUndo;
    final lastAction = _undoRedoService.lastUndoAction;

    return Tooltip(
      message: canUndo 
          ? 'تراجع: ${lastAction?.description ?? "غير محدد"} (Ctrl+Z)'
          : 'لا توجد عمليات للتراجع',
      child: InkWell(
        onTap: canUndo ? _handleUndo : null,
        borderRadius: BorderRadius.circular(8),
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          decoration: BoxDecoration(
            color: canUndo 
                ? RevolutionaryColors.damascusSky.withValues(alpha: 0.1)
                : Colors.grey.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: canUndo 
                  ? RevolutionaryColors.damascusSky.withValues(alpha: 0.3)
                  : Colors.grey.withValues(alpha: 0.3),
            ),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                Icons.undo,
                size: 16,
                color: canUndo ? RevolutionaryColors.damascusSky : Colors.grey,
              ),
              if (widget.showLabels) ...[
                const SizedBox(width: 4),
                Text(
                  'تراجع',
                  style: TextStyle(
                    fontSize: 12,
                    color: canUndo ? RevolutionaryColors.damascusSky : Colors.grey,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
              if (widget.showCounts && _undoRedoService.undoCount > 0) ...[
                const SizedBox(width: 4),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                  decoration: BoxDecoration(
                    color: RevolutionaryColors.damascusSky,
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: Text(
                    '${_undoRedoService.undoCount}',
                    style: const TextStyle(
                      fontSize: 10,
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildRedoButton() {
    final canRedo = _undoRedoService.canRedo;
    final lastAction = _undoRedoService.lastRedoAction;

    return Tooltip(
      message: canRedo 
          ? 'إعادة: ${lastAction?.description ?? "غير محدد"} (Ctrl+Y)'
          : 'لا توجد عمليات للإعادة',
      child: InkWell(
        onTap: canRedo ? _handleRedo : null,
        borderRadius: BorderRadius.circular(8),
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          decoration: BoxDecoration(
            color: canRedo 
                ? RevolutionaryColors.syrianGold.withValues(alpha: 0.1)
                : Colors.grey.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: canRedo 
                  ? RevolutionaryColors.syrianGold.withValues(alpha: 0.3)
                  : Colors.grey.withValues(alpha: 0.3),
            ),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                Icons.redo,
                size: 16,
                color: canRedo ? RevolutionaryColors.syrianGold : Colors.grey,
              ),
              if (widget.showLabels) ...[
                const SizedBox(width: 4),
                Text(
                  'إعادة',
                  style: TextStyle(
                    fontSize: 12,
                    color: canRedo ? RevolutionaryColors.syrianGold : Colors.grey,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
              if (widget.showCounts && _undoRedoService.redoCount > 0) ...[
                const SizedBox(width: 4),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                  decoration: BoxDecoration(
                    color: RevolutionaryColors.syrianGold,
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: Text(
                    '${_undoRedoService.redoCount}',
                    style: const TextStyle(
                      fontSize: 10,
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCountsDisplay() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: Colors.grey.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(6),
      ),
      child: Text(
        'العمليات: ${_undoRedoService.undoCount + _undoRedoService.redoCount}',
        style: const TextStyle(
          fontSize: 10,
          color: Colors.grey,
        ),
      ),
    );
  }

  void _handleUndo() async {
    final success = await _undoRedoService.undo();
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(success ? 'تم التراجع بنجاح' : 'فشل في التراجع'),
          duration: const Duration(seconds: 1),
          behavior: SnackBarBehavior.floating,
        ),
      );
    }
  }

  void _handleRedo() async {
    final success = await _undoRedoService.redo();
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(success ? 'تم الإعادة بنجاح' : 'فشل في الإعادة'),
          duration: const Duration(seconds: 1),
          behavior: SnackBarBehavior.floating,
        ),
      );
    }
  }
}

/// Widget مبسط لعرض أزرار Undo/Redo فقط
class SimpleUndoRedoButtons extends StatelessWidget {
  final VoidCallback? onUndo;
  final VoidCallback? onRedo;
  final bool canUndo;
  final bool canRedo;

  const SimpleUndoRedoButtons({
    super.key,
    this.onUndo,
    this.onRedo,
    this.canUndo = false,
    this.canRedo = false,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        IconButton(
          onPressed: canUndo ? onUndo : null,
          icon: const Icon(Icons.undo),
          tooltip: 'تراجع (Ctrl+Z)',
          iconSize: 20,
        ),
        IconButton(
          onPressed: canRedo ? onRedo : null,
          icon: const Icon(Icons.redo),
          tooltip: 'إعادة (Ctrl+Y)',
          iconSize: 20,
        ),
      ],
    );
  }
}

/// Widget لعرض تاريخ العمليات
class UndoRedoHistoryWidget extends StatefulWidget {
  const UndoRedoHistoryWidget({super.key});

  @override
  State<UndoRedoHistoryWidget> createState() => _UndoRedoHistoryWidgetState();
}

class _UndoRedoHistoryWidgetState extends State<UndoRedoHistoryWidget> {
  late UndoRedoService _undoRedoService;

  @override
  void initState() {
    super.initState();
    _undoRedoService = UndoRedoService();
    _undoRedoService.addListener(_onHistoryChanged);
  }

  @override
  void dispose() {
    _undoRedoService.removeListener(_onHistoryChanged);
    super.dispose();
  }

  void _onHistoryChanged() {
    if (mounted) {
      setState(() {});
    }
  }

  @override
  Widget build(BuildContext context) {
    final undoHistory = _undoRedoService.getUndoHistory();
    final redoHistory = _undoRedoService.getRedoHistory();

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'تاريخ العمليات',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                  child: _buildHistoryColumn(
                    'التراجع',
                    undoHistory,
                    Icons.undo,
                    RevolutionaryColors.damascusSky,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildHistoryColumn(
                    'الإعادة',
                    redoHistory,
                    Icons.redo,
                    RevolutionaryColors.syrianGold,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHistoryColumn(
    String title,
    List<String> history,
    IconData icon,
    Color color,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(icon, size: 16, color: color),
            const SizedBox(width: 4),
            Text(
              title,
              style: TextStyle(
                fontWeight: FontWeight.w500,
                color: color,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        if (history.isEmpty)
          Text(
            'لا توجد عمليات',
            style: TextStyle(
              color: Colors.grey[600],
              fontSize: 12,
            ),
          )
        else
          ...history.take(5).map((operation) => Padding(
                padding: const EdgeInsets.only(bottom: 4),
                child: Text(
                  operation,
                  style: const TextStyle(fontSize: 12),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              )),
        if (history.length > 5)
          Text(
            '... و ${history.length - 5} عمليات أخرى',
            style: TextStyle(
              color: Colors.grey[600],
              fontSize: 10,
            ),
          ),
      ],
    );
  }
}
