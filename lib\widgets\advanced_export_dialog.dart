import 'package:flutter/material.dart';
import '../constants/revolutionary_design_colors.dart';
import '../services/advanced_export_service.dart';
import '../services/logging_service.dart';

/// حوار خيارات التصدير المتقدمة
class AdvancedExportDialog extends StatefulWidget {
  final String reportType;
  final dynamic reportData;
  final Map<String, dynamic> filters;
  final VoidCallback? onExportComplete;

  const AdvancedExportDialog({
    super.key,
    required this.reportType,
    required this.reportData,
    required this.filters,
    this.onExportComplete,
  });

  @override
  State<AdvancedExportDialog> createState() => _AdvancedExportDialogState();
}

class _AdvancedExportDialogState extends State<AdvancedExportDialog> {
  String _selectedFormat = 'PDF';
  bool _includeCharts = true;
  bool _includeStatistics = true;
  bool _includeWatermark = false;
  Color _headerColor = RevolutionaryColors.damascusSky;
  String _customTitle = '';
  bool _isExporting = false;

  final List<String> _formats = ['PDF', 'Excel'];
  final List<Color> _colorOptions = [
    RevolutionaryColors.damascusSky,
    RevolutionaryColors.oliveBranch,
    RevolutionaryColors.successGlow,
    RevolutionaryColors.warningAmber,
    RevolutionaryColors.errorCoral,
    RevolutionaryColors.infoTurquoise,
  ];

  @override
  void initState() {
    super.initState();
    _customTitle = _getDefaultTitle();
  }

  String _getDefaultTitle() {
    switch (widget.reportType) {
      case 'trial_balance':
        return 'ميزان المراجعة';
      case 'profit_loss':
        return 'قائمة الدخل';
      case 'balance_sheet':
        return 'الميزانية العمومية';
      case 'inventory_report':
        return 'تقرير المخزون';
      case 'sales_analysis':
        return 'تحليل المبيعات';
      case 'purchase_analysis':
        return 'تحليل المشتريات';
      default:
        return 'تقرير';
    }
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Row(
        children: [
          Icon(Icons.file_download, color: RevolutionaryColors.damascusSky),
          const SizedBox(width: 8),
          const Text('خيارات التصدير المتقدمة'),
        ],
      ),
      content: SizedBox(
        width: 400,
        child: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // اختيار التنسيق
              _buildFormatSelection(),
              const SizedBox(height: 16),

              // عنوان مخصص
              _buildCustomTitle(),
              const SizedBox(height: 16),

              // لون الرأس
              _buildColorSelection(),
              const SizedBox(height: 16),

              // خيارات المحتوى
              _buildContentOptions(),
              const SizedBox(height: 16),

              // خيارات إضافية
              _buildAdditionalOptions(),
            ],
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: _isExporting ? null : () => Navigator.of(context).pop(),
          child: const Text('إلغاء'),
        ),
        ElevatedButton(
          onPressed: _isExporting ? null : _performExport,
          style: ElevatedButton.styleFrom(
            backgroundColor: RevolutionaryColors.damascusSky,
            foregroundColor: Colors.white,
          ),
          child: _isExporting
              ? const SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  ),
                )
              : Text('تصدير $_selectedFormat'),
        ),
      ],
    );
  }

  Widget _buildFormatSelection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'تنسيق التصدير',
          style: Theme.of(
            context,
          ).textTheme.titleSmall?.copyWith(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 8),
        Row(
          children: _formats.map((format) {
            return Expanded(
              child: RadioListTile<String>(
                title: Text(format),
                value: format,
                groupValue: _selectedFormat,
                onChanged: (value) {
                  setState(() {
                    _selectedFormat = value!;
                  });
                },
                dense: true,
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildCustomTitle() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'عنوان التقرير',
          style: Theme.of(
            context,
          ).textTheme.titleSmall?.copyWith(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 8),
        TextField(
          controller: TextEditingController(text: _customTitle),
          onChanged: (value) => _customTitle = value,
          decoration: const InputDecoration(
            border: OutlineInputBorder(),
            hintText: 'أدخل عنوان مخصص للتقرير',
            isDense: true,
          ),
        ),
      ],
    );
  }

  Widget _buildColorSelection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'لون الرأس',
          style: Theme.of(
            context,
          ).textTheme.titleSmall?.copyWith(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          children: _colorOptions.map((color) {
            final isSelected = _headerColor == color;
            return GestureDetector(
              onTap: () {
                setState(() {
                  _headerColor = color;
                });
              },
              child: Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: color,
                  shape: BoxShape.circle,
                  border: Border.all(
                    color: isSelected ? Colors.black : Colors.grey.shade300,
                    width: isSelected ? 3 : 1,
                  ),
                ),
                child: isSelected
                    ? const Icon(Icons.check, color: Colors.white, size: 20)
                    : null,
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildContentOptions() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'محتوى التقرير',
          style: Theme.of(
            context,
          ).textTheme.titleSmall?.copyWith(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 8),
        CheckboxListTile(
          title: const Text('تضمين الرسوم البيانية'),
          subtitle: const Text('إضافة الرسوم البيانية إلى التقرير'),
          value: _includeCharts,
          onChanged: (value) {
            setState(() {
              _includeCharts = value ?? false;
            });
          },
          dense: true,
        ),
        CheckboxListTile(
          title: const Text('تضمين الإحصائيات'),
          subtitle: const Text('إضافة الإحصائيات والملخصات'),
          value: _includeStatistics,
          onChanged: (value) {
            setState(() {
              _includeStatistics = value ?? false;
            });
          },
          dense: true,
        ),
      ],
    );
  }

  Widget _buildAdditionalOptions() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'خيارات إضافية',
          style: Theme.of(
            context,
          ).textTheme.titleSmall?.copyWith(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 8),
        if (_selectedFormat == 'PDF')
          CheckboxListTile(
            title: const Text('إضافة علامة مائية'),
            subtitle: const Text('إضافة علامة مائية للحماية'),
            value: _includeWatermark,
            onChanged: (value) {
              setState(() {
                _includeWatermark = value ?? false;
              });
            },
            dense: true,
          ),
      ],
    );
  }

  Future<void> _performExport() async {
    setState(() {
      _isExporting = true;
    });

    try {
      final customization = ExportCustomization(
        title: _customTitle.isNotEmpty ? _customTitle : _getDefaultTitle(),
        headerColor: _headerColor,
        includeCharts: _includeCharts,
        includeStatistics: _includeStatistics,
        includeWatermark: _includeWatermark,
      );

      String? filePath;

      if (_selectedFormat == 'PDF') {
        filePath = await AdvancedExportService.exportAdvancedPDF(
          reportType: widget.reportType,
          reportData: widget.reportData,
          filters: widget.filters,
          customization: customization,
          includeWatermark: _includeWatermark,
        );
      } else {
        filePath = await AdvancedExportService.exportAdvancedExcel(
          reportType: widget.reportType,
          reportData: widget.reportData,
          filters: widget.filters,
          customization: customization,
          includeCharts: _includeCharts,
        );
      }

      if (mounted) {
        Navigator.of(context).pop();

        if (filePath != null) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('تم تصدير التقرير بنجاح: $_selectedFormat'),
              backgroundColor: RevolutionaryColors.successGlow,
              action: SnackBarAction(
                label: 'مشاركة',
                textColor: Colors.white,
                onPressed: () => _shareFile(filePath!),
              ),
            ),
          );

          widget.onExportComplete?.call();
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('فشل في تصدير التقرير: $_selectedFormat'),
              backgroundColor: RevolutionaryColors.errorCoral,
            ),
          );
        }
      }
    } catch (e) {
      LoggingService.error(
        'خطأ في عملية التصدير',
        category: 'ExportDialog',
        data: {'error': e.toString()},
      );

      if (mounted) {
        Navigator.of(context).pop();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في التصدير: ${e.toString()}'),
            backgroundColor: RevolutionaryColors.errorCoral,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isExporting = false;
        });
      }
    }
  }

  Future<void> _shareFile(String filePath) async {
    try {
      // سيتم تنفيذ مشاركة الملف هنا
      LoggingService.info(
        'مشاركة ملف التصدير',
        category: 'ExportDialog',
        data: {'filePath': filePath},
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في مشاركة الملف',
        category: 'ExportDialog',
        data: {'error': e.toString()},
      );
    }
  }
}
