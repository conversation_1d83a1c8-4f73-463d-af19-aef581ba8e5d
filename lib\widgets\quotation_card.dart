/// بطاقة عرض السعر
/// تعرض معلومات عرض السعر مع الإجراءات المتاحة
library;

import 'package:flutter/material.dart';
import '../models/quotation.dart';
import '../constants/revolutionary_design_colors.dart';
import '../constants/app_constants.dart';

class QuotationCard extends StatelessWidget {
  final Quotation quotation;
  final VoidCallback? onTap;
  final VoidCallback? onConvertToInvoice;
  final Function(QuotationStatus)? onUpdateStatus;
  final VoidCallback? onDelete;

  const QuotationCard({
    super.key,
    required this.quotation,
    this.onTap,
    this.onConvertToInvoice,
    this.onUpdateStatus,
    this.onDelete,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildHeader(),
              const SizedBox(height: 12),
              _buildDetails(),
              const SizedBox(height: 12),
              _buildFooter(context),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      children: [
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                quotation.quotationNumber,
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: RevolutionaryColors.damascusSky,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                _formatDate(quotation.quotationDate),
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 14,
                ),
              ),
            ],
          ),
        ),
        _buildStatusChip(),
      ],
    );
  }

  Widget _buildStatusChip() {
    Color backgroundColor;
    Color textColor;
    String displayText = quotation.displayStatus;

    switch (quotation.status) {
      case QuotationStatus.draft:
        backgroundColor = Colors.grey[100]!;
        textColor = Colors.grey[700]!;
        break;
      case QuotationStatus.sent:
        backgroundColor = Colors.blue[100]!;
        textColor = Colors.blue[700]!;
        break;
      case QuotationStatus.accepted:
        backgroundColor = Colors.green[100]!;
        textColor = Colors.green[700]!;
        break;
      case QuotationStatus.rejected:
        backgroundColor = Colors.red[100]!;
        textColor = Colors.red[700]!;
        break;
      case QuotationStatus.expired:
        backgroundColor = Colors.orange[100]!;
        textColor = Colors.orange[700]!;
        break;
      case QuotationStatus.converted:
        backgroundColor = Colors.purple[100]!;
        textColor = Colors.purple[700]!;
        break;
    }

    // إذا كان منتهي الصلاحية
    if (quotation.isExpired && quotation.status != QuotationStatus.converted) {
      backgroundColor = Colors.orange[100]!;
      textColor = Colors.orange[700]!;
      displayText = QuotationStatus.expired.displayName;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Text(
        displayText,
        style: TextStyle(
          color: textColor,
          fontSize: 12,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  Widget _buildDetails() {
    return Column(
      children: [
        if (quotation.customerName != null || quotation.supplierName != null)
          Row(
            children: [
              Icon(
                quotation.customerName != null ? Icons.person : Icons.business,
                size: 16,
                color: Colors.grey[600],
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  quotation.customerName ?? quotation.supplierName ?? '',
                  style: const TextStyle(fontSize: 14),
                ),
              ),
            ],
          ),
        const SizedBox(height: 8),
        Row(
          children: [
            Icon(
              Icons.attach_money,
              size: 16,
              color: Colors.grey[600],
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                '${quotation.totalAmount.toStringAsFixed(2)} ${_getCurrencySymbol()}',
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: RevolutionaryColors.damascusSky,
                ),
              ),
            ),
          ],
        ),
        if (quotation.validUntil != null) ...[
          const SizedBox(height: 8),
          Row(
            children: [
              Icon(
                Icons.schedule,
                size: 16,
                color: quotation.isExpired ? Colors.red : Colors.grey[600],
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  'صالح حتى: ${_formatDate(quotation.validUntil!)}',
                  style: TextStyle(
                    fontSize: 14,
                    color: quotation.isExpired ? Colors.red : Colors.grey[600],
                  ),
                ),
              ),
            ],
          ),
        ],
        if (quotation.isConverted) ...[
          const SizedBox(height: 8),
          Row(
            children: [
              const Icon(
                Icons.receipt,
                size: 16,
                color: RevolutionaryColors.successGlow,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  'محول إلى فاتورة رقم: ${quotation.convertedToInvoice}',
                  style: const TextStyle(
                    fontSize: 14,
                    color: RevolutionaryColors.successGlow,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
        ],
      ],
    );
  }

  Widget _buildFooter(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        if (quotation.canBeConverted) ...[
          TextButton.icon(
            onPressed: onConvertToInvoice,
            icon: const Icon(Icons.receipt_long, size: 16),
            label: const Text('تحويل إلى فاتورة'),
            style: TextButton.styleFrom(
              foregroundColor: RevolutionaryColors.successGlow,
            ),
          ),
          const SizedBox(width: 8),
        ],
        PopupMenuButton<String>(
          icon: const Icon(Icons.more_vert),
          onSelected: (value) => _handleMenuAction(context, value),
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'view',
              child: Row(
                children: [
                  Icon(Icons.visibility, size: 16),
                  SizedBox(width: 8),
                  Text('عرض التفاصيل'),
                ],
              ),
            ),
            if (!quotation.isConverted) ...[
              const PopupMenuItem(
                value: 'edit_status',
                child: Row(
                  children: [
                    Icon(Icons.edit, size: 16),
                    SizedBox(width: 8),
                    Text('تغيير الحالة'),
                  ],
                ),
              ),
              if (quotation.canBeConverted)
                const PopupMenuItem(
                  value: 'convert',
                  child: Row(
                    children: [
                      Icon(Icons.receipt_long, size: 16),
                      SizedBox(width: 8),
                      Text('تحويل إلى فاتورة'),
                    ],
                  ),
                ),
              const PopupMenuItem(
                value: 'delete',
                child: Row(
                  children: [
                    Icon(Icons.delete, size: 16, color: Colors.red),
                    SizedBox(width: 8),
                    Text('حذف', style: TextStyle(color: Colors.red)),
                  ],
                ),
              ),
            ],
          ],
        ),
      ],
    );
  }

  void _handleMenuAction(BuildContext context, String action) {
    switch (action) {
      case 'view':
        onTap?.call();
        break;
      case 'edit_status':
        _showStatusDialog(context);
        break;
      case 'convert':
        onConvertToInvoice?.call();
        break;
      case 'delete':
        onDelete?.call();
        break;
    }
  }

  void _showStatusDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تغيير حالة عرض السعر'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: QuotationStatus.values
              .where((status) => status != QuotationStatus.converted)
              .map((status) {
            return ListTile(
              title: Text(status.displayName),
              leading: Radio<QuotationStatus>(
                value: status,
                groupValue: quotation.status,
                onChanged: (value) {
                  Navigator.of(context).pop();
                  if (value != null && value != quotation.status) {
                    onUpdateStatus?.call(value);
                  }
                },
              ),
            );
          }).toList(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  String _getCurrencySymbol() {
    // يمكن تحسين هذا لاحقاً للحصول على رمز العملة من قاعدة البيانات
    return AppConstants.currencySymbols['SYP'] ?? 'ل.س';
  }
}
