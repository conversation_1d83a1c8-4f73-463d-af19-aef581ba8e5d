import 'package:flutter/material.dart';
import '../constants/revolutionary_design_colors.dart';
import '../constants/app_constants.dart';
import '../models/account.dart';
import '../models/journal_entry.dart';
import '../services/account_service.dart';
import '../database/database_helper.dart';
import '../widgets/loading_widget.dart';

class AccountDetailsScreen extends StatefulWidget {
  final Account account;

  const AccountDetailsScreen({super.key, required this.account});

  @override
  State<AccountDetailsScreen> createState() => _AccountDetailsScreenState();
}

class _AccountDetailsScreenState extends State<AccountDetailsScreen>
    with TickerProviderStateMixin {
  final AccountService _accountService = AccountService();

  late TabController _tabController;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  List<JournalEntryDetail> _accountTransactions = [];
  List<Account> _childAccounts = [];
  bool _isLoading = true;
  DateTime? _startDate;
  DateTime? _endDate;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    _loadAccountData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  Future<void> _loadAccountData() async {
    setState(() => _isLoading = true);

    try {
      // تحميل المعاملات
      await _loadAccountTransactions();

      // تحميل الحسابات الفرعية
      if (widget.account.id != null) {
        final childAccounts = await _accountService.getChildAccounts(
          widget.account.id!,
        );
        setState(() {
          _childAccounts = childAccounts;
        });
      }

      _animationController.forward();
    } catch (e) {
      _showErrorSnackBar('حدث خطأ في تحميل بيانات الحساب: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _loadAccountTransactions() async {
    if (widget.account.id == null) return;

    try {
      // استخدام DatabaseHelper مباشرة
      final databaseHelper = DatabaseHelper();
      final db = await databaseHelper.database;

      String query =
          '''
        SELECT jed.*, je.entry_number, je.entry_date, je.description as entry_description, je.is_posted
        FROM ${AppConstants.journalEntryDetailsTable} jed
        INNER JOIN ${AppConstants.journalEntriesTable} je ON jed.journal_entry_id = je.id
        WHERE jed.account_id = ?
      ''';

      List<dynamic> args = [widget.account.id!];

      if (_startDate != null && _endDate != null) {
        query += ' AND je.entry_date BETWEEN ? AND ?';
        args.addAll([
          _startDate!.toIso8601String().split('T')[0],
          _endDate!.toIso8601String().split('T')[0],
        ]);
      }

      query += ' ORDER BY je.entry_date DESC, je.id DESC';

      final result = await db.rawQuery(query, args);

      final transactions = result.map((row) {
        final detail = JournalEntryDetail.fromMap(row);
        return detail;
      }).toList();

      setState(() {
        _accountTransactions = transactions;
      });
    } catch (e) {
      _showErrorSnackBar('حدث خطأ في تحميل المعاملات: $e');
    }
  }

  void _showErrorSnackBar(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: RevolutionaryColors.errorCoral,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('تفاصيل الحساب: ${widget.account.name}'),
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'معلومات الحساب', icon: Icon(Icons.info)),
            Tab(text: 'المعاملات', icon: Icon(Icons.receipt_long)),
            Tab(text: 'الحسابات الفرعية', icon: Icon(Icons.account_tree)),
          ],
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadAccountData,
          ),
        ],
      ),
      body: _isLoading
          ? const LoadingWidget()
          : TabBarView(
              controller: _tabController,
              children: [
                _buildAccountInfoTab(),
                _buildTransactionsTab(),
                _buildChildAccountsTab(),
              ],
            ),
    );
  }

  Widget _buildAccountInfoTab() {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildAccountInfoCard(),
            const SizedBox(height: 16),
            _buildAccountStatsCard(),
          ],
        ),
      ),
    );
  }

  Widget _buildAccountInfoCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: _getAccountTypeColor().withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    widget.account.accountIcon,
                    style: const TextStyle(fontSize: 24),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        widget.account.name,
                        style: Theme.of(context).textTheme.headlineSmall
                            ?.copyWith(fontWeight: FontWeight.bold),
                      ),
                      Text(
                        'كود الحساب: ${widget.account.code}',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: RevolutionaryColors.textSecondary,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildInfoRow('نوع الحساب', widget.account.accountTypeArabic),
            _buildInfoRow('المستوى', widget.account.level.toString()),
            _buildInfoRow(
              'الحالة',
              widget.account.isActive ? 'نشط' : 'غير نشط',
            ),
            _buildInfoRow(
              'تاريخ الإنشاء',
              _formatDate(widget.account.createdAt),
            ),
            _buildInfoRow('آخر تحديث', _formatDate(widget.account.updatedAt)),
            if (widget.account.description != null &&
                widget.account.description!.isNotEmpty)
              _buildInfoRow('الوصف', widget.account.description!),
          ],
        ),
      ),
    );
  }

  Widget _buildAccountStatsCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'إحصائيات الحساب',
              style: Theme.of(
                context,
              ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildStatCard(
                    'الرصيد الحالي',
                    '${widget.account.displayBalance} ل.س',
                    widget.account.balance >= 0
                        ? RevolutionaryColors.successGlow
                        : RevolutionaryColors.errorCoral,
                    Icons.account_balance_wallet,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildStatCard(
                    'عدد المعاملات',
                    _accountTransactions.length.toString(),
                    RevolutionaryColors.damascusSky,
                    Icons.receipt_long,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: _buildStatCard(
                    'الحسابات الفرعية',
                    _childAccounts.length.toString(),
                    RevolutionaryColors.infoTurquoise,
                    Icons.account_tree,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildStatCard(
                    'نوع الرصيد',
                    widget.account.isDebitAccount ? 'مدين' : 'دائن',
                    RevolutionaryColors.damascusSky,
                    Icons.trending_up,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatCard(
    String title,
    String value,
    Color color,
    IconData icon,
  ) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            title,
            style: TextStyle(
              fontSize: 12,
              color: RevolutionaryColors.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              '$label:',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w500,
                color: RevolutionaryColors.textSecondary,
              ),
            ),
          ),
          Expanded(
            child: Text(value, style: Theme.of(context).textTheme.bodyMedium),
          ),
        ],
      ),
    );
  }

  Color _getAccountTypeColor() {
    switch (widget.account.type) {
      case AppConstants.accountTypeAsset:
        return RevolutionaryColors.successGlow;
      case AppConstants.accountTypeLiability:
        return RevolutionaryColors.warningAmber;
      case AppConstants.accountTypeRevenue:
        return RevolutionaryColors.damascusSky;
      case AppConstants.accountTypeExpense:
        return RevolutionaryColors.errorCoral;
      case AppConstants.accountTypePurchase:
        return RevolutionaryColors.infoTurquoise;
      case AppConstants.accountTypeSale:
        return RevolutionaryColors.damascusSky;
      case AppConstants.accountTypeInventory:
        return Colors.purple;
      default:
        return RevolutionaryColors.textSecondary;
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  Widget _buildTransactionsTab() {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: Column(
        children: [
          _buildDateFilter(),
          Expanded(
            child: _accountTransactions.isEmpty
                ? _buildEmptyTransactions()
                : _buildTransactionsList(),
          ),
        ],
      ),
    );
  }

  Widget _buildDateFilter() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: RevolutionaryColors.backgroundCard,
        boxShadow: [
          BoxShadow(
            color: RevolutionaryColors.shadowMedium,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Expanded(
            child: InkWell(
              onTap: () => _selectStartDate(),
              child: Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 8,
                ),
                decoration: BoxDecoration(
                  border: Border.all(color: RevolutionaryColors.borderMedium),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    const Icon(Icons.calendar_today, size: 16),
                    const SizedBox(width: 8),
                    Text(
                      _startDate != null
                          ? 'من: ${_formatDate(_startDate!)}'
                          : 'تاريخ البداية',
                      style: TextStyle(
                        color: _startDate != null
                            ? RevolutionaryColors.textPrimary
                            : RevolutionaryColors.textSecondary,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: InkWell(
              onTap: () => _selectEndDate(),
              child: Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 8,
                ),
                decoration: BoxDecoration(
                  border: Border.all(color: RevolutionaryColors.borderMedium),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    const Icon(Icons.calendar_today, size: 16),
                    const SizedBox(width: 8),
                    Text(
                      _endDate != null
                          ? 'إلى: ${_formatDate(_endDate!)}'
                          : 'تاريخ النهاية',
                      style: TextStyle(
                        color: _endDate != null
                            ? RevolutionaryColors.textPrimary
                            : RevolutionaryColors.textSecondary,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          const SizedBox(width: 8),
          IconButton(
            onPressed: () {
              setState(() {
                _startDate = null;
                _endDate = null;
              });
              _loadAccountTransactions();
            },
            icon: const Icon(Icons.clear),
            tooltip: 'مسح الفلتر',
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyTransactions() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.receipt_long,
            size: 64,
            color: RevolutionaryColors.textSecondary,
          ),
          const SizedBox(height: 16),
          Text(
            'لا توجد معاملات لهذا الحساب',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: RevolutionaryColors.textSecondary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTransactionsList() {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _accountTransactions.length,
      itemBuilder: (context, index) {
        final transaction = _accountTransactions[index];
        return _buildTransactionCard(transaction);
      },
    );
  }

  Widget _buildTransactionCard(JournalEntryDetail transaction) {
    final isDebit = transaction.debitAmount > 0;
    final amount = isDebit ? transaction.debitAmount : transaction.creditAmount;

    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color:
                        (isDebit
                                ? RevolutionaryColors.successGlow
                                : RevolutionaryColors.errorCoral)
                            .withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    isDebit ? Icons.add : Icons.remove,
                    color: isDebit
                        ? RevolutionaryColors.successGlow
                        : RevolutionaryColors.errorCoral,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        isDebit ? 'مدين' : 'دائن',
                        style: Theme.of(context).textTheme.titleMedium
                            ?.copyWith(
                              fontWeight: FontWeight.bold,
                              color: isDebit
                                  ? RevolutionaryColors.successGlow
                                  : RevolutionaryColors.errorCoral,
                            ),
                      ),
                      if (transaction.description != null &&
                          transaction.description!.isNotEmpty)
                        Text(
                          transaction.description!,
                          style: Theme.of(context).textTheme.bodySmall
                              ?.copyWith(
                                color: RevolutionaryColors.textSecondary,
                              ),
                        ),
                    ],
                  ),
                ),
                Text(
                  '${amount.toStringAsFixed(2)} ل.س',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: isDebit
                        ? RevolutionaryColors.successGlow
                        : RevolutionaryColors.errorCoral,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              _formatDate(transaction.createdAt),
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: RevolutionaryColors.textSecondary,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildChildAccountsTab() {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: _childAccounts.isEmpty
          ? _buildEmptyChildAccounts()
          : _buildChildAccountsList(),
    );
  }

  Widget _buildEmptyChildAccounts() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.account_tree,
            size: 64,
            color: RevolutionaryColors.textSecondary,
          ),
          const SizedBox(height: 16),
          Text(
            'لا توجد حسابات فرعية لهذا الحساب',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: RevolutionaryColors.textSecondary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildChildAccountsList() {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _childAccounts.length,
      itemBuilder: (context, index) {
        final childAccount = _childAccounts[index];
        return _buildChildAccountCard(childAccount);
      },
    );
  }

  Widget _buildChildAccountCard(Account childAccount) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: _getAccountTypeColor().withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Text(
            childAccount.accountIcon,
            style: const TextStyle(fontSize: 20),
          ),
        ),
        title: Text(
          childAccount.name,
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('كود: ${childAccount.code}'),
            Text('النوع: ${childAccount.accountTypeArabic}'),
          ],
        ),
        trailing: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            Text(
              '${childAccount.displayBalance} ل.س',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: childAccount.balance >= 0
                    ? RevolutionaryColors.successGlow
                    : RevolutionaryColors.errorCoral,
              ),
            ),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
              decoration: BoxDecoration(
                color: childAccount.isActive
                    ? RevolutionaryColors.successGlow.withValues(alpha: 0.1)
                    : RevolutionaryColors.errorCoral.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                childAccount.isActive ? 'نشط' : 'غير نشط',
                style: TextStyle(
                  fontSize: 10,
                  color: childAccount.isActive
                      ? RevolutionaryColors.successGlow
                      : RevolutionaryColors.errorCoral,
                ),
              ),
            ),
          ],
        ),
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => AccountDetailsScreen(account: childAccount),
            ),
          );
        },
      ),
    );
  }

  Future<void> _selectStartDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _startDate ?? DateTime.now(),
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
    );
    if (picked != null && picked != _startDate) {
      setState(() {
        _startDate = picked;
      });
      _loadAccountTransactions();
    }
  }

  Future<void> _selectEndDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _endDate ?? DateTime.now(),
      firstDate: _startDate ?? DateTime(2020),
      lastDate: DateTime.now(),
    );
    if (picked != null && picked != _endDate) {
      setState(() {
        _endDate = picked;
      });
      _loadAccountTransactions();
    }
  }
}
