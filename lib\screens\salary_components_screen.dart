/// شاشة إدارة عناصر الراتب
/// توفر واجهة لإدارة البدلات والاستقطاعات والمكافآت
library;

import 'package:flutter/material.dart';
import '../models/hr_models.dart';
import '../services/employee_service.dart';
import '../services/logging_service.dart';
import '../widgets/loading_widget.dart';
import '../widgets/custom_error_widget.dart' as custom_widgets;
import '../constants/app_constants.dart';

class SalaryComponentsScreen extends StatefulWidget {
  const SalaryComponentsScreen({super.key});

  @override
  State<SalaryComponentsScreen> createState() => _SalaryComponentsScreenState();
}

class _SalaryComponentsScreenState extends State<SalaryComponentsScreen>
    with TickerProviderStateMixin {
  final EmployeeService _employeeService = EmployeeService();

  late TabController _tabController;
  List<SalaryDetail> _allowances = [];
  List<SalaryDetail> _deductions = [];
  List<SalaryDetail> _bonuses = [];
  List<Employee> _employees = [];
  bool _isLoading = true;
  String? _error;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final employees = await _employeeService.getAllEmployees();

      // بيانات وهمية لعناصر الراتب
      await Future.delayed(const Duration(milliseconds: 500));

      _allowances = [
        SalaryDetail(
          id: 1,
          employeeId: employees.isNotEmpty ? employees.first.id! : 1,
          componentType: AppConstants.salaryComponentAllowance,
          componentName: 'بدل نقل',
          amount: 50000,
          effectiveFrom: DateTime.now().subtract(const Duration(days: 30)),
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
        SalaryDetail(
          id: 2,
          employeeId: employees.isNotEmpty ? employees.first.id! : 1,
          componentType: AppConstants.salaryComponentAllowance,
          componentName: 'بدل طعام',
          amount: 75000,
          effectiveFrom: DateTime.now().subtract(const Duration(days: 30)),
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
      ];

      _deductions = [
        SalaryDetail(
          id: 3,
          employeeId: employees.isNotEmpty ? employees.first.id! : 1,
          componentType: AppConstants.salaryComponentDeduction,
          componentName: 'تأمينات اجتماعية',
          amount: 7,
          isPercentage: true,
          percentageOf: 'basic_salary',
          effectiveFrom: DateTime.now().subtract(const Duration(days: 30)),
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
        SalaryDetail(
          id: 4,
          employeeId: employees.isNotEmpty ? employees.first.id! : 1,
          componentType: AppConstants.salaryComponentDeduction,
          componentName: 'ضريبة دخل',
          amount: 8,
          isPercentage: true,
          percentageOf: 'gross_salary',
          effectiveFrom: DateTime.now().subtract(const Duration(days: 30)),
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
      ];

      _bonuses = [
        SalaryDetail(
          id: 5,
          employeeId: employees.isNotEmpty ? employees.first.id! : 1,
          componentType: AppConstants.salaryComponentBonus,
          componentName: 'مكافأة أداء',
          amount: 100000,
          effectiveFrom: DateTime.now().subtract(const Duration(days: 7)),
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
      ];

      setState(() {
        _employees = employees;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });

      LoggingService.error(
        'خطأ في تحميل عناصر الراتب',
        category: 'SalaryComponentsScreen',
        data: {'error': e.toString()},
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إدارة عناصر الراتب'),
        backgroundColor: Colors.indigo[700],
        foregroundColor: Colors.white,
        bottom: TabBar(
          controller: _tabController,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          indicatorColor: Colors.white,
          tabs: [
            Tab(
              icon: const Icon(Icons.add_circle),
              text: 'البدلات',
              child: _allowances.isNotEmpty
                  ? Badge(
                      label: Text('${_allowances.length}'),
                      child: const Text('البدلات'),
                    )
                  : const Text('البدلات'),
            ),
            Tab(
              icon: const Icon(Icons.remove_circle),
              text: 'الاستقطاعات',
              child: _deductions.isNotEmpty
                  ? Badge(
                      label: Text('${_deductions.length}'),
                      child: const Text('الاستقطاعات'),
                    )
                  : const Text('الاستقطاعات'),
            ),
            Tab(
              icon: const Icon(Icons.star),
              text: 'المكافآت',
              child: _bonuses.isNotEmpty
                  ? Badge(
                      label: Text('${_bonuses.length}'),
                      child: const Text('المكافآت'),
                    )
                  : const Text('المكافآت'),
            ),
          ],
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadData,
            tooltip: 'تحديث',
          ),
        ],
      ),
      body: _buildBody(),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _showAddComponentDialog(),
        backgroundColor: Colors.indigo[700],
        tooltip: 'إضافة عنصر راتب',
        child: const Icon(Icons.add, color: Colors.white),
      ),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const LoadingWidget();
    }

    if (_error != null) {
      return custom_widgets.ErrorWidget(message: _error!, onRetry: _loadData);
    }

    return TabBarView(
      controller: _tabController,
      children: [
        _buildAllowancesTab(),
        _buildDeductionsTab(),
        _buildBonusesTab(),
      ],
    );
  }

  Widget _buildAllowancesTab() {
    if (_allowances.isEmpty) {
      return _buildEmptyState('لا توجد بدلات', Icons.add_circle, Colors.green);
    }

    return RefreshIndicator(
      onRefresh: _loadData,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: _allowances.length,
        itemBuilder: (context, index) {
          final allowance = _allowances[index];
          return _buildComponentCard(allowance, Colors.green);
        },
      ),
    );
  }

  Widget _buildDeductionsTab() {
    if (_deductions.isEmpty) {
      return _buildEmptyState(
        'لا توجد استقطاعات',
        Icons.remove_circle,
        Colors.red,
      );
    }

    return RefreshIndicator(
      onRefresh: _loadData,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: _deductions.length,
        itemBuilder: (context, index) {
          final deduction = _deductions[index];
          return _buildComponentCard(deduction, Colors.red);
        },
      ),
    );
  }

  Widget _buildBonusesTab() {
    if (_bonuses.isEmpty) {
      return _buildEmptyState('لا توجد مكافآت', Icons.star, Colors.orange);
    }

    return RefreshIndicator(
      onRefresh: _loadData,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: _bonuses.length,
        itemBuilder: (context, index) {
          final bonus = _bonuses[index];
          return _buildComponentCard(bonus, Colors.orange);
        },
      ),
    );
  }

  Widget _buildEmptyState(String message, IconData icon, Color color) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, size: 64, color: Colors.grey[400]),
          const SizedBox(height: 16),
          Text(
            message,
            style: TextStyle(fontSize: 18, color: Colors.grey[600]),
          ),
          const SizedBox(height: 16),
          ElevatedButton.icon(
            onPressed: () => _showAddComponentDialog(),
            icon: const Icon(Icons.add),
            label: const Text('إضافة عنصر جديد'),
            style: ElevatedButton.styleFrom(
              backgroundColor: color,
              foregroundColor: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildComponentCard(SalaryDetail component, Color color) {
    final employee = _employees.firstWhere(
      (emp) => emp.id == component.employeeId,
      orElse: () => Employee(
        id: 0,
        employeeNumber: '000',
        nationalId: '000000000',
        firstName: 'موظف',
        lastName: 'غير معروف',
        fullName: 'موظف غير معروف',
        email: '',
        phone: '',
        hireDate: DateTime.now(),
        basicSalary: 0,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
    );

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: color,
          child: Icon(
            _getComponentIcon(component.componentType),
            color: Colors.white,
          ),
        ),
        title: Text(
          component.componentName,
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('الموظف: ${employee.firstName} ${employee.lastName}'),
            Text('النوع: ${_getComponentTypeText(component.componentType)}'),
            if (component.isPercentage)
              Text(
                'نسبة مئوية: ${component.amount}% من ${_getPercentageBaseText(component.percentageOf)}',
              )
            else
              Text('مبلغ ثابت: ${component.amount.toStringAsFixed(0)} ل.س'),
            Text('ساري من: ${_formatDate(component.effectiveFrom)}'),
            if (component.effectiveTo != null)
              Text('ينتهي في: ${_formatDate(component.effectiveTo!)}'),
          ],
        ),
        trailing: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            if (component.isPercentage)
              Text(
                '${component.amount}%',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: color,
                  fontSize: 16,
                ),
              )
            else
              Text(
                '${component.amount.toStringAsFixed(0)} ل.س',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: color,
                  fontSize: 16,
                ),
              ),
            if (component.isTaxable)
              const Text(
                'خاضع للضريبة',
                style: TextStyle(fontSize: 10, color: Colors.grey),
              ),
            if (component.isActive)
              const Icon(Icons.check_circle, color: Colors.green, size: 16)
            else
              const Icon(Icons.cancel, color: Colors.grey, size: 16),
          ],
        ),
        onTap: () => _showComponentDetails(component),
      ),
    );
  }

  IconData _getComponentIcon(String componentType) {
    switch (componentType) {
      case AppConstants.salaryComponentAllowance:
        return Icons.add_circle;
      case AppConstants.salaryComponentDeduction:
        return Icons.remove_circle;
      case AppConstants.salaryComponentBonus:
        return Icons.star;
      default:
        return Icons.attach_money;
    }
  }

  String _getComponentTypeText(String componentType) {
    switch (componentType) {
      case AppConstants.salaryComponentAllowance:
        return 'بدل';
      case AppConstants.salaryComponentDeduction:
        return 'استقطاع';
      case AppConstants.salaryComponentBonus:
        return 'مكافأة';
      default:
        return componentType;
    }
  }

  String _getPercentageBaseText(String? percentageOf) {
    switch (percentageOf) {
      case 'basic_salary':
        return 'الراتب الأساسي';
      case 'gross_salary':
        return 'الراتب الإجمالي';
      default:
        return 'الراتب الأساسي';
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  void _showAddComponentDialog() {
    if (_employees.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('لا يوجد موظفين لإضافة عناصر راتب لهم'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    showDialog(
      context: context,
      builder: (context) => _SalaryComponentFormDialog(
        employees: _employees,
        onSave:
            (
              employeeId,
              componentType,
              componentName,
              amount,
              isPercentage,
              percentageOf,
              isTaxable,
              effectiveFrom,
              effectiveTo,
              notes,
            ) async {
              final scaffoldMessenger = ScaffoldMessenger.of(context);
              try {
                // في التطبيق الحقيقي، ستكون هناك خدمة لحفظ عنصر الراتب
                await Future.delayed(const Duration(milliseconds: 500));

                if (mounted) {
                  scaffoldMessenger.showSnackBar(
                    const SnackBar(
                      content: Text('تم إضافة عنصر الراتب بنجاح'),
                      backgroundColor: Colors.green,
                    ),
                  );
                  _loadData();
                }
              } catch (e) {
                if (mounted) {
                  scaffoldMessenger.showSnackBar(
                    SnackBar(
                      content: Text(
                        'خطأ في إضافة عنصر الراتب: ${e.toString()}',
                      ),
                      backgroundColor: Colors.red,
                    ),
                  );
                }
              }
            },
      ),
    );
  }

  void _showComponentDetails(SalaryDetail component) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(component.componentName),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('النوع: ${_getComponentTypeText(component.componentType)}'),
            const SizedBox(height: 8),
            if (component.isPercentage)
              Text(
                'النسبة: ${component.amount}% من ${_getPercentageBaseText(component.percentageOf)}',
              )
            else
              Text('المبلغ: ${component.amount.toStringAsFixed(0)} ل.س'),
            const SizedBox(height: 8),
            Text('ساري من: ${_formatDate(component.effectiveFrom)}'),
            if (component.effectiveTo != null)
              Text('ينتهي في: ${_formatDate(component.effectiveTo!)}'),
            const SizedBox(height: 8),
            Text('خاضع للضريبة: ${component.isTaxable ? 'نعم' : 'لا'}'),
            Text('نشط: ${component.isActive ? 'نعم' : 'لا'}'),
            if (component.notes != null && component.notes!.isNotEmpty) ...[
              const SizedBox(height: 8),
              Text('ملاحظات: ${component.notes}'),
            ],
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إغلاق'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _showEditComponentDialog(component);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.indigo[700],
              foregroundColor: Colors.white,
            ),
            child: const Text('تعديل'),
          ),
        ],
      ),
    );
  }

  void _showEditComponentDialog(SalaryDetail component) {
    // يمكن إضافة نافذة تعديل عنصر الراتب هنا
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(SnackBar(content: Text('تعديل ${component.componentName}')));
  }
}

class _SalaryComponentFormDialog extends StatefulWidget {
  final List<Employee> employees;
  final Function(
    int employeeId,
    String componentType,
    String componentName,
    double amount,
    bool isPercentage,
    String? percentageOf,
    bool isTaxable,
    DateTime effectiveFrom,
    DateTime? effectiveTo,
    String notes,
  )
  onSave;

  const _SalaryComponentFormDialog({
    required this.employees,
    required this.onSave,
  });

  @override
  State<_SalaryComponentFormDialog> createState() =>
      _SalaryComponentFormDialogState();
}

class _SalaryComponentFormDialogState
    extends State<_SalaryComponentFormDialog> {
  final _formKey = GlobalKey<FormState>();
  final _componentNameController = TextEditingController();
  final _amountController = TextEditingController();
  final _notesController = TextEditingController();

  int? _selectedEmployeeId;
  String _selectedComponentType = AppConstants.salaryComponentAllowance;
  bool _isPercentage = false;
  String _percentageOf = 'basic_salary';
  bool _isTaxable = true;
  DateTime? _effectiveFrom;
  DateTime? _effectiveTo;

  @override
  void dispose() {
    _componentNameController.dispose();
    _amountController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('إضافة عنصر راتب جديد'),
      content: SingleChildScrollView(
        child: Form(
          key: _formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              DropdownButtonFormField<int>(
                value: _selectedEmployeeId,
                decoration: const InputDecoration(
                  labelText: 'الموظف *',
                  border: OutlineInputBorder(),
                ),
                items: widget.employees.map((emp) {
                  return DropdownMenuItem<int>(
                    value: emp.id,
                    child: Text('${emp.firstName} ${emp.lastName}'),
                  );
                }).toList(),
                onChanged: (value) {
                  setState(() {
                    _selectedEmployeeId = value;
                  });
                },
                validator: (value) {
                  if (value == null) {
                    return 'الموظف مطلوب';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              DropdownButtonFormField<String>(
                value: _selectedComponentType,
                decoration: const InputDecoration(
                  labelText: 'نوع العنصر *',
                  border: OutlineInputBorder(),
                ),
                items: const [
                  DropdownMenuItem(
                    value: AppConstants.salaryComponentAllowance,
                    child: Text('بدل'),
                  ),
                  DropdownMenuItem(
                    value: AppConstants.salaryComponentDeduction,
                    child: Text('استقطاع'),
                  ),
                  DropdownMenuItem(
                    value: AppConstants.salaryComponentBonus,
                    child: Text('مكافأة'),
                  ),
                ],
                onChanged: (value) {
                  setState(() {
                    _selectedComponentType = value!;
                  });
                },
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _componentNameController,
                decoration: const InputDecoration(
                  labelText: 'اسم العنصر *',
                  border: OutlineInputBorder(),
                ),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'اسم العنصر مطلوب';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              Row(
                children: [
                  Expanded(
                    child: TextFormField(
                      controller: _amountController,
                      decoration: InputDecoration(
                        labelText: _isPercentage
                            ? 'النسبة المئوية *'
                            : 'المبلغ *',
                        border: const OutlineInputBorder(),
                        suffixText: _isPercentage ? '%' : 'ل.س',
                      ),
                      keyboardType: TextInputType.number,
                      validator: (value) {
                        if (value == null || value.trim().isEmpty) {
                          return _isPercentage
                              ? 'النسبة مطلوبة'
                              : 'المبلغ مطلوب';
                        }
                        final amount = double.tryParse(value);
                        if (amount == null || amount <= 0) {
                          return 'قيمة غير صحيحة';
                        }
                        return null;
                      },
                    ),
                  ),
                  const SizedBox(width: 16),
                  Column(
                    children: [
                      const Text('نسبة مئوية'),
                      Switch(
                        value: _isPercentage,
                        onChanged: (value) {
                          setState(() {
                            _isPercentage = value;
                          });
                        },
                      ),
                    ],
                  ),
                ],
              ),
              if (_isPercentage) ...[
                const SizedBox(height: 16),
                DropdownButtonFormField<String>(
                  value: _percentageOf,
                  decoration: const InputDecoration(
                    labelText: 'نسبة من',
                    border: OutlineInputBorder(),
                  ),
                  items: const [
                    DropdownMenuItem(
                      value: 'basic_salary',
                      child: Text('الراتب الأساسي'),
                    ),
                    DropdownMenuItem(
                      value: 'gross_salary',
                      child: Text('الراتب الإجمالي'),
                    ),
                  ],
                  onChanged: (value) {
                    setState(() {
                      _percentageOf = value!;
                    });
                  },
                ),
              ],
              const SizedBox(height: 16),
              Row(
                children: [
                  Expanded(
                    child: TextFormField(
                      decoration: const InputDecoration(
                        labelText: 'ساري من *',
                        border: OutlineInputBorder(),
                        suffixIcon: Icon(Icons.calendar_today),
                      ),
                      readOnly: true,
                      onTap: () async {
                        final date = await showDatePicker(
                          context: context,
                          initialDate: DateTime.now(),
                          firstDate: DateTime.now().subtract(
                            const Duration(days: 365),
                          ),
                          lastDate: DateTime.now().add(
                            const Duration(days: 365),
                          ),
                        );
                        if (date != null) {
                          setState(() {
                            _effectiveFrom = date;
                          });
                        }
                      },
                      controller: TextEditingController(
                        text: _effectiveFrom != null
                            ? '${_effectiveFrom!.day}/${_effectiveFrom!.month}/${_effectiveFrom!.year}'
                            : '',
                      ),
                      validator: (value) {
                        if (_effectiveFrom == null) {
                          return 'تاريخ البداية مطلوب';
                        }
                        return null;
                      },
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: TextFormField(
                      decoration: const InputDecoration(
                        labelText: 'ينتهي في',
                        border: OutlineInputBorder(),
                        suffixIcon: Icon(Icons.calendar_today),
                      ),
                      readOnly: true,
                      onTap: () async {
                        final date = await showDatePicker(
                          context: context,
                          initialDate: _effectiveFrom ?? DateTime.now(),
                          firstDate: _effectiveFrom ?? DateTime.now(),
                          lastDate: DateTime.now().add(
                            const Duration(days: 1095),
                          ),
                        );
                        if (date != null) {
                          setState(() {
                            _effectiveTo = date;
                          });
                        }
                      },
                      controller: TextEditingController(
                        text: _effectiveTo != null
                            ? '${_effectiveTo!.day}/${_effectiveTo!.month}/${_effectiveTo!.year}'
                            : '',
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              CheckboxListTile(
                title: const Text('خاضع للضريبة'),
                value: _isTaxable,
                onChanged: (value) {
                  setState(() {
                    _isTaxable = value ?? true;
                  });
                },
                controlAffinity: ListTileControlAffinity.leading,
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _notesController,
                decoration: const InputDecoration(
                  labelText: 'ملاحظات',
                  border: OutlineInputBorder(),
                ),
                maxLines: 2,
              ),
            ],
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('إلغاء'),
        ),
        ElevatedButton(
          onPressed: () {
            if (_formKey.currentState!.validate()) {
              final amount = double.parse(_amountController.text);

              widget.onSave(
                _selectedEmployeeId!,
                _selectedComponentType,
                _componentNameController.text.trim(),
                amount,
                _isPercentage,
                _isPercentage ? _percentageOf : null,
                _isTaxable,
                _effectiveFrom!,
                _effectiveTo,
                _notesController.text.trim(),
              );
              Navigator.of(context).pop();
            }
          },
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.indigo[700],
            foregroundColor: Colors.white,
          ),
          child: const Text('إضافة'),
        ),
      ],
    );
  }
}
