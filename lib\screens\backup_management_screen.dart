import 'dart:io';
import 'package:flutter/material.dart';
import 'package:file_picker/file_picker.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../constants/revolutionary_design_colors.dart';
import '../services/secure_backup_service.dart';
import '../services/performance_service.dart';
import '../widgets/loading_widget.dart';

class BackupManagementScreen extends StatefulWidget {
  const BackupManagementScreen({super.key});

  @override
  State<BackupManagementScreen> createState() => _BackupManagementScreenState();
}

class _BackupManagementScreenState extends State<BackupManagementScreen>
    with TickerProviderStateMixin {
  final PerformanceService _performanceService = PerformanceService();

  late TabController _tabController;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  BackupSettings? _backupSettings;
  List<BackupInfo> _availableBackups = [];
  bool _isLoading = true;
  bool _isCreatingBackup = false;
  bool _isRestoringBackup = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    _loadBackupData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  Future<void> _loadBackupData() async {
    setState(() => _isLoading = true);

    try {
      final settings = await _performanceService.measureOperation(
        'load_backup_settings',
        () => SecureBackupService.getBackupSettings(),
        category: 'backup',
        metadata: {'screen': 'backup_management'},
      );

      final backups = await _performanceService.measureOperation(
        'load_available_backups',
        () => SecureBackupService.getAvailableBackups(),
        category: 'backup',
        metadata: {'screen': 'backup_management'},
      );

      if (!mounted) return;

      setState(() {
        _backupSettings = settings;
        _availableBackups = backups;
        _isLoading = false;
      });

      _animationController.forward();

      // تسجيل إحصائيات النسخ الاحتياطية
      _performanceService.updateMetric(
        'available_backups_count',
        backups.length.toDouble(),
      );
      _performanceService.updateMetric(
        'auto_backup_enabled',
        settings.autoBackupEnabled ? 1.0 : 0.0,
      );
    } catch (e) {
      if (!mounted) return;
      setState(() => _isLoading = false);
      _showErrorSnackBar('خطأ في تحميل بيانات النسخ الاحتياطية: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إدارة النسخ الاحتياطية'),
        backgroundColor: RevolutionaryColors.damascusSky,
        foregroundColor: Colors.white,
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          tabs: const [
            Tab(icon: Icon(Icons.backup), text: 'النسخ الاحتياطية'),
            Tab(icon: Icon(Icons.settings), text: 'الإعدادات'),
            Tab(icon: Icon(Icons.restore), text: 'الاستعادة'),
          ],
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadBackupData,
            tooltip: 'تحديث البيانات',
          ),
        ],
      ),
      body: _isLoading
          ? const LoadingWidget(
              message: 'جاري تحميل بيانات النسخ الاحتياطية...',
            )
          : FadeTransition(
              opacity: _fadeAnimation,
              child: TabBarView(
                controller: _tabController,
                children: [
                  _buildBackupsTab(),
                  _buildSettingsTab(),
                  _buildRestoreTab(),
                ],
              ),
            ),
      floatingActionButton: _tabController.index == 0
          ? FloatingActionButton.extended(
              onPressed: _isCreatingBackup ? null : _createNewBackup,
              backgroundColor: RevolutionaryColors.damascusSky,
              icon: _isCreatingBackup
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                  : const Icon(Icons.add),
              label: Text(
                _isCreatingBackup ? 'جاري الإنشاء...' : 'نسخة احتياطية جديدة',
              ),
            )
          : null,
    );
  }

  Widget _buildBackupsTab() {
    return RefreshIndicator(
      onRefresh: _loadBackupData,
      child: _availableBackups.isEmpty
          ? _buildEmptyBackupsState()
          : ListView.builder(
              padding: const EdgeInsets.all(16),
              itemCount: _availableBackups.length,
              itemBuilder: (context, index) {
                final backup = _availableBackups[index];
                return _buildBackupCard(backup);
              },
            ),
    );
  }

  Widget _buildEmptyBackupsState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.backup_outlined, size: 80, color: Colors.grey[400]),
          const SizedBox(height: 16),
          Text(
            'لا توجد نسخ احتياطية',
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey[600],
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'اضغط على الزر أدناه لإنشاء نسخة احتياطية جديدة',
            style: TextStyle(fontSize: 14, color: Colors.grey[500]),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: _createNewBackup,
            icon: const Icon(Icons.add),
            label: const Text('إنشاء نسخة احتياطية'),
            style: ElevatedButton.styleFrom(
              backgroundColor: RevolutionaryColors.damascusSky,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBackupCard(BackupInfo backup) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: RevolutionaryColors.damascusSky.withValues(alpha: 0.1),
          child: Icon(Icons.backup, color: RevolutionaryColors.damascusSky),
        ),
        title: Text(
          backup.fileName,
          style: const TextStyle(fontWeight: FontWeight.w600),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('الحجم: ${_formatFileSize(backup.fileSize)}'),
            Text('التاريخ: ${_formatDate(backup.createdAt)}'),
          ],
        ),
        trailing: PopupMenuButton<String>(
          onSelected: (value) => _handleBackupAction(value, backup),
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'validate',
              child: ListTile(
                leading: Icon(Icons.verified),
                title: Text('التحقق من صحة النسخة'),
                contentPadding: EdgeInsets.zero,
              ),
            ),
            const PopupMenuItem(
              value: 'export',
              child: ListTile(
                leading: Icon(Icons.file_download),
                title: Text('تصدير النسخة'),
                contentPadding: EdgeInsets.zero,
              ),
            ),
            const PopupMenuItem(
              value: 'delete',
              child: ListTile(
                leading: Icon(Icons.delete, color: Colors.red),
                title: Text('حذف النسخة', style: TextStyle(color: Colors.red)),
                contentPadding: EdgeInsets.zero,
              ),
            ),
          ],
        ),
        onTap: () => _showBackupDetails(backup),
      ),
    );
  }

  Widget _buildSettingsTab() {
    if (_backupSettings == null) {
      return const Center(child: CircularProgressIndicator());
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSettingsCard(),
          const SizedBox(height: 16),
          _buildAutoBackupCard(),
          const SizedBox(height: 16),
          _buildSecurityCard(),
        ],
      ),
    );
  }

  Widget _buildRestoreTab() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(Icons.warning, color: RevolutionaryColors.syrianGold),
                      const SizedBox(width: 8),
                      const Text(
                        'تحذير مهم',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  const Text(
                    'استعادة النسخة الاحتياطية ستؤدي إلى حذف جميع البيانات الحالية واستبدالها بالبيانات من النسخة الاحتياطية. تأكد من إنشاء نسخة احتياطية من البيانات الحالية قبل المتابعة.',
                    style: TextStyle(fontSize: 14),
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),
          ElevatedButton.icon(
            onPressed: _isRestoringBackup ? null : _selectBackupFileForRestore,
            icon: _isRestoringBackup
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : const Icon(Icons.file_upload),
            label: Text(
              _isRestoringBackup ? 'جاري الاستعادة...' : 'اختيار ملف للاستعادة',
            ),
            style: ElevatedButton.styleFrom(
              backgroundColor: RevolutionaryColors.syrianGold,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
            ),
          ),
        ],
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message), backgroundColor: RevolutionaryColors.syrianGold),
    );
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message), backgroundColor: RevolutionaryColors.syrianGold),
    );
  }

  String _formatFileSize(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
  }

  String _formatDate(DateTime date) {
    return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year} ${date.hour.toString().padLeft(2, '0')}:${date.minute.toString().padLeft(2, '0')}';
  }

  /// إنشاء نسخة احتياطية جديدة
  Future<void> _createNewBackup() async {
    if (!_backupSettings!.hasBackupPassword) {
      _showSetupPasswordDialog();
      return;
    }

    _showCreateBackupDialog();
  }

  /// عرض حوار إعداد كلمة مرور النسخ الاحتياطية
  void _showSetupPasswordDialog() {
    final passwordController = TextEditingController();
    final confirmPasswordController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إعداد كلمة مرور النسخ الاحتياطية'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'يجب إعداد كلمة مرور لحماية النسخ الاحتياطية قبل إنشاء أول نسخة احتياطية.',
              style: TextStyle(fontSize: 14),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: passwordController,
              obscureText: true,
              decoration: const InputDecoration(
                labelText: 'كلمة المرور',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.lock),
              ),
            ),
            const SizedBox(height: 12),
            TextField(
              controller: confirmPasswordController,
              obscureText: true,
              decoration: const InputDecoration(
                labelText: 'تأكيد كلمة المرور',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.lock_outline),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              if (passwordController.text != confirmPasswordController.text) {
                _showErrorSnackBar('كلمات المرور غير متطابقة');
                return;
              }

              if (passwordController.text.length < 8) {
                _showErrorSnackBar('كلمة المرور يجب أن تكون 8 أحرف على الأقل');
                return;
              }

              // Store context before async operation
              final navigator = Navigator.of(context);

              final success = await SecureBackupService.setupBackupPassword(
                passwordController.text,
              );

              if (!mounted) return;

              if (success) {
                navigator.pop();
                _showSuccessSnackBar(
                  'تم إعداد كلمة مرور النسخ الاحتياطية بنجاح',
                );
                _loadBackupData();
              } else {
                _showErrorSnackBar('فشل في إعداد كلمة مرور النسخ الاحتياطية');
              }
            },
            child: const Text('حفظ'),
          ),
        ],
      ),
    );
  }

  /// عرض حوار إنشاء نسخة احتياطية
  void _showCreateBackupDialog() {
    final passwordController = TextEditingController();
    final descriptionController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إنشاء نسخة احتياطية جديدة'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: passwordController,
              obscureText: true,
              decoration: const InputDecoration(
                labelText: 'كلمة مرور النسخ الاحتياطية',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.lock),
              ),
            ),
            const SizedBox(height: 12),
            TextField(
              controller: descriptionController,
              decoration: const InputDecoration(
                labelText: 'وصف النسخة (اختياري)',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.description),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.pop(context);
              await _performCreateBackup(
                passwordController.text,
                descriptionController.text.isEmpty
                    ? null
                    : descriptionController.text,
              );
            },
            child: const Text('إنشاء'),
          ),
        ],
      ),
    );
  }

  /// تنفيذ إنشاء النسخة الاحتياطية
  Future<void> _performCreateBackup(
    String password,
    String? description,
  ) async {
    setState(() => _isCreatingBackup = true);

    try {
      final backupPath = await _performanceService.measureOperation(
        'create_backup',
        () => SecureBackupService.createBackup(
          backupPassword: password,
          description: description,
        ),
        category: 'backup',
        metadata: {'screen': 'backup_management'},
      );

      if (!mounted) return;

      if (backupPath != null) {
        _showSuccessSnackBar('تم إنشاء النسخة الاحتياطية بنجاح');
        _loadBackupData();
      } else {
        _showErrorSnackBar('فشل في إنشاء النسخة الاحتياطية');
      }
    } catch (e) {
      if (!mounted) return;
      _showErrorSnackBar('خطأ في إنشاء النسخة الاحتياطية: $e');
    } finally {
      if (mounted) {
        setState(() => _isCreatingBackup = false);
      }
    }
  }

  /// معالجة إجراءات النسخ الاحتياطية
  void _handleBackupAction(String action, BackupInfo backup) {
    switch (action) {
      case 'validate':
        _validateBackup(backup);
        break;
      case 'export':
        _exportBackup(backup);
        break;
      case 'delete':
        _deleteBackup(backup);
        break;
    }
  }

  /// التحقق من صحة النسخة الاحتياطية
  Future<void> _validateBackup(BackupInfo backup) async {
    final passwordController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('التحقق من صحة النسخة الاحتياطية'),
        content: TextField(
          controller: passwordController,
          obscureText: true,
          decoration: const InputDecoration(
            labelText: 'كلمة مرور النسخ الاحتياطية',
            border: OutlineInputBorder(),
            prefixIcon: Icon(Icons.lock),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.pop(context);

              final isValid = await SecureBackupService.validateBackup(
                backupPath: backup.filePath,
                backupPassword: passwordController.text,
              );

              if (!mounted) return;

              if (isValid) {
                _showSuccessSnackBar(
                  'النسخة الاحتياطية صحيحة وقابلة للاستخدام',
                );
              } else {
                _showErrorSnackBar(
                  'النسخة الاحتياطية تالفة أو كلمة المرور خاطئة',
                );
              }
            },
            child: const Text('تحقق'),
          ),
        ],
      ),
    );
  }

  /// تصدير النسخة الاحتياطية
  Future<void> _exportBackup(BackupInfo backup) async {
    try {
      final result = await FilePicker.platform.saveFile(
        dialogTitle: 'حفظ النسخة الاحتياطية',
        fileName: backup.fileName,
        type: FileType.any,
      );

      if (result != null) {
        // نسخ الملف إلى المكان المحدد
        final sourceFile = File(backup.filePath);
        final targetFile = File(result);
        await sourceFile.copy(targetFile.path);

        if (!mounted) return;
        _showSuccessSnackBar('تم تصدير النسخة الاحتياطية بنجاح');
      }
    } catch (e) {
      if (!mounted) return;
      _showErrorSnackBar('خطأ في تصدير النسخة الاحتياطية: $e');
    }
  }

  /// حذف النسخة الاحتياطية
  Future<void> _deleteBackup(BackupInfo backup) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text(
          'هل أنت متأكد من حذف النسخة الاحتياطية "${backup.fileName}"؟',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('حذف'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await SecureBackupService.deleteBackup(backup.filePath);
        if (!mounted) return;
        _showSuccessSnackBar('تم حذف النسخة الاحتياطية بنجاح');
        _loadBackupData();
      } catch (e) {
        if (!mounted) return;
        _showErrorSnackBar('خطأ في حذف النسخة الاحتياطية: $e');
      }
    }
  }

  /// عرض تفاصيل النسخة الاحتياطية
  void _showBackupDetails(BackupInfo backup) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('تفاصيل النسخة الاحتياطية'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildDetailRow('اسم الملف', backup.fileName),
            _buildDetailRow('الحجم', _formatFileSize(backup.fileSize)),
            _buildDetailRow('تاريخ الإنشاء', _formatDate(backup.createdAt)),
            _buildDetailRow('المسار', backup.filePath),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(child: Text(value)),
        ],
      ),
    );
  }

  /// بناء بطاقة الإعدادات العامة
  Widget _buildSettingsCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'الإعدادات العامة',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            ListTile(
              leading: Icon(Icons.folder, color: RevolutionaryColors.damascusSky),
              title: const Text('مجلد النسخ الاحتياطية'),
              subtitle: Text(
                _backupSettings!.backupLocation ?? 'المجلد الافتراضي',
              ),
              trailing: const Icon(Icons.arrow_forward_ios),
              onTap: _changeBackupLocation,
            ),
            const Divider(),
            ListTile(
              leading: Icon(Icons.info, color: RevolutionaryColors.syrianGold),
              title: const Text('آخر نسخة احتياطية'),
              subtitle: Text(
                _backupSettings!.formattedLastBackupDate ??
                    'لم يتم إنشاء نسخ احتياطية بعد',
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء بطاقة النسخ التلقائي
  Widget _buildAutoBackupCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'النسخ الاحتياطي التلقائي',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            SwitchListTile(
              title: const Text('تفعيل النسخ التلقائي'),
              subtitle: const Text(
                'إنشاء نسخ احتياطية تلقائياً حسب الجدولة المحددة',
              ),
              value: _backupSettings!.autoBackupEnabled,
              onChanged: _toggleAutoBackup,
            ),
            if (_backupSettings!.autoBackupEnabled) ...[
              const Divider(),
              ListTile(
                leading: Icon(Icons.schedule, color: RevolutionaryColors.damascusSky),
                title: const Text('فترة النسخ التلقائي'),
                subtitle: Text('كل ${_backupSettings!.intervalHours} ساعة'),
                trailing: const Icon(Icons.edit),
                onTap: _changeBackupInterval,
              ),
              ListTile(
                leading: Icon(Icons.storage, color: RevolutionaryColors.damascusSky),
                title: const Text('الحد الأقصى للنسخ'),
                subtitle: Text('${_backupSettings!.maxBackups} نسخة احتياطية'),
                trailing: const Icon(Icons.edit),
                onTap: _changeMaxBackups,
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// بناء بطاقة الأمان
  Widget _buildSecurityCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'الأمان والحماية',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            ListTile(
              leading: Icon(
                _backupSettings!.hasBackupPassword
                    ? Icons.lock
                    : Icons.lock_open,
                color: _backupSettings!.hasBackupPassword
                    ? RevolutionaryColors.syrianGold
                    : RevolutionaryColors.syrianGold,
              ),
              title: Text(
                _backupSettings!.hasBackupPassword
                    ? 'كلمة المرور محددة'
                    : 'لم يتم تحديد كلمة مرور',
              ),
              subtitle: const Text('كلمة مرور لحماية النسخ الاحتياطية'),
              trailing: const Icon(Icons.edit),
              onTap: _changeBackupPassword,
            ),
          ],
        ),
      ),
    );
  }

  /// اختيار ملف للاستعادة
  Future<void> _selectBackupFileForRestore() async {
    final result = await FilePicker.platform.pickFiles(
      type: FileType.custom,
      allowedExtensions: ['slb'],
      dialogTitle: 'اختيار ملف النسخة الاحتياطية',
    );

    if (!mounted) return;

    if (result != null && result.files.single.path != null) {
      _showRestoreDialog(result.files.single.path!);
    }
  }

  /// عرض حوار الاستعادة
  void _showRestoreDialog(String backupPath) {
    final backupPasswordController = TextEditingController();
    final dbPasswordController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('استعادة النسخة الاحتياطية'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'تحذير: ستؤدي هذه العملية إلى حذف جميع البيانات الحالية!',
              style: TextStyle(color: Colors.red, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: backupPasswordController,
              obscureText: true,
              decoration: const InputDecoration(
                labelText: 'كلمة مرور النسخة الاحتياطية',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.lock),
              ),
            ),
            const SizedBox(height: 12),
            TextField(
              controller: dbPasswordController,
              obscureText: true,
              decoration: const InputDecoration(
                labelText: 'كلمة مرور قاعدة البيانات',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.storage),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.pop(context);
              await _performRestore(
                backupPath,
                backupPasswordController.text,
                dbPasswordController.text,
              );
            },
            style: ElevatedButton.styleFrom(backgroundColor: RevolutionaryColors.syrianGold),
            child: const Text('استعادة'),
          ),
        ],
      ),
    );
  }

  /// تنفيذ الاستعادة
  Future<void> _performRestore(
    String backupPath,
    String backupPassword,
    String dbPassword,
  ) async {
    setState(() => _isRestoringBackup = true);

    try {
      final success = await _performanceService.measureOperation(
        'restore_backup',
        () => SecureBackupService.restoreBackup(
          backupPath: backupPath,
          backupPassword: backupPassword,
          databasePassword: dbPassword,
        ),
        category: 'backup',
        metadata: {'screen': 'backup_management'},
      );

      if (!mounted) return;

      if (success) {
        _showSuccessSnackBar('تم استعادة النسخة الاحتياطية بنجاح');
        // إعادة تشغيل التطبيق قد تكون مطلوبة
      } else {
        _showErrorSnackBar('فشل في استعادة النسخة الاحتياطية');
      }
    } catch (e) {
      if (!mounted) return;
      _showErrorSnackBar('خطأ في استعادة النسخة الاحتياطية: $e');
    } finally {
      if (mounted) {
        setState(() => _isRestoringBackup = false);
      }
    }
  }

  // طرق مساعدة للإعدادات
  Future<void> _changeBackupLocation() async {
    try {
      // اختيار مجلد جديد للنسخ الاحتياطية
      String? selectedDirectory = await FilePicker.platform.getDirectoryPath(
        dialogTitle: 'اختر مجلد النسخ الاحتياطية',
      );

      if (selectedDirectory != null) {
        // التحقق من صحة المجلد وإمكانية الكتابة فيه
        final directory = Directory(selectedDirectory);
        if (!await directory.exists()) {
          await directory.create(recursive: true);
        }

        // حفظ المسار الجديد
        final prefs = await SharedPreferences.getInstance();
        await prefs.setString('backup_location', selectedDirectory);

        // إعادة تحميل الإعدادات
        await _loadBackupData();

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'تم تغيير مجلد النسخ الاحتياطية إلى: $selectedDirectory',
              ),
              backgroundColor: RevolutionaryColors.syrianGold,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تغيير مجلد النسخ الاحتياطية: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _toggleAutoBackup(bool value) async {
    try {
      setState(() => _isLoading = true);

      bool success;
      if (value) {
        // تفعيل النسخ التلقائي مع الإعدادات الحالية
        success = await SecureBackupService.enableAutoBackup(
          intervalHours: _backupSettings?.intervalHours ?? 24,
          maxBackups: _backupSettings?.maxBackups ?? 10,
        );
      } else {
        // إلغاء تفعيل النسخ التلقائي
        success = await SecureBackupService.disableAutoBackup();
      }

      if (success) {
        // إعادة تحميل الإعدادات
        await _loadBackupData();

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                value
                    ? 'تم تفعيل النسخ الاحتياطي التلقائي'
                    : 'تم إلغاء تفعيل النسخ الاحتياطي التلقائي',
              ),
              backgroundColor: RevolutionaryColors.syrianGold,
            ),
          );
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'فشل في ${value ? 'تفعيل' : 'إلغاء تفعيل'} النسخ الاحتياطي التلقائي',
              ),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تغيير إعدادات النسخ التلقائي: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  Future<void> _changeBackupInterval() async {
    final TextEditingController controller = TextEditingController(
      text: _backupSettings?.intervalHours.toString() ?? '24',
    );

    final result = await showDialog<int>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تغيير فترة النسخ التلقائي'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('أدخل فترة النسخ الاحتياطي التلقائي بالساعات:'),
            const SizedBox(height: 16),
            TextField(
              controller: controller,
              keyboardType: TextInputType.number,
              decoration: const InputDecoration(
                labelText: 'عدد الساعات',
                hintText: 'مثال: 24',
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 16),
            const Text(
              'القيم المقترحة:\n• 1 ساعة = نسخ متكررة\n• 6 ساعات = نسخ منتظمة\n• 24 ساعة = نسخ يومية\n• 168 ساعة = نسخ أسبوعية',
              style: TextStyle(fontSize: 12, color: Colors.grey),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              final hours = int.tryParse(controller.text);
              if (hours != null && hours > 0 && hours <= 8760) {
                Navigator.pop(context, hours);
              } else {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('يرجى إدخال رقم صحيح بين 1 و 8760 ساعة'),
                    backgroundColor: Colors.red,
                  ),
                );
              }
            },
            child: const Text('حفظ'),
          ),
        ],
      ),
    );

    if (result != null) {
      try {
        // تحديث الإعدادات
        final success = await SecureBackupService.enableAutoBackup(
          intervalHours: result,
          maxBackups: _backupSettings?.maxBackups ?? 10,
        );

        if (success) {
          await _loadBackupData();
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('تم تغيير فترة النسخ التلقائي إلى $result ساعة'),
                backgroundColor: RevolutionaryColors.syrianGold,
              ),
            );
          }
        } else {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('فشل في تغيير فترة النسخ التلقائي'),
                backgroundColor: Colors.red,
              ),
            );
          }
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('خطأ في تغيير فترة النسخ التلقائي: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  Future<void> _changeMaxBackups() async {
    final TextEditingController controller = TextEditingController(
      text: _backupSettings?.maxBackups.toString() ?? '10',
    );

    final result = await showDialog<int>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تغيير الحد الأقصى للنسخ الاحتياطية'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('أدخل الحد الأقصى لعدد النسخ الاحتياطية المحفوظة:'),
            const SizedBox(height: 16),
            TextField(
              controller: controller,
              keyboardType: TextInputType.number,
              decoration: const InputDecoration(
                labelText: 'عدد النسخ',
                hintText: 'مثال: 10',
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 16),
            const Text(
              'ملاحظة: عند تجاوز هذا العدد، سيتم حذف النسخ الأقدم تلقائياً لتوفير مساحة التخزين.',
              style: TextStyle(fontSize: 12, color: Colors.grey),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              final maxBackups = int.tryParse(controller.text);
              if (maxBackups != null && maxBackups > 0 && maxBackups <= 100) {
                Navigator.pop(context, maxBackups);
              } else {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('يرجى إدخال رقم صحيح بين 1 و 100'),
                    backgroundColor: Colors.red,
                  ),
                );
              }
            },
            child: const Text('حفظ'),
          ),
        ],
      ),
    );

    if (result != null) {
      try {
        // تحديث الإعدادات
        final success = await SecureBackupService.enableAutoBackup(
          intervalHours: _backupSettings?.intervalHours ?? 24,
          maxBackups: result,
        );

        if (success) {
          await _loadBackupData();
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('تم تغيير الحد الأقصى للنسخ إلى $result نسخة'),
                backgroundColor: RevolutionaryColors.syrianGold,
              ),
            );
          }
        } else {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('فشل في تغيير الحد الأقصى للنسخ'),
                backgroundColor: Colors.red,
              ),
            );
          }
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('خطأ في تغيير الحد الأقصى للنسخ: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  Future<void> _changeBackupPassword() async {
    final TextEditingController currentPasswordController =
        TextEditingController();
    final TextEditingController newPasswordController = TextEditingController();
    final TextEditingController confirmPasswordController =
        TextEditingController();
    bool obscureCurrentPassword = true;
    bool obscureNewPassword = true;
    bool obscureConfirmPassword = true;

    final result = await showDialog<bool>(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          title: Text(
            _backupSettings?.hasBackupPassword == true
                ? 'تغيير كلمة مرور النسخ الاحتياطية'
                : 'إعداد كلمة مرور النسخ الاحتياطية',
          ),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                if (_backupSettings?.hasBackupPassword == true) ...[
                  TextField(
                    controller: currentPasswordController,
                    obscureText: obscureCurrentPassword,
                    decoration: InputDecoration(
                      labelText: 'كلمة المرور الحالية',
                      border: const OutlineInputBorder(),
                      suffixIcon: IconButton(
                        icon: Icon(
                          obscureCurrentPassword
                              ? Icons.visibility
                              : Icons.visibility_off,
                        ),
                        onPressed: () {
                          setState(() {
                            obscureCurrentPassword = !obscureCurrentPassword;
                          });
                        },
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),
                ],
                TextField(
                  controller: newPasswordController,
                  obscureText: obscureNewPassword,
                  decoration: InputDecoration(
                    labelText: 'كلمة المرور الجديدة',
                    border: const OutlineInputBorder(),
                    suffixIcon: IconButton(
                      icon: Icon(
                        obscureNewPassword
                            ? Icons.visibility
                            : Icons.visibility_off,
                      ),
                      onPressed: () {
                        setState(() {
                          obscureNewPassword = !obscureNewPassword;
                        });
                      },
                    ),
                  ),
                ),
                const SizedBox(height: 16),
                TextField(
                  controller: confirmPasswordController,
                  obscureText: obscureConfirmPassword,
                  decoration: InputDecoration(
                    labelText: 'تأكيد كلمة المرور',
                    border: const OutlineInputBorder(),
                    suffixIcon: IconButton(
                      icon: Icon(
                        obscureConfirmPassword
                            ? Icons.visibility
                            : Icons.visibility_off,
                      ),
                      onPressed: () {
                        setState(() {
                          obscureConfirmPassword = !obscureConfirmPassword;
                        });
                      },
                    ),
                  ),
                ),
                const SizedBox(height: 16),
                const Text(
                  'متطلبات كلمة المرور:\n• لا تقل عن 8 أحرف\n• تحتوي على أحرف كبيرة وصغيرة\n• تحتوي على أرقام\n• تحتوي على رموز خاصة',
                  style: TextStyle(fontSize: 12, color: Colors.grey),
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context, false),
              child: const Text('إلغاء'),
            ),
            ElevatedButton(
              onPressed: () async {
                // Store context references before async operations
                final scaffoldMessenger = ScaffoldMessenger.of(context);
                final navigator = Navigator.of(context);

                // التحقق من صحة البيانات
                if (newPasswordController.text !=
                    confirmPasswordController.text) {
                  scaffoldMessenger.showSnackBar(
                    const SnackBar(
                      content: Text('كلمة المرور وتأكيدها غير متطابقين'),
                      backgroundColor: Colors.red,
                    ),
                  );
                  return;
                }

                if (newPasswordController.text.length < 8) {
                  scaffoldMessenger.showSnackBar(
                    const SnackBar(
                      content: Text('كلمة المرور يجب أن تكون 8 أحرف على الأقل'),
                      backgroundColor: Colors.red,
                    ),
                  );
                  return;
                }

                // التحقق من كلمة المرور الحالية إذا كانت موجودة
                if (_backupSettings?.hasBackupPassword == true) {
                  final isCurrentPasswordValid =
                      await SecureBackupService.verifyBackupPassword(
                        currentPasswordController.text,
                      );
                  if (!mounted) return;

                  if (!isCurrentPasswordValid) {
                    scaffoldMessenger.showSnackBar(
                      const SnackBar(
                        content: Text('كلمة المرور الحالية غير صحيحة'),
                        backgroundColor: Colors.red,
                      ),
                    );
                    return;
                  }
                }

                navigator.pop(true);
              },
              child: const Text('حفظ'),
            ),
          ],
        ),
      ),
    );

    if (result == true) {
      try {
        final success = await SecureBackupService.setupBackupPassword(
          newPasswordController.text,
        );

        if (success) {
          await _loadBackupData();
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(
                  _backupSettings?.hasBackupPassword == true
                      ? 'تم تغيير كلمة مرور النسخ الاحتياطية بنجاح'
                      : 'تم إعداد كلمة مرور النسخ الاحتياطية بنجاح',
                ),
                backgroundColor: RevolutionaryColors.syrianGold,
              ),
            );
          }
        } else {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('فشل في تغيير كلمة مرور النسخ الاحتياطية'),
                backgroundColor: Colors.red,
              ),
            );
          }
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('خطأ في تغيير كلمة مرور النسخ الاحتياطية: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }
}
