/// خدمة إدارة وثائق الموظفين
/// توفر عمليات إدارة وثائق الموظفين ورفع الملفات
library;

import 'dart:io';
import '../database/database_helper.dart';
import '../constants/app_constants.dart';
import '../services/logging_service.dart';
import '../services/audit_service.dart';
import '../exceptions/validation_exception.dart';
import '../models/hr_models.dart';

class EmployeeDocumentService {
  final DatabaseHelper _databaseHelper = DatabaseHelper();

  /// إضافة وثيقة جديدة للموظف
  Future<EmployeeDocument> addEmployeeDocument({
    required int employeeId,
    required String documentType,
    required String documentName,
    required String filePath,
    String? description,
    DateTime? expiryDate,
    bool isRequired = false,
    required int uploadedBy,
  }) async {
    try {
      // التحقق من صحة البيانات
      if (documentName.trim().isEmpty) {
        throw ValidationException('اسم الوثيقة مطلوب');
      }
      if (filePath.trim().isEmpty) {
        throw ValidationException('مسار الملف مطلوب');
      }

      // التحقق من وجود الملف
      final file = File(filePath);
      if (!await file.exists()) {
        throw ValidationException('الملف غير موجود في المسار المحدد');
      }

      // التحقق من وجود الموظف
      final db = await _databaseHelper.database;
      final employeeExists = await db.query(
        AppConstants.employeesTable,
        where: 'id = ?',
        whereArgs: [employeeId],
        limit: 1,
      );

      if (employeeExists.isEmpty) {
        throw ValidationException('الموظف غير موجود');
      }

      // التحقق من عدم تكرار نوع الوثيقة للموظف (إذا كانت مطلوبة)
      if (isRequired) {
        final existingDocument = await _getEmployeeDocumentByType(
          employeeId,
          documentType,
        );
        if (existingDocument != null && existingDocument.isActive) {
          throw ValidationException('وثيقة من هذا النوع موجودة مسبقاً للموظف');
        }
      }

      final document = EmployeeDocument(
        employeeId: employeeId,
        documentType: documentType,
        documentName: documentName.trim(),
        filePath: filePath,
        description: description?.trim(),
        expiryDate: expiryDate,
        isRequired: isRequired,
        uploadedAt: DateTime.now(),
        uploadedBy: uploadedBy,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      // حفظ الوثيقة في قاعدة البيانات
      final id = await db.insert(AppConstants.employeeDocumentsTable, document.toMap());

      final savedDocument = document.copyWith(id: id);

      // تسجيل في سجل المراجعة
      await AuditService.log(
        action: 'CREATE',
        entityType: 'EmployeeDocument',
        entityId: id,
        description: 'إضافة وثيقة جديدة للموظف',
        newValues: savedDocument.toMap(),
      );

      LoggingService.info(
        'تم إضافة وثيقة جديدة للموظف بنجاح',
        category: 'EmployeeDocumentService',
        data: {
          'documentId': id,
          'employeeId': employeeId,
          'documentType': documentType,
          'documentName': documentName,
        },
      );

      return savedDocument;
    } catch (e) {
      LoggingService.error(
        'خطأ في إضافة وثيقة الموظف',
        category: 'EmployeeDocumentService',
        data: {
          'employeeId': employeeId,
          'documentType': documentType,
          'error': e.toString(),
        },
      );
      rethrow;
    }
  }

  /// الحصول على جميع وثائق موظف
  Future<List<EmployeeDocument>> getEmployeeDocuments(
    int employeeId, {
    bool activeOnly = true,
  }) async {
    try {
      final db = await _databaseHelper.database;
      
      String whereClause = 'employee_id = ?';
      List<dynamic> whereArgs = [employeeId];
      
      if (activeOnly) {
        whereClause += ' AND is_active = ?';
        whereArgs.add(1);
      }

      final result = await db.query(
        AppConstants.employeeDocumentsTable,
        where: whereClause,
        whereArgs: whereArgs,
        orderBy: 'created_at DESC',
      );

      return result.map((map) => EmployeeDocument.fromMap(map)).toList();
    } catch (e) {
      LoggingService.error(
        'خطأ في جلب وثائق الموظف',
        category: 'EmployeeDocumentService',
        data: {'employeeId': employeeId, 'error': e.toString()},
      );
      return [];
    }
  }

  /// الحصول على وثيقة بالمعرف
  Future<EmployeeDocument?> getDocumentById(int id) async {
    try {
      final db = await _databaseHelper.database;
      final result = await db.query(
        AppConstants.employeeDocumentsTable,
        where: 'id = ?',
        whereArgs: [id],
        limit: 1,
      );

      if (result.isNotEmpty) {
        return EmployeeDocument.fromMap(result.first);
      }
      return null;
    } catch (e) {
      LoggingService.error(
        'خطأ في جلب الوثيقة',
        category: 'EmployeeDocumentService',
        data: {'documentId': id, 'error': e.toString()},
      );
      return null;
    }
  }

  /// الحصول على وثائق منتهية الصلاحية
  Future<List<EmployeeDocument>> getExpiredDocuments() async {
    try {
      final db = await _databaseHelper.database;
      final today = DateTime.now().toIso8601String().split('T')[0];
      
      final result = await db.query(
        AppConstants.employeeDocumentsTable,
        where: 'expiry_date IS NOT NULL AND expiry_date < ? AND is_active = ?',
        whereArgs: [today, 1],
        orderBy: 'expiry_date ASC',
      );

      return result.map((map) => EmployeeDocument.fromMap(map)).toList();
    } catch (e) {
      LoggingService.error(
        'خطأ في جلب الوثائق منتهية الصلاحية',
        category: 'EmployeeDocumentService',
        data: {'error': e.toString()},
      );
      return [];
    }
  }

  /// الحصول على وثائق قريبة من انتهاء الصلاحية
  Future<List<EmployeeDocument>> getExpiringSoonDocuments({int days = 30}) async {
    try {
      final db = await _databaseHelper.database;
      final today = DateTime.now();
      final futureDate = today.add(Duration(days: days));
      
      final result = await db.query(
        AppConstants.employeeDocumentsTable,
        where: '''
          expiry_date IS NOT NULL AND 
          expiry_date > ? AND 
          expiry_date <= ? AND 
          is_active = ?
        ''',
        whereArgs: [
          today.toIso8601String().split('T')[0],
          futureDate.toIso8601String().split('T')[0],
          1,
        ],
        orderBy: 'expiry_date ASC',
      );

      return result.map((map) => EmployeeDocument.fromMap(map)).toList();
    } catch (e) {
      LoggingService.error(
        'خطأ في جلب الوثائق قريبة الانتهاء',
        category: 'EmployeeDocumentService',
        data: {'error': e.toString()},
      );
      return [];
    }
  }

  /// تحديث وثيقة
  Future<EmployeeDocument> updateDocument(EmployeeDocument document) async {
    try {
      if (document.id == null) {
        throw ValidationException('معرف الوثيقة مطلوب للتحديث');
      }

      final updatedDocument = document.copyWith(updatedAt: DateTime.now());

      final db = await _databaseHelper.database;
      await db.update(
        AppConstants.employeeDocumentsTable,
        updatedDocument.toMap(),
        where: 'id = ?',
        whereArgs: [document.id],
      );

      // تسجيل في سجل المراجعة
      await AuditService.log(
        action: 'UPDATE',
        entityType: 'EmployeeDocument',
        entityId: document.id!,
        description: 'تحديث بيانات الوثيقة',
        newValues: updatedDocument.toMap(),
      );

      LoggingService.info(
        'تم تحديث الوثيقة بنجاح',
        category: 'EmployeeDocumentService',
        data: {'documentId': document.id},
      );

      return updatedDocument;
    } catch (e) {
      LoggingService.error(
        'خطأ في تحديث الوثيقة',
        category: 'EmployeeDocumentService',
        data: {'documentId': document.id, 'error': e.toString()},
      );
      rethrow;
    }
  }

  /// حذف وثيقة (إلغاء تفعيل)
  Future<void> deleteDocument(int id) async {
    try {
      final document = await getDocumentById(id);
      if (document == null) {
        throw ValidationException('الوثيقة غير موجودة');
      }

      // إلغاء تفعيل الوثيقة بدلاً من حذفها
      final deactivatedDocument = document.copyWith(
        isActive: false,
        updatedAt: DateTime.now(),
      );

      await updateDocument(deactivatedDocument);

      // تسجيل في سجل المراجعة
      await AuditService.log(
        action: 'DELETE',
        entityType: 'EmployeeDocument',
        entityId: id,
        description: 'إلغاء تفعيل الوثيقة',
      );

      LoggingService.info(
        'تم إلغاء تفعيل الوثيقة بنجاح',
        category: 'EmployeeDocumentService',
        data: {'documentId': id},
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في حذف الوثيقة',
        category: 'EmployeeDocumentService',
        data: {'documentId': id, 'error': e.toString()},
      );
      rethrow;
    }
  }

  /// الحصول على وثيقة موظف بنوع معين
  Future<EmployeeDocument?> _getEmployeeDocumentByType(
    int employeeId,
    String documentType,
  ) async {
    try {
      final db = await _databaseHelper.database;
      final result = await db.query(
        AppConstants.employeeDocumentsTable,
        where: 'employee_id = ? AND document_type = ? AND is_active = ?',
        whereArgs: [employeeId, documentType, 1],
        limit: 1,
      );

      if (result.isNotEmpty) {
        return EmployeeDocument.fromMap(result.first);
      }
      return null;
    } catch (e) {
      LoggingService.error(
        'خطأ في جلب وثيقة الموظف بالنوع',
        category: 'EmployeeDocumentService',
        data: {
          'employeeId': employeeId,
          'documentType': documentType,
          'error': e.toString(),
        },
      );
      return null;
    }
  }

  /// الحصول على إحصائيات الوثائق
  Future<Map<String, int>> getDocumentStatistics() async {
    try {
      final db = await _databaseHelper.database;
      
      // إجمالي الوثائق النشطة
      final totalResult = await db.rawQuery(
        'SELECT COUNT(*) as count FROM ${AppConstants.employeeDocumentsTable} WHERE is_active = 1',
      );
      final totalDocuments = totalResult.first['count'] as int;

      // الوثائق منتهية الصلاحية
      final today = DateTime.now().toIso8601String().split('T')[0];
      final expiredResult = await db.rawQuery(
        'SELECT COUNT(*) as count FROM ${AppConstants.employeeDocumentsTable} WHERE expiry_date < ? AND is_active = 1',
        [today],
      );
      final expiredDocuments = expiredResult.first['count'] as int;

      // الوثائق قريبة الانتهاء (30 يوم)
      final futureDate = DateTime.now().add(const Duration(days: 30)).toIso8601String().split('T')[0];
      final expiringSoonResult = await db.rawQuery(
        'SELECT COUNT(*) as count FROM ${AppConstants.employeeDocumentsTable} WHERE expiry_date > ? AND expiry_date <= ? AND is_active = 1',
        [today, futureDate],
      );
      final expiringSoonDocuments = expiringSoonResult.first['count'] as int;

      return {
        'total': totalDocuments,
        'expired': expiredDocuments,
        'expiringSoon': expiringSoonDocuments,
        'valid': totalDocuments - expiredDocuments - expiringSoonDocuments,
      };
    } catch (e) {
      LoggingService.error(
        'خطأ في جلب إحصائيات الوثائق',
        category: 'EmployeeDocumentService',
        data: {'error': e.toString()},
      );
      return {
        'total': 0,
        'expired': 0,
        'expiringSoon': 0,
        'valid': 0,
      };
    }
  }
}
