import 'package:flutter/material.dart';
import '../constants/revolutionary_design_colors.dart';
import '../models/journal_entry.dart';
import '../services/journal_entry_service.dart';
import '../widgets/journal_entry_card.dart';
import '../widgets/loading_widget.dart';
import 'add_journal_entry_screen.dart';
import 'journal_entry_details_screen.dart';

class JournalEntriesScreen extends StatefulWidget {
  const JournalEntriesScreen({super.key});

  @override
  State<JournalEntriesScreen> createState() => _JournalEntriesScreenState();
}

class _JournalEntriesScreenState extends State<JournalEntriesScreen>
    with TickerProviderStateMixin {
  final JournalEntryService _journalEntryService = JournalEntryService();
  final TextEditingController _searchController = TextEditingController();

  List<JournalEntry> _journalEntries = [];
  List<JournalEntry> _filteredEntries = [];
  bool _isLoading = true;
  String _selectedStatus = 'all';

  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    _loadJournalEntries();
  }

  @override
  void dispose() {
    _animationController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _loadJournalEntries() async {
    setState(() => _isLoading = true);

    try {
      final entries = await _journalEntryService.getAllJournalEntries();
      setState(() {
        _journalEntries = entries;
        _filteredEntries = entries;
        _isLoading = false;
      });
      _animationController.forward();
    } catch (e) {
      setState(() => _isLoading = false);
      _showErrorSnackBar('حدث خطأ في تحميل القيود: $e');
    }
  }

  void _filterEntries() {
    setState(() {
      _filteredEntries = _journalEntries.where((entry) {
        final matchesSearch =
            entry.entryNumber.contains(_searchController.text) ||
            entry.description.toLowerCase().contains(
              _searchController.text.toLowerCase(),
            );

        final matchesStatus =
            _selectedStatus == 'all' ||
            (_selectedStatus == 'posted' && entry.isPosted) ||
            (_selectedStatus == 'draft' && !entry.isPosted);

        return matchesSearch && matchesStatus;
      }).toList();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('القيود المحاسبية'),
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: _addNewJournalEntry,
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadJournalEntries,
          ),
        ],
      ),
      body: Column(
        children: [
          _buildSearchAndFilter(),
          _buildSummaryCards(),
          Expanded(
            child: _isLoading
                ? const LoadingWidget()
                : _buildJournalEntriesList(),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchAndFilter() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: RevolutionaryColors.backgroundCard,
        boxShadow: [
          BoxShadow(
            color: RevolutionaryColors.shadowMedium,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // شريط البحث
          TextField(
            controller: _searchController,
            decoration: InputDecoration(
              hintText: 'البحث في القيود...',
              prefixIcon: const Icon(Icons.search),
              suffixIcon: _searchController.text.isNotEmpty
                  ? IconButton(
                      icon: const Icon(Icons.clear),
                      onPressed: () {
                        _searchController.clear();
                        _filterEntries();
                      },
                    )
                  : null,
            ),
            onChanged: (_) => _filterEntries(),
          ),
          const SizedBox(height: 12),
          // فلتر الحالة
          Row(
            children: [
              _buildFilterChip('all', 'الكل'),
              const SizedBox(width: 8),
              _buildFilterChip('draft', 'مسودة'),
              const SizedBox(width: 8),
              _buildFilterChip('posted', 'مرحل'),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildFilterChip(String status, String label) {
    final isSelected = _selectedStatus == status;
    return FilterChip(
      label: Text(label),
      selected: isSelected,
      onSelected: (selected) {
        setState(() {
          _selectedStatus = status;
          _filterEntries();
        });
      },
      selectedColor: RevolutionaryColors.damascusSky.withValues(alpha: 0.2),
      checkmarkColor: RevolutionaryColors.damascusSky,
    );
  }

  Widget _buildSummaryCards() {
    final totalEntries = _journalEntries.length;
    final postedEntries = _journalEntries.where((e) => e.isPosted).length;
    final draftEntries = totalEntries - postedEntries;

    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          Expanded(
            child: _buildSummaryCard(
              'إجمالي القيود',
              totalEntries.toString(),
              Icons.receipt_long,
              RevolutionaryColors.damascusSky,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: _buildSummaryCard(
              'قيود مرحلة',
              postedEntries.toString(),
              Icons.check_circle,
              RevolutionaryColors.successGlow,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: _buildSummaryCard(
              'مسودات',
              draftEntries.toString(),
              Icons.edit,
              RevolutionaryColors.warningAmber,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryCard(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.2)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            value,
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              color: color,
              fontWeight: FontWeight.bold,
            ),
          ),
          Text(
            title,
            style: Theme.of(
              context,
            ).textTheme.bodySmall?.copyWith(color: RevolutionaryColors.textSecondary),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildJournalEntriesList() {
    if (_filteredEntries.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.receipt_long_outlined,
              size: 64,
              color: RevolutionaryColors.textHint,
            ),
            const SizedBox(height: 16),
            Text(
              'لا توجد قيود محاسبية',
              style: Theme.of(
                context,
              ).textTheme.titleLarge?.copyWith(color: RevolutionaryColors.textHint),
            ),
            const SizedBox(height: 8),
            Text(
              'اضغط على + لإضافة قيد جديد',
              style: Theme.of(
                context,
              ).textTheme.bodyMedium?.copyWith(color: RevolutionaryColors.textHint),
            ),
          ],
        ),
      );
    }

    return FadeTransition(
      opacity: _fadeAnimation,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: _filteredEntries.length,
        itemBuilder: (context, index) {
          final entry = _filteredEntries[index];
          return JournalEntryCard(
            journalEntry: entry,
            onTap: () => _viewJournalEntryDetails(entry),
            onEdit: () => _editJournalEntry(entry),
            onDelete: () => _deleteJournalEntry(entry),
            onPost: () => _postJournalEntry(entry),
          );
        },
      ),
    );
  }

  Future<void> _addNewJournalEntry() async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const AddJournalEntryScreen()),
    );

    if (result == true) {
      _loadJournalEntries();
    }
  }

  void _viewJournalEntryDetails(JournalEntry entry) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => JournalEntryDetailsScreen(journalEntry: entry),
      ),
    );
  }

  Future<void> _editJournalEntry(JournalEntry entry) async {
    if (entry.isPosted) {
      _showErrorSnackBar('لا يمكن تعديل قيد مرحل');
      return;
    }

    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => AddJournalEntryScreen(journalEntryToEdit: entry),
      ),
    );

    if (result == true) {
      _loadJournalEntries();
    }
  }

  void _deleteJournalEntry(JournalEntry entry) {
    if (entry.isPosted) {
      _showErrorSnackBar('لا يمكن حذف قيد مرحل');
      return;
    }

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text('هل أنت متأكد من حذف القيد "${entry.entryNumber}"؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.pop(context);
              await _performDeleteJournalEntry(entry);
            },
            style: ElevatedButton.styleFrom(backgroundColor: RevolutionaryColors.errorCoral),
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }

  void _postJournalEntry(JournalEntry entry) {
    if (entry.isPosted) {
      _showErrorSnackBar('القيد مرحل مسبقاً');
      return;
    }

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الترحيل'),
        content: Text(
          'هل أنت متأكد من ترحيل القيد "${entry.entryNumber}"؟\nلن تتمكن من تعديله بعد الترحيل.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.pop(context);
              await _performPostJournalEntry(entry);
            },
            child: const Text('ترحيل'),
          ),
        ],
      ),
    );
  }

  Future<void> _performDeleteJournalEntry(JournalEntry entry) async {
    try {
      await _journalEntryService.deleteJournalEntry(entry.id!);
      _showSuccessSnackBar('تم حذف القيد بنجاح');
      _loadJournalEntries();
    } catch (e) {
      _showErrorSnackBar('حدث خطأ في حذف القيد: $e');
    }
  }

  Future<void> _performPostJournalEntry(JournalEntry entry) async {
    try {
      await _journalEntryService.postJournalEntry(entry.id!);
      _showSuccessSnackBar('تم ترحيل القيد بنجاح');
      _loadJournalEntries();
    } catch (e) {
      _showErrorSnackBar('حدث خطأ في ترحيل القيد: $e');
    }
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message), backgroundColor: RevolutionaryColors.successGlow),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message), backgroundColor: RevolutionaryColors.errorCoral),
    );
  }
}
