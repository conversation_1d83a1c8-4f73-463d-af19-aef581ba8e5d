/// نموذج دور المستخدم
/// يحتوي على معلومات الدور والصلاحيات المرتبطة به
class UserRole {
  final int? id;
  final String name;
  final String description;
  final bool isActive;
  final List<String> permissions;
  final DateTime createdAt;
  final DateTime updatedAt;
  final String? notes;

  const UserRole({
    this.id,
    required this.name,
    required this.description,
    required this.isActive,
    required this.permissions,
    required this.createdAt,
    required this.updatedAt,
    this.notes,
  });

  /// إنشاء UserRole من Map
  factory UserRole.fromMap(Map<String, dynamic> map) {
    return UserRole(
      id: map['id']?.toInt(),
      name: map['name'] ?? '',
      description: map['description'] ?? '',
      isActive: map['is_active'] == 1,
      permissions: (map['permissions'] as String?)?.split(',') ?? [],
      createdAt: DateTime.parse(map['created_at']),
      updatedAt: DateTime.parse(map['updated_at']),
      notes: map['notes'],
    );
  }

  /// تحويل UserRole إلى Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'is_active': isActive ? 1 : 0,
      'permissions': permissions.join(','),
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'notes': notes,
    };
  }

  /// إنشاء نسخة معدلة من UserRole
  UserRole copyWith({
    int? id,
    String? name,
    String? description,
    bool? isActive,
    List<String>? permissions,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? notes,
  }) {
    return UserRole(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      isActive: isActive ?? this.isActive,
      permissions: permissions ?? this.permissions,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      notes: notes ?? this.notes,
    );
  }

  @override
  String toString() {
    return 'UserRole(id: $id, name: $name, description: $description, isActive: $isActive, permissions: $permissions)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is UserRole &&
        other.id == id &&
        other.name == name &&
        other.description == description &&
        other.isActive == isActive;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        name.hashCode ^
        description.hashCode ^
        isActive.hashCode;
  }
}

/// الأدوار الافتراضية في النظام
class DefaultRoles {
  static const String admin = 'مدير النظام';
  static const String accountant = 'محاسب';
  static const String cashier = 'أمين صندوق';
  static const String viewer = 'مشاهد';
  static const String dataEntry = 'مدخل بيانات';

  /// الحصول على جميع الأدوار الافتراضية
  static List<UserRole> getDefaultRoles() {
    final now = DateTime.now();
    
    return [
      UserRole(
        id: 1,
        name: admin,
        description: 'صلاحيات كاملة لإدارة النظام',
        isActive: true,
        permissions: AdminPermissions.all,
        createdAt: now,
        updatedAt: now,
      ),
      UserRole(
        id: 2,
        name: accountant,
        description: 'صلاحيات المحاسبة والتقارير المالية',
        isActive: true,
        permissions: AccountantPermissions.all,
        createdAt: now,
        updatedAt: now,
      ),
      UserRole(
        id: 3,
        name: cashier,
        description: 'صلاحيات إدارة الصندوق والمبيعات',
        isActive: true,
        permissions: CashierPermissions.all,
        createdAt: now,
        updatedAt: now,
      ),
      UserRole(
        id: 4,
        name: viewer,
        description: 'صلاحيات المشاهدة فقط',
        isActive: true,
        permissions: ViewerPermissions.all,
        createdAt: now,
        updatedAt: now,
      ),
      UserRole(
        id: 5,
        name: dataEntry,
        description: 'صلاحيات إدخال البيانات الأساسية',
        isActive: true,
        permissions: DataEntryPermissions.all,
        createdAt: now,
        updatedAt: now,
      ),
    ];
  }
}

/// صلاحيات المدير
class AdminPermissions {
  static const List<String> all = [
    'user_management',
    'role_management',
    'system_settings',
    'backup_restore',
    'audit_logs',
    'financial_reports',
    'accounting_operations',
    'inventory_management',
    'invoice_management',
    'payment_management',
    'tax_management',
    'data_export',
    'system_optimization',
  ];
}

/// صلاحيات المحاسب
class AccountantPermissions {
  static const List<String> all = [
    'accounting_operations',
    'financial_reports',
    'journal_entries',
    'chart_of_accounts',
    'tax_management',
    'payment_management',
    'invoice_management',
    'data_export',
  ];
}

/// صلاحيات أمين الصندوق
class CashierPermissions {
  static const List<String> all = [
    'invoice_management',
    'payment_management',
    'sales_reports',
    'inventory_view',
    'customer_management',
  ];
}

/// صلاحيات المشاهد
class ViewerPermissions {
  static const List<String> all = [
    'view_reports',
    'view_invoices',
    'view_accounts',
    'view_inventory',
  ];
}

/// صلاحيات مدخل البيانات
class DataEntryPermissions {
  static const List<String> all = [
    'customer_management',
    'supplier_management',
    'item_management',
    'invoice_entry',
    'basic_reports',
  ];
}
