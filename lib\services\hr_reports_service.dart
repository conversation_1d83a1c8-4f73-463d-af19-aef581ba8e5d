/// خدمة تقارير الموارد البشرية
/// توفر تقارير شاملة ومتقدمة للموارد البشرية
library;

import '../database/database_helper.dart';
import '../constants/app_constants.dart';
import '../services/logging_service.dart';


/// نموذج تقرير الموظفين
class EmployeeReport {
  final int totalEmployees;
  final int activeEmployees;
  final int inactiveEmployees;
  final int terminatedEmployees;
  final Map<String, int> departmentCounts;
  final Map<String, int> positionCounts;
  final double averageSalary;
  final double totalSalaryExpense;
  final int averageAge;
  final double averageYearsOfService;

  const EmployeeReport({
    required this.totalEmployees,
    required this.activeEmployees,
    required this.inactiveEmployees,
    required this.terminatedEmployees,
    required this.departmentCounts,
    required this.positionCounts,
    required this.averageSalary,
    required this.totalSalaryExpense,
    required this.averageAge,
    required this.averageYearsOfService,
  });
}

/// نموذج تقرير الحضور
class AttendanceReport {
  final int totalWorkingDays;
  final int totalPresentDays;
  final int totalAbsentDays;
  final int totalLateDays;
  final double attendanceRate;
  final double punctualityRate;
  final double totalWorkingHours;
  final double totalOvertimeHours;
  final Map<String, double> employeeAttendanceRates;
  final Map<String, int> monthlyAttendance;

  const AttendanceReport({
    required this.totalWorkingDays,
    required this.totalPresentDays,
    required this.totalAbsentDays,
    required this.totalLateDays,
    required this.attendanceRate,
    required this.punctualityRate,
    required this.totalWorkingHours,
    required this.totalOvertimeHours,
    required this.employeeAttendanceRates,
    required this.monthlyAttendance,
  });
}

/// نموذج تقرير الرواتب
class PayrollReport {
  final double totalGrossSalary;
  final double totalNetSalary;
  final double totalTaxes;
  final double totalInsurance;
  final double totalDeductions;
  final double totalBonuses;
  final double totalAllowances;
  final Map<String, double> departmentSalaries;
  final Map<String, double> monthlySalaries;
  final double averageGrossSalary;
  final double averageNetSalary;

  const PayrollReport({
    required this.totalGrossSalary,
    required this.totalNetSalary,
    required this.totalTaxes,
    required this.totalInsurance,
    required this.totalDeductions,
    required this.totalBonuses,
    required this.totalAllowances,
    required this.departmentSalaries,
    required this.monthlySalaries,
    required this.averageGrossSalary,
    required this.averageNetSalary,
  });
}

/// نموذج تقرير القروض
class LoansReport {
  final int totalLoans;
  final int activeLoans;
  final int completedLoans;
  final double totalLoanAmount;
  final double totalPaidAmount;
  final double totalRemainingAmount;
  final double totalOverdueAmount;
  final Map<String, double> employeeLoanAmounts;
  final Map<String, int> monthlyLoans;

  const LoansReport({
    required this.totalLoans,
    required this.activeLoans,
    required this.completedLoans,
    required this.totalLoanAmount,
    required this.totalPaidAmount,
    required this.totalRemainingAmount,
    required this.totalOverdueAmount,
    required this.employeeLoanAmounts,
    required this.monthlyLoans,
  });
}

class HRReportsService {
  final DatabaseHelper _databaseHelper = DatabaseHelper();

  /// إنشاء تقرير شامل للموظفين
  Future<EmployeeReport> generateEmployeeReport({
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      final db = await _databaseHelper.database;

      // إحصائيات الموظفين العامة
      final totalResult = await db.rawQuery(
        'SELECT COUNT(*) as count FROM ${AppConstants.employeesTable}',
      );
      final totalEmployees = totalResult.first['count'] as int;

      final activeResult = await db.rawQuery(
        'SELECT COUNT(*) as count FROM ${AppConstants.employeesTable} WHERE status = ?',
        [AppConstants.employeeStatusActive],
      );
      final activeEmployees = activeResult.first['count'] as int;

      final inactiveResult = await db.rawQuery(
        'SELECT COUNT(*) as count FROM ${AppConstants.employeesTable} WHERE status = ?',
        [AppConstants.employeeStatusInactive],
      );
      final inactiveEmployees = inactiveResult.first['count'] as int;

      final terminatedResult = await db.rawQuery(
        'SELECT COUNT(*) as count FROM ${AppConstants.employeesTable} WHERE status = ?',
        [AppConstants.employeeStatusTerminated],
      );
      final terminatedEmployees = terminatedResult.first['count'] as int;

      // إحصائيات الأقسام
      final departmentResult = await db.rawQuery(
        '''
        SELECT d.name, COUNT(e.id) as count 
        FROM ${AppConstants.departmentsTable} d
        LEFT JOIN ${AppConstants.employeesTable} e ON d.id = e.department_id
        WHERE e.status = ?
        GROUP BY d.id, d.name
      ''',
        [AppConstants.employeeStatusActive],
      );

      final departmentCounts = <String, int>{};
      for (final row in departmentResult) {
        departmentCounts[row['name'] as String] = row['count'] as int;
      }

      // إحصائيات المناصب
      final positionResult = await db.rawQuery(
        '''
        SELECT p.title, COUNT(e.id) as count 
        FROM ${AppConstants.positionsTable} p
        LEFT JOIN ${AppConstants.employeesTable} e ON p.id = e.position_id
        WHERE e.status = ?
        GROUP BY p.id, p.title
      ''',
        [AppConstants.employeeStatusActive],
      );

      final positionCounts = <String, int>{};
      for (final row in positionResult) {
        positionCounts[row['title'] as String] = row['count'] as int;
      }

      // إحصائيات الرواتب
      final salaryResult = await db.rawQuery(
        '''
        SELECT AVG(basic_salary) as avg_salary, SUM(basic_salary) as total_salary
        FROM ${AppConstants.employeesTable}
        WHERE status = ?
      ''',
        [AppConstants.employeeStatusActive],
      );

      final averageSalary =
          (salaryResult.first['avg_salary'] as num?)?.toDouble() ?? 0.0;
      final totalSalaryExpense =
          (salaryResult.first['total_salary'] as num?)?.toDouble() ?? 0.0;

      // إحصائيات العمر وسنوات الخدمة (تقديرية)
      const averageAge = 35; // يمكن حسابها من تاريخ الميلاد إذا كان متوفراً
      const averageYearsOfService = 5.0; // يمكن حسابها من تاريخ التوظيف

      return EmployeeReport(
        totalEmployees: totalEmployees,
        activeEmployees: activeEmployees,
        inactiveEmployees: inactiveEmployees,
        terminatedEmployees: terminatedEmployees,
        departmentCounts: departmentCounts,
        positionCounts: positionCounts,
        averageSalary: averageSalary,
        totalSalaryExpense: totalSalaryExpense,
        averageAge: averageAge,
        averageYearsOfService: averageYearsOfService,
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في إنشاء تقرير الموظفين',
        category: 'HRReportsService',
        data: {'error': e.toString()},
      );
      rethrow;
    }
  }

  /// إنشاء تقرير الحضور والانصراف
  Future<AttendanceReport> generateAttendanceReport({
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      final db = await _databaseHelper.database;

      final start =
          startDate ?? DateTime.now().subtract(const Duration(days: 30));
      final end = endDate ?? DateTime.now();

      // إحصائيات الحضور العامة
      final totalDaysResult = await db.rawQuery(
        '''
        SELECT COUNT(DISTINCT attendance_date) as count
        FROM ${AppConstants.attendanceTable}
        WHERE attendance_date BETWEEN ? AND ?
      ''',
        [
          start.toIso8601String().split('T')[0],
          end.toIso8601String().split('T')[0],
        ],
      );

      final totalWorkingDays = totalDaysResult.first['count'] as int;

      final presentResult = await db.rawQuery(
        '''
        SELECT COUNT(*) as count
        FROM ${AppConstants.attendanceTable}
        WHERE attendance_date BETWEEN ? AND ? AND status = 'present'
      ''',
        [
          start.toIso8601String().split('T')[0],
          end.toIso8601String().split('T')[0],
        ],
      );

      final totalPresentDays = presentResult.first['count'] as int;

      final absentResult = await db.rawQuery(
        '''
        SELECT COUNT(*) as count
        FROM ${AppConstants.attendanceTable}
        WHERE attendance_date BETWEEN ? AND ? AND status = 'absent'
      ''',
        [
          start.toIso8601String().split('T')[0],
          end.toIso8601String().split('T')[0],
        ],
      );

      final totalAbsentDays = absentResult.first['count'] as int;

      final lateResult = await db.rawQuery(
        '''
        SELECT COUNT(*) as count
        FROM ${AppConstants.attendanceTable}
        WHERE attendance_date BETWEEN ? AND ? AND late_minutes > 0
      ''',
        [
          start.toIso8601String().split('T')[0],
          end.toIso8601String().split('T')[0],
        ],
      );

      final totalLateDays = lateResult.first['count'] as int;

      // حساب المعدلات
      final attendanceRate = totalWorkingDays > 0
          ? (totalPresentDays / totalWorkingDays) * 100
          : 0.0;
      final punctualityRate = totalPresentDays > 0
          ? ((totalPresentDays - totalLateDays) / totalPresentDays) * 100
          : 0.0;

      // إحصائيات ساعات العمل
      final hoursResult = await db.rawQuery(
        '''
        SELECT SUM(total_hours) as total_hours, SUM(overtime_hours) as overtime_hours
        FROM ${AppConstants.attendanceTable}
        WHERE attendance_date BETWEEN ? AND ?
      ''',
        [
          start.toIso8601String().split('T')[0],
          end.toIso8601String().split('T')[0],
        ],
      );

      final totalWorkingHours =
          (hoursResult.first['total_hours'] as num?)?.toDouble() ?? 0.0;
      final totalOvertimeHours =
          (hoursResult.first['overtime_hours'] as num?)?.toDouble() ?? 0.0;

      // معدلات الحضور للموظفين
      final employeeAttendanceResult = await db.rawQuery(
        '''
        SELECT e.first_name || ' ' || e.last_name as name,
               COUNT(CASE WHEN a.status = 'present' THEN 1 END) * 100.0 / COUNT(*) as rate
        FROM ${AppConstants.employeesTable} e
        LEFT JOIN ${AppConstants.attendanceTable} a ON e.id = a.employee_id
        WHERE a.attendance_date BETWEEN ? AND ?
        GROUP BY e.id, e.first_name, e.last_name
      ''',
        [
          start.toIso8601String().split('T')[0],
          end.toIso8601String().split('T')[0],
        ],
      );

      final employeeAttendanceRates = <String, double>{};
      for (final row in employeeAttendanceResult) {
        employeeAttendanceRates[row['name'] as String] =
            (row['rate'] as num?)?.toDouble() ?? 0.0;
      }

      // الحضور الشهري
      final monthlyAttendanceResult = await db.rawQuery(
        '''
        SELECT strftime('%Y-%m', attendance_date) as month, COUNT(*) as count
        FROM ${AppConstants.attendanceTable}
        WHERE attendance_date BETWEEN ? AND ? AND status = 'present'
        GROUP BY strftime('%Y-%m', attendance_date)
        ORDER BY month
      ''',
        [
          start.toIso8601String().split('T')[0],
          end.toIso8601String().split('T')[0],
        ],
      );

      final monthlyAttendance = <String, int>{};
      for (final row in monthlyAttendanceResult) {
        monthlyAttendance[row['month'] as String] = row['count'] as int;
      }

      return AttendanceReport(
        totalWorkingDays: totalWorkingDays,
        totalPresentDays: totalPresentDays,
        totalAbsentDays: totalAbsentDays,
        totalLateDays: totalLateDays,
        attendanceRate: attendanceRate,
        punctualityRate: punctualityRate,
        totalWorkingHours: totalWorkingHours,
        totalOvertimeHours: totalOvertimeHours,
        employeeAttendanceRates: employeeAttendanceRates,
        monthlyAttendance: monthlyAttendance,
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في إنشاء تقرير الحضور',
        category: 'HRReportsService',
        data: {'error': e.toString()},
      );
      rethrow;
    }
  }

  /// إنشاء تقرير الرواتب
  Future<PayrollReport> generatePayrollReport({
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      final db = await _databaseHelper.database;

      final start =
          startDate ?? DateTime.now().subtract(const Duration(days: 30));
      final end = endDate ?? DateTime.now();

      // إحصائيات الرواتب العامة
      final payrollResult = await db.rawQuery(
        '''
        SELECT
          SUM(gross_salary) as total_gross,
          SUM(net_salary) as total_net,
          SUM(tax_amount) as total_tax,
          SUM(insurance_amount) as total_insurance,
          SUM(deductions) as total_deductions,
          SUM(bonuses) as total_bonuses,
          SUM(allowances) as total_allowances,
          AVG(gross_salary) as avg_gross,
          AVG(net_salary) as avg_net
        FROM ${AppConstants.payrollTable}
        WHERE pay_date BETWEEN ? AND ?
      ''',
        [
          start.toIso8601String().split('T')[0],
          end.toIso8601String().split('T')[0],
        ],
      );

      final result = payrollResult.first;
      final totalGrossSalary =
          (result['total_gross'] as num?)?.toDouble() ?? 0.0;
      final totalNetSalary = (result['total_net'] as num?)?.toDouble() ?? 0.0;
      final totalTaxes = (result['total_tax'] as num?)?.toDouble() ?? 0.0;
      final totalInsurance =
          (result['total_insurance'] as num?)?.toDouble() ?? 0.0;
      final totalDeductions =
          (result['total_deductions'] as num?)?.toDouble() ?? 0.0;
      final totalBonuses = (result['total_bonuses'] as num?)?.toDouble() ?? 0.0;
      final totalAllowances =
          (result['total_allowances'] as num?)?.toDouble() ?? 0.0;
      final averageGrossSalary =
          (result['avg_gross'] as num?)?.toDouble() ?? 0.0;
      final averageNetSalary = (result['avg_net'] as num?)?.toDouble() ?? 0.0;

      // رواتب الأقسام
      final departmentSalariesResult = await db.rawQuery(
        '''
        SELECT d.name, SUM(p.gross_salary) as total_salary
        FROM ${AppConstants.departmentsTable} d
        JOIN ${AppConstants.employeesTable} e ON d.id = e.department_id
        JOIN ${AppConstants.payrollTable} p ON e.id = p.employee_id
        WHERE p.pay_date BETWEEN ? AND ?
        GROUP BY d.id, d.name
      ''',
        [
          start.toIso8601String().split('T')[0],
          end.toIso8601String().split('T')[0],
        ],
      );

      final departmentSalaries = <String, double>{};
      for (final row in departmentSalariesResult) {
        departmentSalaries[row['name'] as String] =
            (row['total_salary'] as num?)?.toDouble() ?? 0.0;
      }

      // الرواتب الشهرية
      final monthlySalariesResult = await db.rawQuery(
        '''
        SELECT strftime('%Y-%m', pay_date) as month, SUM(gross_salary) as total_salary
        FROM ${AppConstants.payrollTable}
        WHERE pay_date BETWEEN ? AND ?
        GROUP BY strftime('%Y-%m', pay_date)
        ORDER BY month
      ''',
        [
          start.toIso8601String().split('T')[0],
          end.toIso8601String().split('T')[0],
        ],
      );

      final monthlySalaries = <String, double>{};
      for (final row in monthlySalariesResult) {
        monthlySalaries[row['month'] as String] =
            (row['total_salary'] as num?)?.toDouble() ?? 0.0;
      }

      return PayrollReport(
        totalGrossSalary: totalGrossSalary,
        totalNetSalary: totalNetSalary,
        totalTaxes: totalTaxes,
        totalInsurance: totalInsurance,
        totalDeductions: totalDeductions,
        totalBonuses: totalBonuses,
        totalAllowances: totalAllowances,
        departmentSalaries: departmentSalaries,
        monthlySalaries: monthlySalaries,
        averageGrossSalary: averageGrossSalary,
        averageNetSalary: averageNetSalary,
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في إنشاء تقرير الرواتب',
        category: 'HRReportsService',
        data: {'error': e.toString()},
      );
      rethrow;
    }
  }

  /// إنشاء تقرير القروض
  Future<LoansReport> generateLoansReport({
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      final db = await _databaseHelper.database;

      final start =
          startDate ?? DateTime.now().subtract(const Duration(days: 365));
      final end = endDate ?? DateTime.now();

      // إحصائيات القروض العامة
      final loansResult = await db.rawQuery(
        '''
        SELECT
          COUNT(*) as total_loans,
          COUNT(CASE WHEN status = 'active' THEN 1 END) as active_loans,
          COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_loans,
          SUM(amount) as total_amount,
          SUM(paid_amount) as total_paid,
          SUM(amount - paid_amount) as total_remaining
        FROM ${AppConstants.loansTable}
        WHERE created_at BETWEEN ? AND ?
      ''',
        [start.toIso8601String(), end.toIso8601String()],
      );

      final result = loansResult.first;
      final totalLoans = result['total_loans'] as int;
      final activeLoans = result['active_loans'] as int;
      final completedLoans = result['completed_loans'] as int;
      final totalLoanAmount =
          (result['total_amount'] as num?)?.toDouble() ?? 0.0;
      final totalPaidAmount = (result['total_paid'] as num?)?.toDouble() ?? 0.0;
      final totalRemainingAmount =
          (result['total_remaining'] as num?)?.toDouble() ?? 0.0;

      // حساب المبالغ المتأخرة
      final overdueResult = await db.rawQuery(
        '''
        SELECT SUM(li.amount) as overdue_amount
        FROM ${AppConstants.loanInstallmentsTable} li
        JOIN ${AppConstants.loansTable} l ON li.loan_id = l.id
        WHERE li.due_date < ? AND li.status = 'pending'
      ''',
        [DateTime.now().toIso8601String().split('T')[0]],
      );

      final totalOverdueAmount =
          (overdueResult.first['overdue_amount'] as num?)?.toDouble() ?? 0.0;

      // قروض الموظفين
      final employeeLoansResult = await db.rawQuery(
        '''
        SELECT e.first_name || ' ' || e.last_name as name, SUM(l.amount) as total_amount
        FROM ${AppConstants.employeesTable} e
        JOIN ${AppConstants.loansTable} l ON e.id = l.employee_id
        WHERE l.created_at BETWEEN ? AND ?
        GROUP BY e.id, e.first_name, e.last_name
      ''',
        [start.toIso8601String(), end.toIso8601String()],
      );

      final employeeLoanAmounts = <String, double>{};
      for (final row in employeeLoansResult) {
        employeeLoanAmounts[row['name'] as String] =
            (row['total_amount'] as num?)?.toDouble() ?? 0.0;
      }

      // القروض الشهرية
      final monthlyLoansResult = await db.rawQuery(
        '''
        SELECT strftime('%Y-%m', created_at) as month, COUNT(*) as count
        FROM ${AppConstants.loansTable}
        WHERE created_at BETWEEN ? AND ?
        GROUP BY strftime('%Y-%m', created_at)
        ORDER BY month
      ''',
        [start.toIso8601String(), end.toIso8601String()],
      );

      final monthlyLoans = <String, int>{};
      for (final row in monthlyLoansResult) {
        monthlyLoans[row['month'] as String] = row['count'] as int;
      }

      return LoansReport(
        totalLoans: totalLoans,
        activeLoans: activeLoans,
        completedLoans: completedLoans,
        totalLoanAmount: totalLoanAmount,
        totalPaidAmount: totalPaidAmount,
        totalRemainingAmount: totalRemainingAmount,
        totalOverdueAmount: totalOverdueAmount,
        employeeLoanAmounts: employeeLoanAmounts,
        monthlyLoans: monthlyLoans,
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في إنشاء تقرير القروض',
        category: 'HRReportsService',
        data: {'error': e.toString()},
      );
      rethrow;
    }
  }

  /// تصدير التقرير إلى CSV
  Future<String> exportReportToCSV(
    String reportType,
    Map<String, dynamic> data,
  ) async {
    try {
      final buffer = StringBuffer();

      switch (reportType) {
        case 'employees':
          buffer.writeln('النوع,العدد');
          buffer.writeln('إجمالي الموظفين,${data['totalEmployees']}');
          buffer.writeln('الموظفين النشطين,${data['activeEmployees']}');
          buffer.writeln('الموظفين غير النشطين,${data['inactiveEmployees']}');
          buffer.writeln('الموظفين المنتهين,${data['terminatedEmployees']}');
          break;

        case 'attendance':
          buffer.writeln('النوع,العدد');
          buffer.writeln('إجمالي أيام العمل,${data['totalWorkingDays']}');
          buffer.writeln('أيام الحضور,${data['totalPresentDays']}');
          buffer.writeln('أيام الغياب,${data['totalAbsentDays']}');
          buffer.writeln('أيام التأخير,${data['totalLateDays']}');
          break;

        case 'payroll':
          buffer.writeln('النوع,المبلغ');
          buffer.writeln('إجمالي الراتب الإجمالي,${data['totalGrossSalary']}');
          buffer.writeln('إجمالي الراتب الصافي,${data['totalNetSalary']}');
          buffer.writeln('إجمالي الضرائب,${data['totalTaxes']}');
          buffer.writeln('إجمالي التأمين,${data['totalInsurance']}');
          break;

        case 'loans':
          buffer.writeln('النوع,العدد/المبلغ');
          buffer.writeln('إجمالي القروض,${data['totalLoans']}');
          buffer.writeln('القروض النشطة,${data['activeLoans']}');
          buffer.writeln('القروض المكتملة,${data['completedLoans']}');
          buffer.writeln('إجمالي مبلغ القروض,${data['totalLoanAmount']}');
          break;
      }

      LoggingService.info(
        'تم تصدير التقرير إلى CSV',
        category: 'HRReportsService',
        data: {'reportType': reportType},
      );

      return buffer.toString();
    } catch (e) {
      LoggingService.error(
        'خطأ في تصدير التقرير',
        category: 'HRReportsService',
        data: {'reportType': reportType, 'error': e.toString()},
      );
      rethrow;
    }
  }
}
